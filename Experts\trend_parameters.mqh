// Параметры для Guppy Multiple Moving Average (GMMA) и ATR фильтра

// Настройки Guppy Multiple Moving Average (GMMA)
input string __gmma__ = "Настройки Guppy Multiple Moving Average (GMMA)";
// Использовать GMMA для определения тренда
input bool   GMMA_UseGuppy = true;
// Периоды краткосрочных EMA для GMMA (строка через запятую, например: "3,5,8,10,12,15")
input string GMMA_Short_Periods = "3,5,8,10,12,15";
// Периоды долгосрочных EMA для GMMA (строка через запятую, например: "30,35,40,45,50,60")
input string GMMA_Long_Periods = "30,35,40,45,50,60";
// Метод усреднения для GMMA (рекомендуется EMA для классического GMMA)
input ENUM_MA_METHOD GMMA_MA_Method = MODE_EMA;
// Применяемая цена для GMMA
input ENUM_APPLIED_PRICE GMMA_AppliedPrice = PRICE_CLOSE;

// Настройки ATR фильтра волатильности
input string __atr_filter__ = "Настройки ATR фильтра волатильности";
// Использовать ATR фильтр для подтверждения сигналов и оценки волатильности
input bool   ATR_UseFilter = true;
// Период ATR
input int    ATR_Period = 14; // Стандартный период для ATR
// Множитель ATR. Используется для определения порога значимой волатильности.
// Например, если 1.0, то сигнал подтверждается, если текущий ATR выше среднего ATR за ATR_Period.
// Может также использоваться для динамических стопов/тейков.
input double ATR_ThresholdMultiplier = 1.0;
// Минимальное абсолютное значение ATR в пунктах для рассмотрения торговли (0 - не используется).
// Помогает избежать торговли в периоды крайне низкой волатильности.
input int    ATR_MinAbsoluteValue_pips = 0; 