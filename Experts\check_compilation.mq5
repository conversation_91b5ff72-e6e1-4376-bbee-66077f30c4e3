//+------------------------------------------------------------------+
//| Check Compilation                                                |
//+------------------------------------------------------------------+
#property copyright "Test"
#property version   "1.00"

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("Компиляция прошла успешно!");
   Print("Время компиляции: ", TimeToString(TimeCurrent()));
   
   // Проверим основные символьные константы
   double min_lot = SymbolInfoDouble("EURUSD", SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble("EURUSD", SYMBOL_VOLUME_MAX);
   double lot_step = SymbolInfoDouble("EURUSD", SYMBOL_VOLUME_STEP);
   
   Print("EURUSD лот: мин=", min_lot, " макс=", max_lot, " шаг=", lot_step);
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
}
