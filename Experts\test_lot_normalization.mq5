//+------------------------------------------------------------------+
//| Test Lot Normalization                                           |
//+------------------------------------------------------------------+
#property copyright "Test"
#property version   "1.00"

//+------------------------------------------------------------------+
//| Нормализация размера лота                                       |
//+------------------------------------------------------------------+
double NormalizeLot(double lot, string symbol = "EURUSD")
{
   double min_lot = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MAX);
   double lot_step = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);
   
   if(lot_step <= 0) lot_step = 0.01; // Защита от нулевого шага
   
   // Округляем до ближайшего шага
   lot = MathRound(lot / lot_step) * lot_step;
   
   // Ограничиваем минимумом и максимумом
   lot = MathMax(min_lot, MathMin(max_lot, lot));
   
   // Дополнительная проверка на корректность
   if(lot < min_lot) lot = min_lot;
   
   // Форматируем до 2 знаков после запятой для стандартных лотов
   lot = NormalizeDouble(lot, 2);
   
   Print("Нормализация лота: вход=", DoubleToString(lot, 8), 
         " мин=", DoubleToString(min_lot, 2),
         " макс=", DoubleToString(max_lot, 2),
         " шаг=", DoubleToString(lot_step, 4),
         " результат=", DoubleToString(lot, 2));
   
   return lot;
}

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("=== Тест нормализации лотов ===");
   
   // Тестируем проблемные значения из лога
   double test_lots[] = {0.02666667, 0.01777778, 0.0533, 0.0800};
   
   for(int i = 0; i < ArraySize(test_lots); i++)
   {
      double normalized = NormalizeLot(test_lots[i]);
      Print("Тест ", i+1, ": ", DoubleToString(test_lots[i], 8), " -> ", DoubleToString(normalized, 2));
   }
   
   Print("=== Тест завершен ===");
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
}
