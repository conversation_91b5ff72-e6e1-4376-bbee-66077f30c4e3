# 🧠 Реализация базы знаний

_As вы выполняете задачи и ссылаетесь на соответствующие файлы, обновите этот файл как нашу память, чтобы помочь с будущими задачами._

Этот файл структурирован для отслеживания разработки системы базы знаний и интеграции с крупными языковыми моделями. Пожалуйста, держите этот файл обновленным по мере продвижения курсора через поток реализации. ✅




## 🧠 Правила курсора

При взаимодействии с этим файлом:

&gt; Всегда обновляйте выполненные задачи с [x], когда закончите.
&gt; Приведите одобрение пользователя перед началом следующего этапа.
&gt; Войдите в систему любые соответствующие файлы, измененные рядом с задачей (в комментарии или суб-булле).
&gt; Не выполняйте будущие задачи до явного запроса.
&gt; Задачи сгруппированы на этапе разработки. Следуйте по заказу.
&gt; Используйте последовательное именование и структуру при добавлении новых задач для улучшения читаемости и навигации.
&gt; Ссылка на связанную документацию или ресурсы, если применимо для более легкого контекста и отслеживания.
&gt; Если задача заблокирована, четко отметьте причину и любые зависимости или решения, необходимые для продолжения.
&gt; Просмотрите завершенные фазы для полноты, прежде чем отмечать готовую фазу.
&gt; При сотрудничестве, четко назначьте ответственность за задачи (например, инициалы или название команды).
&gt; Добавьте временные метки (необязательно), когда задачи выполнены или обновлены, чтобы поддерживать график.
&gt; Используйте встроенные комментарии для незначительных заметок или обоснования, если реализация отклоняется от исходного плана.
&gt; Держите этот файл наклоняться - архив или суммировать более старые задачи, когда фаза полностью завершена и больше не ссылается активно.
