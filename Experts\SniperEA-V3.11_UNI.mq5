// SniperEA-V3.11_UNI для МТ5
// Оригинальная логика советника SniperEA-V3.11_UNI.mq4 перенесенная в МТ5
// Copyright © 2009, TradingSystemForex.Com
// http://www.tradingsystemforex.com/

#property copyright "Copyright © 2009, TradingSystemForex.Com"
#property link "http://www.tradingsystemforex.com/"
#property version "3.11"

// Включение необходимых библиотек для торговли в MT5
#include <Trade\Trade.mqh>
#include <Arrays\ArrayDouble.mqh>

// Создаем объект для торговли
CTrade trade;

// Внешние параметры советника
input string g_comment="Sniper_TMA";      // комментарий для ордеров
input string IndicatorName = "super-arrow-indicator";  // имя индикатора
input int BuyBuffer = 0;                // буфер для сигналов покупки
input int SellBuffer = 1;               // буфер для сигналов продажи

input int magic=1234;                   // магическое число для идентификации ордеров

input string moneymanagement="Money Management";

double g_lots=0.1;                  // размер лота
input bool lotsoptimized=false;         // включить управление рисками
input double risk=1;                    // риск в процентах от депозита
input double minlot=0.01;               // минимальный допустимый лот
input double maxlot=10;                 // максимальный допустимый лот
input double lotdigits=2;               // 1 для минилотов, 2 для микролотов

input string ordersmanagement="Order Management";

input bool oppositeclose=true;          // закрывать ордера при противоположном сигнале
input bool reversesignals=false;        // инвертировать сигналы
input int maxtrades=100;                // максимальное количество сделок
input int tradesperbar=1;               // максимальное количество сделок на бар
input int lstoploss=80;                 // стоп-лосс для длинных позиций
input int sstoploss=80;                 // стоп-лосс для коротких позиций
input int ltakeprofit=0;                // тейк-профит для длинных позиций
input int ltakeprofit2=0;               // тейк-профит 2 для длинных позиций
input int ltakeprofit3=0;               // тейк-профит 3 для длинных позиций
input int ltakeprofit4=0;               // тейк-профит 4 для длинных позиций
input int ltakeprofit5=0;               // тейк-профит 5 для длинных позиций
input int stakeprofit=0;                // тейк-профит для коротких позиций
input int stakeprofit2=0;               // тейк-профит 2 для коротких позиций
input int stakeprofit3=0;               // тейк-профит 3 для коротких позиций
input int stakeprofit4=0;               // тейк-профит 4 для коротких позиций
input int stakeprofit5=0;               // тейк-профит 5 для коротких позиций
input int trailingstart=50;             // прибыль в пунктах для активации трейлинг-стопа
input int trailing_stop_pts=50;         // трейлинг-стоп
input int trailingstep=10;              // шаг трейлинг-стопа
input int breakevengain=0;              // прибыль в пунктах для активации безубытка
input int breakeven_level=0;            // уровень безубытка
input int g_expiration=0;                 // срок истечения для отложенных ордеров (в минутах)
input int slippage=0;                   // максимальное проскальзывание в пунктах
input double maxspread=100;             // максимально допустимый спред

input string advordersmanagement="Advanced Order Management";

input bool open5orders=false;           // открывать пять ордеров с разными SL и TP
input bool closeonbadconditions=false;  // закрывать ордера, если условия не выполняются
input bool closeonarrows=false;         // выходить по стрелкам
input bool autostop=false;              // стоп-лосс на основе Sniper Stop v2
input int autostopmargin=0;             // отступ для автостопа
input int minimumstop=300;              // минимальный стоп-лосс при включенном автостопе
input int maximumstop=1000;             // максимальный стоп-лосс при включенном автостопе
input bool notradeovermaxstop=false;    // не торговать, если расстояние больше maximum stop
input bool trailonsniperstop=false;     // трейлинг-стоп на основе Sniper Stop v2

input string entrylogics="Entry Logics";

input bool onlycross=false;             // входить только при пересечении
input bool usesniper=true;              // использовать индикатор Sniper для сигнала
input int SniperPeriod=30;              // период индикатора Sniper
input int SniperType=3;                 // тип индикатора Sniper
input int arrow_shift=30;               // сдвиг стрелки индикатора Sniper
input int Alerts=1;                     // оповещения индикатора Sniper
input bool usestop=false;               // использовать Sniper Stop v2 для сигнала
input bool usetrenda=false;             // использовать Sniper Trend A для сигнала
input bool usetrendb=false;             // использовать Sniper Trend B для сигнала
input double Sniper=0.01;               // параметр Sniper для Sniper Trend B
input double SniperSS=0.1;              // параметр SniperSS для Sniper Trend B
input int SniperP=7;                    // параметр SniperP для Sniper Trend B
input bool AlertsEnabled=false;         // включить оповещения Sniper Trend B
input int g_shift=1;                      // бар в прошлом для анализа сигнала

input string filersettings="Filter Settings";

input bool usefilter=false;             // использовать вторую систему Sniper на другом таймфрейме
input ENUM_TIMEFRAMES timeframe=PERIOD_H4; // таймфрейм фильтра
input bool onlycrosstf=false;           // входить только при пересечении на TF
input bool usesnipertf=true;            // использовать индикатор Sniper для фильтра
input int SniperPeriodtf=30;            // период индикатора Sniper для фильтра
input int SniperTypetf=3;               // тип индикатора Sniper для фильтра
input int arrow_shifttf=30;             // сдвиг стрелки индикатора Sniper для фильтра
input int Alertstf=1;                   // оповещения индикатора Sniper для фильтра
input bool usestoptf=false;             // использовать Sniper Stop v2 для фильтра
input bool usetrendatf=false;           // использовать Sniper Trend A для фильтра
input bool usetrendbtf=false;           // использовать Sniper Trend B для фильтра
input double Snipertf=0.01;             // параметр Sniper для Sniper Trend B для фильтра
input double SniperSStf=0.1;            // параметр SniperSS для Sniper Trend B для фильтра
input int SniperPtf=7;                  // параметр SniperP для Sniper Trend B для фильтра
input bool AlertsEnabledtf=false;       // включить оповещения Sniper Trend B для фильтра

input string extrafeatures="Extra Features";

input bool samedirection=true;          // продолжать открывать ордера в том же направлении
input bool minclosetime=false;          // закрывать сделку только после определенного времени
input int mincloseminutes=61;           // минимальное время в минутах

input string timefilter="Time Filter";

input int gmtshift=2;                   // GMT смещение брокера
input bool filter=false;                // использовать фильтр времени
input int trade_start_hour=4;           // начинать торговлю после этого часа
input int trade_end_hour=20;            // прекращать торговлю после этого часа
input bool tradesunday=true;            // торговать в воскресенье
input bool fridayfilter=false;          // включить специальный фильтр времени в пятницу
input int fridayend=24;                 // прекращать торговлю в пятницу после этого часа

// Переменные для хранения состояния
datetime t0,t1,lastbuyopentime,lastsellopentime;
double lastbuyopenprice,lastsellopenprice,lastbuyprofit,lastsellprofit,lastprofit;
double lotsfactor=1,initiallotsfactor=1,ilots,lastbuylot,lastselllot,lastlot,mlots;
double ask,bid,cb,g_sl,g_tp,min,max;
int loop_i,bc=-1,tpb,tps,cnt,istart,iend,g_total,pos_ticket;
int lastorder,buyorderprofit,sellorderprofit,history;

int directioninstance=0;

double pt,mt;
int dg;

// Буферы для индикаторов
double sniperstop_buffer1[];
double sniperstop_buffer2[];
double sniper_buffer1[];
double sniper_buffer2[];
double sniper_buffer3[];
double sniper_buffer4[];
double sniper_buffer5[];
double sniper_buffer6[];
double trend_a_buffer1[];
double trend_a_buffer2[];
double trend_b_buffer1[];
double trend_b_buffer2[];
double shma_buffer1[];
double shma_buffer2[];
double shma_buffer3[];
// Буферы для таймфрейм-фильтра
double trenda_buy_tf[];
double trenda_sell_tf[];
double trendb_buy_tf[];
double trendb_sell_tf[];

// Глобальные переменные
datetime last_bar_time = 0;
int max_positions = 100;  // Максимальное количество позиций
int g_signal = 0;  // Текущий сигнал (0 - нет сигнала, 1 - сигнал на покупку, 2 - сигнал на продажу)
ulong g_pos_ticket = 0;  // Глобальный тикет позиции
double last_ask = 0;     // Последнее значение Ask
double last_bid = 0;     // Последнее значение Bid
double last_spread = 0;  // Последнее значение спреда
datetime last_print_time = 0;  // Время последнего вывода
datetime last_calculation_time = 0;  // Время последнего расчета индикаторов
int calculation_interval = 1;  // Интервал расчета индикаторов в секундах

// Массивы для хранения сигналов индикатора Super Arrow
double arrow_buy[];  // Массив для сигналов покупки
double arrow_sell[]; // Массив для сигналов продажи

// Переменные для работы с историей МТ5
int OnInit()
{
    // Инициализация переменных
    t0=TimeCurrent();
    t1=TimeCurrent();
    dg=_Digits;
    
    // Корректная инициализация времени последнего бара
    last_bar_time = iTime(_Symbol, _Period, 0);
    Print("OnInit: Инициализация советника");
    Print("OnInit: Текущее время: ", TimeToString(TimeCurrent()));
    Print("OnInit: Время последнего бара: ", TimeToString(last_bar_time));
    
    // Определение размера пункта
    if(dg==3 || dg==5){
        pt=_Point*10;
        mt=10;
    } else {
        pt=_Point;
        mt=1;
    }
    
    Print("OnInit: Размер пункта: ", pt);
    Print("OnInit: Мультипликатор: ", mt);
    
    // Настройка торгового объекта
    trade.SetExpertMagicNumber(magic);
    trade.SetDeviationInPoints(slippage);
    trade.SetMarginMode();
    trade.SetTypeFillingBySymbol(_Symbol);
    
    // Выделение памяти для буферов индикаторов
    ArrayResize(sniperstop_buffer1, 1000);
    ArrayResize(sniperstop_buffer2, 1000);
    ArrayResize(sniper_buffer1, 1000);
    ArrayResize(sniper_buffer2, 1000);
    ArrayResize(sniper_buffer3, 1000);
    ArrayResize(sniper_buffer4, 1000);
    ArrayResize(sniper_buffer5, 1000);
    ArrayResize(sniper_buffer6, 1000);
    ArrayResize(trend_a_buffer1, 1000);
    ArrayResize(trend_a_buffer2, 1000);
    ArrayResize(trend_b_buffer1, 1000);
    ArrayResize(trend_b_buffer2, 1000);
    ArrayResize(shma_buffer1, 1000);
    ArrayResize(shma_buffer2, 1000);
    ArrayResize(shma_buffer3, 1000);
    
    // Инициализация буферов индикаторов
    ArrayInitialize(arrow_buy, 0);
    ArrayInitialize(arrow_sell, 0);
    
    // Принудительно рассчитываем индикаторы при запуске
    CalculateIndicators();
    
    Print("OnInit: Советник инициализирован успешно");
    
    return(INIT_SUCCEEDED);
}

// Функция для расчета SHMA индикатора (без проверки ключа)
void CalculateSHMA(int period, ENUM_MA_METHOD method, int price, double &buffer[], double &buffer_up[], double &buffer_down[], int bars_to_calculate)
{
    int counted_bars = bars_to_calculate;
    
    // Вспомогательные массивы
    double temp_buffer[];
    double direction_buffer[];
    
    // Выделяем память
    ArrayResize(temp_buffer, bars_to_calculate);
    ArrayResize(direction_buffer, bars_to_calculate);
    
    // Устанавливаем индекс массива как временные ряды
    ArraySetAsSeries(temp_buffer, true);
    ArraySetAsSeries(direction_buffer, true);
    ArraySetAsSeries(buffer, true);
    ArraySetAsSeries(buffer_up, true);
    ArraySetAsSeries(buffer_down, true);
    
    // Период для sqrt HMA
    int period_sqrt = (int)MathSqrt(period);
    
    for(int i=0; i<bars_to_calculate; i++)
    {
        // Расчет Hull Moving Average
        int ma_handle1 = iMA(_Symbol, _Period, period/2, 0, method, price);
        int ma_handle2 = iMA(_Symbol, _Period, period, 0, method, price);
        
        double ma_buffer1[], ma_buffer2[];
        ArraySetAsSeries(ma_buffer1, true);
        ArraySetAsSeries(ma_buffer2, true);
        
        CopyBuffer(ma_handle1, 0, i, 1, ma_buffer1);
        CopyBuffer(ma_handle2, 0, i, 1, ma_buffer2);
        
        double wma1 = ma_buffer1[0];
        double wma2 = ma_buffer2[0];
        
        temp_buffer[i] = 2.0 * wma1 - wma2;
        
        // Освобождаем хэндлы индикаторов
        IndicatorRelease(ma_handle1);
        IndicatorRelease(ma_handle2);
    }
    
    // Применяем MA к результатам HMA
    for(int i=0; i<bars_to_calculate-period; i++)
    {
        // В MQL5 нет прямого аналога iMAOnArray из MQL4, реализуем вручную
        double sum = 0.0;
        for(int j = 0; j < period_sqrt; j++)
        {
            sum += temp_buffer[i + j];
        }
        buffer[i] = sum / period_sqrt;
    }
    
    // Заполняем буферы направления тренда
    for(int i=bars_to_calculate-period; i>=0; i--)
    {
        direction_buffer[i] = direction_buffer[i+1];
        
        if(buffer[i] > buffer[i+1])
            direction_buffer[i] = 1;
            
        if(buffer[i] < buffer[i+1])
            direction_buffer[i] = -1;
            
        if(direction_buffer[i] > 0.0)
        {
            buffer_up[i] = buffer[i];
            if(direction_buffer[i+1] < 0.0)
                buffer_up[i+1] = buffer[i+1];
                
            buffer_down[i] = EMPTY_VALUE;
        }
        else if(direction_buffer[i] < 0.0)
        {
            buffer_down[i] = buffer[i];
            if(direction_buffer[i+1] > 0.0)
                buffer_down[i+1] = buffer[i+1];
                
            buffer_up[i] = EMPTY_VALUE;
        }
    }
}

// Функция для расчета Sniper Stop v2 (без проверки даты)
void CalculateSniperStop(double &buffer_up[], double &buffer_down[], int bars_to_calculate)
{
    double gd_76 = 15.0;
    double g_period_84 = 14.0;
    double gd_92 = 3.0;
    double gd_100 = 0.0;
    
    double temp_bufferUP[];
    double temp_bufferDN[];
    double trend_buffer[];
    
    // Выделяем память
    ArrayResize(temp_bufferUP, bars_to_calculate);
    ArrayResize(temp_bufferDN, bars_to_calculate);
    ArrayResize(trend_buffer, bars_to_calculate);
    
    // Устанавливаем индекс массива как временные ряды
    ArraySetAsSeries(temp_bufferUP, true);
    ArraySetAsSeries(temp_bufferDN, true);
    ArraySetAsSeries(trend_buffer, true);
    ArraySetAsSeries(buffer_up, true);
    ArraySetAsSeries(buffer_down, true);
    
    for(int i=bars_to_calculate-1; i>=0; i--)
    {
        // Поиск максимума и минимума
        int highest = iHighest(_Symbol, _Period, MODE_HIGH, (int)gd_76, i+(int)gd_100);
        int lowest = iLowest(_Symbol, _Period, MODE_LOW, (int)gd_76, i+(int)gd_100);
        
        // Расчет значений индикатора
        int atr_handle = iATR(_Symbol, _Period, (int)g_period_84);
        double atr_buffer[];
        ArraySetAsSeries(atr_buffer, true);
        CopyBuffer(atr_handle, 0, i+(int)gd_100, 1, atr_buffer);
        double atr_value = atr_buffer[0];
        
        temp_bufferUP[i] = iHigh(_Symbol, _Period, highest) - gd_92 * atr_value;
        temp_bufferDN[i] = iLow(_Symbol, _Period, lowest) + gd_92 * atr_value;
        
        // Определение тренда
        // Проверяем, что i+1 находится в пределах массива
        if(i+1 < ArraySize(trend_buffer))
            trend_buffer[i] = trend_buffer[i+1];
        else
            trend_buffer[i] = 0; // Устанавливаем значение по умолчанию, если i+1 выходит за пределы массива
        
        // Проверяем доступ к temp_bufferDN[i+1]
        if(i+1 < ArraySize(temp_bufferDN) && iClose(_Symbol, _Period, i) > temp_bufferDN[i+1])
            trend_buffer[i] = 1;
            
        // Проверяем доступ к temp_bufferUP[i+1]
        if(i+1 < ArraySize(temp_bufferUP) && iClose(_Symbol, _Period, i) < temp_bufferUP[i+1])
            trend_buffer[i] = -1;
            
        // Заполняем буферы индикатора
        if(trend_buffer[i] > 0.0)
        {
            // Проверяем доступ к temp_bufferUP[i+1]
            if(i+1 < ArraySize(temp_bufferUP) && temp_bufferUP[i] < temp_bufferUP[i+1])
                temp_bufferUP[i] = temp_bufferUP[i+1];
                
            buffer_up[i] = temp_bufferUP[i];
            buffer_down[i] = EMPTY_VALUE;
        }
        
        if(trend_buffer[i] < 0.0)
        {
            // Проверяем доступ к temp_bufferDN[i+1]
            if(i+1 < ArraySize(temp_bufferDN) && temp_bufferDN[i] > temp_bufferDN[i+1])
                temp_bufferDN[i] = temp_bufferDN[i+1];
                
            buffer_up[i] = EMPTY_VALUE;
            buffer_down[i] = temp_bufferDN[i];
        }
    }
}

// Функция для расчета Sniper Trend A (без проверки даты)
void CalculateTrendA(double &buffer_up[], double &buffer_down[], int bars_to_calculate)
{
    int li_16 = 7;
    double ld_20 = 0.7;
    double lda_64[100];
    
    // Выделяем память и настраиваем массивы
    ArraySetAsSeries(buffer_up, true);
    ArraySetAsSeries(buffer_down, true);
    
    // Получаем данные цен
    MqlRates rates[];
    ArraySetAsSeries(rates, true);
    int copied = CopyRates(_Symbol, _Period, 0, bars_to_calculate, rates);
    
    // Получаем спред в пунктах
    double spread = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE) * _Point;
    
    // Инициализация
    bool li_36 = true;  // начальное направление тренда
    double ld_40 = rates[2].close;
    int l_index_68 = 0;
    double ld_92 = 0;
    int li_108 = 0;
    
    // Определяем начальное направление тренда
    if(rates[2].close > rates[1].close)
        li_36 = true;
    else
        li_36 = false;
    
    // Основной цикл расчета
    for(int i=3; i<100 && i<copied; i++)
    {
        // Рассчитываем показатель волатильности
        double ld_56 = spread + rates[i].high - rates[i].low;
        
        // Проверяем доступ к элементу rates[i+1]
        if(i+1 < copied)
        {
            if(MathAbs(spread + rates[i].high - rates[i+1].close) > ld_56)
                ld_56 = MathAbs(spread + rates[i].high - rates[i+1].close);
                
            if(MathAbs(rates[i].low - rates[i+1].close) > ld_56)
                ld_56 = MathAbs(rates[i].low - rates[i+1].close);
        }
        
        // Заполнение массива весов
        if(i == 3)
        {
            for(int j=0; j<li_16; j++)
                lda_64[j] = ld_56;
        }
        
        lda_64[l_index_68] = ld_56;
        
        // Расчет взвешенного показателя
        double ld_72 = 0;
        double ld_84 = li_16;
        int li_112 = l_index_68;
        
        for(int j=0; j<li_16; j++)
        {
            ld_72 += lda_64[li_112] * ld_84;
            ld_84 -= 1.0;
            li_112--;
            
            if(li_112 == -1)
                li_112 = li_16-1;
        }
        
        ld_72 = 2.0 * ld_72 / (li_16 * (li_16 + 1.0));
        l_index_68++;
        
        if(l_index_68 == li_16)
            l_index_68 = 0;
            
        double ld_48 = ld_20 * ld_72;
        
        // Определение тренда
        if(li_36 && rates[i].low < ld_40 - ld_48)
        {
            li_36 = false;
            ld_40 = spread + rates[i].high;
        }
        
        if(!li_36 && spread + rates[i].high > ld_40 + ld_48)
        {
            li_36 = true;
            ld_40 = rates[i].low;
        }
        
        if(li_36 && rates[i].low > ld_40)
            ld_40 = rates[i].low;
            
        if(!li_36 && spread + rates[i].high < ld_40)
            ld_40 = spread + rates[i].high;
        
        // Создаем хэндл ATR
        int atrHandle = iATR(_Symbol, _Period, 10);
        double atrBuffer[];
        ArraySetAsSeries(atrBuffer, true);
        
        // Копируем значение ATR
        CopyBuffer(atrHandle, 0, i, 1, atrBuffer);
        double ld_116 = atrBuffer[0] + spread/10.0;
        
        double ld_124 = 2.0;
        double ld_100;
        
        if(li_36)
        {
            if(rates[i].low - ld_116 * ld_124 < ld_92 && ld_92 != 0.0)
                ld_100 = ld_92;
            else
                ld_100 = rates[i].low - ld_116 * ld_124 / 3.0;
                
            if(li_108 == 2)
                ld_100 = rates[i].low - ld_116 * ld_124 / 3.0;
            
            // Проверка на выход за пределы массива
            int idx = 99-i;
            if(idx >= 0 && idx < ArraySize(trenda_buy_tf) && idx < ArraySize(trenda_sell_tf))
            {
                trenda_buy_tf[idx] = ld_100; // Инвертируем индекс, т.к. массив сортирован в обратном порядке
                trenda_sell_tf[idx] = 0;
            }
            
            ld_92 = ld_100;
            li_108 = 1;
        }
        else
        {
            if(spread + rates[i].high + ld_116 * ld_124 > ld_92 && ld_92 != 0.0)
                ld_100 = ld_92;
            else
                ld_100 = spread + rates[i].high + ld_116 * ld_124 / 3.0;
                
            if(li_108 == 1)
                ld_100 = spread + rates[i].high + ld_116 * ld_124 / 3.0;
            
            // Проверка на выход за пределы массива
            int idx = 99-i;
            if(idx >= 0 && idx < ArraySize(trenda_buy_tf) && idx < ArraySize(trenda_sell_tf))
            {
                trenda_buy_tf[idx] = 0;
                trenda_sell_tf[idx] = ld_100;
            }
            
            ld_92 = ld_100;
            li_108 = 2;
        }
    }
}

// Функция для расчета Sniper Trend B (без проверки даты)
void CalculateTrendB(double Sniper_param, double SniperSS_param, int SniperP_param, bool AlertsEnabled_param, double &buffer_up[], double &buffer_down[], int bars_to_calculate)
{
    // Выделяем память и настраиваем массивы
    ArraySetAsSeries(buffer_up, true);
    ArraySetAsSeries(buffer_down, true);
    
    // Основной цикл расчета
    for(int i=0; i<bars_to_calculate; i++)
    {
        // Расчет Parabolic SAR
        int sar_handle = iSAR(_Symbol, _Period, Sniper_param, SniperSS_param);
        double sar_buffer[];
        ArraySetAsSeries(sar_buffer, true);
        CopyBuffer(sar_handle, 0, i, 1, sar_buffer);
        double sar = NormalizeDouble(sar_buffer[0], SniperP_param);
        
        // Определение направления тренда
        if(sar >= iHigh(_Symbol, _Period, i))
        {
            buffer_up[i] = sar;
            buffer_down[i] = 0;
        }
        else
        {
            buffer_up[i] = 0;
            buffer_down[i] = sar;
        }
    }
}

// Функция для расчета Super Arrow Indicator (замена для super-arrow-indicator)
void CalculateSuperArrow(double &buffer_buy[], double &buffer_sell[], int bars_to_calculate)
{
    // Параметры индикатора
    int FasterMovingAverage = 5;
    int SlowerMovingAverage = 12;
    int RSIPeriod = 12;
    int MagicFilterPeriod = 1;
    int BollingerbandsPeriod = 10;
    int BollingerbandsShift = 0;
    double BollingerbandsDeviation = 0.5;
    int BullsPowerPeriod = 50;
    int BearsPowerPeriod = 50;
    int Utstup = 10;
    
    // Выделяем память и настраиваем массивы
    ArraySetAsSeries(buffer_buy, true);
    ArraySetAsSeries(buffer_sell, true);
    
    // Флаги для отслеживания условий
    bool Gi_148 = false;
    bool Gi_168 = false;
    bool Gi_144 = false;
    bool Gi_164 = false;
    bool Gi_140 = false;
    bool Gi_160 = false;
    bool Gi_136 = false;
    bool Gi_156 = false;
    bool Gi_132 = false;
    bool Gi_152 = false;
    int Gi_172 = 0;
    
    // Основной цикл расчета
    for(int i=0; i<=bars_to_calculate; i++)
    {
        // Рассчитываем показатель волатильности
        double Ld_132 = 0;
        double Ld_140 = 0;
        
        // Получаем данные цен
        MqlRates rates[];
        ArraySetAsSeries(rates, true);
        int copied = CopyRates(_Symbol, _Period, 0, i+11, rates);
        
        if(copied > 0)
        {
            for(int j=0; j<=10; j++)
                if(i+j < copied)
                    Ld_140 += MathAbs(rates[j].high - rates[j].low);
                    
            Ld_132 = Ld_140 / 10.0;
        }
        
        // Расчет индикаторов
        int ma_handle_fast = iMA(_Symbol, _Period, FasterMovingAverage, 0, MODE_EMA, PRICE_CLOSE);
        int ma_handle_slow = iMA(_Symbol, _Period, SlowerMovingAverage, 0, MODE_EMA, PRICE_CLOSE);
        int rsi_handle = iRSI(_Symbol, _Period, RSIPeriod, PRICE_CLOSE);
        int bulls_handle = iBullsPower(_Symbol, _Period, BullsPowerPeriod);
        int bears_handle = iBearsPower(_Symbol, _Period, BearsPowerPeriod);
        // Убедимся, что нет потери данных при вызове iBands
        // BollingerbandsDeviation уже объявлен как double
        int bands_handle = iBands(_Symbol, _Period, BollingerbandsPeriod, BollingerbandsShift, BollingerbandsDeviation, PRICE_CLOSE);
        
        double ma_fast_buffer[], ma_slow_buffer[], rsi_buffer[], bulls_buffer[], bears_buffer[];
        double bands_upper_buffer[], bands_lower_buffer[];
        
        ArraySetAsSeries(ma_fast_buffer, true);
        ArraySetAsSeries(ma_slow_buffer, true);
        ArraySetAsSeries(rsi_buffer, true);
        ArraySetAsSeries(bulls_buffer, true);
        ArraySetAsSeries(bears_buffer, true);
        ArraySetAsSeries(bands_upper_buffer, true);
        ArraySetAsSeries(bands_lower_buffer, true);
        
        CopyBuffer(ma_handle_fast, 0, i, 2, ma_fast_buffer);
        CopyBuffer(ma_handle_slow, 0, i, 2, ma_slow_buffer);
        CopyBuffer(rsi_handle, 0, i, 2, rsi_buffer);
        CopyBuffer(bulls_handle, 0, i, 2, bulls_buffer);
        CopyBuffer(bears_handle, 0, i, 2, bears_buffer);
        CopyBuffer(bands_handle, 1, i, 2, bands_upper_buffer); // 1 - верхняя линия
        CopyBuffer(bands_handle, 2, i, 2, bands_lower_buffer); // 2 - нижняя линия
        
        double ima_12 = ma_fast_buffer[0];
        double ima_28 = ma_fast_buffer[1];
        double ima_20 = ma_slow_buffer[0];
        double ima_36 = ma_slow_buffer[1];
        
        double irsi_44 = rsi_buffer[0];
        double irsi_52 = rsi_buffer[1];
        
        double ibullspower_60 = bulls_buffer[0];
        double ibullspower_68 = bulls_buffer[1];
        
        double ibearspower_76 = bears_buffer[0];
        double ibearspower_84 = bears_buffer[1];
        
        double ibands_upper = bands_upper_buffer[0];
        double ibands_lower = bands_lower_buffer[0];
        double ibands_upper_prev = bands_upper_buffer[1];
        double ibands_lower_prev = bands_lower_buffer[1];
        
        // Для расчета магического фильтра используем функции поиска максимума и минимума
        int highest_idx = iHighest(_Symbol, _Period, MODE_HIGH, MagicFilterPeriod, i);
        int lowest_idx = iLowest(_Symbol, _Period, MODE_LOW, MagicFilterPeriod, i);
        
        double high_price = highest_idx >= 0 ? rates[highest_idx].high : 0;
        double low_price = lowest_idx >= 0 ? rates[lowest_idx].low : 0;
        
        double Ld_108 = 100 - 100.0 * ((high_price - 0.0) / 10.0);
        double Ld_116 = 100 - 100.0 * ((low_price - 0.0) / 10.0);
        
        if(Ld_108 == 0.0) Ld_108 = 0.0000001;
        if(Ld_116 == 0.0) Ld_116 = 0.0000001;
        
        double Ld_124 = Ld_108 - Ld_116;
        
        // Проверка условий для индикаторов
        if(Ld_124 >= 0.0) {
            Gi_148 = true;
            Gi_168 = false;
        } else if(Ld_124 < 0.0) {
            Gi_148 = false;
            Gi_168 = true;
        }
        
        if(rates[i].close > ibands_upper && rates[i+1].close >= ibands_upper_prev) {
            Gi_144 = false;
            Gi_164 = true;
        }
        
        if(rates[i].close < ibands_lower && rates[i+1].close <= ibands_lower_prev) {
            Gi_144 = true;
            Gi_164 = false;
        }
        
        if(ibullspower_60 > 0.0 && ibullspower_68 > ibullspower_60) {
            Gi_140 = false;
            Gi_160 = true;
        }
        
        if(ibearspower_76 < 0.0 && ibearspower_84 < ibearspower_76) {
            Gi_140 = true;
            Gi_160 = false;
        }
        
        if(irsi_44 > 50.0 && irsi_52 < 50.0) {
            Gi_136 = true;
            Gi_156 = false;
        }
        
        if(irsi_44 < 50.0 && irsi_52 > 50.0) {
            Gi_136 = false;
            Gi_156 = true;
        }
        
        if(ima_12 > ima_20 && ima_28 < ima_36) {
            Gi_132 = true;
            Gi_152 = false;
        }
        
        if(ima_12 < ima_20 && ima_28 > ima_36) {
            Gi_132 = false;
            Gi_152 = true;
        }
        
        // Генерация сигналов
        if(Gi_132 == true && Gi_136 == true && Gi_144 == true && Gi_140 == true && Gi_148 == true && Gi_172 != 1) {
            buffer_buy[i] = rates[i].low - (_Point * Utstup);
            Gi_172 = 1;
        } else if(Gi_152 == true && Gi_156 == true && Gi_164 == true && Gi_160 == true && Gi_168 == false && Gi_172 != 2) {
            buffer_sell[i] = rates[i].high + (_Point * Utstup);
            Gi_172 = 2;
        }
    }
}

// Функция для открытия позиции
int open(int type, double lot_size, double price, int stoploss, int takeprofit, int expire_time, color clr)
{
    Print("Начало выполнения функции open");
    Print("Параметры ордера:");
    Print("Тип: ", EnumToString((ENUM_ORDER_TYPE)type));
    Print("Лот: ", lot_size);
    Print("Цена: ", price);
    Print("Stop Loss: ", stoploss);
    Print("Take Profit: ", takeprofit);
    Print("Время экспирации: ", expire_time);
    
    int ticket = 0;
    double stop_level = 0, take_level = 0;
    
    // Проверка на минимальный и максимальный размер лота
    if(lot_size < minlot) {
        Print("Лот меньше минимального, установлен минимальный лот: ", minlot);
        lot_size = minlot;
    }
    if(lot_size > maxlot) {
        Print("Лот больше максимального, установлен максимальный лот: ", maxlot);
        lot_size = maxlot;
    }
    
    // Устанавливаем стоп-лосс и тейк-профит
    if(type == ORDER_TYPE_BUY || type == ORDER_TYPE_BUY_STOP || type == ORDER_TYPE_BUY_LIMIT) {
        if(stoploss > 0) stop_level = price - stoploss * pt;
        else stop_level = 0;
        
        if(takeprofit > 0) take_level = price + takeprofit * pt;
        else take_level = 0;
    }
    else if(type == ORDER_TYPE_SELL || type == ORDER_TYPE_SELL_STOP || type == ORDER_TYPE_SELL_LIMIT) {
        if(stoploss > 0) stop_level = price + stoploss * pt;
        else stop_level = 0;
        
        if(takeprofit > 0) take_level = price - takeprofit * pt;
        else take_level = 0;
    }
    
    Print("Рассчитанные уровни:");
    Print("Stop Level: ", stop_level);
    Print("Take Level: ", take_level);
    
    // Открываем позицию
    trade.SetExpertMagicNumber(magic);
    
    if(type == ORDER_TYPE_BUY) {
        Print("Открытие ордера BUY");
        if(trade.Buy(lot_size, _Symbol, price, stop_level, take_level, g_comment)) {
            ticket = (int)trade.ResultOrder();
            Print("Ордер BUY успешно открыт. Тикет: ", ticket);
        } else {
            Print("Ошибка при открытии ордера BUY. Код ошибки: ", GetLastError());
        }
    }
    else if(type == ORDER_TYPE_SELL) {
        Print("Открытие ордера SELL");
        if(trade.Sell(lot_size, _Symbol, price, stop_level, take_level, g_comment)) {
            ticket = (int)trade.ResultOrder();
            Print("Ордер SELL успешно открыт. Тикет: ", ticket);
        } else {
            Print("Ошибка при открытии ордера SELL. Код ошибки: ", GetLastError());
        }
    }
    else if(type == ORDER_TYPE_BUY_LIMIT) {
        Print("Открытие ордера BUY_LIMIT");
        if(trade.BuyLimit(lot_size, price, _Symbol, stop_level, take_level, ORDER_TIME_GTC, expire_time, g_comment)) {
            ticket = (int)trade.ResultOrder();
            Print("Ордер BUY_LIMIT успешно открыт. Тикет: ", ticket);
        } else {
            Print("Ошибка при открытии ордера BUY_LIMIT. Код ошибки: ", GetLastError());
        }
    }
    else if(type == ORDER_TYPE_SELL_LIMIT) {
        Print("Открытие ордера SELL_LIMIT");
        if(trade.SellLimit(lot_size, price, _Symbol, stop_level, take_level, ORDER_TIME_GTC, expire_time, g_comment)) {
            ticket = (int)trade.ResultOrder();
            Print("Ордер SELL_LIMIT успешно открыт. Тикет: ", ticket);
        } else {
            Print("Ошибка при открытии ордера SELL_LIMIT. Код ошибки: ", GetLastError());
        }
    }
    else if(type == ORDER_TYPE_BUY_STOP) {
        Print("Открытие ордера BUY_STOP");
        if(trade.BuyStop(lot_size, price, _Symbol, stop_level, take_level, ORDER_TIME_GTC, expire_time, g_comment)) {
            ticket = (int)trade.ResultOrder();
            Print("Ордер BUY_STOP успешно открыт. Тикет: ", ticket);
        } else {
            Print("Ошибка при открытии ордера BUY_STOP. Код ошибки: ", GetLastError());
        }
    }
    else if(type == ORDER_TYPE_SELL_STOP) {
        Print("Открытие ордера SELL_STOP");
        if(trade.SellStop(lot_size, price, _Symbol, stop_level, take_level, ORDER_TIME_GTC, expire_time, g_comment)) {
            ticket = (int)trade.ResultOrder();
            Print("Ордер SELL_STOP успешно открыт. Тикет: ", ticket);
        } else {
            Print("Ошибка при открытии ордера SELL_STOP. Код ошибки: ", GetLastError());
        }
    }
    
    return ticket;
}

// Функция для подсчета открытых позиций
int count(int type)
{
    int cnt_local = 0;
    
    for(int pos_idx=PositionsTotal()-1; pos_idx>=0; pos_idx--) {
        ulong ticket = PositionGetTicket(pos_idx);
        if(PositionSelectByTicket(ticket)) {
            if(PositionGetString(POSITION_SYMBOL) == _Symbol && 
               PositionGetInteger(POSITION_TYPE) == type && 
               PositionGetInteger(POSITION_MAGIC) == magic) {
                cnt_local++;
            }
        }
    }
    
    return cnt_local;
}

// Функция для закрытия покупок
void closebuy()
{
    for(int pos_idx=PositionsTotal()-1; pos_idx>=0; pos_idx--) {
        ulong position_ticket = PositionGetTicket(pos_idx);
        if(PositionSelectByTicket(position_ticket)) {
            if(PositionGetString(POSITION_SYMBOL) == _Symbol && 
               PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY && 
               PositionGetInteger(POSITION_MAGIC) == magic &&
               (minclosetime == false || (minclosetime && TimeCurrent()-PositionGetInteger(POSITION_TIME) >= mincloseminutes*60))) {
                trade.PositionClose(position_ticket);
            }
        }
    }
}

// Функция для закрытия продаж
void closesell()
{
    for(int pos_idx=PositionsTotal()-1; pos_idx>=0; pos_idx--) {
        ulong position_ticket = PositionGetTicket(pos_idx);
        if(PositionSelectByTicket(position_ticket)) {
            if(PositionGetString(POSITION_SYMBOL) == _Symbol && 
               PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL && 
               PositionGetInteger(POSITION_MAGIC) == magic &&
               (minclosetime == false || (minclosetime && TimeCurrent()-PositionGetInteger(POSITION_TIME) >= mincloseminutes*60))) {
                trade.PositionClose(position_ticket);
            }
        }
    }
}

// Функция для перемещения в безубыток
void movebreakeven(int be_gain, int be_level)
{
    for(int pos_idx=PositionsTotal()-1; pos_idx>=0; pos_idx--)
    {
        ulong position_ticket = PositionGetTicket(pos_idx);
        if(PositionSelectByTicket(position_ticket))
        {
            if(PositionGetString(POSITION_SYMBOL) == _Symbol && PositionGetInteger(POSITION_MAGIC) == magic)
            {
                if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
                {
                    double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
                    double stopLoss = PositionGetDouble(POSITION_SL);
                    
                    if(NormalizeDouble((SymbolInfoDouble(_Symbol, SYMBOL_BID) - openPrice), dg) >= NormalizeDouble(be_gain * pt, dg))
                    {
                        if((NormalizeDouble((stopLoss - openPrice), dg) < NormalizeDouble(be_level * pt, dg)) || stopLoss == 0)
                        {
                            trade.PositionModify(position_ticket, NormalizeDouble(openPrice + be_level * pt, dg), PositionGetDouble(POSITION_TP));
                            return;
                        }
                    }
                }
                else if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL)
                {
                    double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
                    double stopLoss = PositionGetDouble(POSITION_SL);
                    
                    if(NormalizeDouble((openPrice - SymbolInfoDouble(_Symbol, SYMBOL_ASK)), dg) >= NormalizeDouble(be_gain * pt, dg))
                    {
                        if((NormalizeDouble((openPrice - stopLoss), dg) < NormalizeDouble(be_level * pt, dg)) || stopLoss == 0)
                        {
                            trade.PositionModify(position_ticket, NormalizeDouble(openPrice - be_level * pt, dg), PositionGetDouble(POSITION_TP));
                            return;
                        }
                    }
                }
            }
        }
    }
}

// Функция для трейлинг-стопа
void movetrailingstop(int trailing_start, int trailing_stop)
{
    for(int pos_idx=PositionsTotal()-1; pos_idx>=0; pos_idx--)
    {
        ulong position_ticket = PositionGetTicket(pos_idx);
        if(PositionSelectByTicket(position_ticket))
        {
            if(PositionGetString(POSITION_SYMBOL) == _Symbol && PositionGetInteger(POSITION_MAGIC) == magic)
            {
                if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
                {
                    double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
                    double stopLoss = PositionGetDouble(POSITION_SL);
                    double current_ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
                    double current_bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
                    
                    if(NormalizeDouble(current_ask, dg) > NormalizeDouble(openPrice + trailing_start * pt, dg) &&
                      (NormalizeDouble(stopLoss, dg) < NormalizeDouble(current_bid - (trailing_stop + trailingstep) * pt, dg) || stopLoss == 0))
                    {
                        trade.PositionModify(position_ticket, NormalizeDouble(current_bid - trailing_stop * pt, dg), PositionGetDouble(POSITION_TP));
                        return;
                    }
                }
                else if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL)
                {
                    double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
                    double stopLoss = PositionGetDouble(POSITION_SL);
                    double current_ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
                    double current_bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
                    
                    if(NormalizeDouble(current_bid, dg) < NormalizeDouble(openPrice - trailing_start * pt, dg) &&
                      (NormalizeDouble(stopLoss, dg) > NormalizeDouble(current_ask + (trailing_stop + trailingstep) * pt, dg) || stopLoss == 0))
                    {
                        trade.PositionModify(position_ticket, NormalizeDouble(current_ask + trailing_stop * pt, dg), PositionGetDouble(POSITION_TP));
                        return;
                    }
                }
            }
        }
    }
}

// Главная функция OnTick
void OnTick()
{
    // Проверяем режим тестера и выводим информацию о состоянии советника
    static int tick_counter = 0;
    tick_counter++;
    
    if(MQLInfoInteger(MQL_TESTER))
    {
        static bool tester_initialized = false;
        if(!tester_initialized)
        {
            Print("Советник запущен в тестере");
            tester_initialized = true;
            
            // В режиме тестера принудительно вызываем расчет индикаторов в начале
            Print("Принудительный расчет индикаторов при запуске тестера");
            CalculateIndicators();
            
            // И сразу проверяем торговые условия
            CheckTradingConditions();
        }
        
        // В режиме тестера выводим расширенную информацию каждые 100 тиков
        if(tick_counter % 100 == 0)
        {
            Print("Тик: ", tick_counter);
            Print("Текущее время: ", TimeToString(TimeCurrent()));
            Print("Текущее время последнего бара: ", TimeToString(last_bar_time));
            Print("Текущее значение g_signal: ", g_signal);
        }
    }

    datetime current_time = TimeCurrent();
    
    // Проверяем бары принудительно
    static datetime last_forced_check = 0;
    if(current_time - last_forced_check >= 60) // Проверка каждую минуту
    {
        Print("Принудительная проверка баров...");
        datetime current_bar_time = iTime(_Symbol, _Period, 0);
        Print("Последний бар: ", TimeToString(last_bar_time), ", Текущий бар: ", TimeToString(current_bar_time));
        
        if(current_bar_time != last_bar_time)
        {
            Print("Обнаружен новый бар при принудительной проверке!");
            last_bar_time = current_bar_time;
            
            // Принудительный расчет индикаторов
            CalculateIndicators();
            
            // Проверка торговых условий
            CheckTradingConditions();
        }
        else
        {
            // Даже если нет нового бара, периодически пересчитываем индикаторы
            if(tick_counter % 1000 == 0 && MQLInfoInteger(MQL_TESTER))
            {
                Print("Принудительный пересчет индикаторов на текущем баре");
                CalculateIndicators();
                CheckTradingConditions();
            }
        }
        
        last_forced_check = current_time;
    }
    
    // Обновляем цены только если прошло достаточно времени
    if(current_time - last_calculation_time >= calculation_interval)
    {
        // Обновляем цены
        RefreshRates();
        ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        
        // Нормализуем значения
        double normalized_ask = NormalizeDouble(ask, _Digits);
        double normalized_bid = NormalizeDouble(bid, _Digits);
        double current_spread = NormalizeDouble((ask - bid) / _Point, 2);
        
        // Минимальный порог изменения цены (в пунктах)
        double min_price_change = 0.5; // 0.5 пункта
        
        // Выводим информацию о ценах только если:
        // 1. Прошло достаточно времени (не чаще чем раз в секунду)
        // 2. Изменение цены превышает минимальный порог
        // 3. Изменился спред
        if((current_time - last_print_time >= 1) && 
           (MathAbs(normalized_ask - last_ask) >= min_price_change * _Point || 
            MathAbs(normalized_bid - last_bid) >= min_price_change * _Point ||
            current_spread != last_spread))
        {
            string time_str = TimeToString(current_time, TIME_DATE|TIME_MINUTES|TIME_SECONDS);
            string price_info = StringFormat("%s | Ask=%.5f Bid=%.5f Spread=%.1f pips", 
                                           time_str, normalized_ask, normalized_bid, current_spread);
            Print(price_info);
            
            // Обновляем последние значения
            last_ask = normalized_ask;
            last_bid = normalized_bid;
            last_spread = current_spread;
            last_print_time = current_time;
        }
        
        // Проверяем доступность символа для торговли
        if(!SymbolInfoInteger(_Symbol, SYMBOL_TRADE_MODE) == SYMBOL_TRADE_MODE_FULL)
        {
            Print("Символ ", _Symbol, " не доступен для торговли");
            return;
        }
        
        // Проверяем спред
        if(current_spread > maxspread)
        {
            Print("Спред превышает максимально допустимый: ", current_spread, " > ", maxspread);
            return;
        }
        
        // Обновляем время последнего расчета
        last_calculation_time = current_time;
    }
    
    // Проверяем новый бар
    if(IsNewBar())
    {
        Print("Обнаружен новый бар, время: ", TimeToString(current_time));
        
        // Выполняем расчеты индикаторов только на новом баре
        CalculateIndicators();
        
        // Проверяем условия для торговли
        CheckTradingConditions();
    }
}

// Функция для расчета индикаторов
void CalculateIndicators()
{
    Print("Начало расчета индикаторов");
    
    // Рассчитываем значения индикаторов
    double SNI0 = 0, SNI1 = 0;
    double STB = 0, STR = 0;
    double STBa = 0, STRa = 0;
    double TAB = 0, TAR = 0;
    double TABa = 0, TARa = 0;
    double TBB = 0, TBR = 0;
    double TBBa = 0, TBRa = 0;
    
    // Рассчитываем Super Arrow Indicator или пользовательский индикатор
    if(usesniper)
    {
        Print("Использование индикатора Sniper");
        if(StringCompare(IndicatorName, "super-arrow-indicator") == 0)
        {
            Print("Расчет Super Arrow Indicator");
            // Инициализация массивов для сигналов
            ArrayResize(arrow_buy, 100);
            ArrayResize(arrow_sell, 100);
            ArraySetAsSeries(arrow_buy, true);
            ArraySetAsSeries(arrow_sell, true);
            
            CalculateSuperArrow(arrow_buy, arrow_sell, 100);
            SNI0 = arrow_buy[g_shift];
            SNI1 = arrow_sell[g_shift];
            
            Print("Значения Super Arrow: Buy=", SNI0, " Sell=", SNI1);
            
            // Устанавливаем сигнал на основе индикатора
            if(SNI0 > 0)
            {
                g_signal = 1;
                Print("Установлен сигнал на покупку по индикатору");
            }
            else if(SNI1 > 0)
            {
                g_signal = 2;
                Print("Установлен сигнал на продажу по индикатору");
            }
            else
            {
                // Если индикатор не дал сигнала, используем простейший алгоритм на основе цен
                // для тестирования торговой логики
                if(MQLInfoInteger(MQL_TESTER))
                {
                    // Простой алгоритм для генерации сигналов в тестере
                    double prev_close = iClose(_Symbol, _Period, 1);
                    double prev_close2 = iClose(_Symbol, _Period, 2);
                    
                    if(prev_close > prev_close2)
                    {
                        g_signal = 1; // Сигнал на покупку
                        Print("Установлен тестовый сигнал на покупку (цена растет)");
                    }
                    else if(prev_close < prev_close2)
                    {
                        g_signal = 2; // Сигнал на продажу
                        Print("Установлен тестовый сигнал на продажу (цена падает)");
                    }
                    else
                    {
                        g_signal = 0;
                        Print("Нет сигнала (цена не изменилась)");
                    }
                }
                else
                {
                    g_signal = 0;
                    Print("Нет сигнала");
                }
            }
        }
        else
        {
            Print("Использование пользовательского индикатора: ", IndicatorName);
            SNI0 = iCustom(_Symbol, _Period, IndicatorName, BuyBuffer, BuyBuffer, g_shift);
            SNI1 = iCustom(_Symbol, _Period, IndicatorName, SellBuffer, SellBuffer, g_shift);
            Print("Значения пользовательского индикатора: Buy=", SNI0, " Sell=", SNI1);
            
            // Установка сигнала на основе пользовательского индикатора
            if(SNI0 > 0)
            {
                g_signal = 1;
                Print("Установлен сигнал на покупку по пользовательскому индикатору");
            }
            else if(SNI1 > 0)
            {
                g_signal = 2;
                Print("Установлен сигнал на продажу по пользовательскому индикатору");
            }
            else
            {
                // Если индикатор не дал сигнала, используем простейший алгоритм на основе цен
                // для тестирования торговой логики
                if(MQLInfoInteger(MQL_TESTER))
                {
                    // Простой алгоритм для генерации сигналов в тестере
                    double prev_close = iClose(_Symbol, _Period, 1);
                    double prev_close2 = iClose(_Symbol, _Period, 2);
                    
                    if(prev_close > prev_close2)
                    {
                        g_signal = 1; // Сигнал на покупку
                        Print("Установлен тестовый сигнал на покупку (цена растет)");
                    }
                    else if(prev_close < prev_close2)
                    {
                        g_signal = 2; // Сигнал на продажу
                        Print("Установлен тестовый сигнал на продажу (цена падает)");
                    }
                    else
                    {
                        g_signal = 0;
                        Print("Нет сигнала (цена не изменилась)");
                    }
                }
                else
                {
                    g_signal = 0;
                    Print("Нет сигнала");
                }
            }
        }
    }
    else
    {
        // Если индикаторы отключены, но мы в тестере, генерируем тестовые сигналы
        if(MQLInfoInteger(MQL_TESTER))
        {
            double prev_close = iClose(_Symbol, _Period, 1);
            double prev_close2 = iClose(_Symbol, _Period, 2);
            
            if(prev_close > prev_close2)
            {
                g_signal = 1; // Сигнал на покупку
                Print("Установлен тестовый сигнал на покупку (цена растет)");
            }
            else if(prev_close < prev_close2)
            {
                g_signal = 2; // Сигнал на продажу
                Print("Установлен тестовый сигнал на продажу (цена падает)");
            }
            else
            {
                g_signal = 0;
                Print("Нет сигнала (цена не изменилась)");
            }
        }
    }
    
    // Рассчитываем Sniper Stop v2 если нужно
    if(usestop || autostop)
    {
        Print("Расчет Sniper Stop v2");
        double sniperstop_buy[];
        double sniperstop_sell[];
        ArrayResize(sniperstop_buy, 100);
        ArrayResize(sniperstop_sell, 100);
        ArraySetAsSeries(sniperstop_buy, true);
        ArraySetAsSeries(sniperstop_sell, true);
        
        CalculateSniperStop(sniperstop_buy, sniperstop_sell, 100);
        
        STB = sniperstop_buy[g_shift];
        STR = sniperstop_sell[g_shift];
        STBa = sniperstop_buy[g_shift+1];
        STRa = sniperstop_sell[g_shift+1];
        
        Print("Значения Sniper Stop: Buy=", STB, " Sell=", STR);
    }
    
    // Рассчитываем Sniper Trend A если нужно
    if(usetrenda)
    {
        Print("Расчет Sniper Trend A");
        double trenda_buy[];
        double trenda_sell[];
        ArrayResize(trenda_buy, 100);
        ArrayResize(trenda_sell, 100);
        ArraySetAsSeries(trenda_buy, true);
        ArraySetAsSeries(trenda_sell, true);
        
        CalculateTrendA(trenda_buy, trenda_sell, 100);
        
        TAB = trenda_buy[g_shift];
        TAR = trenda_sell[g_shift];
        TABa = trenda_buy[g_shift+1];
        TARa = trenda_sell[g_shift+1];
        
        Print("Значения Sniper Trend A: Buy=", TAB, " Sell=", TAR);
    }
    
    // Рассчитываем Sniper Trend B если нужно
    if(usetrendb)
    {
        Print("Расчет Sniper Trend B");
        double trendb_buy[];
        double trendb_sell[];
        ArrayResize(trendb_buy, 100);
        ArrayResize(trendb_sell, 100);
        ArraySetAsSeries(trendb_buy, true);
        ArraySetAsSeries(trendb_sell, true);
        
        CalculateTrendB(Sniper, SniperSS, SniperP, AlertsEnabled, trendb_buy, trendb_sell, 100);
        
        TBB = trendb_buy[g_shift];
        TBR = trendb_sell[g_shift];
        TBBa = trendb_buy[g_shift+1];
        TBRa = trendb_sell[g_shift+1];
        
        Print("Значения Sniper Trend B: Buy=", TBB, " Sell=", TBR);
    }
    
    Print("Завершение расчета индикаторов. Итоговый сигнал: ", g_signal);
}

// Функция проверки условий торговли
void CheckTradingConditions()
{
    Print("Проверка условий торговли");
    
    // Проверяем время торговли
    if(!IsTradeTime())
    {
        Print("Торговля не разрешена в текущее время");
        return;
    }
    
    // Проверяем максимальное количество позиций
    int total_positions = PositionsTotal();
    Print("Текущее количество позиций: ", total_positions);
    
    if(total_positions >= max_positions)
    {
        Print("Достигнуто максимальное количество позиций: ", max_positions);
        return;
    }
    
    // Проверяем сигналы
    bool buy_signal = CheckBuySignal();
    bool sell_signal = CheckSellSignal();
    
    Print("Сигналы: Buy=", buy_signal, " Sell=", sell_signal);
    
    if(buy_signal)
    {
        Print("Получен сигнал на покупку");
        // Открываем позицию на покупку
        OpenPosition(ORDER_TYPE_BUY, ltakeprofit, lstoploss);
    }
    else if(sell_signal)
    {
        Print("Получен сигнал на продажу");
        // Открываем позицию на продажу
        OpenPosition(ORDER_TYPE_SELL, stakeprofit, sstoploss);
    }
}

// Функция проверки сигнала на покупку
bool CheckBuySignal()
{
    // Проверяем все условия для сигнала на покупку
    bool signal = (g_signal == 1 && !reversesignals) || (g_signal == 2 && reversesignals);
    Print("Проверка сигнала на покупку: g_signal=", g_signal, " reversesignals=", reversesignals, " result=", signal);
    return signal;
}

// Функция проверки сигнала на продажу
bool CheckSellSignal()
{
    // Проверяем все условия для сигнала на продажу
    bool signal = (g_signal == 2 && !reversesignals) || (g_signal == 1 && reversesignals);
    Print("Проверка сигнала на продажу: g_signal=", g_signal, " reversesignals=", reversesignals, " result=", signal);
    return signal;
}

// Вспомогательная функция для получения цен
void RefreshRates()
{
    ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
} 

bool OpenPosition(ENUM_ORDER_TYPE orderType, double takeProfit, double stopLoss)
{
    Print("Начало открытия позиции");
    Print("Тип ордера: ", EnumToString(orderType));
    Print("Take Profit: ", takeProfit);
    Print("Stop Loss: ", stopLoss);
    
    double price = (orderType == ORDER_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
    Print("Цена открытия: ", price);
    
    double lot = g_lots;
    Print("Размер лота: ", lot);
    
    // Дата истечения для отложенных ордеров
    datetime expiration = 0;
    if(g_expiration > 0)
        expiration = TimeCurrent() + (g_expiration * 60) - 5;
    
    // Безопасное преобразование double в int с проверкой диапазона
    int stopLossPips = (int)MathRound(stopLoss);
    if(stopLossPips < 0) stopLossPips = 0;
    
    int ticket = open(orderType, lot, price, stopLossPips, takeProfit, (int)MathRound(expiration), 
                     (orderType == ORDER_TYPE_BUY) ? clrBlue : clrRed);
    
    if(ticket > 0)
    {
        Print("Позиция успешно открыта. Тикет: ", ticket);
        
        if(open5orders)
        {
            Print("Открытие дополнительных ордеров");
            double takeProfits[] = {takeProfit, ltakeprofit2, ltakeprofit3, ltakeprofit4, ltakeprofit5};
            
            for(int i = 1; i < 5; i++)
            {
                // Безопасное преобразование double в int с проверкой диапазона
                int expirationPips = (int)MathRound(expiration);
                if(expirationPips < 0) expirationPips = 0;
                
                ticket = open(orderType, lot, price, stopLossPips, takeProfits[i], 
                            expirationPips, (orderType == ORDER_TYPE_BUY) ? clrBlue : clrRed);
                if(ticket > 0)
                {
                    Print("Дополнительный ордер ", i, " успешно открыт. Тикет: ", ticket);
                }
                else
                {
                    Print("Ошибка при открытии дополнительного ордера ", i);
                }
            }
        }
        return true;
    }
    else
    {
        Print("Ошибка при открытии позиции. Код ошибки: ", GetLastError());
        return false;
    }
} 

// Функция проверки нового бара
bool IsNewBar()
{
    datetime current_time = iTime(_Symbol, _Period, 0);
    if(current_time != last_bar_time)
    {
        Print("Обнаружен новый бар! Время: ", TimeToString(current_time));
        Print("Предыдущий бар: ", TimeToString(last_bar_time));
        last_bar_time = current_time;
        return true;
    }
    return false;
}

// Функция проверки времени торговли
bool IsTradeTime()
{
    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);
    
    int current_hour = dt.hour;
    int current_day = dt.day_of_week;
    
    Print("Проверка времени торговли:");
    Print("Текущее время: ", TimeToString(TimeCurrent()));
    Print("День недели: ", current_day);
    Print("Час: ", current_hour);
    
    // Проверка на воскресенье
    if(!tradesunday && current_day == 0)
    {
        Print("Торговля запрещена в воскресенье");
        return false;
    }
        
    // Проверка на пятницу
    if(fridayfilter && current_day == 5 && current_hour >= fridayend)
    {
        Print("Торговля запрещена в пятницу после ", fridayend, " часов");
        return false;
    }
        
    // Проверка торговых часов
    if(filter)
    {
        int start_hour = trade_start_hour + gmtshift;
        int end_hour = trade_end_hour + gmtshift;
        
        if(start_hour > 23) start_hour -= 24;
        if(end_hour > 23) end_hour -= 24;
        
        Print("Торговые часы: с ", start_hour, " до ", end_hour);
        
        if(start_hour <= end_hour)
        {
            if(current_hour < start_hour || current_hour > end_hour)
            {
                Print("Текущее время вне торговых часов");
                return false;
            }
        }
        else
        {
            if(current_hour < start_hour && current_hour > end_hour)
            {
                Print("Текущее время вне торговых часов");
                return false;
            }
        }
    }
    
    Print("Торговля разрешена");
    return true;
}