//+------------------------------------------------------------------+
//|                                        AF-TrendKiller_MT5.mq5 |
//|                                Copyright 2023, AFSID-Group.Cv     |
//|                                       https://afs-id.com          |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, AFSID-Group.Cv"
#property link      "https://afs-id.com"
#property version   "1.00"
#property description "AF-TrendKiller - стратегия из AF-Investing с индикаторами THV Cobra и Retracement"

// Подключение необходимых библиотек
#include <Trade\Trade.mqh>
#include <Trade\SymbolInfo.mqh>
#include <Trade\AccountInfo.mqh>

// Определение основных констант
#define EXPERT_MAGIC 23794  // Magic для AF-TrendKiller

// Структура для сигналов
struct STrendKillerSignals
{
   bool buySignal;      // Сигнал на покупку
   bool sellSignal;     // Сигнал на продажу
   bool closeBuySignal; // Сигнал на закрытие покупки
   bool closeSellSignal;// Сигнал на закрытие продажи
};

// Параметры стратегии
input string GeneralSettings = "==== Общие настройки ====";
input string EAName = "AF-TrendKiller MT5";
input bool LabelInfo = true;              // Отображать информацию на графике
input int CustomMagic = 0;                // Пользовательский Magic Number (0 - использовать по умолчанию)

// Параметры управления рисками
input string RiskSettings = "==== Управление рисками ====";
input double Lots = 0.01;                 // Фиксированный размер лота
input bool AutoLotSize = true;            // Автоматический расчет размера лота
input double RiskInPercent = 2.0;         // Риск от депозита (%)
input int lotdecimal = 2;                 // Десятичные знаки в размере лота
input double MaxLots = 10.0;              // Максимальный размер лота
input double SlipPage = 5.0;              // Проскальзывание

// Параметры управления ордерами
input string OrderSettings = "==== Управление ордерами ====";
input int MaxTrades = 10;                 // Максимальное количество сделок
input double TakeProfit = 50.0;           // Тейк-профит (в пунктах)
input double PipStep = 150.0;             // Шаг для мартингейла (в пунктах)
input double LotExponent = 1.2;           // Множитель лота для мартингейла

// Параметры трейлинг-стопа
input string TrailingSettings = "==== Трейлинг-стоп ====";
input bool UseTrailingStop = true;        // Использовать трейлинг-стоп
input double TrailStart = 20.0;           // Начало трейлинга (в пунктах)
input double TrailStop = 10.0;            // Шаг трейлинга (в пунктах)

// Параметры контроля убытков
input string EquitySettings = "==== Контроль убытков ====";
input bool UseEquityStop = true;          // Использовать остановку по эквити
input double TotalEquityRisk = 10.0;      // Риск эквити (%)

// Параметры индикатора THV Cobra
input string THVSettings = "==== Настройки THV Cobra ====";
input int THV_Period = 9;                 // Период THV Cobra
input double THV_Multiplier = 2.0;        // Множитель THV Cobra
input int THV_SignalPeriod = 5;           // Период сигнальной линии THV Cobra

// Параметры индикатора Retracement
input string RetracementSettings = "==== Настройки Retracement ====";
input int Retracement_Period = 14;        // Период Retracement
input double Fibo_Level1 = 0.236;         // Уровень Фибоначчи 1
input double Fibo_Level2 = 0.382;         // Уровень Фибоначчи 2
input double Fibo_Level3 = 0.5;           // Уровень Фибоначчи 3
input double Fibo_Level4 = 0.618;         // Уровень Фибоначчи 4
input double Fibo_Level5 = 0.786;         // Уровень Фибоначчи 5
input double Fibo_Level6 = 1.0;           // Уровень Фибоначчи 6

// Экземпляры классов
CTrade Trade;
CSymbolInfo SymbolInfo;
CAccountInfo AccountInfo;

// Глобальные переменные
int thv_cobra_handle = INVALID_HANDLE;    // Хэндл индикатора THV Cobra
int retracement_handle = INVALID_HANDLE;  // Хэндл индикатора Retracement
datetime LastTradeTime = 0;               // Время последней сделки
bool IsNews = false;                      // Флаг новостей
double initialBalance = 0;                // Начальный баланс
double maxEquity = 0;                     // Максимальный эквити
double maxDrawdown = 0;                   // Максимальная просадка
int wins = 0, losses = 0;                 // Статистика сделок
double openLots = 0;                      // Открытый объем

// Имена объектов на графике
string LabelName = "AF-TrendKiller_Info";

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // Инициализация символа
   if(!SymbolInfo.Name(_Symbol))
   {
      Print("Ошибка инициализации символа");
      return(INIT_FAILED);
   }
   
   // Установка Magic Number
   int magic = (CustomMagic > 0) ? CustomMagic : EXPERT_MAGIC;
   Trade.SetExpertMagicNumber(magic);
   
   // Инициализация индикаторов
   thv_cobra_handle = iCustom(_Symbol, PERIOD_CURRENT, "THV_Cobra_MT5", 
                            THV_Period, THV_Multiplier, THV_SignalPeriod);
                            
   retracement_handle = iCustom(_Symbol, PERIOD_CURRENT, "Retracement_MT5", 
                              Retracement_Period, Fibo_Level1, Fibo_Level2, 
                              Fibo_Level3, Fibo_Level4, Fibo_Level5, Fibo_Level6);
   
   if(thv_cobra_handle == INVALID_HANDLE || retracement_handle == INVALID_HANDLE)
   {
      Print("Ошибка загрузки индикаторов: THV=", thv_cobra_handle, ", Retracement=", retracement_handle);
      return(INIT_FAILED);
   }
   
   // Инициализация графической панели
   if(LabelInfo)
   {
      CreateInfoPanel();
   }
   
   // Инициализация статистики
   initialBalance = AccountInfo.Balance();
   maxEquity = AccountInfo.Equity();
   
   Print("AF-TrendKiller MT5 инициализирован успешно");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Освобождение хэндлов индикаторов
   if(thv_cobra_handle != INVALID_HANDLE)
      IndicatorRelease(thv_cobra_handle);
   if(retracement_handle != INVALID_HANDLE)
      IndicatorRelease(retracement_handle);
   
   // Удаление графических объектов
   if(LabelInfo)
   {
      ObjectDelete(0, LabelName);
   }
   
   Print("AF-TrendKiller MT5 выгружен, причина: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Обновляем информацию о символе
   SymbolInfo.Refresh();
   SymbolInfo.RefreshRates();
   
   // Обновляем статистику и информационную панель
   UpdateStats();
   if(LabelInfo)
   {
      UpdateInfoPanel();
   }
   
   // Проверка возможности торговли
   if(!CanTrade())
   {
      Print("Торговля невозможна в данный момент");
      return;
   }
   
   // Управление открытыми позициями
   if(UseTrailingStop)
   {
      TrailingPositions();
   }
   
   // Проверка эквити-стопа
   if(UseEquityStop && CheckEquityStop())
   {
      Print("Сработал эквити-стоп, новые позиции не открываются");
      return;
   }
   
   // Проверка сигналов и открытие новых позиций
   STrendKillerSignals signals = GetSignals();
   
   // Закрытие позиций по сигналам
   if(signals.closeBuySignal || signals.closeSellSignal)
   {
      ClosePositionsBySignal(signals.closeBuySignal, signals.closeSellSignal);
   }
   
   // Открытие новых позиций
   int total = CountPositions();
   if(total < MaxTrades)
   {
      if(signals.buySignal && CanOpenBuy())
      {
         OpenPosition(ORDER_TYPE_BUY);
      }
      
      if(signals.sellSignal && CanOpenSell())
      {
         OpenPosition(ORDER_TYPE_SELL);
      }
   }
}

//+------------------------------------------------------------------+
//| Проверка возможности торговли                                    |
//+------------------------------------------------------------------+
bool CanTrade()
{
   // Проверка доступности рынка
   if(!MQLInfoInteger(MQL_TRADE_ALLOWED))
   {
      Print("Торговля запрещена. Включите в настройках терминала.");
      return false;
   }
   
   // Проверка наличия лицензии и т.д.
   
   return true;
}

//+------------------------------------------------------------------+
//| Получение сигналов от индикаторов                                |
//+------------------------------------------------------------------+
STrendKillerSignals GetSignals()
{
   STrendKillerSignals signals = {0};
   
   // Получаем данные THV Cobra
   double thvMain[], thvSignal[];
   ArraySetAsSeries(thvMain, true);
   ArraySetAsSeries(thvSignal, true);
   
   // Получаем данные Retracement
   double fibo236[], fibo382[], fibo50[], fibo618[], fibo100[];
   double fiboHigh[], fiboLow[];
   ArraySetAsSeries(fibo236, true);
   ArraySetAsSeries(fibo382, true);
   ArraySetAsSeries(fibo50, true);
   ArraySetAsSeries(fibo618, true);
   ArraySetAsSeries(fibo100, true);
   ArraySetAsSeries(fiboHigh, true);
   ArraySetAsSeries(fiboLow, true);
   
   // Копируем данные из индикаторов
   if(CopyBuffer(thv_cobra_handle, 0, 0, 3, thvMain) <= 0 || 
      CopyBuffer(thv_cobra_handle, 1, 0, 3, thvSignal) <= 0)
   {
      Print("Ошибка получения данных индикатора THV Cobra");
      return signals;
   }
   
   if(CopyBuffer(retracement_handle, 0, 0, 3, fibo236) <= 0 || 
      CopyBuffer(retracement_handle, 1, 0, 3, fibo382) <= 0 ||
      CopyBuffer(retracement_handle, 2, 0, 3, fibo50) <= 0 ||
      CopyBuffer(retracement_handle, 3, 0, 3, fibo618) <= 0 ||
      CopyBuffer(retracement_handle, 4, 0, 3, fibo100) <= 0 ||
      CopyBuffer(retracement_handle, 5, 0, 3, fiboHigh) <= 0 ||
      CopyBuffer(retracement_handle, 6, 0, 3, fiboLow) <= 0)
   {
      Print("Ошибка получения данных индикатора Retracement");
      return signals;
   }
   
   // Получаем ценовые данные
   double close[], high[], low[];
   ArraySetAsSeries(close, true);
   ArraySetAsSeries(high, true);
   ArraySetAsSeries(low, true);
   
   if(CopyClose(_Symbol, PERIOD_CURRENT, 0, 3, close) <= 0 ||
      CopyHigh(_Symbol, PERIOD_CURRENT, 0, 3, high) <= 0 ||
      CopyLow(_Symbol, PERIOD_CURRENT, 0, 3, low) <= 0)
   {
      Print("Ошибка получения ценовых данных");
      return signals;
   }
   
   // Проверяем сигналы
   // Сигнал на покупку:
   // 1. THV Cobra пересекает сигнальную линию снизу вверх
   // 2. Цена находится выше уровня Фибоначчи 0.618
   if(thvMain[1] > thvSignal[1] && thvMain[2] <= thvSignal[2] && close[1] > fibo618[1])
   {
      signals.buySignal = true;
   }
   
   // Сигнал на продажу:
   // 1. THV Cobra пересекает сигнальную линию сверху вниз
   // 2. Цена находится ниже уровня Фибоначчи 0.382
   if(thvMain[1] < thvSignal[1] && thvMain[2] >= thvSignal[2] && close[1] < fibo382[1])
   {
      signals.sellSignal = true;
   }
   
   // Сигналы на закрытие позиций:
   // Закрытие Buy: THV Cobra пересекает сигнальную линию сверху вниз и цена ниже 0.5 Фибо
   if(thvMain[1] < thvSignal[1] && thvMain[2] >= thvSignal[2] && close[1] < fibo50[1])
   {
      signals.closeBuySignal = true;
   }
   
   // Закрытие Sell: THV Cobra пересекает сигнальную линию снизу вверх и цена выше 0.5 Фибо
   if(thvMain[1] > thvSignal[1] && thvMain[2] <= thvSignal[2] && close[1] > fibo50[1])
   {
      signals.closeSellSignal = true;
   }
   
   return signals;
}

//+------------------------------------------------------------------+
//| Создание информационной панели                                   |
//+------------------------------------------------------------------+
void CreateInfoPanel()
{
   if(ObjectFind(0, LabelName) >= 0)
      ObjectDelete(0, LabelName);
      
   ObjectCreate(0, LabelName, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, LabelName, OBJPROP_CORNER, CORNER_RIGHT_UPPER);
   ObjectSetInteger(0, LabelName, OBJPROP_XDISTANCE, 10);
   ObjectSetInteger(0, LabelName, OBJPROP_YDISTANCE, 15);
   ObjectSetInteger(0, LabelName, OBJPROP_COLOR, clrWhite);
   ObjectSetInteger(0, LabelName, OBJPROP_FONTSIZE, 9);
   ObjectSetInteger(0, LabelName, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, LabelName, OBJPROP_BACK, false);
}

//+------------------------------------------------------------------+
//| Обновление информационной панели                                 |
//+------------------------------------------------------------------+
void UpdateInfoPanel()
{
   string text = "=== AF-TrendKiller MT5 ===\n";
   text += "Баланс: " + DoubleToString(AccountInfo.Balance(), 2) + "\n";
   text += "Эквити: " + DoubleToString(AccountInfo.Equity(), 2) + "\n";
   text += "Просадка: " + DoubleToString(maxDrawdown, 2) + "%\n";
   text += "Открытые позиции: " + IntegerToString(CountPositions()) + "\n";
   text += "Общий объем: " + DoubleToString(openLots, 2) + "\n";
   text += "Выигрыши/Проигрыши: " + IntegerToString(wins) + "/" + IntegerToString(losses);
   
   ObjectSetString(0, LabelName, OBJPROP_TEXT, text);
}

//+------------------------------------------------------------------+
//| Обновление статистики торговли                                   |
//+------------------------------------------------------------------+
void UpdateStats()
{
   double equity = AccountInfo.Equity();
   
   // Обновление максимального эквити
   if(equity > maxEquity)
      maxEquity = equity;
      
   // Расчет текущей просадки
   double currentDrawdown = 0;
   if(maxEquity > 0)
      currentDrawdown = (maxEquity - equity) / maxEquity * 100.0;
      
   // Обновление максимальной просадки
   if(currentDrawdown > maxDrawdown)
      maxDrawdown = currentDrawdown;
      
   // Подсчет открытого объема
   openLots = 0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == Trade.RequestMagic())
      {
         openLots += PositionGetDouble(POSITION_VOLUME);
      }
   }
}

//+------------------------------------------------------------------+
//| Проверка эквити-стопа                                            |
//+------------------------------------------------------------------+
bool CheckEquityStop()
{
   if(!UseEquityStop)
      return false;
      
   double equity = AccountInfo.Equity();
   
   // Проверка просадки
   if(maxDrawdown >= TotalEquityRisk)
   {
      return true;
   }
   
   return false;
}

//+------------------------------------------------------------------+
//| Подсчет количества открытых позиций                              |
//+------------------------------------------------------------------+
int CountPositions()
{
   int count = 0;
   
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == Trade.RequestMagic())
      {
         count++;
      }
   }
   
   return count;
}

//+------------------------------------------------------------------+
//| Проверка возможности открытия покупки                            |
//+------------------------------------------------------------------+
bool CanOpenBuy()
{
   // Проверка времени последней сделки
   if(TimeCurrent() - LastTradeTime < 60)
      return false;
      
   // Проверка новостей
   if(IsNews)
      return false;
      
   // Проверка максимального количества сделок
   if(CountPositions() >= MaxTrades)
      return false;
      
   // Дополнительные проверки...
   
   return true;
}

//+------------------------------------------------------------------+
//| Проверка возможности открытия продажи                            |
//+------------------------------------------------------------------+
bool CanOpenSell()
{
   // Проверка времени последней сделки
   if(TimeCurrent() - LastTradeTime < 60)
      return false;
      
   // Проверка новостей
   if(IsNews)
      return false;
      
   // Проверка максимального количества сделок
   if(CountPositions() >= MaxTrades)
      return false;
      
   // Дополнительные проверки...
   
   return true;
}

//+------------------------------------------------------------------+
//| Открытие позиции                                                 |
//+------------------------------------------------------------------+
void OpenPosition(ENUM_ORDER_TYPE orderType)
{
   double volume = CalculateLotSize();
   double price = 0;
   double sl = 0;
   double tp = 0;
   
   if(orderType == ORDER_TYPE_BUY)
   {
      price = SymbolInfo.Ask();
      sl = price - PipStep * _Point * 10;
      tp = price + TakeProfit * _Point * 10;
      
      if(Trade.Buy(volume, _Symbol, price, sl, tp, "AF-TrendKiller Buy"))
      {
         Print("Buy order opened: ", Trade.ResultOrder(), ", Volume: ", volume, ", Price: ", price);
         LastTradeTime = TimeCurrent();
      }
      else
      {
         Print("Error opening Buy order: ", Trade.ResultRetcode(), ", ", Trade.ResultRetcodeDescription());
      }
   }
   else if(orderType == ORDER_TYPE_SELL)
   {
      price = SymbolInfo.Bid();
      sl = price + PipStep * _Point * 10;
      tp = price - TakeProfit * _Point * 10;
      
      if(Trade.Sell(volume, _Symbol, price, sl, tp, "AF-TrendKiller Sell"))
      {
         Print("Sell order opened: ", Trade.ResultOrder(), ", Volume: ", volume, ", Price: ", price);
         LastTradeTime = TimeCurrent();
      }
      else
      {
         Print("Error opening Sell order: ", Trade.ResultRetcode(), ", ", Trade.ResultRetcodeDescription());
      }
   }
}

//+------------------------------------------------------------------+
//| Расчет размера лота                                             |
//+------------------------------------------------------------------+
double CalculateLotSize()
{
   double lotSize = Lots;
   
   // Расчет размера лота в зависимости от мартингейла
   int positions = CountPositions();
   if(positions > 0)
   {
      lotSize = NormalizeDouble(Lots * MathPow(LotExponent, positions), lotdecimal);
   }
   
   // Автоматический расчет размера лота по риску
   if(AutoLotSize)
   {
      double balance = AccountInfo.Balance();
      double riskAmount = balance * RiskInPercent / 100.0;
      double pipValue = SymbolInfo.TickValue() * 10;
      double pipRisk = PipStep; // Риск в пунктах
      
      if(pipValue > 0 && pipRisk > 0)
      {
         lotSize = NormalizeDouble(riskAmount / (pipValue * pipRisk), lotdecimal);
      }
   }
   
   // Ограничения размера лота
   double minLot = SymbolInfo.LotsMin();
   double maxLot = MathMin(SymbolInfo.LotsMax(), MaxLots);
   double stepLot = SymbolInfo.LotsStep();
   
   lotSize = MathMax(minLot, lotSize);
   lotSize = MathMin(maxLot, lotSize);
   lotSize = NormalizeDouble(lotSize, lotdecimal);
   
   return lotSize;
}

//+------------------------------------------------------------------+
//| Закрытие позиций по сигналу                                      |
//+------------------------------------------------------------------+
void ClosePositionsBySignal(bool closeBuy, bool closeSell)
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == Trade.RequestMagic())
      {
         ulong ticket = PositionGetTicket(i);
         if(PositionSelectByTicket(ticket))
         {
            ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            
            if((posType == POSITION_TYPE_BUY && closeBuy) || (posType == POSITION_TYPE_SELL && closeSell))
            {
               Trade.PositionClose(ticket);
               Print("Позиция закрыта по сигналу: ", ticket);
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Применение трейлинг-стопа к позициям                             |
//+------------------------------------------------------------------+
void TrailingPositions()
{
   if(!UseTrailingStop)
      return;
      
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == Trade.RequestMagic())
      {
         ulong ticket = PositionGetTicket(i);
         if(PositionSelectByTicket(ticket))
         {
            double currentSL = PositionGetDouble(POSITION_SL);
            double currentTP = PositionGetDouble(POSITION_TP);
            double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
            double currentPrice = PositionGetDouble(POSITION_PRICE_CURRENT);
            ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            
            double newSL = 0;
            bool modifySL = false;
            
            if(posType == POSITION_TYPE_BUY)
            {
               // Для длинных позиций
               double profit = currentPrice - openPrice;
               if(profit >= TrailStart * _Point * 10)
               {
                  newSL = currentPrice - TrailStop * _Point * 10;
                  if(newSL > currentSL || currentSL == 0)
                  {
                     modifySL = true;
                  }
               }
            }
            else if(posType == POSITION_TYPE_SELL)
            {
               // Для коротких позиций
               double profit = openPrice - currentPrice;
               if(profit >= TrailStart * _Point * 10)
               {
                  newSL = currentPrice + TrailStop * _Point * 10;
                  if(newSL < currentSL || currentSL == 0)
                  {
                     modifySL = true;
                  }
               }
            }
            
            if(modifySL)
            {
               if(Trade.PositionModify(ticket, newSL, currentTP))
               {
                  Print("Трейлинг-стоп применен для позиции #", ticket, ": новый SL = ", newSL);
               }
            }
         }
      }
   }
} 