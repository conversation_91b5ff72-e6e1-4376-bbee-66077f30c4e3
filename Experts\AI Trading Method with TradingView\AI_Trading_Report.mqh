//+------------------------------------------------------------------+
//|                                        AI_Trading_Report.mqh |
//|                                         Copyright ChartPrime |
//|                                                              |
//+------------------------------------------------------------------+
#property copyright "ChartPrime"
#property link      ""

// Класс для формирования и сохранения отчетов о торговле
class CAITradingReport
{
private:
   string            m_symbol;           // Торговый символ
   ENUM_TIMEFRAMES   m_timeframe;        // Таймфрейм
   string            m_reportDir;        // Директория для отчетов
   string            m_reportFile;       // Имя файла отчета
   int               m_fileHandle;       // Хендл файла
   bool              m_debug;            // Флаг отладки
   
   // Статистика торговли
   int               m_totalTrades;      // Общее количество сделок
   int               m_winTrades;        // Выигрышные сделки
   int               m_lossTrades;       // Проигрышные сделки
   double            m_grossProfit;      // Общая прибыль
   double            m_grossLoss;        // Общий убыток
   double            m_netProfit;        // Чистая прибыль
   double            m_maxDrawdown;      // Максимальная просадка
   double            m_profitFactor;     // Фактор прибыли
   double            m_expectancy;       // Математическое ожидание
   datetime          m_startDate;        // Дата начала торговли
   datetime          m_endDate;          // Дата окончания торговли
   
   // Массивы для хранения истории сигналов
   datetime          m_signalTimes[];    // Время сигналов
   int               m_signalTypes[];    // Типы сигналов (1 - покупка, -1 - продажа)
   double            m_signalValues[];   // Значения индикаторов
   double            m_results[];        // Результаты (в пунктах)
   
   // Приватные методы
   void              CalculateStatistics();
   string            FormatDateTime(datetime time);
   string            FormatDouble(double value, int digits=2);
   
public:
                     CAITradingReport();
                    ~CAITradingReport();
   
   // Инициализация отчета
   bool              Initialize(string symbol, ENUM_TIMEFRAMES timeframe);
   
   // Добавление информации о сигнале
   void              AddSignal(datetime time, int signalType, double krValue, double mlValue, double result=0);
   
   // Добавление информации о сделке
   void              AddTrade(datetime openTime, datetime closeTime, int type, double openPrice, 
                              double closePrice, double profit, double sl, double tp);
   
   // Генерация и сохранение отчета
   bool              GenerateReport(bool includeSignals=true, bool includeChart=true);
   
   // Сохранение скриншота графика
   bool              SaveChartScreenshot(string filename);
   
   // Сохранение графика торговли
   bool              SaveChart();
   
   // Аксессоры
   double            GetNetProfit() const { return m_netProfit; }
   double            GetProfitFactor() const { return m_profitFactor; }
   int               GetTotalTrades() const { return m_totalTrades; }
   double            GetWinPercentage() const { return m_totalTrades > 0 ? (double)m_winTrades/m_totalTrades*100.0 : 0; }
   
   // Добавим публичный метод для управления режимом отладки
   void              SetDebugMode(bool debug) { m_debug = debug; }
   bool              GetDebugMode() const { return m_debug; }
   
   // Проверка существования директории
   bool              DirectoryExists(string path);
   
   // Создание директории
   bool              CreateDirectory(string path);
};

//+------------------------------------------------------------------+
//| Конструктор класса                                               |
//+------------------------------------------------------------------+
CAITradingReport::CAITradingReport()
{
   m_symbol = Symbol();
   m_timeframe = PERIOD_CURRENT;
   m_reportDir = "Reports";
   m_fileHandle = INVALID_HANDLE;
   m_debug = false;  // Инициализация флага отладки
   
   m_totalTrades = 0;
   m_winTrades = 0;
   m_lossTrades = 0;
   m_grossProfit = 0;
   m_grossLoss = 0;
   m_netProfit = 0;
   m_maxDrawdown = 0;
   m_profitFactor = 0;
   m_expectancy = 0;
   m_startDate = 0;
   m_endDate = 0;
}

//+------------------------------------------------------------------+
//| Деструктор класса                                                |
//+------------------------------------------------------------------+
CAITradingReport::~CAITradingReport()
{
   if(m_fileHandle != INVALID_HANDLE)
      FileClose(m_fileHandle);
}

//+------------------------------------------------------------------+
//| Инициализация отчета                                             |
//+------------------------------------------------------------------+
bool CAITradingReport::Initialize(string symbol, ENUM_TIMEFRAMES timeframe)
{
   if(symbol == "")
      symbol = Symbol();
      
   m_symbol = symbol;
   m_timeframe = timeframe;
   
   // Используем только директорию Files
   m_reportDir = TerminalInfoString(TERMINAL_DATA_PATH) + "\\MQL5\\Files";
   
   Print("Путь к директории отчетов: ", m_reportDir);
   
   // Формируем имя файла отчета
   string timeframe_str = EnumToString(m_timeframe);
   string date_str = TimeToString(TimeCurrent(), TIME_DATE);
   date_str = StringReplace(date_str, ".", "");
   
   m_reportFile = StringFormat("%s\\AI_Trading_%s_%s_%s.html", 
                              m_reportDir,
                              m_symbol,
                              timeframe_str,
                              date_str);
   
   Print("Путь к файлу отчета: ", m_reportFile);
   
   // Проверяем возможность создания файла
   int test_handle = FileOpen(m_reportFile, FILE_WRITE|FILE_TXT|FILE_ANSI);
   if(test_handle != INVALID_HANDLE)
   {
      FileClose(test_handle);
      FileDelete(m_reportFile);
      Print("Тестовое создание файла успешно");
   }
   else
   {
      Print("Предупреждение при проверке доступа к файлу отчета: ", GetLastError());
      Print("Продолжаем работу, так как ошибка не критична");
   }
   
   // Сбрасываем статистику
   m_totalTrades = 0;
   m_winTrades = 0;
   m_lossTrades = 0;
   m_grossProfit = 0;
   m_grossLoss = 0;
   m_netProfit = 0;
   m_maxDrawdown = 0;
   m_profitFactor = 0;
   m_expectancy = 0;
   m_startDate = TimeCurrent();
   m_endDate = TimeCurrent();
   
   // Очищаем массивы
   ArrayResize(m_signalTimes, 0);
   ArrayResize(m_signalTypes, 0);
   ArrayResize(m_signalValues, 0);
   ArrayResize(m_results, 0);
   
   return true; // Всегда возвращаем true, так как ошибки не критичны
}

//+------------------------------------------------------------------+
//| Добавление информации о сигнале                                   |
//+------------------------------------------------------------------+
void CAITradingReport::AddSignal(datetime time, int signalType, double krValue, double mlValue, double result=0)
{
   int size = ArraySize(m_signalTimes);
   ArrayResize(m_signalTimes, size + 1);
   ArrayResize(m_signalTypes, size + 1);
   ArrayResize(m_signalValues, size + 1);
   ArrayResize(m_results, size + 1);
   
   m_signalTimes[size] = time;
   m_signalTypes[size] = signalType;
   m_signalValues[size] = krValue + mlValue; // Просто сумма для примера, можно использовать другую логику
   m_results[size] = result;
}

//+------------------------------------------------------------------+
//| Добавление информации о сделке                                    |
//+------------------------------------------------------------------+
void CAITradingReport::AddTrade(datetime openTime, datetime closeTime, int type, double openPrice, 
                          double closePrice, double profit, double sl, double tp)
{
   // Обновляем статистику
   m_totalTrades++;
   
   if(profit > 0)
   {
      m_winTrades++;
      m_grossProfit += profit;
   }
   else
   {
      m_lossTrades++;
      m_grossLoss += profit; // profit отрицательный в данном случае
   }
   
   m_netProfit = m_grossProfit + m_grossLoss;
   
   // Обновляем даты
   if(m_startDate == 0 || openTime < m_startDate)
      m_startDate = openTime;
      
   if(closeTime > m_endDate)
      m_endDate = closeTime;
      
   // Фактор прибыли
   if(m_grossLoss != 0)
      m_profitFactor = MathAbs(m_grossProfit / m_grossLoss);
   else
      m_profitFactor = m_grossProfit > 0 ? 999.0 : 0;
      
   // Математическое ожидание
   if(m_totalTrades > 0)
   {
      double win_rate = (double)m_winTrades / m_totalTrades;
      double avg_win = m_winTrades > 0 ? m_grossProfit / m_winTrades : 0;
      double avg_loss = m_lossTrades > 0 ? MathAbs(m_grossLoss / m_lossTrades) : 0;
      
      if(avg_loss > 0)
         m_expectancy = (win_rate * avg_win / avg_loss) - (1 - win_rate);
      else
         m_expectancy = win_rate > 0 ? 999.0 : -999.0;
   }
}

//+------------------------------------------------------------------+
//| Генерация HTML отчета                                              |
//+------------------------------------------------------------------+
bool CAITradingReport::GenerateReport(bool includeSignals, bool includeChart)
{
   // Открываем файл для записи
   int fileHandle = FileOpen(m_reportFile, FILE_WRITE|FILE_TXT|FILE_ANSI);
   if(fileHandle == INVALID_HANDLE)
   {
      Print("Предупреждение при создании файла отчета: ", GetLastError());
      
      // Пробуем создать файл с другим именем
      m_reportFile = StringFormat("%s\\AI_Trading_Report_%s_%s.html", 
                                 m_reportDir,
                                 m_symbol,
                                 TimeToString(TimeCurrent(), TIME_DATE));
                                 
      fileHandle = FileOpen(m_reportFile, FILE_WRITE|FILE_TXT|FILE_ANSI);
      if(fileHandle == INVALID_HANDLE)
      {
         Print("Ошибка при создании файла отчета: ", GetLastError());
         return false;
      }
   }
   
   // Сохраняем график перед генерацией отчета
   if(includeChart)
   {
      SaveChart();
   }
   
   // Рассчитываем статистику
   CalculateStatistics();
   
   // Записываем HTML заголовок с CSS стилями
   string html = "<html><head><style>\n"
                "body { font-family: Arial, sans-serif; margin: 20px; }\n"
                "table { border-collapse: collapse; width: 100%; }\n"
                "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n"
                "th { background-color: #4CAF50; color: white; }\n"
                "tr:nth-child(even) { background-color: #f2f2f2; }\n"
                ".positive { color: green; }\n"
                ".negative { color: red; }\n"
                "</style></head><body>\n";
   
   FileWriteString(fileHandle, html);
   
   // Записываем заголовок отчета
   html = StringFormat("<h2>Отчет торговли AI Expert (%s)</h2>\n",
                      TimeToString(TimeCurrent()));
   FileWriteString(fileHandle, html);
   
   // Таблица статистики
   FileWriteString(fileHandle, "<table>\n");
   FileWriteString(fileHandle, "<tr><th>Параметр</th><th>Значение</th></tr>\n");
   
   // Используем StringFormat для безопасного преобразования типов
   string rows = StringFormat(
      "<tr><td>Всего сделок</td><td>%d</td></tr>\n"
      "<tr><td>Прибыльных сделок</td><td>%d</td></tr>\n"
      "<tr><td>Убыточных сделок</td><td>%d</td></tr>\n"
      "<tr><td>Общая прибыль</td><td class='positive'>%.2f</td></tr>\n"
      "<tr><td>Общий убыток</td><td class='negative'>%.2f</td></tr>\n"
      "<tr><td>Чистая прибыль</td><td class='%s'>%.2f</td></tr>\n"
      "<tr><td>Фактор прибыли</td><td>%.2f</td></tr>\n",
      m_totalTrades,
      m_winTrades,
      m_lossTrades,
      m_grossProfit,
      m_grossLoss,
      (m_netProfit >= 0 ? "positive" : "negative"),
      m_netProfit,
      m_profitFactor
   );
   
   FileWriteString(fileHandle, rows);
   FileWriteString(fileHandle, "</table>\n");
   
   // Добавляем график в отчет
   string chartFileName = StringFormat("AI_Trading_Chart_%s.png", 
                                     TimeToString(TimeCurrent(), TIME_DATE));
   
   if(FileIsExist(chartFileName))
   {
      FileWriteString(fileHandle, "<h3>График торговли</h3>\n");
      FileWriteString(fileHandle, StringFormat("<img src='%s' style='max-width:100%%;'>\n", 
                                             chartFileName));
   }
   
   // Закрываем HTML
   FileWriteString(fileHandle, "</body></html>");
   
   // Закрываем файл
   FileClose(fileHandle);
   
   if(m_debug)
      Print("Отчет сохранен в файл: ", m_reportFile);
   
   return true;
}

//+------------------------------------------------------------------+
//| Расчет торговой статистики                                        |
//+------------------------------------------------------------------+
void CAITradingReport::CalculateStatistics()
{
   m_totalTrades = m_winTrades + m_lossTrades;
   
   // Проверяем деление на ноль
   if(m_grossLoss != 0)
      m_profitFactor = MathAbs(m_grossProfit / m_grossLoss);
   else if(m_grossProfit > 0)
      m_profitFactor = 100; // Условное большое значение если нет убытков
   else
      m_profitFactor = 0;
      
   m_netProfit = m_grossProfit + m_grossLoss; // Учитываем, что m_grossLoss отрицательный
   
   // Добавляем проверку корректности значений
   if(m_totalTrades < 0) m_totalTrades = 0;
   if(m_winTrades < 0) m_winTrades = 0;
   if(m_lossTrades < 0) m_lossTrades = 0;
   
   // Логируем результаты для отладки
   if(m_debug)
   {
      Print("Статистика торговли:");
      Print("Всего сделок: ", m_totalTrades);
      Print("Прибыльных сделок: ", m_winTrades);
      Print("Убыточных сделок: ", m_lossTrades);
      Print("Общая прибыль: ", m_grossProfit);
      Print("Общий убыток: ", m_grossLoss);
      Print("Чистая прибыль: ", m_netProfit);
      Print("Фактор прибыли: ", m_profitFactor);
   }
}

//+------------------------------------------------------------------+
//| Форматирование даты/времени                                       |
//+------------------------------------------------------------------+
string CAITradingReport::FormatDateTime(datetime time)
{
   return TimeToString(time, TIME_DATE|TIME_MINUTES);
}

//+------------------------------------------------------------------+
//| Форматирование числа                                              |
//+------------------------------------------------------------------+
string CAITradingReport::FormatDouble(double value, int digits=2)
{
   return DoubleToString(value, digits);
}

//+------------------------------------------------------------------+
//| Сохранение скриншота графика                                      |
//+------------------------------------------------------------------+
bool CAITradingReport::SaveChartScreenshot(string filename)
{
   if(!ChartScreenShot(0, filename, 1280, 720, ALIGN_RIGHT))
   {
      Print("Ошибка при создании скриншота графика: ", GetLastError());
      return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Сохранение графика торговли                                        |
//+------------------------------------------------------------------+
bool CAITradingReport::SaveChart()
{
   // Формируем имя файла для графика
   string chartFileName = StringFormat("AI_Trading_Chart_%s.png", 
                                     TimeToString(TimeCurrent(), TIME_DATE));
   
   // Сохраняем скриншот графика
   if(!ChartScreenShot(0, chartFileName, 1024, 768, ALIGN_RIGHT))
   {
      if(m_debug)
         Print("Ошибка при сохранении графика: ", GetLastError());
      return false;
   }
   
   if(m_debug)
      Print("График сохранен в файл: ", chartFileName);
   
   return true;
}

//+------------------------------------------------------------------+
//| Проверка существования директории                                 |
//+------------------------------------------------------------------+
bool CAITradingReport::DirectoryExists(string path)
{
   return FileIsExist(path + "\\dummy.tmp", FILE_COMMON);
}

//+------------------------------------------------------------------+
//| Создание директории                                               |
//+------------------------------------------------------------------+
bool CAITradingReport::CreateDirectory(string path)
{
   return FolderCreate(path);
}

//+------------------------------------------------------------------+ 