﻿//+------------------------------------------------------------------+
//|                                           Turbo-profit v.3.1.mq5 |
//|                              Copyright В© 2023, Forex-Investor.net|
//|                                       Forex-Investor.net         |
//+------------------------------------------------------------------+
#property copyright "Copyright В© 2023, Forex-Investor.net"
#property link      "Forex-Investor.net"
#property version   "3.1"
#property description "Turbo-profit v.3.1 РґР»СЏ MetaTrader 5"
#property description "РђРґР°РїС‚РёСЂРѕРІР°РЅРЅР°СЏ РІРµСЂСЃРёСЏ СЃРѕРІРµС‚РЅРёРєР° РёР· MetaTrader 4"
#property description "РћРїС‚РёРјРёР·РёСЂРѕРІР°РЅРѕ РґР»СЏ Р±С‹СЃС‚СЂРѕРіРѕ С‚РµСЃС‚РёСЂРѕРІР°РЅРёСЏ"

// РћРїСЂРµРґРµР»РµРЅРёСЏ РїРµСЂРµС‡РёСЃР»РµРЅРёР№ РґР»СЏ Р°РґР°РїС‚РёРІРЅРѕРіРѕ СЂР°Р·РјРµСЂР° СЃРµС‚РєРё
enum ENUM_MARKET_SESSION {
   SESSION_ASIAN = 0,     // РђР·РёР°С‚СЃРєР°СЏ СЃРµСЃСЃРёСЏ (00:00-08:00)
   SESSION_EUROPEAN = 1,  // Р•РІСЂРѕРїРµР№СЃРєР°СЏ СЃРµСЃСЃРёСЏ (08:00-16:00)
   SESSION_AMERICAN = 2   // РђРјРµСЂРёРєР°РЅСЃРєР°СЏ СЃРµСЃСЃРёСЏ (16:00-24:00)
};

enum ENUM_SYMBOL_TYPE {
   TYPE_FOREX_MAJOR = 0,  // РћСЃРЅРѕРІРЅС‹Рµ РІР°Р»СЋС‚РЅС‹Рµ РїР°СЂС‹ (EURUSD, GBPUSD Рё С‚.Рґ.)
   TYPE_FOREX_CROSS = 1,  // РљСЂРѕСЃСЃ-РІР°Р»СЋС‚РЅС‹Рµ РїР°СЂС‹ (EURGBP, AUDJPY Рё С‚.Рґ.)
   TYPE_COMMODITY = 2,    // РЎС‹СЂСЊРµРІС‹Рµ РёРЅСЃС‚СЂСѓРјРµРЅС‚С‹ (XAUUSD, XAGUSD Рё С‚.Рґ.)
   TYPE_INDEX = 3         // РРЅРґРµРєСЃС‹ (DE30, US30 Рё С‚.Рґ.)
};

// РњРµС‚РѕРґС‹ РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёСЏ РёР· РїСЂРѕСЃР°РґРєРё
enum ENUM_RECOVERY_METHOD {
   RECOVERY_AVERAGING = 0,       // РРЅС‚РµР»Р»РµРєС‚СѓР°Р»СЊРЅРѕРµ СѓСЃСЂРµРґРЅРµРЅРёРµ
   RECOVERY_PARTIAL_CLOSE = 1,   // Р§Р°СЃС‚РёС‡РЅРѕРµ Р·Р°РєСЂС‹С‚РёРµ
   RECOVERY_HEDGING = 2,         // РҐРµРґР¶РёСЂРѕРІР°РЅРёРµ
   RECOVERY_COMBINED = 3         // РљРѕРјР±РёРЅРёСЂРѕРІР°РЅРЅС‹Р№ РјРµС‚РѕРґ
};

// РџРµСЂРµС‡РёСЃР»РµРЅРёСЏ РґР»СЏ РїСЂРѕРґРІРёРЅСѓС‚РѕРіРѕ Р°РЅР°Р»РёР·Р° СЂС‹РЅРєР°
// РўРёРїС‹ СЂС‹РЅРѕС‡РЅРѕР№ СЃС‚СЂСѓРєС‚СѓСЂС‹
enum ENUM_MARKET_STRUCTURE
{
   STRUCTURE_UNDEFINED,     // РќРµРѕРїСЂРµРґРµР»РµРЅРЅР°СЏ СЃС‚СЂСѓРєС‚СѓСЂР°
   STRUCTURE_UPTREND,       // Р’РѕСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ (HH+HL)
   STRUCTURE_DOWNTREND,     // РќРёСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ (LL+LH)
   STRUCTURE_RANGING,       // Р‘РѕРєРѕРІРѕРµ РґРІРёР¶РµРЅРёРµ
   STRUCTURE_REVERSAL_UP,   // Р Р°Р·РІРѕСЂРѕС‚ РІРІРµСЂС…
   STRUCTURE_REVERSAL_DOWN  // Р Р°Р·РІРѕСЂРѕС‚ РІРЅРёР·
};

// РўРёРїС‹ С†РµРЅРѕРІС‹С… РїР°С‚С‚РµСЂРЅРѕРІ
enum ENUM_PRICE_PATTERN
{
   PATTERN_NONE,            // РќРµС‚ РїР°С‚С‚РµСЂРЅР°
   PATTERN_PINBAR_BULL,     // Р‘С‹С‡РёР№ РїРёРЅ-Р±Р°СЂ
   PATTERN_PINBAR_BEAR,     // РњРµРґРІРµР¶РёР№ РїРёРЅ-Р±Р°СЂ
   PATTERN_ENGULFING_BULL,  // Р‘С‹С‡СЊРµ РїРѕРіР»РѕС‰РµРЅРёРµ
   PATTERN_ENGULFING_BEAR,  // РњРµРґРІРµР¶СЊРµ РїРѕРіР»РѕС‰РµРЅРёРµ
   PATTERN_INSIDE_BAR,      // Р’РЅСѓС‚СЂРµРЅРЅРёР№ Р±Р°СЂ
   PATTERN_OUTSIDE_BAR,     // Р’РЅРµС€РЅРёР№ Р±Р°СЂ
   PATTERN_DOJI,            // Р”РѕРґР¶Рё
   PATTERN_THREE_BAR_BULL,  // Р‘С‹С‡РёР№ С‚СЂРµС…Р±Р°СЂРЅС‹Р№ РїР°С‚С‚РµСЂРЅ
   PATTERN_THREE_BAR_BEAR,  // РњРµРґРІРµР¶РёР№ С‚СЂРµС…Р±Р°СЂРЅС‹Р№ РїР°С‚С‚РµСЂРЅ
   PATTERN_TWEEZER_BOTTOM,  // РџРёРЅС†РµС‚ (РґРЅРѕ)
   PATTERN_TWEEZER_TOP,     // РџРёРЅС†РµС‚ (РІРµСЂС€РёРЅР°)
   PATTERN_GARTLEY,         // Р“Р°СЂРјРѕРЅРёС‡РµСЃРєРёР№ РїР°С‚С‚РµСЂРЅ Р“Р°СЂС‚Р»Рё
   PATTERN_BUTTERFLY,       // Р“Р°СЂРјРѕРЅРёС‡РµСЃРєРёР№ РїР°С‚С‚РµСЂРЅ Р‘Р°Р±РѕС‡РєР°
   PATTERN_BAT,             // Р“Р°СЂРјРѕРЅРёС‡РµСЃРєРёР№ РїР°С‚С‚РµСЂРЅ Р›РµС‚СѓС‡Р°СЏ РјС‹С€СЊ
   PATTERN_CRAB,            // Р“Р°СЂРјРѕРЅРёС‡РµСЃРєРёР№ РїР°С‚С‚РµСЂРЅ РљСЂР°Р±
   PATTERN_BULL_TREND,      // Р‘С‹С‡РёР№ С‚СЂРµРЅРґ
   PATTERN_BEAR_TREND,      // РњРµРґРІРµР¶РёР№ С‚СЂРµРЅРґ
   PATTERN_RANGING          // Р‘РѕРєРѕРІРѕРµ РґРІРёР¶РµРЅРёРµ
};

// РЎС‚СЂСѓРєС‚СѓСЂР° РґР»СЏ С…СЂР°РЅРµРЅРёСЏ РёРЅС„РѕСЂРјР°С†РёРё Рѕ РєР»СЋС‡РµРІРѕРј СѓСЂРѕРІРЅРµ
struct KeyLevel
{
   double            price;       // Р¦РµРЅР° СѓСЂРѕРІРЅСЏ
   double            strength;    // РЎРёР»Р° СѓСЂРѕРІРЅСЏ (0-100)
   int               touches;     // РљРѕР»РёС‡РµСЃС‚РІРѕ РєР°СЃР°РЅРёР№
   datetime          created;     // Р’СЂРµРјСЏ СЃРѕР·РґР°РЅРёСЏ
   datetime          last_touch;  // Р’СЂРµРјСЏ РїРѕСЃР»РµРґРЅРµРіРѕ РєР°СЃР°РЅРёСЏ
   bool              is_support;  // РЇРІР»СЏРµС‚СЃСЏ Р»Рё СѓСЂРѕРІРЅРµРј РїРѕРґРґРµСЂР¶РєРё (РёРЅР°С‡Рµ СЃРѕРїСЂРѕС‚РёРІР»РµРЅРёРµ)
   double            volume_activity; // РђРєС‚РёРІРЅРѕСЃС‚СЊ РѕР±СЉРµРјРѕРІ РЅР° СѓСЂРѕРІРЅРµ
   double            rejection_strength; // РЎРёР»Р° РѕС‚Р±РѕСЏ РѕС‚ СѓСЂРѕРІРЅСЏ (0-100)
   bool              is_order_cluster; // РЇРІР»СЏРµС‚СЃСЏ Р»Рё Р·РѕРЅРѕР№ СЃРєРѕРїР»РµРЅРёСЏ РѕСЂРґРµСЂРѕРІ
};

// РЎС‚СЂСѓРєС‚СѓСЂР° РґР»СЏ С…СЂР°РЅРµРЅРёСЏ РёРЅС„РѕСЂРјР°С†РёРё Рѕ СЂС‹РЅРѕС‡РЅРѕР№ СЃС‚СЂСѓРєС‚СѓСЂРµ
struct MarketStructureInfo
{
   ENUM_MARKET_STRUCTURE  type;           // РўРёРї СЃС‚СЂСѓРєС‚СѓСЂС‹
   double                 last_high;      // РџРѕСЃР»РµРґРЅРёР№ РјР°РєСЃРёРјСѓРј
   double                 last_low;       // РџРѕСЃР»РµРґРЅРёР№ РјРёРЅРёРјСѓРј
   double                 prev_high;      // РџСЂРµРґС‹РґСѓС‰РёР№ РјР°РєСЃРёРјСѓРј
   double                 prev_low;       // РџСЂРµРґС‹РґСѓС‰РёР№ РјРёРЅРёРјСѓРј
   int                    trend_strength; // РЎРёР»Р° С‚СЂРµРЅРґР° (0-100)
};

// РЎС‚СЂСѓРєС‚СѓСЂР° РґР»СЏ РЅР°СЃС‚СЂРѕРµРє С‚РѕСЂРіРѕРІРѕР№ СЃРµСЃСЃРёРё
struct SessionSettings
{
   double   lot_multiplier;           // РњРЅРѕР¶РёС‚РµР»СЊ СЂР°Р·РјРµСЂР° Р»РѕС‚Р° РґР»СЏ СЃРµСЃСЃРёРё
   double   grid_step_multiplier;     // РњРЅРѕР¶РёС‚РµР»СЊ С€Р°РіР° СЃРµС‚РєРё РґР»СЏ СЃРµСЃСЃРёРё
   double   tp_multiplier;            // РњРЅРѕР¶РёС‚РµР»СЊ РґР»СЏ С‚РµР№Рє-РїСЂРѕС„РёС‚Р°
   double   sl_multiplier;            // РњРЅРѕР¶РёС‚РµР»СЊ РґР»СЏ СЃС‚РѕРї-Р»РѕСЃСЃР°
   int      trailing_activation;      // РђРєС‚РёРІР°С†РёСЏ С‚СЂРµР№Р»РёРЅРіР° (РІ РїСѓРЅРєС‚Р°С…)
   double   averaging_multiplier;     // РњРЅРѕР¶РёС‚РµР»СЊ РґР»СЏ СѓСЃСЂРµРґРЅРµРЅРёСЏ
   bool     use_additional_filters;   // РСЃРїРѕР»СЊР·РѕРІР°С‚СЊ РґРѕРїРѕР»РЅРёС‚РµР»СЊРЅС‹Рµ С„РёР»СЊС‚СЂС‹
   string   description;              // РћРїРёСЃР°РЅРёРµ РЅР°СЃС‚СЂРѕРµРє СЃРµСЃСЃРёРё
};

// РЎС‚СЂСѓРєС‚СѓСЂР° РґР»СЏ С‚РѕС‡РєРё РѕРїС‚РёРјР°Р»СЊРЅРѕРіРѕ СѓСЃСЂРµРґРЅРµРЅРёСЏ
struct AveragingPoint
{
   double            price;       // Р¦РµРЅР° РґР»СЏ СѓСЃСЂРµРґРЅРµРЅРёСЏ
   double            lot_coef;    // РљРѕСЌС„С„РёС†РёРµРЅС‚ РґР»СЏ СЂР°СЃС‡РµС‚Р° Р»РѕС‚Р°
   double            probability; // Р’РµСЂРѕСЏС‚РЅРѕСЃС‚СЊ СЂР°Р·РІРѕСЂРѕС‚Р° (0-100)
   ENUM_PRICE_PATTERN pattern;    // Р¦РµРЅРѕРІРѕР№ РїР°С‚С‚РµСЂРЅ РІ СЌС‚РѕР№ С‚РѕС‡РєРµ
   int               level_index; // РРЅРґРµРєСЃ СЃРІСЏР·Р°РЅРЅРѕРіРѕ РєР»СЋС‡РµРІРѕРіРѕ СѓСЂРѕРІРЅСЏ (-1 РµСЃР»Рё РЅРµС‚)
   bool              confirmed_h1; // РЈСЂРѕРІРµРЅСЊ РїРѕРґС‚РІРµСЂР¶РґРµРЅ РЅР° H1
   bool              confirmed_h4; // РЈСЂРѕРІРµРЅСЊ РїРѕРґС‚РІРµСЂР¶РґРµРЅ РЅР° H4
   bool              confirmed_d1; // РЈСЂРѕРІРµРЅСЊ РїРѕРґС‚РІРµСЂР¶РґРµРЅ РЅР° D1
   int               mtf_confirmations; // РљРѕР»РёС‡РµСЃС‚РІРѕ РїРѕРґС‚РІРµСЂР¶РґРµРЅРёР№ РЅР° СЂР°Р·РЅС‹С… С‚Р°Р№РјС„СЂРµР№РјР°С…
};

// РЎС‚СЂСѓРєС‚СѓСЂР° РґР»СЏ РёРЅС„РѕСЂРјР°С†РёРё Рѕ СЃРёР»Рµ РїР°С‚С‚РµСЂРЅР°
struct PatternStrength
{
   ENUM_PRICE_PATTERN  pattern;       // РўРёРї РїР°С‚С‚РµСЂРЅР°
   double              strength;      // РЎРёР»Р° РїР°С‚С‚РµСЂРЅР° (0-100)
   double              price_level;   // Р¦РµРЅРѕРІРѕР№ СѓСЂРѕРІРµРЅСЊ РїР°С‚С‚РµСЂРЅР°
   int                 bar_index;     // РРЅРґРµРєСЃ Р±Р°СЂР°, РіРґРµ СЃС„РѕСЂРјРёСЂРѕРІР°РЅ РїР°С‚С‚РµСЂРЅ
   bool                confirmed_h1;  // РџРѕРґС‚РІРµСЂР¶РґРµРЅ РЅР° H1
   bool                confirmed_h4;  // РџРѕРґС‚РІРµСЂР¶РґРµРЅ РЅР° H4
   bool                confirmed_d1;  // РџРѕРґС‚РІРµСЂР¶РґРµРЅ РЅР° D1
   bool                on_key_level;  // РќР°С…РѕРґРёС‚СЃСЏ РЅР° РєР»СЋС‡РµРІРѕРј СѓСЂРѕРІРЅРµ
   int                 trend_align;   // РЎРѕРѕС‚РІРµС‚СЃС‚РІРёРµ С‚СЂРµРЅРґСѓ (-1 РїСЂРѕС‚РёРІ, 0 РЅРµР№С‚СЂР°Р»СЊРЅРѕ, 1 РїРѕ С‚СЂРµРЅРґСѓ)
};

// РЎС‚СЂСѓРєС‚СѓСЂР° РґР»СЏ С…СЂР°РЅРµРЅРёСЏ Р·РѕРЅ РѕР±СЉРµРјР°
struct VolumeZone
{
   double   price;          // Р¦РµРЅР° Р·РѕРЅС‹
   long     delta_volume;   // Р”РµР»СЊС‚Р° РѕР±СЉРµРјР° (РїРѕР»РѕР¶РёС‚РµР»СЊРЅР°СЏ РґР»СЏ РїРѕРєСѓРїР°С‚РµР»РµР№, РѕС‚СЂРёС†Р°С‚РµР»СЊРЅР°СЏ РґР»СЏ РїСЂРѕРґР°РІС†РѕРІ)
   datetime created;        // Р’СЂРµРјСЏ СЃРѕР·РґР°РЅРёСЏ Р·РѕРЅС‹
   int      touches;        // РљРѕР»РёС‡РµСЃС‚РІРѕ РєР°СЃР°РЅРёР№ С†РµРЅС‹
   bool     is_active;      // РђРєС‚РёРІРЅР° Р»Рё Р·РѕРЅР°
};

// РЎС‚СЂСѓРєС‚СѓСЂР° РґР»СЏ С…СЂР°РЅРµРЅРёСЏ РёРЅС„РѕСЂРјР°С†РёРё Рѕ С…РµРґР¶РёСЂСѓСЋС‰РёС… РїРѕР·РёС†РёСЏС…
struct HedgingInfo
{
   bool     is_active;            // РђРєС‚РёРІРЅРѕ Р»Рё С…РµРґР¶РёСЂРѕРІР°РЅРёРµ
   double   original_lot;         // Р Р°Р·РјРµСЂ РѕСЂРёРіРёРЅР°Р»СЊРЅРѕР№ РїРѕР·РёС†РёРё
   double   hedge_lot;            // Р Р°Р·РјРµСЂ С…РµРґР¶РёСЂСѓСЋС‰РµР№ РїРѕР·РёС†РёРё
   double   hedge_price;          // Р¦РµРЅР° С…РµРґР¶РёСЂСѓСЋС‰РµР№ РїРѕР·РёС†РёРё
   ulong    hedge_ticket;         // РўРёРєРµС‚ С…РµРґР¶РёСЂСѓСЋС‰РµР№ РїРѕР·РёС†РёРё
   datetime created_time;         // Р’СЂРµРјСЏ СЃРѕР·РґР°РЅРёСЏ С…РµРґР¶РёСЂСѓСЋС‰РµР№ РїРѕР·РёС†РёРё
   double   target_price;         // Р¦РµР»РµРІР°СЏ С†РµРЅР° РґР»СЏ Р·Р°РєСЂС‹С‚РёСЏ С…РµРґР¶Р°
   bool     partial_closed;       // Р‘С‹Р»Рѕ Р»Рё С‡Р°СЃС‚РёС‡РЅРѕРµ Р·Р°РєСЂС‹С‚РёРµ
   double   profit_at_hedge;      // РџСЂРёР±С‹Р»СЊ/СѓР±С‹С‚РѕРє РЅР° РјРѕРјРµРЅС‚ С…РµРґР¶РёСЂРѕРІР°РЅРёСЏ
   int      related_layer;        // РЎРІСЏР·Р°РЅРЅС‹Р№ СЃР»РѕР№
   int      hedge_direction;      // РќР°РїСЂР°РІР»РµРЅРёРµ С…РµРґР¶РёСЂРѕРІР°РЅРёСЏ (0-Buy, 1-Sell)
   double   optimal_ratio;        // РћРїС‚РёРјР°Р»СЊРЅРѕРµ СЃРѕРѕС‚РЅРѕС€РµРЅРёРµ РѕР±СЉРµРјРѕРІ
   double   current_correlation;  // РўРµРєСѓС‰Р°СЏ РєРѕСЂСЂРµР»СЏС†РёСЏ СЃ РѕСЃРЅРѕРІРЅРѕР№ РїРѕР·РёС†РёРµР№
};

// РџРѕРґРєР»СЋС‡РµРЅРёРµ РЅРѕР±С…РѕРґРёРјС‹С… Р±РёР±Р»РёРѕС‚РµРє
#include <Trade\\Trade.mqh>
#include <Trade\\PositionInfo.mqh>
#include <Arrays\\ArrayDouble.mqh>
#include <Arrays\\ArrayInt.mqh>
#include <Indicators\\Trend.mqh>

// Р“Р»РѕР±Р°Р»СЊРЅС‹Рµ РїРµСЂРµРјРµРЅРЅС‹Рµ РґР»СЏ СЂР°Р±РѕС‚С‹ СЃРѕРІРµС‚РЅРёРєР°
string g_info_string = "";  // Р”Р»СЏ РѕС‚РѕР±СЂР°Р¶РµРЅРёСЏ РёРЅС„РѕСЂРјР°С†РёРё Рѕ РїСЂРѕРґРІРёРЅСѓС‚РѕРј Р°РЅР°Р»РёР·Рµ СЂС‹РЅРєР°

// Р’С…РѕРґРЅС‹Рµ РїР°СЂР°РјРµС‚СЂС‹ СЃРѕРІРµС‚РЅРёРєР°
// Р РµР¶РёРј СЂР°Р±РѕС‚С‹
input string __r__ = "Р РµР¶РёРј СЂР°Р±РѕС‚С‹";
input bool   Exit_mode = false;  // Р РµР¶РёРј РІС‹С…РѕРґР° РёР· РїРѕР·РёС†РёР№

// РќР°СЃС‚СЂРѕР№РєРё СѓРјРЅРѕРіРѕ РІС‹С…РѕРґР° РёР· РїСЂРѕСЃР°РґРєРё СЃ РїСЂРёР±С‹Р»СЊСЋ
input string __smart_exit__ = "РќР°СЃС‚СЂРѕР№РєРё СѓРјРЅРѕРіРѕ РІС‹С…РѕРґР° РёР· РїСЂРѕСЃР°РґРєРё СЃ РїСЂРёР±С‹Р»СЊСЋ";
input bool   UseSmartExit = true;              // РСЃРїРѕР»СЊР·РѕРІР°С‚СЊ СѓРјРЅС‹Р№ РІС‹С…РѕРґ РёР· РїСЂРѕСЃР°РґРєРё
input ENUM_RECOVERY_METHOD RecoveryMethod = RECOVERY_COMBINED; // РњРµС‚РѕРґ РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёСЏ
input double DrawdownMax_Percent = 15.0;       // РњР°РєСЃРёРјР°Р»СЊРЅР°СЏ РїСЂРѕСЃР°РґРєР° (% РѕС‚ РґРµРїРѕР·РёС‚Р°)
input int    DrawdownMax_Time = 48;           // РњР°РєСЃ. РІСЂРµРјСЏ РІ РїСЂРѕСЃР°РґРєРµ (С‡Р°СЃС‹)
input double TargetProfit_Percent = 1.0;       // Р¦РµР»РµРІР°СЏ РїСЂРёР±С‹Р»СЊ (% РѕС‚ СѓР±С‹С‚РєР°)
input int    MaxRecoveryPositions = 5;         // РњР°РєСЃРёРјСѓРј РґРѕРїРѕР»РЅРёС‚РµР»СЊРЅС‹С… РїРѕР·РёС†РёР№
input double SmartLotMultiplier = 1.3;         // РњРЅРѕР¶РёС‚РµР»СЊ Р»РѕС‚Р° РїСЂРё СѓСЃСЂРµРґРЅРµРЅРёРё
input int    PartialClosePercent = 30;         // РџСЂРѕС†РµРЅС‚ С‡Р°СЃС‚РёС‡РЅРѕРіРѕ Р·Р°РєСЂС‹С‚РёРµ РЅР° РѕС‚РєР°С‚Р°С…
input bool   UseMarketStructure = true;        // РЈС‡РёС‚С‹РІР°С‚СЊ СЃС‚СЂСѓРєС‚СѓСЂСѓ СЂС‹РЅРєР°
input int    MinProfitToClose = 15;            // РњРёРЅРёРјР°Р»СЊРЅР°СЏ РїСЂРёР±С‹Р»СЊ РґР»СЏ Р·Р°РєСЂС‹С‚РёСЏ (РїСѓРЅРєС‚С‹)
input double TrailingActivationLevel = 0.5;    // РЈСЂРѕРІРµРЅСЊ Р°РєС‚РёРІР°С†РёРё С‚СЂРµР№Р»РёРЅРіР° (0.0-1.0)
input int    DrawdownPause_After_Exit = 4;     // РџР°СѓР·Р° РїРѕСЃР»Рµ РІС‹С…РѕРґР° (С‡Р°СЃС‹)

// РќР°СЃС‚СЂРѕР№РєРё СѓРјРЅРѕРіРѕ С…РµРґР¶РёСЂРѕРІР°РЅРёСЏ
input string __smart_hedging__ = "РќР°СЃС‚СЂРѕР№РєРё СѓРјРЅРѕРіРѕ С…РµРґР¶РёСЂРѕРІР°РЅРёСЏ";
input bool   UseSmartHedging = true;           // РСЃРїРѕР»СЊР·РѕРІР°С‚СЊ СѓРјРЅРѕРµ С…РµРґР¶РёСЂРѕРІР°РЅРёРµ
input double HedgingThreshold = 10.0;          // РџРѕСЂРѕРі Р°РєС‚РёРІР°С†РёРё С…РµРґР¶РёСЂРѕРІР°РЅРёСЏ (% РѕС‚ РґРµРїРѕР·РёС‚Р°)
input double HedgingVolatilityFactor = 1.5;    // Р¤Р°РєС‚РѕСЂ РІРѕР»Р°С‚РёР»СЊРЅРѕСЃС‚Рё РґР»СЏ С…РµРґР¶РёСЂРѕРІР°РЅРёСЏ
input double OptimalHedgeRatio = 0.7;          // РћРїС‚РёРјР°Р»СЊРЅРѕРµ СЃРѕРѕС‚РЅРѕС€РµРЅРёРµ С…РµРґР¶Р° Рє РїРѕР·РёС†РёРё
input int    MaxHedgePositions = 3;            // РњР°РєСЃРёРјР°Р»СЊРЅРѕРµ РєРѕР»РёС‡РµСЃС‚РІРѕ С…РµРґР¶РёСЂСѓС‰РёС… РїРѕР·РёС†РёР№
input int    MinDistanceForHedge = 20;         // РњРёРЅРёРјР°Р»СЊРЅРѕРµ СЂР°СЃСЃС‚РѕСЏРЅРёРµ РґР»СЏ С…РµРґР¶Р° (РїСѓРЅРєС‚С‹)
input int    PartialHedgeClosePercent = 50;    // РџСЂРѕС†РµРЅС‚ С‡Р°СЃС‚РёС‡РЅРѕРіРѕ Р·Р°РєСЂС‹С‚РёСЏ С…РµРґР¶Р°
input bool   AutoAdjustHedgeVolume = true;     // РђРІС‚РѕРјР°С‚РёС‡РµСЃРєР°СЏ РєРѕСЂСЂРµРєС‚РёСЂРѕРІРєР° РѕР±СЉРµРјР° С…РµРґР¶Р°
input double TargetNetProfit = 0.5;            // Р¦РµР»РµРІР°СЏ С‡РёСЃС‚Р°СЏ РїСЂРёР±С‹Р»СЊ РїСЂРё С…РµРґР¶РёСЂРѕРІР°РЅРёРё (%)
input int    HedgeOpenDelaySeconds = 5;        // Р—Р°РґРµСЂР¶РєР° РїРµСЂРµРґ РѕС‚РєСЂС‹С‚РёРµРј С…РµРґР¶Р° (СЃРµРєСѓРЅРґС‹)

// Р¤РёР»СЊС‚СЂ С‚СЂРµРЅРґР°
input string __trend__ = "РќР°СЃС‚СЂРѕР№РєРё С„РёР»СЊС‚СЂР° С‚СЂРµРЅРґР°";
input bool   UseTrendFilter = true;      // РСЃРїРѕР»СЊР·РѕРІР°С‚СЊ С„РёР»СЊС‚СЂ С‚СЂРµРЅРґР°
input bool   TradeOnlyWithTrend = false; // РўРѕСЂРіРѕРІР°С‚СЊ С‚РѕР»СЊРєРѕ РїРѕ С‚СЂРµРЅРґСѓ
input int    TrendDetectionMethod = 0;   // РњРµС‚РѕРґ РѕРїСЂРµРґРµР»РµРЅРёСЏ С‚СЂРµРЅРґР° (0-ADX, 1-MA, 2-РѕР±Р°)

// РќР°СЃС‚СЂРѕР№РєРё ADX
input int    ADX_Period = 14;            // РџРµСЂРёРѕРґ ADX
input int    ADX_TrendLevel = 25;        // РЈСЂРѕРІРµРЅСЊ СЃРёР»С‹ С‚СЂРµРЅРґР° ADX
input bool   ADX_UseDIFilter = true;     // РСЃРїРѕР»СЊР·РѕРІР°С‚СЊ С„РёР»СЊС‚СЂ РїРѕ DI+ Рё DI-

// РќР°СЃС‚СЂРѕР№РєРё Moving Average
input int    MA_Fast_Period = 20;        // РџРµСЂРёРѕРґ Р±С‹СЃС‚СЂРѕР№ MA
input int    MA_Slow_Period = 50;        // РџРµСЂРёРѕРґ РјРµРґР»РµРЅРЅРѕР№ MA
input int    MA_Method = MODE_SMA;       // РњРµС‚РѕРґ СЂР°СЃС‡РµС‚Р° MA 
input ENUM_APPLIED_PRICE MA_Applied_Price = PRICE_CLOSE; // Р¦РµРЅР° РґР»СЏ MA
input int    MA_SignalBar = 1;           // Р‘Р°СЂ РґР»СЏ СЃРёРіРЅР°Р»Р° (0-С‚РµРєСѓС‰РёР№, 1-РїСЂРµРґС‹РґСѓС‰РёР№)

// РќР°СЃС‚СЂРѕР№РєРё Р°РґР°РїС‚РёРІРЅРѕРіРѕ СЂР°Р·РјРµСЂР° СЃРµС‚РєРё
input string __adaptive_grid__ = "РќР°СЃС‚СЂРѕР№РєРё Р°РґР°РїС‚РёРІРЅРѕРіРѕ СЂР°Р·РјРµСЂР° СЃРµС‚РєРё";
input bool   UseAdaptiveGrid = true;     // РСЃРїРѕР»СЊР·РѕРІР°С‚СЊ Р°РґР°РїС‚РёРІРЅС‹Р№ СЂР°Р·РјРµСЂ СЃРµС‚РєРё
input bool   UseATRForGrid = true;       // РџСЂРёРІСЏР·Р°С‚СЊ С€Р°Рі СЃРµС‚РєРё Рє РІРѕР»Р°С‚РёР»СЊРЅРѕСЃС‚Рё (ATR)
input int    ATR_Period = 14;            // РџРµСЂРёРѕРґ ATR
input double ATR_Multiplier = 1.0;       // РњРЅРѕР¶РёС‚РµР»СЊ ATR РґР»СЏ С€Р°РіР° СЃРµС‚РєРё
input bool   SessionBasedSettings = true; // РР·РјРµРЅСЏС‚СЊ РїР°СЂР°РјРµС‚СЂС‹ РІ Р·Р°РІРёСЃРёРјРѕСЃС‚Рё РѕС‚ СЃРµСЃСЃРёРё

// РќР°СЃС‚СЂРѕР№РєРё С‚РѕСЂРіРѕРІС‹С… СЃРµСЃСЃРёР№
input string __session_settings__ = "РќР°СЃС‚СЂРѕР№РєРё РґР»СЏ С‚РѕСЂРіРѕРІС‹С… СЃРµСЃСЃРёР№";
input bool   UseSessionProfiles = true;  // РСЃРїРѕР»СЊР·РѕРІР°С‚СЊ РїСЂРѕС„РёР»Рё РЅР°СЃС‚СЂРѕРµРє РґР»СЏ СЂР°Р·РЅС‹С… СЃРµСЃСЃРёР№

// РќР°СЃС‚СЂРѕР№РєРё РђР·РёР°С‚СЃРєРѕР№ СЃРµСЃСЃРёРё
input double AsianSessionLotMult = 0.8;  // РњРЅРѕР¶РёС‚РµР»СЊ Р»РѕС‚Р° РґР»СЏ РђР·РёР°С‚СЃРєРѕР№ СЃРµСЃСЃРёРё
input double AsianSessionGridMult = 0.8; // РњРЅРѕР¶РёС‚РµР»СЊ С€Р°РіР° СЃРµС‚РєРё РґР»СЏ РђР·РёР°С‚СЃРєРѕР№ СЃРµСЃСЃРёРё
input double AsianSessionTPMult = 0.7;   // РњРЅРѕР¶РёС‚РµР»СЊ TP РґР»СЏ РђР·РёР°С‚СЃРєРѕР№ СЃРµСЃСЃРёРё
input double AsianSessionSLMult = 0.8;   // РњРЅРѕР¶РёС‚РµР»СЊ SL РґР»СЏ РђР·РёР°С‚СЃРєРѕР№ СЃРµСЃСЃРёРё
input int    AsianSessionTrailAct = 20;  // РђРєС‚РёРІР°С†РёСЏ С‚СЂРµР№Р»РёРЅРіР° РґР»СЏ РђР·РёР°С‚СЃРєРѕР№ СЃРµСЃСЃРёРё
input double AsianSessionAvgMult = 1.3;  // РњРЅРѕР¶РёС‚РµР»СЊ СѓСЃСЂРµРґРЅРµРЅРёСЏ РґР»СЏ РђР·РёР°С‚СЃРєРѕР№ СЃРµСЃСЃРёРё

// РќР°СЃС‚СЂРѕР№РєРё Р•РІСЂРѕРїРµР№СЃРєРѕР№ СЃРµСЃСЃРёРё
input double EuropeanSessionLotMult = 1.0;  // РњРЅРѕР¶РёС‚РµР»СЊ Р»РѕС‚Р° РґР»СЏ Р•РІСЂРѕРїРµР№СЃРєРѕР№ СЃРµСЃСЃРёРё
input double EuropeanSessionGridMult = 1.0; // РњРЅРѕР¶РёС‚РµР»СЊ С€Р°РіР° СЃРµС‚РєРё РґР»СЏ Р•РІСЂРѕРїРµР№СЃРєРѕР№ СЃРµСЃСЃРёРё
input double EuropeanSessionTPMult = 1.0;   // РњРЅРѕР¶РёС‚РµР»СЊ TP РґР»СЏ Р•РІСЂРѕРїРµР№СЃРєРѕР№ СЃРµСЃСЃРёРё
input double EuropeanSessionSLMult = 1.0;   // РњРЅРѕР¶РёС‚РµР»СЊ SL РґР»СЏ Р•РІСЂРѕРїРµР№СЃРєРѕР№ СЃРµСЃСЃРёРё
input int    EuropeanSessionTrailAct = 30;  // РђРєС‚РёРІР°С†РёСЏ С‚СЂРµР№Р»РёРЅРіР° РґР»СЏ Р•РІСЂРѕРїРµР№СЃРєРѕР№ СЃРµСЃСЃРёРё
input double EuropeanSessionAvgMult = 1.25; // РњРЅРѕР¶РёС‚РµР»СЊ СѓСЃСЂРµРґРЅРµРЅРёСЏ РґР»СЏ Р•РІСЂРѕРїРµР№СЃРєРѕР№ СЃРµСЃСЃРёРё

// РќР°СЃС‚СЂРѕР№РєРё РђРјРµСЂРёРєР°РЅСЃРєРѕР№ СЃРµСЃСЃРёРё
input double AmericanSessionLotMult = 1.2;  // РњРЅРѕР¶РёС‚РµР»СЊ Р»РѕС‚Р° РґР»СЏ РђРјРµСЂРёРєР°РЅСЃРєРѕР№ СЃРµСЃСЃРёРё
input double AmericanSessionGridMult = 1.2; // РњРЅРѕР¶РёС‚РµР»СЊ С€Р°РіР° СЃРµС‚РєРё РґР»СЏ РђРјРµСЂРёРєР°РЅСЃРєРѕР№ СЃРµСЃСЃРёРё
input double AmericanSessionTPMult = 1.3;   // РњРЅРѕР¶РёС‚РµР»СЊ TP РґР»СЏ РђРјРµСЂРёРєР°РЅСЃРєРѕР№ СЃРµСЃСЃРёРё
input double AmericanSessionSLMult = 1.2;   // РњРЅРѕР¶РёС‚РµР»СЊ SL РґР»СЏ РђРјРµСЂРёРєР°РЅСЃРєРѕР№ СЃРµСЃСЃРёРё
input int    AmericanSessionTrailAct = 40;  // РђРєС‚РёРІР°С†РёСЏ С‚СЂРµР№Р»РёРЅРіР° РґР»СЏ РђРјРµСЂРёРєР°РЅСЃРєРѕР№ СЃРµСЃСЃРёРё
input double AmericanSessionAvgMult = 1.2;  // РњРЅРѕР¶РёС‚РµР»СЊ СѓСЃСЂРµРґРЅРµРЅРёСЏ РґР»СЏ РђРјРµСЂРёРєР°РЅСЃРєРѕР№ СЃРµСЃСЃРёРё

// РЎС‚Р°СЂС‹Рµ РјРЅРѕР¶РёС‚РµР»Рё (РґР»СЏ СЃРѕРІРјРµСЃС‚РёРјРѕСЃС‚Рё)
double AsianSessionMultiplier = 0.8; // РњРЅРѕР¶РёС‚РµР»СЊ РґР»СЏ РђР·РёР°С‚СЃРєРѕР№ СЃРµСЃСЃРёРё
double EuropeanSessionMultiplier = 1.0; // РњРЅРѕР¶РёС‚РµР»СЊ РґР»СЏ Р•РІСЂРѕРїРµР№СЃРєРѕР№ СЃРµСЃСЃРёРё
double AmericanSessionMultiplier = 1.2; // РњРЅРѕР¶РёС‚РµР»СЊ РґР»СЏ РђРјРµСЂРёРєР°РЅСЃРєРѕР№ СЃРµСЃСЃРёРё
input bool   AutoAdjustForSymbol = true; // РђРІС‚РѕРїР°СЂР°РјРµС‚СЂРѕРІ РґР»СЏ СЂР°Р·РЅС‹С… РёРЅСЃС‚СЂСѓРјРµРЅС‚РѕРІ
double ForexMajorMultiplier = 1.0; // РњРЅРѕР¶РёС‚РµР»СЊ РґР»СЏ РѕСЃРЅРѕРІРЅС‹С… РІР°Р»СЋС‚РЅС‹С… РїР°СЂ
double ForexCrossMultiplier = 1.2; // РњРЅРѕР¶РёС‚РµР»СЊ РґР»СЏ РєСЂРѕСЃСЃ-РІР°Р»СЋС‚РЅС‹С… РїР°СЂ
double CommodityMultiplier = 1.5;  // РњРЅРѕР¶РёС‚РµР»СЊ РґР»СЏ СЃС‹СЂСЊРµРІС‹С… РёРЅСЃС‚СЂСѓРјРµРЅС‚РѕРІ
double IndexMultiplier = 0.8;      // РњРЅРѕР¶РёС‚РµР»СЊ РґР»СЏ РёРЅРґРµРєСЃРѕРІ

// РќР°СЃС‚СЂРѕР№РєРё СЂР°Р·РјРµСЂР° Р»РѕС‚Р°
input string __l__ = "РќР°СЃС‚СЂРѕР№РєР° СЂР°Р·РјРµСЂР° Р»РѕС‚Р° (СЂРёСЃРє)";
input bool   LotConst_or_not = false;  // РСЃРїРѕР»СЊР·РѕРІР°С‚СЊ РїРѕСЃС‚РѕСЏРЅРЅС‹Р№ Р»РѕС‚
input double Lot = 0.01;               // Р Р°Р·РјРµСЂ Р»РѕС‚Р° [0.01,0.1,0.01]
input double RiskPercent = 1.0;        // РџСЂРѕС†РµРЅС‚ СЂРёСЃРєР° [0.5,5.0,0.5]
input double LotMultiplicator = 1.25;  // РњРЅРѕР¶РёС‚РµР»СЊ Р»РѕС‚Р° [1.1,1.5,0.05]

// РќР°СЃС‚СЂРѕР№РєРё С‚РѕСЂРіРѕРІС‹С… СЃР»РѕРµРІ
input string __s__ = "РќР°СЃС‚СЂРѕР№РєР° С‚РѕСЂРіРѕРІС‹С… СЃР»РѕРµРІ";
input int    Work_Sloy_mode = 1;     // Р РµР¶РёРј СЂР°Р±РѕС‚С‹ СЃР»РѕРµРІ (0-1)
input int    TorgSloy = 3;           // РљРѕР»РёС‡РµСЃС‚РІРѕ С‚РѕСЂРіРѕРІС‹С… СЃР»РѕРµРІ (1-3)
input bool   D_D_LOT_auto_calc = true; // РђРІС‚РѕРјР°С‚РёС‡РµСЃРєРёР№ СЂР°СЃС‡РµС‚ Р»РѕС‚Р° РґР»СЏ Р”Р”
input double D_D_Lot = 0.5;          // Р Р°Р·РјРµСЂ Р»РѕС‚Р° РґР»СЏ Р”Р” [0.1,1.0,0.1]
input int    N_enable_Sloy = 7;      // РќР°С‡Р°Р»СЊРЅРѕРµ РєРѕР»РёС‡РµСЃС‚РІРѕ СЃР»РѕРµРІ [3,10,1]

// РќР°СЃС‚СЂРѕР№РєРё С‚РѕСЂРіРѕРІРѕР№ СЃРµС‚РєРё
input string _____ = "РќР°СЃС‚СЂРѕР№РєР° С‚РѕСЂРіРѕРІРѕР№ СЃРµС‚РєРё (РїСѓРЅРєС‚РѕРІ)";
input int    hSETKY = 10;            // РЁР°Рі СЃРµС‚РєРё [5,50,5]
input int    Uvel_hSETKY = 1;        // Р РµР¶РёРј СѓРІРµР»РёС‡РµРЅРёСЏ С€Р°РіР° СЃРµС‚РєРё (0-2)
input int    ShagUvel_hSETKY = 10;   // РЁР°Рі СѓРІРµР»РёС‡РµРЅРёСЏ СЃРµС‚РєРё [5,30,5]

// РќР°СЃС‚СЂРѕР№РєРё 3-РіРѕ СЃР»РѕСЏ
input bool   mode_enable_3_sloy = false;  // Р’РєР»СЋС‡РµРЅРёРµ 3-РіРѕ СЃР»РѕСЏ
input double h_D_T = 500.0;              // Р Р°СЃСЃС‚РѕСЏРЅРёРµ РјРµР¶РґСѓ СЌРєСЃС‚СЂРµРјСѓРјР°РјРё РґР»СЏ 3-РіРѕ СЃР»РѕСЏ
input double pr_h_3_sl = 25.0;           // РџСЂРѕС†РµРЅС‚ РІС‹СЃРѕС‚С‹ 3-РіРѕ СЃР»РѕСЏ

// РќР°СЃС‚СЂРѕР№РєРё Р·Р°С‰РёС‚С‹ Рё С‚СЂРµР№Р»РёРЅРіР°
input int    slippage = 10;           // РџСЂРѕСЃРєР°Р»СЊР·С‹РІР°РЅРёРµ
input int    ProtectionTP = 7;        // Р—Р°С‰РёС‚РЅС‹Р№ С‚РµР№Рє-РїСЂРѕС„РёС‚ [5,50,5]
input int    TrallingStop = 7;        // РўСЂРµР№Р»РёРЅРі-СЃС‚РѕРї [3,30,1]
input int    TrallTP = 15;            // РўСЂРµР№Р»РёРЅРі С‚РµР№Рє-РїСЂРѕС„РёС‚Р° [5,50,5]

// РќР°СЃС‚СЂРѕР№РєРё РјР°СЂР¶Рё
input string ______________ = "РќР°СЃС‚Рѕ.СЃРІ.РјР°СЂР¶.РѕС‚.РЅР°С‡Р°Р»";
input double Min_Proc_Sv_Sr = 80.0;     // РњРёРЅРёРјР°Р»СЊРЅС‹Р№ РїСЂРѕС†РµРЅС‚ СЃРІРѕР±РѕРґРЅРѕР№ РјР°СЂР¶Рё (%)

// РРґРµРЅС‚РёС„РёРєР°С†РёСЏ Рё РІРёР·СѓР°Р»РёР·Р°С†РёСЏ
input int    Magic = 1230;            // РњР°РіРёС‡РµСЃРєРёР№ РЅРѕРјРµСЂ СЃРѕРІРµС‚РЅРёРєР°
input bool   ShowTableOnTesting = true;  // РџРѕРєР°Р·С‹РІР°С‚СЊ С‚Р°Р±Р»РёС†Сѓ РїСЂРё С‚РµСЃС‚РёСЂРѕРІР°РЅРёРё
input int    Text_Syze = 14;          // Р Р°Р·РјРµСЂ С‚РµРєСЃС‚Р°
input color  ColorTableOnTesting = Yellow;  // Р¦РІРµС‚ С‚Р°Р±Р»РёС†С‹ РїСЂРё С‚РµСЃС‚РёСЂРѕРІР°РЅРёРё
input color  ColorLogotipName = Maroon;     // Р¦РІРµС‚ РЅР°Р·РІР°РЅРёСЏ Р»РѕРіРѕС‚РёРїР°
input color  ColorLogotipSite = Gray;       // Р¦РІРµС‚ СЃР°Р№С‚Р° Р»РѕРіРѕС‚РёРїР°
input color  color_Trade_area_of_3rd_layer = DarkSlateGray; // Р¦РІРµС‚ С‚РѕСЂРіРѕРІРѕР№ Р·РѕРЅС‹ 3-РіРѕ СЃР»РѕСЏ

// РќР°СЃС‚СЂРѕР№РєРё РѕС‚РѕР±СЂР°Р¶РµРЅРёСЏ РёРЅС„РѕСЂРјР°С†РёРё
input string __info__ = "РќР°СЃС‚СЂРѕР№РєРё РѕС‚РѕР±СЂР°Р¶РµРЅРёСЏ РёРЅС„РѕСЂРјР°С†РёРё";
input bool   ShowAdvancedInfo = true;     // РџРѕРєР°Р·С‹РІР°С‚СЊ СЂР°СЃС€РёСЂРµРЅРЅСѓСЋ РёРЅС„РѕСЂРјР°С†РёСЋ Рѕ СЂС‹РЅРєРµ
input bool   ShowVolumeZones = true;      // РџРѕРєР°Р·С‹РІР°С‚СЊ Р·РѕРЅС‹ РѕР±СЉРµРјРѕРІ
input bool   ShowKeyLevels = true;        // РџРѕРєР°Р·С‹РІР°С‚СЊ РєР»СЋС‡РµРІС‹Рµ СѓСЂРѕРІРЅРё
input int    MaxVolumeZonesToShow = 20;   // РњР°РєСЃРёРјР°Р»СЊРЅРѕРµ РєРѕР»РёС‡РµСЃС‚РІРѕ Р·РѕРЅ РѕР±СЉРµРјР° РґР»СЏ РѕС‚РѕР±СЂР°Р¶РµРЅРёСЏ

// РќР°СЃС‚СЂРѕР№РєРё РѕРїС‚РёРјРёР·Р°С†РёРё РґР»СЏ С‚РµСЃС‚РёСЂРѕРІР°РЅРёСЏ
input string __optimization__ = "РќР°СЃС‚СЂРѕР№РєРё РѕРїС‚РёРјРёР·Р°С†РёРё РґР»СЏ С‚РµСЃС‚РёСЂРѕРІР°РЅРёСЏ";
input bool   OptimizeForTesting = true;   // РћРїС‚РёРјРёР·РёСЂРѕРІР°С‚СЊ РґР»СЏ С‚РµСЃС‚РёСЂРѕРІР°РЅРёСЏ (СѓСЃРєРѕСЂРµРЅРёРµ)
input int    TimerInterval = 60;          // РРЅС‚РµСЂРІР°Р» С‚Р°Р№РјРµСЂР° РІ СЃРµРєСѓРЅРґР°С… (1-60) [5,60,5]
input int    AnalysisUpdateFrequency = 20; // Р§Р°СЃС‚РѕС‚Р° РѕР±РЅРѕРІР»РµРЅРёСЏ Р°РЅР°Р»РёР·Р° (РєР°Р¶РґС‹Рµ N С‚РёРєРѕРІ) [5,50,5]

// Р“Р»РѕР±Р°Р»СЊРЅС‹Рµ РїРµСЂРµРјРµРЅРЅС‹Рµ
string   m_symbol;                       // РЎРёРјРІРѕР» (РІР°Р»СЋС‚РЅР°СЏ РїР°СЂР°)
string   m_prefix;                       // РџСЂРµС„РёРєСЃ РґР»СЏ РѕР±СЉРµРєС‚РѕРІ РЅР° РіСЂР°С„РёРєРµ
int      m_magic;                        // РњР°РіРёС‡РµСЃРєРёР№ РЅРѕРјРµСЂ СЃРѕРІРµС‚РЅРёРєР°
bool     m_exit_mode;                    // Р РµР¶РёРј РІС‹С…РѕРґР° РёР· РїРѕР·РёС†РёР№
double   m_point;                        // Р Р°Р·РјРµСЂ РїСѓРЅРєС‚Р°
int      m_point_digits;                 // РњРЅРѕР¶РёС‚РµР»СЊ РґР»СЏ РїСѓРЅРєС‚РѕРІ (10 РґР»СЏ 3-С… Р·РЅР°РєРѕРІ, 1 РґР»СЏ 4-С… Рё 5-С‚Рё Р·РЅР°РєРѕРІ)
int      m_digits;                       // РљРѕР»РёС‡РµСЃС‚РІРѕ Р·РЅР°РєРѕРІ РїРѕСЃР»Рµ Р·Р°РїСЏС‚РѕР№

// РџР°СЂР°РјРµС‚СЂС‹ РІРёР·СѓР°Р»РёР·Р°С†РёРё
bool     m_show_advanced_info;           // РћС‚РѕР±СЂР°Р¶Р°С‚СЊ СЂР°СЃС€РёСЂРµРЅРЅСѓСЋ РёРЅС„РѕСЂРјР°С†РёСЋ Рѕ СЂС‹РЅРєРµ
VolumeZone m_volume_zones[];             // РњР°СЃСЃРёРІ Р·РѕРЅ РѕР±СЉРµРјР°

// РџРµСЂРµРјРµРЅРЅС‹Рµ РґР»СЏ РѕРїС‚РёРјРёР·Р°С†РёРё
bool     m_optimize_for_testing;         // РћРїС‚РёРјРёР·РёСЂРѕРІР°С‚СЊ РґР»СЏ С‚РµСЃС‚РёСЂРѕРІР°РЅРёСЏ
int      m_timer_interval;               // РРЅС‚РµСЂРІР°Р» С‚Р°Р№РјРµСЂР°
int      m_analysis_update_frequency;    // Р§Р°СЃС‚РѕС‚Р° РѕР±РЅРѕРІР»РµРЅРёСЏ Р°РЅР°Р»РёР·Р°
int      m_tick_counter;                 // РЎС‡РµС‚С‡РёРє С‚РёРєРѕРІ

// РџР°СЂР°РјРµС‚СЂС‹ РґР»СЏ СЂР°Р±РѕС‚С‹ СЃ Р»РѕС‚РѕРј
bool     m_lot_const;                    // РСЃРїРѕР»СЊР·РѕРІР°С‚СЊ РїРѕСЃС‚РѕСЏРЅРЅС‹Р№ Р»РѕС‚
double   m_lot;                          // Р Р°Р·РјРµСЂ Р»РѕС‚Р°
double   m_risk_percent;                 // РџСЂРѕС†РµРЅС‚ СЂРёСЃРєР°
double   m_lot_multiplicator;            // РњРЅРѕР¶РёС‚РµР»СЊ Р»РѕС‚Р°
double   m_min_lot;                      // РњРёРЅРёРјР°Р»СЊРЅС‹Р№ СЂР°Р·РјРµСЂ Р»РѕС‚Р°
double   m_max_lot;                      // РњР°РєСЃРёРјР°Р»СЊРЅС‹Р№ СЂР°Р·РјРµСЂ Р»РѕС‚Р°

// РџР°СЂР°РјРµС‚СЂС‹ Р°РґР°РїС‚РёРІРЅРѕРіРѕ MM
bool     m_use_adaptive_mm;              // РСЃРїРѕР»СЊР·РѕРІР°С‚СЊ Р°РґР°РїС‚РёРІРЅС‹Р№ MM
double   m_signal_strength_multiplier;   // РњРЅРѕР¶РёС‚РµР»СЊ СЃРёР»С‹ СЃРёРіРЅР°Р»Р° РґР»СЏ Р»РѕС‚Р°
double   m_trend_against_reduction;      // РљРѕСЌС„С„РёС†РёРµРЅС‚ СѓРјРµРЅСЊС€РµРЅРёСЏ Р»РѕС‚Р° РїСЂРѕС‚РёРІ С‚СЂРµРЅРґР°
double   m_min_probability_for_entry;    // РњРёРЅРёРјР°Р»СЊРЅР°СЏ РІРµСЂРѕСЏС‚РЅРѕСЃС‚СЊ РґР»СЏ РІС…РѕРґР°
double   m_probability_lot_boost;        // РџСЂРёСЂРѕСЃС‚ Р»РѕС‚Р° Р·Р° РєР°Р¶РґС‹Рµ 10% РІРµСЂРѕСЏС‚РЅРѕСЃС‚Рё
int      m_max_signal_lookback;          // РљРѕР»РёС‡РµСЃС‚РІРѕ Р±Р°СЂРѕРІ РґР»СЏ РїРѕРёСЃРєР° СЃРёРіРЅР°Р»Р°

// РџР°СЂР°РјРµС‚СЂС‹ СЃР»РѕРµРІ
int      m_work_sloy_mode;               // Р РµР¶РёРј СЂР°Р±РѕС‚С‹ СЃР»РѕРµРІ (0-1)
int      m_torg_sloy;                    // РљРѕР»РёС‡РµСЃС‚РІРѕ С‚РѕСЂРіРѕРІС‹С… СЃР»РѕРµРІ (1-3)
bool     m_dd_lot_auto_calc;             // РђРІС‚РѕРјР°С‚РёС‡РµСЃРєРёР№ СЂР°СЃС‡РµС‚ Р»РѕС‚Р° РґР»СЏ Р”Р”
double   m_dd_lot;                       // Р Р°Р·РјРµСЂ Р»РѕС‚Р° РґР»СЏ Р”Р”
int      m_n_enable_sloy;                // РќР°С‡Р°Р»СЊРЅРѕРµ РєРѕР»РёС‡РµСЃС‚РІРѕ СЃР»РѕРµРІ

// РџР°СЂР°РјРµС‚СЂС‹ С‚РѕСЂРіРѕРІРѕР№ СЃРµС‚РєРё
int      m_hsetky;                       // РЁР°Рі СЃРµС‚РєРё
int      m_uvel_hsetky;                  // Р РµР¶РёРј СѓРІРµР»РёС‡РµРЅРёСЏ С€Р°РіР° СЃРµС‚РєРё (0-2)
int      m_shag_uvel_hsetky;             // РЁР°Рі СѓРІРµР»РёС‡РµРЅРёСЏ СЃРµС‚РєРё

// РџР°СЂР°РјРµС‚СЂС‹ 3-РіРѕ СЃР»РѕСЏ
bool     m_mode_enable_3_sloy;           // Р’РєР»СЋС‡РµРЅРёРµ 3-РіРѕ СЃР»РѕСЏ
double   m_h_d_t;                        // Р Р°СЃСЃС‚РѕСЏРЅРёРµ РјРµР¶РґСѓ СЌРєСЃС‚СЂРµРјСѓРјР°РјРё РґР»СЏ 3-РіРѕ СЃР»РѕСЏ
double   m_pr_h_3_sl;                    // РџСЂРѕС†РµРЅС‚ РІС‹СЃРѕС‚С‹ 3-РіРѕ СЃР»РѕСЏ

// РџР°СЂР°РјРµС‚СЂС‹ Р·Р°С‰РёС‚С‹ Рё С‚РѕСЂРіРѕРІР»Рё
int      m_slippage;                     // РџСЂРѕСЃРєР°Р»СЊР·С‹РІР°РЅРёРµ
int      m_protection_tp;                // Р—Р°С‰РёС‚РЅС‹Р№ С‚РµР№Рє-РїСЂРѕС„РёС‚
int      m_tralling_stop;                // РўСЂРµР№Р»РёРЅРі-СЃС‚РѕРї
int      m_trall_tp;                     // РўСЂРµР№Р»РёРЅРі С‚РµР№Рє-РїСЂРѕС„РёС‚Р°
double   m_min_proc_sv_sr;               // РњРёРЅРёРјР°Р»СЊРЅС‹Р№ РїСЂРѕС†РµРЅС‚ СЃРІРѕР±РѕРґРЅРѕР№ РјР°СЂР¶Рё (%)
double   m_free_margin_limit;            // РџСЂРµРґРµР» СЃРІРѕР±РѕРґРЅРѕР№ РјР°СЂР¶Рё

// Р’РёР·СѓР°Р»СЊРЅС‹Рµ РїР°СЂР°РјРµС‚СЂС‹
bool     m_show_table_on_testing;        // РџРѕРєР°Р·С‹РІР°С‚СЊ С‚Р°Р±Р»РёС†Сѓ РїСЂРё С‚РµСЃС‚РёСЂРѕРІР°РЅРёРё
int      m_text_size;                    // Р Р°Р·РјРµСЂ С‚РµРєСЃС‚Р°
color    m_color_table_on_testing;       // Р¦РІРµС‚ С‚Р°Р±Р»РёС†С‹ РїСЂРё С‚РµСЃС‚РёСЂРѕРІР°РЅРёРё
color    m_color_logotip_name;           // Р¦РІРµС‚ РЅР°Р·РІР°РЅРёСЏ Р»РѕРіРѕС‚РёРїР°
color    m_color_logotip_site;           // Р¦РІРµС‚ СЃР°Р№С‚Р° Р»РѕРіРѕС‚РёРїР°
color    m_color_trade_area_of_3rd_layer; // Р¦РІРµС‚ С‚РѕСЂРіРѕРІРѕР№ Р·РѕРЅС‹ 3-РіРѕ СЃР»РѕСЏ

// РџРµСЂРµРјРµРЅРЅС‹Рµ РґР»СЏ С…СЂР°РЅРµРЅРёСЏ РїРѕР·РёС†РёР№ Рё СѓСЂРѕРІРЅРµР№
int      m_active_layers[3];             // РђРєС‚РёРІРЅС‹Рµ СЃР»РѕРё (0 - РѕС‚РєР»СЋС‡РµРЅ, 1 - РѕР±С‹С‡РЅС‹Р№, 2 - С‚РѕР»СЊРєРѕ РґР»СЏ РѕС‚РєСЂС‹С‚РёСЏ, 3 - С‚РѕР»СЊРєРѕ РґР»СЏ Р·Р°РєСЂС‹С‚РёСЏ)

// РћР±С‰РµРµ РєРѕР»РёС‡РµСЃС‚РІРѕ РїРѕР·РёС†РёР№
int      m_buy_positions_total;          // РћР±С‰РµРµ РєРѕР»РёС‡РµСЃС‚РІРѕ Buy РїРѕР·РёС†РёР№
int      m_sell_positions_total;         // РћР±С‰РµРµ РєРѕР»РёС‡РµСЃС‚РІРѕ Sell РїРѕР·РёС†РёР№

// РњР°СЃСЃРёРІС‹ РґР»СЏ Buy РїРѕР·РёС†РёР№
int      m_buy_positions_count[3];       // РљРѕР»РёС‡РµСЃС‚РІРѕ Buy РїРѕР·РёС†РёР№ РґР»СЏ РєР°Р¶РґРѕРіРѕ СЃР»РѕСЏ
ulong    m_buy_tickets[3][100];          // РўРёРєРµС‚С‹ Buy РїРѕР·РёС†РёР№
double   m_buy_levels[3][100];           // РЈСЂРѕРІРЅРё Buy РїРѕР·РёС†РёР№
double   m_buy_lots[3][100];             // Р Р°Р·РјРµСЂС‹ Р»РѕС‚РѕРІ Buy РїРѕР·РёС†РёР№
double   m_buy_sl[3][100];               // РЎС‚РѕРї-Р»РѕСЃСЃС‹ Buy РїРѕР·РёС†РёР№
double   m_buy_tp[3][100];              // РўРµР№Рє-РїСЂРѕС„РёС‚С‹ Buy РїРѕР·РёС†РёР№
double   m_buy_lots_total[3];            // РћР±С‰РёРµ РѕР±СЉРµРјС‹ Buy РїРѕР·РёС†РёР№

// РњР°СЃСЃРёРІС‹ РґР»СЏ Sell РїРѕР·РёС†РёР№
int      m_sell_positions_count[3];      // РљРѕР»РёС‡РµСЃС‚РІРѕ Sell РїРѕР·РёС†РёР№ РґР»СЏ РєР°Р¶РґРѕРіРѕ СЃР»РѕСЏ
ulong    m_sell_tickets[3][100];         // РўРёРєРµС‚С‹ Sell РїРѕР·РёС†РёР№
double   m_sell_levels[3][100];          // РЈСЂРѕРІРЅРё Sell РїРѕР·РёС†РёР№
double   m_sell_lots[3][100];            // Р Р°Р·РјРµСЂС‹ Р»РѕС‚РѕРІ Sell РїРѕР·РёС†РёР№
double   m_sell_sl[3][100];              // РЎС‚РѕРї-Р»РѕСЃСЃС‹ Sell РїРѕР·РёС†РёР№
double   m_sell_tp[3][100];              // РўРµР№Рє-РїСЂРѕС„РёС‚С‹ Sell РїРѕР·РёС†РёР№
double   m_sell_lots_total[3];           // РћР±С‰РёРµ РѕР±СЉРµРјС‹ Sell РїРѕР·РёС†РёР№

// РџРµСЂРµРјРµРЅРЅС‹Рµ РґР»СЏ СЂР°Р±РѕС‚С‹ СЃ 3-Рј СЃР»РѕРµРј
double   m_max_price;                    // РњР°РєСЃРёРјР°Р»СЊРЅР°СЏ С†РµРЅР° РґР»СЏ СЂР°СЃС‡РµС‚Р° 3-РіРѕ СЃР»РѕСЏ
double   m_min_price;                    // РњРёРЅРёРјР°Р»СЊРЅР°СЏ С†РµРЅР° РґР»СЏ СЂР°СЃС‡РµС‚Р° 3-РіРѕ СЃР»РѕСЏ
int      m_max_price_index;              // РРЅРґРµРєСЃ Р±Р°СЂР° СЃ РјР°РєСЃРёРјР°Р»СЊРЅРѕР№ С†РµРЅРѕР№
int      m_min_price_index;              // РРЅРґРµРєСЃ Р±Р°СЂР° СЃ РјРёРЅРёРјР°Р»СЊРЅРѕР№ С†РµРЅРѕР№
double   m_trade_high;                   // Р’РµСЂС…РЅСЏСЏ РіСЂР°РЅРёС†Р° С‚РѕСЂРіРѕРІРѕР№ Р·РѕРЅС‹ 3-РіРѕ СЃР»РѕСЏ
double   m_trade_low;                    // РќРёР¶РЅСЏСЏ РіСЂР°РЅРёС†Р° С‚РѕСЂРіРѕРІРѕР№ Р·РѕРЅС‹ 3-РіРѕ СЃР»РѕСЏ
string   m_trade_area_name;              // РРјСЏ РѕР±СЉРµРєС‚Р° С‚РѕСЂРіРѕРІРѕР№ Р·РѕРЅС‹ 3-РіРѕ СЃР»РѕСЏ

// РџСЂРѕС‡РёРµ РїРµСЂРµРјРµРЅРЅС‹Рµ
double   m_mid_price;                    // РЎСЂРµРґРЅСЏСЏ С†РµРЅР°
double   m_high_price;                   // РњР°РєСЃРёРјР°Р»СЊРЅР°СЏ С†РµРЅР°
double   m_low_price;                    // РњРёРЅРёРјР°Р»СЊРЅР°СЏ С†РµРЅР°
double   m_upper_bound;                  // Р’РµСЂС…РЅСЏСЏ РіСЂР°РЅРёС†Р°
double   m_lower_bound;                  // РќРёР¶РЅСЏСЏ РіСЂР°РЅРёС†Р°

// РџРµСЂРµРјРµРЅРЅС‹Рµ РґР»СЏ С„РёР»СЊС‚СЂР° С‚СЂРµРЅРґР°
bool     m_use_trend_filter;             // РСЃРїРѕР»СЊР·РѕРІР°С‚СЊ С„РёР»СЊС‚СЂ С‚СЂРµРЅРґР°
bool     m_trade_only_with_trend;        // РўРѕСЂРіРѕРІР°С‚СЊ С‚РѕР»СЊРєРѕ РїРѕ С‚СЂРµРЅРґСѓ
int      m_trend_detection_method;       // РњРµС‚РѕРґ РѕРїСЂРµРґРµР»РµРЅРёСЏ С‚СЂРµРЅРґР°
int      m_adx_period;                   // РџРµСЂРёРѕРґ ADX
int      m_adx_trend_level;              // РЈСЂРѕРІРµРЅСЊ СЃРёР»С‹ С‚СЂРµРЅРґР° ADX
bool     m_adx_use_di_filter;            // РСЃРїРѕР»СЊР·РѕРІР°С‚СЊ С„РёР»СЊС‚СЂ РїРѕ DI+ Рё DI-
int      m_ma_fast_period;               // РџРµСЂРёРѕРґ Р±С‹СЃС‚СЂРѕР№ MA
int      m_ma_slow_period;               // РџРµСЂРёРѕРґ РјРµРґР»РµРЅРЅРѕР№ MA
int      m_ma_method;                    // РњРµС‚РѕРґ СЂР°СЃС‡РµС‚Р° MA
ENUM_APPLIED_PRICE m_ma_applied_price;   // Р¦РµРЅР° РґР»СЏ MA
int      m_ma_signal_bar;                // Р‘Р°СЂ РґР»СЏ СЃРёРіРЅР°Р»Р°
int      m_trend_mode_setka;             // РЁР°Рі СЃРµС‚РєРё РґР»СЏ С‚СЂРµРЅРґРѕРІРѕРіРѕ СЂС‹РЅРєР°
int      m_flat_mode_setka;              // РЁР°Рі СЃРµС‚РєРё РґР»СЏ С„Р»СЌС‚РѕРІРѕРіРѕ СЂС‹РЅРєР°
double   m_trend_mode_lot_mult;          // РњРЅРѕР¶РёС‚РµР»СЊ Р»РѕС‚Р° РґР»СЏ С‚СЂРµРЅРґРѕРІРѕРіРѕ СЂС‹РЅРєР°
double   m_flat_mode_lot_mult;           // РњРЅРѕР¶РёС‚РµР»СЊ Р»РѕС‚Р° РґР»СЏ С„Р»СЌС‚РѕРІРѕРіРѕ СЂС‹РЅРєР°
int      m_current_market_type;          // РўРµРєСѓС‰РёР№ С‚РёРї СЂС‹РЅРєР° (0-С„Р»СЌС‚, 1-РІРѕСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ, -1-РЅРёСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ)

// РџРµСЂРµРјРµРЅРЅС‹Рµ РґР»СЏ Р°РґР°РїС‚РёРІРЅРѕРіРѕ СЂР°Р·РјРµСЂР° СЃРµС‚РєРё
bool     m_use_adaptive_grid;            // РСЃРїРѕР»СЊР·РѕРІР°С‚СЊ Р°РґР°РїС‚РёРІРЅС‹Р№ СЂР°Р·РјРµСЂ СЃРµС‚РєРё
bool     m_use_atr_for_grid;             // РџСЂРёРІСЏР·Р°С‚СЊ С€Р°Рі СЃРµС‚РєРё Рє РІРѕР»Р°С‚РёР»СЊРЅРѕСЃС‚Рё (ATR)
int      m_atr_period;                   // РџРµСЂРёРѕРґ ATR
double   m_atr_multiplier;               // РњРЅРѕР¶РёС‚РµР»СЊ ATR РґР»СЏ С€Р°РіР° СЃРµС‚РєРё
bool     m_session_based_settings;       // РР·РјРµРЅСЏС‚СЊ РїР°СЂР°РјРµС‚СЂС‹ РІ Р·Р°РІРёСЃРёРјРѕСЃС‚Рё РѕС‚ СЃРµСЃСЃРёРё
bool     m_use_session_profiles;         // РСЃРїРѕР»СЊР·РѕРІР°С‚СЊ РїСЂРѕС„РёР»Рё РЅР°СЃС‚СЂРѕРµРє РґР»СЏ СЂР°Р·РЅС‹С… СЃРµСЃСЃРёР№
double   m_asian_session_multiplier;     // РњРЅРѕР¶РёС‚РµР»СЊ РґР»СЏ РђР·РёР°С‚СЃРєРѕР№ СЃРµСЃСЃРёРё

// РЎС‚СЂСѓРєС‚СѓСЂР° РґР»СЏ РїСЂРѕС„РёР»РµР№ СЃРµСЃСЃРёР№
struct SessionProfile
{
   string   name;                 // РќР°Р·РІР°РЅРёРµ СЃРµСЃСЃРёРё
   int      start_hour;           // Р§Р°СЃ РЅР°С‡Р°Р»Р° СЃРµСЃСЃРёРё
   int      end_hour;             // Р§Р°СЃ РѕРєРѕРЅС‡Р°РЅРёСЏ СЃРµСЃСЃРёРё
   double   volatility_factor;    // Р¤Р°РєС‚РѕСЂ РІРѕР»Р°С‚РёР»СЊРЅРѕСЃС‚Рё
   double   grid_step_modifier;   // РњРѕРґРёС„РёРєР°С‚РѕСЂ С€Р°РіР° СЃРµС‚РєРё
   double   lot_modifier;         // РњРѕРґРёС„РёРєР°С‚РѕСЂ Р»РѕС‚Р°
   double   tp_modifier;          // РњРѕРґРёС„РёРєР°С‚РѕСЂ С‚РµР№Рє-РїСЂРѕС„РёС‚Р°
   double   sl_modifier;          // РњРѕРґРёС„РёРєР°С‚РѕСЂ СЃС‚РѕРї-Р»РѕСЃСЃР°
   double   lot_multiplier;           // РњРЅРѕР¶РёС‚РµР»СЊ СЂР°Р·РјРµСЂР° Р»РѕС‚Р° РґР»СЏ СЃРµСЃСЃРёРё
   double   grid_step_multiplier;     // РњРЅРѕР¶РёС‚РµР»СЊ С€Р°РіР° СЃРµС‚РєРё РґР»СЏ СЃРµСЃСЃРёРё
   double   tp_multiplier;            // РњРЅРѕР¶РёС‚РµР»СЊ РґР»СЏ С‚РµР№Рє-РїСЂРѕС„РёС‚Р°
   double   sl_multiplier;            // РњРЅРѕР¶РёС‚РµР»СЊ РґР»СЏ СЃС‚РѕРї-Р»РѕСЃСЃР°
   int      trailing_activation;      // РђРєС‚РёРІР°С†РёСЏ С‚СЂРµР№Р»РёРЅРіР° (РІ РїСѓРЅРєС‚Р°С…)
   double   averaging_multiplier;     // РњРЅРѕР¶РёС‚РµР»СЊ РґР»СЏ СѓСЃСЂРµРґРЅРµРЅРёСЏ
   bool     use_additional_filters;   // РСЃРїРѕР»СЊР·РѕРІР°С‚СЊ РґРѕРїРѕР»РЅРёС‚РµР»СЊРЅС‹Рµ С„РёР»СЊС‚СЂС‹
   string   description;              // РћРїРёСЃР°РЅРёРµ РЅР°СЃС‚СЂРѕРµРє СЃРµСЃСЃРёРё
};

// РњР°СЃСЃРёРІС‹ РґР»СЏ С‚РѕС‡РµРє СѓСЃСЂРµРґРЅРµРЅРёСЏ Рё РїСЂРѕС„РёР»РµР№ СЃРµСЃСЃРёР№
SessionProfile m_session_profiles[3];    // РџСЂРѕС„РёР»Рё С‚РѕСЂРіРѕРІС… СЃРµСЃСЃРёР№
double   m_buy_averaging_points[5];      // РўРѕС‡РєРё СѓСЃСЂРµРґРЅРµРЅРёСЏ РґР»СЏ РїРѕРєСѓРїРѕРє
double   m_sell_averaging_points[5];     // РўРѕС‡РєРё СѓСЃСЂРµРґРЅРµРЅРёСЏ РґР»СЏ РїСЂРѕРґР°Р¶

// РџСЂРѕС„РёР»Рё РЅР°СЃС‚СЂРѕРµРє РґР»СЏ СЃРµСЃСЃРёР№
SessionSettings m_asian_profile;         // РќР°СЃС‚СЂРѕР№РєРё РґР»СЏ РђР·РёР°С‚СЃРєРѕР№ СЃРµСЃСЃРёРё
SessionSettings m_european_profile;      // РќР°СЃС‚СЂРѕР№РєРё РґР»СЏ Р•РІСЂРѕРїРµР№СЃРєРѕР№ СЃРµСЃСЃРёРё
SessionSettings m_american_profile;      // РќР°СЃС‚СЂРѕР№РєРё РґР»СЏ РђРјРµСЂРёРєР°РЅСЃРєРѕР№ СЃРµСЃСЃРёРё
SessionSettings m_current_profile;       // РўРµРєСѓС‰РёРµ Р°РєС‚РёРІРЅС‹Рµ РЅР°СЃС‚СЂРѕР№РєРё СЃРµСЃСЃРёРё
double   m_european_session_multiplier;  // РњРЅРѕР¶РёС‚РµР»СЊ РґР»СЏ Р•РІСЂРѕРїРµР№СЃРєРѕР№ СЃРµСЃСЃРёРё
double   m_american_session_multiplier;  // РњРЅРѕР¶РёС‚РµР»СЊ РґР»СЏ РђРјРµСЂРёРєР°РЅСЃРєРѕР№ СЃРµСЃСЃРёРё
bool     m_auto_adjust_for_symbol;       // РђРІС‚РѕРїРѕРґСЃС‚СЂРѕР№РєР° РїР°СЂР°РјРµС‚СЂРѕРІ РґР»СЏ СЂР°Р·РЅС‹С… РёРЅСЃС‚СЂСѓРјРµРЅС‚РѕРІ
double   m_forex_major_multiplier;       // РњРЅРѕР¶РёС‚РµР»СЊ РґР»СЏ РѕСЃРЅРѕРІРЅС‹С… РІР°Р»СЋС‚РЅС‹С… РїР°СЂ
double   m_forex_cross_multiplier;       // РњРЅРѕР¶РёС‚РµР»СЊ РґР»СЏ РєСЂРѕСЃСЃ-РІР°Р»СЋС‚РЅС‹С… РїР°СЂ
double   m_commodity_multiplier;         // РњРЅРѕР¶РёС‚РµР»СЊ РґР»СЏ СЃС‹СЂСЊРµРІС‹С… РёРЅСЃС‚СЂСѓРјРµРЅС‚РѕРІ
double   m_index_multiplier;             // РњРЅРѕР¶РёС‚РµР»СЊ РґР»СЏ РёРЅРґРµРєСЃРѕРІ
double   m_current_grid_step;            // РўРµРєСѓС‰РёР№ Р°РґР°РїС‚РёРІРЅС‹Р№ С€Р°Рі СЃРµС‚РєРё
double   m_current_lot_multiplier;       // РўРµРєСѓС‰РёР№ РјРЅРѕР¶РёС‚РµР»СЊ Р»РѕС‚Р°
double   m_current_grid_step_multiplier; // РўРµРєСѓС‰РёР№ РјРЅРѕР¶РёС‚РµР»СЊ С€Р°РіР° СЃРµС‚РєРё
double   m_current_tp_multiplier;        // РўРµРєСѓС‰РёР№ РјРЅРѕР¶РёС‚РµР»СЊ TP
double   m_current_sl_multiplier;        // РўРµРєСѓС‰РёР№ РјРЅРѕР¶РёС‚РµР»СЊ SL
int      m_current_trailing_activation;  // РўРµРєСѓС‰РµРµ Р·РЅР°С‡РµРЅРёРµ Р°РєС‚РёРІР°С†РёРё С‚СЂРµР№Р»РёРЅРіР°
double   m_current_averaging_multiplier; // РўРµРєСѓС‰РёР№ РјРЅРѕР¶РёС‚РµР»СЊ СѓСЃСЂРµРґРЅРµРЅРёСЏ
int      m_atr_handle;                   // РҐРµРЅРґР» РёРЅРґРёРєР°С‚РѕСЂР° ATR
ENUM_MARKET_SESSION m_current_session;   // РўРµРєСѓС‰Р°СЏ С‚РѕСЂРіРѕРІР°СЏ СЃРµСЃСЃРёСЏ
ENUM_SYMBOL_TYPE m_symbol_type;          // РўРёРї С‚РѕСЂРіРѕРІРѕРіРѕ РёРЅСЃС‚СЂСѓРјРµРЅС‚Р°

// РџРµСЂРµРјРµРЅРЅС‹Рµ РґР»СЏ СѓРјРЅРѕРіРѕ РІС‹С…РѕРґР° РёР· РїСЂРѕСЃР°РґРєРё
bool     m_use_smart_exit;               // РСЃРїРѕР»СЊР·РѕРІР°С‚СЊ СѓРјРЅС‹Р№ РІС‹С…РѕРґ РёР· РїСЂРѕСЃР°РґРєРё
ENUM_RECOVERY_METHOD m_recovery_method;  // РњРµС‚РѕРґ РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёСЏ РёР· РїСЂРѕСЃР°РґРєРё
double   m_drawdown_max_percent;         // РњР°РєСЃРёРјР°Р»СЊРЅР°СЏ РїСЂРѕСЃР°РґРєР° РІ РїСЂРѕС†РµРЅС‚Р°С…
int      m_drawdown_max_time;            // РњР°РєСЃРёРјР°Р»СЊРЅРѕРµ РІСЂРµРјСЏ РІ РїСЂРѕСЃР°РґРєРµ (С‡Р°СЃС‹)
double   m_target_profit_percent;        // Р¦РµР»РµРІР°СЏ РїСЂРёР±С‹Р»СЊ (% РѕС‚ СѓР±С‹С‚РєР°)
int      m_max_recovery_positions;       // РњР°РєСЃРёРјР°Р»СЊРЅРѕРµ РєРѕР»РёС‡РµСЃС‚РІРѕ РїРѕР·РёС†РёР№ РґР»СЏ РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёСЏ
double   m_smart_lot_multiplier;         // РњРЅРѕР¶РёС‚РµР»СЊ Р»РѕС‚Р° РїСЂРё СѓРјРЅРѕРј СѓСЃСЂРµРґРЅРµРЅРёРё
int      m_partial_close_percent;        // РџСЂРѕС†РµРЅС‚ С‡Р°СЃС‚РёС‡РЅРѕРіРѕ Р·Р°РєСЂС‹С‚РёСЏ РЅР° РѕС‚РєР°С‚Р°С…
bool     m_use_market_structure;         // РЈС‡РёС‚С‹РІР°С‚СЊ СЃС‚СЂСѓРєС‚СѓСЂСѓ СЂС‹РЅРєР°
int      m_min_profit_to_close;          // РњРёРЅРёРјР°Р»СЊРЅР°СЏ РїСЂРёР±С‹Р»СЊ РґР»СЏ Р·Р°РєСЂС‹С‚РёСЏ (РїСѓРЅРєС‚С‹)
double   m_trailing_activation_level;    // РЈСЂРѕРІРµРЅСЊ Р°РєС‚РёРІР°С†РёРё С‚СЂРµР№Р»РёРЅРіР°
int      m_drawdown_pause_after_exit;    // РџР°СѓР·Р° РїРѕСЃР»Рµ РІС‹С…РѕРґР° (С‡Р°СЃС‹)

// РЎРѕСЃС‚РѕСЏРЅРёРµ РїСЂРѕСЃР°РґРєРё Рё РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёСЏ
bool     m_drawdown_detected;            // РћР±РЅР°СЂСѓР¶РµРЅР° РїСЂРѕСЃР°РґРєР°
datetime m_drawdown_start_time;          // Р’СЂРµРјСЏ РЅР°С‡Р°Р»Р° РїСЂРѕСЃР°РґРєРё
double   m_drawdown_max_value;           // РњР°РєСЃРёРјР°Р»СЊРЅРѕРµ Р·РЅР°С‡РµРЅРёРµ РїСЂРѕСЃР°РґРєРё
bool     m_recovery_active;              // Р’РѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёРµ Р°РєС‚РёРІРЅРѕ
int      m_recovery_positions_added;     // РљРѕР»РёС‡РµСЃС‚РІРѕ РґРѕР±Р°РІР»РµРЅРЅС‹С… РїРѕР·РёС†РёР№ РґР»СЏ РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёСЏ
int      m_positions_closed;             // РљРѕР»РёС‡РµСЃС‚РІРѕ Р·Р°РєСЂС‹С‚С‹С… РїРѕР·РёС†РёР№ РїСЂРё РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёРё
datetime m_drawdown_pause_until;         // Р’СЂРµРјСЏ РґРѕ РєРѕС‚РѕСЂРѕРіРѕ С‚РѕСЂРіРѕРІР»СЏ РїСЂРёРѕСЃС‚Р°РЅРѕРІР»РµРЅР°
double   m_current_drawdown_percent;     // РўРµРєСѓС‰РёР№ РїСЂРѕС†РµРЅС‚ РїСЂРѕСЃР°РґРєРё
double   m_recovery_percent;             // РџСЂРѕС†РµРЅС‚ РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёСЏ
int      m_recovery_position_tickets[100]; // РўРёРєРµС‚С‹ РїРѕР·РёС†РёР№ РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёСЏ

// РњР°СЃСЃРёРІС‹ РґР»СЏ С…СЂР°РЅРµРЅРёСЏ Р·РЅР°С‡РµРЅРёР№ РёРЅРґРёРєР°С‚РѕСЂРѕРІ
double   m_adx_main[], m_plus_di[], m_minus_di[];
double   m_ma_fast[], m_ma_slow[];

// РћР±СЉРµРєС‚ РґР»СЏ РІС‹РїРѕР»РЅРµРЅРёСЏ С‚РѕСЂРіРѕРІС‹С… РѕРїРµСЂР°С†РёР№
CTrade   m_trade;

// РҐРµРЅРґР»С‹ РёРЅРґРёРєР°С‚РѕСЂРѕРІ
int      m_adx_handle = INVALID_HANDLE;  // РҐРµРЅРґР» РёРЅРґРёРєР°С‚РѕСЂР° ADX
int      m_ma_fast_handle = INVALID_HANDLE; // РҐРµРЅРґР» Р±С‹СЃС‚СЂРѕР№ MA
int      m_ma_slow_handle = INVALID_HANDLE; // РҐРµРЅРґР» РјРµРґР»РµРЅРЅРѕР№ MA

// РџРµСЂРµРјРµРЅРЅС‹Рµ РґР»СЏ РїСЂРѕРґРІРёРЅСѓС‚РѕРіРѕ Р°РЅР°Р»РёР·Р° СЂС‹РЅРєР°
MarketStructureInfo     m_market_structure;       // РўРµРєСѓС‰Р°СЏ СЃС‚СЂСѓРєС‚СѓСЂР° СЂС‹РЅРєР°
ENUM_PRICE_PATTERN      m_current_pattern;        // РўРµРєСѓС‰РёР№ РѕР±РЅР°СЂСѓР¶РµРЅРЅС‹Р№ РїР°С‚С‚РµСЂРЅ
int                     m_pattern_bar;            // РРЅРґРµРєСЃ Р±Р°СЂР°, РЅР° РєРѕС‚РѕСЂРѕРј РѕР±РЅР°СЂСѓР¶РµРЅ РїР°С‚С‚РµСЂРЅ
KeyLevel                m_key_levels[];           // РњР°СЃСЃРёРІ РєР»СЋС‡РµРІС‹С… СѓСЂРѕРІРЅРµР№
AveragingPoint          m_averaging_points[];     // РўРѕС‡РєРё РѕРїС‚РёРјР°Р»СЊРЅРѕРіРѕ СѓСЃСЂРµРґРЅРµРЅРёСЏ
int                     m_max_key_levels;         // РњР°РєСЃРёРјР°Р»СЊРЅРѕРµ РєРѕР»РёС‡РµСЃС‚РІРѕ РєР»СЋС‡РµРІС‹С… СѓСЂРѕРІРЅРµР№
int                     m_key_levels_count;       // РўРµРєСѓС‰РµРµ РєРѕР»РёС‡РµСЃС‚РІРѕ РєР»СЋС‡РµРІС‹С… СѓСЂРѕРІРЅРµР№
int                     m_max_averaging_points;   // РњР°РєСЃРёРјР°Р»СЊРЅРѕРµ РєРѕР»РёС‡РµСЃС‚РІРѕ С‚РѕС‡РµРє СѓСЃСЂРµРґРЅРµРЅРёСЏ

// РџРµСЂРµРјРµРЅРЅС‹Рµ РґР»СЏ РїСЂРѕРґРІРёРЅСѓС‚РѕРіРѕ Р°РЅР°Р»РёР·Р° СЂС‹РЅРєР°
PatternStrength         m_current_patterns[5];    // РњР°СЃСЃРёРІ С‚РµРєСѓС‰РёС… РїР°С‚С‚РµСЂРЅРѕРІ (РґРѕ 5)
int                     m_pattern_count;          // РљРѕР»РёС‡РµСЃС‚РІРѕ РѕР±РЅР°СЂСѓР¶РµРЅРЅС‹С… РїР°С‚С‚РµСЂРЅРѕРІ

// Р°РЅР°Р»РёР· СЂС‹РЅРєР°
input string            AdvancedAnalysis = "==== Р°РЅР°Р»РёР· СЂС‹РЅРєР° ====";
input bool              UseAdvancedAnalysis = true; // Р’РєР»СЋС‡РёС‚СЊ Р°РЅР°Р»РёР· СЂС‹РЅРєР°
input bool              UsePatternRecognition = true; // Р Р°СЃРїРѕР·РЅР°РІР°РЅРёРµ С†РµРЅРѕРІС‹С… РїР°С‚С‚РµСЂРЅРѕРІ
input bool              UseKeyLevelAnalysis = true; // РђРЅР°Р»РёР· РєР»СЋС‡РµРІС‹С… СѓСЂРѕРІРЅРµР№
input bool              UseSmartAveraging = true;  // РЈРјРЅРѕРµ СѓСЃСЂРµРґРЅРµРЅРёРµ
input int               KeyLevelsToTrack = 10;     // РљРѕР»РёС‡РµСЃС‚РІРѕ РѕС‚СЃР»РµР¶РёРІР°РµРјС‹С… СѓСЂРѕРІРЅРµР№
input int               HistoryBarsAnalysis = 500; // РљРѕР»РёС‡РµСЃС‚РІРѕ Р±Р°СЂРѕРІ РґР»СЏ Р°РЅР°Р»РёР·Р° РёСЃС‚РѕСЂРёРё
input double            KeyLevelTouchZone = 5.0;   // Р—РѕРЅР° РєР°СЃР°РЅРёСЏ СѓСЂРѕРІРЅСЏ (РїСѓРЅРєС‚РѕРІ)
input int               PatternConfirmationBars = 3; // Р‘Р°СЂС‹ РґР»СЏ РїРѕРґС‚РІРµСЂР¶РґРµРЅРёСЏ РїР°С‚С‚РµСЂРЅР°
input double            FiboPullbackMinimum = 0.382; // РњРёРЅРёРјР°Р»СЊРЅС‹Р№ РѕС‚РєР°С‚ РґР»СЏ СѓСЃСЂРµРґРЅРµРЅРёСЏ (СѓСЂРѕРІРµРЅСЊ Р¤РёР±Рѕ)
input double            StrongLevelThreshold = 70.0; // РџРѕСЂРѕРі СЃРёР»С‹ РґР»СЏ СЃРёР»СЊРЅРѕРіРѕ СѓСЂРѕРІРЅСЏ
input int               RSI_Period = 14;           // РџРµСЂРёРѕРґ RSI РґР»СЏ РѕРїСЂРµРґРµР»РµРЅРёСЏ СЌРєСЃС‚СЂРµРјСѓРјРѕРІ
input double            RSI_Oversold = 30.0;       // РЈСЂРѕРІРµРЅСЊ РїРµСЂРµРїСЂРѕРґР°РЅРЅРѕСЃС‚Рё RSI
input double            RSI_Overbought = 70.0;     // РЈСЂРѕРІРµРЅСЊ РїРµСЂРµРєСѓРїР»РµРЅРЅРѕСЃС‚Рё RSI
input int               StdDev_Period = 20;        // РџРµСЂРёРѕРґ СЃС‚Р°РЅРґР°СЂС‚РЅРѕРіРѕ РѕС‚РєР»РѕРЅРµРЅРёСЏ

// РҐРµРЅРґР»С‹ РґРѕРїРѕР»РЅРёС‚РµР»СЊРЅС‹С… РёРЅРґРёРєР°С‚РѕСЂРѕРІ
int                     m_rsi_handle = INVALID_HANDLE; // РҐРµРЅРґР» РёРЅРґРёРєР°С‚РѕСЂР° RSI
int                     m_bb_handle = INVALID_HANDLE;  // РҐРµРЅРґР» РёРЅРґРёРєР°С‚РѕСЂР° Bollinger Bands
int                     m_stddev_handle = INVALID_HANDLE; // РҐРµРЅРґР» СЃС‚Р°РЅРґР°СЂС‚РЅРѕРіРѕ РѕС‚РєР»РѕРЅРµРЅРёСЏ

// РџР°СЂР°РјРµС‚СЂС‹ РјСѓР»СЊС‚РёС‚Р°Р№РјС„СЂРµР№РјРЅРѕРіРѕ Р°РЅР°Р»РёР·Р° РєР»СЋС‡РµРІС‹С… СѓСЂРѕРІРЅРµР№
input string __multi_timeframe__ = "РќР°СЃС‚СЂРѕР№РєРё РјСѓР»СЊС‚РёС‚Р°Р№РјС„СЂРµР№РјРЅРѕРіРѕ Р°РЅР°Р»РёР·Р°";
input bool   UseMultiTimeframeAnalysis = true; // РСЃРїРѕР»СЊР·РѕРІР°С‚СЊ РјСѓР»СЊС‚РёС‚Р°Р№РјС„СЂРµР№РјРЅС‹Р№ Р°РЅР°Р»РёР·
input bool   ConfirmLevelsWith_H1 = true;      // РџРѕРґС‚РІРµСЂР¶РґР°С‚СЊ СѓСЂРѕРІРЅРё РЅР° H1
input bool   ConfirmLevelsWith_H4 = true;      // РџРѕРґС‚РІРµСЂР¶РґР°С‚СЊ СѓСЂРѕРІРЅРё РЅР° H4
input bool   ConfirmLevelsWith_D1 = true;      // РџРѕРґС‚РІРµСЂР¶РґР°С‚СЊ СѓСЂРѕРІРЅРё РЅР° D1
input double MTF_ProbabilityBoost = 10.0;      // РџСЂРёСЂРѕСЃС‚ РІРµСЂРѕСЏС‚РЅРѕСЃС‚Рё Р·Р° РїРѕРґС‚РІРµСЂР¶РґРµРЅРёРµ СѓСЂРѕРІРЅСЏ (%)
input double MTF_LotCoefficientBoost = 0.1;    // РџСЂРёСЂРѕСЃС‚ РєРѕСЌС„С„РёС†РёРµРЅС‚Р° Р»РѕС‚Р° Р·Р° РїРѕРґС‚РІРµСЂР¶РґРµРЅРёРµ СѓСЂРѕРІРЅСЏ

// РќР°СЃС‚СЂРѕР№РєРё Р°РґР°РїС‚РёРІРЅРѕРіРѕ MM
input string __adaptive_mm__ = "РќР°СЃС‚СЂРѕР№РєРё Р°РґР°РїС‚РёРІРЅРѕРіРѕ MM РЅР° РѕСЃРЅРѕРІРµ СЃРёР»С‹ СЃРёРіРЅР°Р»РѕРІ";
input bool   UseAdaptiveMM = true;     // РСЃРїРѕР»СЊР·РѕРІР°С‚СЊ Р°РґР°РїС‚РёРІРЅС‹Р№ MM
input double SignalStrengthMultiplier = 0.5; // РњРЅРѕР¶РёС‚РµР»СЊ СЃРёР»С‹ СЃРёРіРЅР°Р»Р° РґР»СЏ Р»РѕС‚Р° (0.1-1.0)
input double TrendAgainstReduction = 0.7;   // РљРѕСЌС„С„РёС†РёРµРЅС‚ СѓРјРµРЅСЊС€РµРЅРёСЏ Р»РѕС‚Р° РїСЂРѕС‚РёРІ С‚СЂРµРЅРґР° (0.5-1.0)
input double MinProbabilityForEntry = 60.0; // РњРёРЅРёРјР°Р»СЊРЅР°СЏ РІРµСЂРѕСЏС‚РЅРѕСЃС‚СЊ РґР»СЏ РІС…РѕРґР° (%)
input double ProbabilityLotBoost = 0.3;     // РџСЂРёСЂРѕСЃС‚ Р»РѕС‚Р° Р·Р° РєР°Р¶РґС‹Рµ 10% РІРµСЂРѕСЏС‚РЅРѕСЃС‚Рё РІС‹С€Рµ РјРёРЅРёРјР°Р»СЊРЅРѕР№
input int    MaxSignalLookback = 5;         // РљРѕР»РёС‡РµСЃС‚РІРѕ Р±Р°СЂРѕРІ РґР»СЏ РїРѕРёСЃРєР° СЃРёРіРЅР°Р»Р°

//+------------------------------------------------------------------+
//| Р¤СѓРЅРєС†РёСЏ РёРЅРёС†РёР°Р»РёР·Р°С†РёРё СЌРєСЃРїРµСЂС‚Р°                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // РРЅРёС†РёР°Р»РёР·Р°С†РёСЏ РїРѕ СѓРјРѕР»С‡Р°РЅРёСЋ
   m_symbol = Symbol();
   m_prefix = "TP_";
   m_magic = Magic;
   m_exit_mode = Exit_mode;
   m_trade_area_name = m_prefix + "Trade_Area";
   
   // РРЅРёС†РёР°Р»РёР·Р°С†РёСЏ Р°РєС‚РёРІРЅС‹С… СЃР»РѕРµРІ
   ArrayInitialize(m_active_layers, 0);
   
   // РРЅРёС†РёР°Р»РёР·Р°С†РёСЏ РјР°СЃСЃРёРІРѕРІ РґР»СЏ РїРѕР·РёС†РёР№
   ArrayInitialize(m_buy_positions_count, 0);
   ArrayInitialize(m_sell_positions_count, 0);
   ArrayInitialize(m_buy_lots_total, 0.0);
   ArrayInitialize(m_sell_lots_total, 0.0);
   
   // РџРѕР»СѓС‡РµРЅРёРµ РёРЅС„РѕСЂРјР°С†РёРё Рѕ С‚РµРєСѓС‰РµРј СЃРёРјРІРѕР»Рµ
   m_digits = (int)SymbolInfoInteger(m_symbol, SYMBOL_DIGITS);
   m_point = SymbolInfoDouble(m_symbol, SYMBOL_POINT);
   m_point_digits = (m_digits == 3 || m_digits == 5) ? 10 : 1;
   
   // РџРѕР»СѓС‡РµРЅРёРµ РёРЅС„РѕСЂРјР°С†РёРё Рѕ РјРёРЅРёРјР°Р»СЊРЅРѕРј Рё РјР°РєСЃРёРјР°Р»СЊРЅРѕРј Р»РѕС‚Рµ
   m_min_lot = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MIN);
   m_max_lot = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MAX);
   
   // РЈСЃС‚Р°РЅРѕРІРєР° РїР°СЂР°РјРµС‚СЂРѕРІ Р»РѕС‚Р°
   m_lot_const = LotConst_or_not;
   m_lot = Lot;
   m_risk_percent = RiskPercent;
   m_lot_multiplicator = LotMultiplicator;
   
   // РРЅРёС†РёР°Р»РёР·Р°С†РёСЏ РїРµСЂРµРјРµРЅРЅС‹С… РѕРїС‚РёРјРёР·Р°С†РёРё
   m_optimize_for_testing = OptimizeForTesting;
   m_timer_interval = MathMax(1, MathMin(60, TimerInterval));
   m_analysis_update_frequency = MathMax(1, AnalysisUpdateFrequency);
   m_tick_counter = 0;
   
   // РЈСЃС‚Р°РЅРѕРІРєР° РїР°СЂР°РјРµС‚СЂРѕРІ Р°РґР°РїС‚РёРІРЅРѕРіРѕ MM
   m_use_adaptive_mm = UseAdaptiveMM;
   m_signal_strength_multiplier = SignalStrengthMultiplier;
   m_trend_against_reduction = TrendAgainstReduction;
   m_min_probability_for_entry = MinProbabilityForEntry;
   m_probability_lot_boost = ProbabilityLotBoost;
   m_max_signal_lookback = MaxSignalLookback;
   
   // РЈСЃС‚Р°РЅРѕРІРєР° РїР°СЂР°РјРµС‚СЂРѕРІ СЃР»РѕРµРІ
   m_work_sloy_mode = Work_Sloy_mode;
   m_torg_sloy = TorgSloy;
   m_dd_lot_auto_calc = D_D_LOT_auto_calc;
   m_dd_lot = D_D_Lot;
   m_n_enable_sloy = N_enable_Sloy;
   
   // РЈСЃС‚Р°РЅРѕРІРєР° РїР°СЂР°РјРµС‚СЂРѕРІ СЃРµС‚РєРё
   m_hsetky = hSETKY;
   m_uvel_hsetky = Uvel_hSETKY;
   m_shag_uvel_hsetky = ShagUvel_hSETKY;
   
   // РЈСЃС‚Р°РЅРѕРІРєР° РїР°СЂР°РјРµС‚СЂРѕРІ С‚СЂРµС‚СЊРµРіРѕ СЃР»РѕСЏ
   m_mode_enable_3_sloy = mode_enable_3_sloy;
   m_h_d_t = h_D_T * m_point_digits;
   m_pr_h_3_sl = pr_h_3_sl;
   
   // РЈСЃС‚Р°РЅРѕРІРєР° РїР°СЂР°РјРµС‚СЂРѕРІ С‚РѕСЂРіРѕРІР»Рё
   m_slippage = slippage;
   m_protection_tp = ProtectionTP;
   m_tralling_stop = TrallingStop;
   m_trall_tp = TrallTP;
   m_min_proc_sv_sr = Min_Proc_Sv_Sr;
   
   // РЈСЃС‚Р°РЅРѕРІРєР° РІРёР·СѓР°Р»СЊРЅС‹С… РїР°СЂР°РјРµС‚СЂРѕРІ
   m_show_table_on_testing = ShowTableOnTesting;
   m_text_size = Text_Syze;
   m_color_table_on_testing = ColorTableOnTesting;
   m_color_logotip_name = ColorLogotipName;
   m_color_logotip_site = ColorLogotipSite;
   m_color_trade_area_of_3rd_layer = color_Trade_area_of_3rd_layer;
   m_show_advanced_info = ShowAdvancedInfo;
   
   // РРЅРёС†РёР°Р»РёР·Р°С†РёСЏ РїР°СЂР°РјРµС‚СЂРѕРІ С„РёР»СЊС‚СЂР° С‚СЂРµРЅРґР°
   m_use_trend_filter = UseTrendFilter;
   m_trade_only_with_trend = TradeOnlyWithTrend;
   m_trend_detection_method = TrendDetectionMethod;
   m_adx_period = ADX_Period;
   m_adx_trend_level = ADX_TrendLevel;
   m_adx_use_di_filter = ADX_UseDIFilter;
   m_ma_fast_period = MA_Fast_Period;
   m_ma_slow_period = MA_Slow_Period;
   m_ma_method = MA_Method;
   m_ma_applied_price = MA_Applied_Price;
   m_ma_signal_bar = MA_SignalBar;
   m_trend_mode_setka = TrendMode_Setka;
   m_flat_mode_setka = FlatMode_Setka;
   m_trend_mode_lot_mult = TrendMode_LotMult;
   m_flat_mode_lot_mult = FlatMode_LotMult;
   m_current_market_type = 0; // РџРѕ СѓРјРѕР»С‡Р°РЅРёСЋ СЃС‡РёС‚Р°РµРј СЂС‹РЅРѕРє С„Р»СЌС‚РѕРІС‹Рј
   
   // РРЅРёС†РёР°Р»РёР·Р°С†РёСЏ РїР°СЂР°РјРµС‚СЂРѕРІ Р°РґР°РїС‚РёРІРЅРѕРіРѕ СЂР°Р·РјРµСЂР° СЃРµС‚РєРё
   m_use_adaptive_grid = UseAdaptiveGrid;
   m_use_atr_for_grid = UseATRForGrid;
   m_atr_period = ATR_Period;
   m_atr_multiplier = ATR_Multiplier;
   m_session_based_settings = SessionBasedSettings;
   m_asian_session_multiplier = AsianSessionMultiplier;
   m_european_session_multiplier = EuropeanSessionMultiplier;
   m_american_session_multiplier = AmericanSessionMultiplier;
   m_auto_adjust_for_symbol = AutoAdjustForSymbol;
   m_forex_major_multiplier = ForexMajorMultiplier;
   m_forex_cross_multiplier = ForexCrossMultiplier;
   m_commodity_multiplier = CommodityMultiplier;
   m_index_multiplier = IndexMultiplier;
   m_current_grid_step = 0.0; // Р‘СѓРґРµС‚ СЂР°СЃСЃС‡РёС‚Р°РЅ РїРѕР·Р¶Рµ
   
   // РРЅРёС†РёР°Р»РёР·Р°С†РёСЏ РїР°СЂР°РјРµС‚СЂРѕРІ СѓРјРЅРѕРіРѕ РІС‹С…РѕРґР° РёР· РїСЂРѕСЃР°РґРєРё
   m_use_smart_exit = UseSmartExit;
   m_recovery_method = RecoveryMethod;
   m_drawdown_max_percent = DrawdownMax_Percent;
   m_drawdown_max_time = DrawdownMax_Time;
   m_target_profit_percent = TargetProfit_Percent;
   m_max_recovery_positions = MaxRecoveryPositions;
   m_smart_lot_multiplier = SmartLotMultiplier;
   m_partial_close_percent = PartialClosePercent;
   m_use_market_structure = UseMarketStructure;
   m_min_profit_to_close = MinProfitToClose;
   m_trailing_activation_level = TrailingActivationLevel;
   m_drawdown_pause_after_exit = DrawdownPause_After_Exit;
   
   // РРЅРёС†РёР°Р»РёР·Р°С†РёСЏ СЃРѕСЃС‚РѕСЏРЅРёСЏ РїСЂРѕСЃР°РґРєРё Рё РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёСЏ
   m_drawdown_detected = false;
   m_drawdown_start_time = 0;
   m_drawdown_max_value = 0.0;
   m_recovery_active = false;
   m_recovery_positions_added = 0;
   m_positions_closed = 0;
   m_drawdown_pause_until = 0;
   m_current_drawdown_percent = 0.0;
   m_recovery_percent = 0.0;
   ArrayInitialize(m_recovery_position_tickets, 0);
   
   // РћРїСЂРµРґРµР»РµРЅРёРµ С‚РµРєСѓС‰РµР№ СЃРµСЃСЃРёРё Рё С‚РёРїР° РёРЅСЃС‚СЂСѓРјРµРЅС‚Р°
   m_current_session = (ENUM_MARKET_SESSION)DetectCurrentSession();
   DetermineSymbolType();
   
   // РћРѕР·РґР°РЅРёРµ РёРЅРґРёРєР°С‚РѕСЂРѕРІ, РµСЃР»Рё РёСЃРїРѕР»СЊР·СѓРµС‚СЃСЏ С„РёР»СЊС‚СЂ С‚СЂРµРЅРґР°
   if(m_use_trend_filter)
   {
      if(m_trend_detection_method == 0 || m_trend_detection_method == 2) // ADX РёР»Рё РѕР±Р° РјРµС‚РѕРґР°
      {
         m_adx_handle = iADX(m_symbol, PERIOD_CURRENT, m_adx_period);
         if(m_adx_handle == INVALID_HANDLE)
         {
            Print("РћС€РёР±РєР° СЃРѕР·РґР°РЅРёСЏ РёРЅРґРёРєР°С‚РѕСЂР° ADX");
            return(INIT_FAILED);
         }
      }
      
      if(m_trend_detection_method == 1 || m_trend_detection_method == 2) // MA РёР»Рё РѕР±Р° РјРµС‚РѕРґР°
      {
         m_ma_fast_handle = iMA(m_symbol, PERIOD_CURRENT, m_ma_fast_period, 0, (ENUM_MA_METHOD)m_ma_method, m_ma_applied_price);
         if(m_ma_fast_handle == INVALID_HANDLE)
         {
            Print("РћС€РёР±РєР° СЃРѕР·РґР°РЅРёСЏ Р±С‹СЃС‚СЂРѕР№ MA");
            return(INIT_FAILED);
         }
         
         m_ma_slow_handle = iMA(m_symbol, PERIOD_CURRENT, m_ma_slow_period, 0, (ENUM_MA_METHOD)m_ma_method, m_ma_applied_price);
         if(m_ma_slow_handle == INVALID_HANDLE)
         {
            Print("РћС€РёР±РєР° СЃРѕР·РґР°РЅРёСЏ РјРµРґР»РµРЅРЅРѕР№ MA");
            return(INIT_FAILED);
         }
      }
   }
   
   // РЎРѕР·РґР°РЅРёРµ РёРЅРґРёРєР°С‚РѕСЂР° ATR, РµСЃР»Рё РёСЃРїРѕР»СЊР·СѓРµС‚СЃСЏ Р°РґР°РїС‚РёРІРЅС‹Р№ СЂР°Р·РјРµСЂ СЃРµС‚РєРё
   if(m_use_adaptive_grid && m_use_atr_for_grid)
   {
      m_atr_handle = iATR(m_symbol, PERIOD_CURRENT, m_atr_period);
      if(m_atr_handle == INVALID_HANDLE)
      {
         Print("РћС€РёР±РєР° СЃРѕР·РґР°РЅРёСЏ РёРЅРґРёРєР°С‚РѕСЂР° ATR");
         return(INIT_FAILED);
      }
   }
   
   // РЈСЃС‚Р°РЅРѕРІРєР° С‚Р°Р№РјРµСЂР° СЃ СѓС‡РµС‚РѕРј РѕРїС‚РёРјРёР·Р°С†РёРё
   if(m_optimize_for_testing && (bool)MQLInfoInteger(MQL_TESTER))
   {
      EventSetTimer(m_timer_interval); // РЈРІРµР»РёС‡РµРЅРЅС‹Р№ РёРЅС‚РµСЂРІР°Р» РґР»СЏ С‚РµСЃС‚РёСЂРѕРІР°РЅРёСЏ
      Print("РЈСЃС‚Р°РЅРѕРІР»РµРЅ РѕРїС‚РёРјРёР·РёСЂРѕРІР°РЅРЅС‹Р№ С‚Р°Р№РјРµСЂ СЃ РёРЅС‚РµСЂРІР°Р»РѕРј ", m_timer_interval, " СЃРµРєСѓРЅРґ");
   }
   else
   {
      EventSetTimer(1); // РЎС‚Р°РЅРґР°СЂС‚РЅС‹Р№ РёРЅС‚РµСЂРІР°Р» РґР»СЏ СЂРµР°Р»СЊРЅРѕР№ С‚РѕСЂРіРѕРІР»Рё
   }
   
   // РРЅРёС†РёР°Р»РёР·Р°С†РёСЏ РґР»СЏ РїСЂРѕРґРІРёРЅСѓС‚РѕРіРѕ Р°РЅР°Р»РёР·Р° СЂС‹РЅРєР°
   if(UseAdvancedAnalysis)
   {
      // РРЅРёС†РёР°Р»РёР·Р°С†РёСЏ СЃС‚СЂСѓРєС‚СѓСЂС‹ СЂС‹РЅРєР°
      m_market_structure.type = STRUCTURE_UNDEFINED;
      m_market_structure.trend_strength = 0;
      
      // РЈСЃС‚Р°РЅРѕРІРєР° РјР°РєСЃРёРјР°Р»СЊРЅРѕРіРѕ РєРѕР»РёС‡РµСЃС‚РІР° РѕС‚СЃР»РµР¶РёРІР°РµРјС‹С… СѓСЂРѕРІРЅРµР№
      m_max_key_levels = KeyLevelsToTrack;
      m_key_levels_count = 0; // РРЅРёС†РёР°Р»РёР·Р°С†РёСЏ СЃС‡РµС‚С‡РёРєР° РєР»СЋС‡РµРІС‹С… СѓСЂРѕРІРЅРµР№
      ArrayResize(m_key_levels, m_max_key_levels);
      
      // РЈСЃС‚Р°РЅРѕРІРєР° РјР°РєСЃРёРјР°Р»СЊРЅРѕРіРѕ РєРѕР»РёС‡РµСЃС‚РІР° С‚РѕС‡РµРє СѓСЃСЂРµРґРЅРµРЅРёСЏ
      m_max_averaging_points = KeyLevelsToTrack * 2; // РЎРѕР·РґР°РµРј РІ 2 СЂР°Р·Р° Р±РѕР»СЊС€Рµ С‚РѕС‡РµРє СѓСЃСЂРµРґРЅРµРЅРёСЏ, С‡РµРј СѓСЂРѕРІРЅРµР№
      ArrayResize(m_averaging_points, m_max_averaging_points);
      
      // РРЅРёС†РёР°Р»РёР·Р°С†РёСЏ РёРЅРґРёРєР°С‚РѕСЂРѕРІ
      if(UsePatternRecognition || UseSmartAveraging)
      {
         m_rsi_handle = iRSI(m_symbol, PERIOD_CURRENT, RSI_Period, PRICE_CLOSE);
         m_bb_handle = iBands(m_symbol, PERIOD_CURRENT, 20, 2, 0, PRICE_CLOSE);
         m_stddev_handle = iStdDev(m_symbol, PERIOD_CURRENT, StdDev_Period, 0, MODE_SMA, PRICE_CLOSE);
         
         if(m_rsi_handle == INVALID_HANDLE || m_bb_handle == INVALID_HANDLE || m_stddev_handle == INVALID_HANDLE)
         {
            Print("РћС€РёР±РєР° РїСЂРё СЃРѕР·РґР°РЅРёРё РёРЅРґРёРєР°С‚РѕСЂРѕРІ РґР»СЏ РїСЂРѕРґРІРёРЅСѓС‚РѕРіРѕ Р°РЅР°Р»РёР·Р° СЂС‹РЅРєР°");
            return INIT_FAILED;
         }
      }
      
      // РђРЅР°Р»РёР· РёСЃС‚РѕСЂРёС‡РµСЃРєРёС… РґР°РЅРЅС‹С… РґР»СЏ РѕРїСЂРµРґРµР»РµРЅРёСЏ РєР»СЋС‡РµРІС‹С… СѓСЂРѕРІРЅРµР№
      if(UseKeyLevelAnalysis)
      {
         IdentifyKeyLevels();
      }
      
      // РћР°СЃРїРѕР·РЅР°РІР°РЅРёРµ С‚РµРєСѓС‰РµР№ СЃС‚СЂСѓРєС‚СѓСЂС‹ СЂС‹РЅРєР°
      DetectMarketStructure();
      
      // Р Р°СЃРїРѕР·РЅР°РІР°РЅРёРµ С‚РµРєСѓС‰РµРіРѕ С†РµРЅРѕРІРѕРіРѕ РїР°С‚С‚РµСЂРЅР°
      if(UsePatternRecognition)
      {
         m_current_pattern = DetectPricePattern();
      }
      
      // Р Р°СЃС‡РµС‚ С‚РѕС‡РµРє РѕРїС‚РёРјР°Р»СЊРЅРѕРіРѕ СѓСЃСЂРµРґРЅРµРЅРёСЏ
      if(UseSmartAveraging)
      {
         CalculateOptimalAveragingPoints(1);  // Р”Р»СЏ Buy
         CalculateOptimalAveragingPoints(-1); // Р”Р»СЏ Sell
      }
      
      Print("РџСЂРѕРґРІРёРЅСѓС‚С‹Р№ Р°РЅР°Р»РёР· СЂС‹РЅРєР° РёРЅРёС†РёР°Р»РёР·РёСЂРѕРІР°РЅ");
   }
   
   // РРЅРёС†РёР°Р»РёР·Р°С†РёСЏ РїСЂРѕС„РёР»РµР№ С‚РѕСЂРіРѕРІС‹С… СЃРµСЃСЃРёР№
   InitSessionProfiles();
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| РћРїСЂРµРґРµР»РµРЅРёРµ С‚РµРєСѓС‰РµР№ С‚РѕСЂРіРѕРІРѕР№ СЃРµСЃСЃРёРё                               |
//+------------------------------------------------------------------+
int DetectCurrentSession()
{
   // РџРѕР»СѓС‡РµРЅРёРµ С‚РµРєСѓС‰РµРіРѕ РІСЂРµРјРµРЅРё СЃРµСЂРІРµСЂР°
   datetime server_time = TimeCurrent();
   
   // РџСЂРµРѕР±СЂР°Р·РѕРІР°РЅРёРµ РІСЂРµРјРµРЅРё СЃРµСЂРІРµСЂР° РІ СЃС‚СЂСѓРєС‚СѓСЂСѓ СЃ РєРѕРјРїРѕРЅРµРЅС‚Р°РјРё РІСЂРµРјРµРЅРё
   MqlDateTime dt;
   TimeToStruct(server_time, dt);
   
   int hour = dt.hour;
   
   // РћРїСЂРµРґРµР»РµРЅРёРµ СЃРµСЃСЃРёРё РїРѕ РІСЂРµРјРµРЅРё (РњРЎРљ)
   // РђР·РёР°С‚СЃРєР°СЏ: 00:00-08:00
   // Р•РІСЂРѕРїРµР№СЃРєР°СЏ: 08:00-16:00
   // РђРјРµСЂРёРєР°РЅСЃРєР°СЏ: 16:00-24:00
   
   if(hour >= 0 && hour < 8)
      return SESSION_ASIAN;
   else if(hour >= 8 && hour < 16)
      return SESSION_EUROPEAN;
   else // hour >= 16 && hour < 24
      return SESSION_AMERICAN;
}

//+------------------------------------------------------------------+
//| РћРїСЂРµРґРµР»РµРЅРёРµ С‚РёРїР° РёРЅСЃС‚СЂСѓРјРµРЅС‚Р°                                      |
//+------------------------------------------------------------------+
void DetermineSymbolType()
{
   string symbol = m_symbol;
   
   // РџСЂРѕРІРµСЂРєР° РЅР° РѕСЃРЅРѕРІРЅС‹Рµ РІР°Р»СЋС‚РЅС‹Рµ РїР°СЂС‹
   if(StringFind(symbol, "USD") != -1 && 
      (StringFind(symbol, "EUR") != -1 || StringFind(symbol, "GBP") != -1 || 
       StringFind(symbol, "JPY") != -1 || StringFind(symbol, "AUD") != -1 || 
       StringFind(symbol, "NZD") != -1 || StringFind(symbol, "CAD") != -1 || 
       StringFind(symbol, "CHF") != -1))
   {
      m_symbol_type = TYPE_FOREX_MAJOR;
      return;
   }
   
   // РџСЂРѕРІРµСЂРєР° РЅР° РєСЂРѕСЃСЃ-РІР°Р»СЋС‚РЅС‹Рµ РїР°СЂС‹
   if((StringFind(symbol, "EUR") != -1 || StringFind(symbol, "GBP") != -1 || 
       StringFind(symbol, "JPY") != -1 || StringFind(symbol, "AUD") != -1 || 
       StringFind(symbol, "NZD") != -1 || StringFind(symbol, "CAD") != -1 || 
       StringFind(symbol, "CHF") != -1) && StringFind(symbol, "USD") == -1)
   {
      m_symbol_type = TYPE_FOREX_CROSS;
      return;
   }
   
   // РџСЂРѕРІРµСЂРєР° РЅР° СЃС‹СЂСЊРµРІС‹Рµ РёРЅСЃС‚СЂСѓРјРµРЅС‚С‹
   if(StringFind(symbol, "XAU") != -1 || StringFind(symbol, "XAG") != -1 || 
      StringFind(symbol, "GOLD") != -1 || StringFind(symbol, "SILVER") != -1 || 
      StringFind(symbol, "OIL") != -1 || StringFind(symbol, "BRENT") != -1)
   {
      m_symbol_type = TYPE_COMMODITY;
      return;
   }
   
   // РџСЂРѕРІРµСЂРєР° РЅР° РёРЅРґРµРєСЃС‹
   if(StringFind(symbol, "JP") != -1 || StringFind(symbol, "DE") != -1 || 
      StringFind(symbol, "UK") != -1 || StringFind(symbol, "US") != -1 || 
      StringFind(symbol, "NQ") != -1 || StringFind(symbol, "SP") != -1)
   {
      m_symbol_type = TYPE_INDEX;
      return;
   }
   
   // РџРѕ СѓРјРѕР»С‡Р°РЅРёСЋ - РѕСЃРЅРѕРІРЅР°СЏ РІР°Р»СЋС‚РЅР°СЏ РїР°СЂР°
   m_symbol_type = TYPE_FOREX_MAJOR;
}

//+------------------------------------------------------------------+
//| Р¤СѓРЅРєС†РёСЏ РґРµРёРЅРёС†РёР°Р»РёР·Р°С†РёРё СЌРєСЃРїРµСЂС‚Р°                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // РЈРґР°Р»РµРЅРёРµ С‚Р°Р№РјРµСЂР°
   EventKillTimer();
   
   // РЈРґР°Р»РµРЅРёРµ РѕР±СЉРµРєС‚РѕРІ
   ObjectsDeleteAll(0, m_prefix);
   
   // РћС‡РёСЃС‚РєР° РєРѕРјРјРµРЅС‚Р°СЂРёСЏ
   Comment("");
   
   // РћСЃРІРѕР±РѕР¶РґРµРЅРёРµ РёРЅРґРёРєР°С‚РѕСЂРѕРІ
   if(m_adx_handle != INVALID_HANDLE)
      IndicatorRelease(m_adx_handle);
      
   if(m_ma_fast_handle != INVALID_HANDLE)
      IndicatorRelease(m_ma_fast_handle);
      
   if(m_ma_slow_handle != INVALID_HANDLE)
      IndicatorRelease(m_ma_slow_handle);
      
   // РћСЃРІРѕР±РѕР¶РґРµРЅРёРµ РёРЅРґРёРєР°С‚РѕСЂР° ATR
   if(m_atr_handle != INVALID_HANDLE)
      IndicatorRelease(m_atr_handle);
   
   // РћСЃРІРѕР±РѕР¶РґРµРЅРёРµ СЂРµСЃСѓСЂСЃРѕРІ РёРЅРґРёРєР°С‚РѕСЂРѕРІ РґР»СЏ РїСЂРѕРґРІРёРЅСѓС‚РѕРіРѕ Р°РЅР°Р»РёР·Р° СЂС‹РЅРєР°
   if(UseAdvancedAnalysis)
   {
      if(m_rsi_handle != INVALID_HANDLE)
         IndicatorRelease(m_rsi_handle);
         
      if(m_bb_handle != INVALID_HANDLE)
         IndicatorRelease(m_bb_handle);
         
      if(m_stddev_handle != INVALID_HANDLE)
         IndicatorRelease(m_stddev_handle);
         
      Print("Р РµСЃСѓСЂСЃС‹ РїСЂРѕРґРІРёРЅСѓС‚РѕРіРѕ Р°РЅР°Р»РёР·Р° СЂС‹РЅРєР° РѕСЃРІРѕР±РѕР¶РґРµРЅС‹");
   }
}

//+------------------------------------------------------------------+
//| РћСЃРЅРѕРІРЅР°СЏ С„СѓРЅРєС†РёСЏ СЌРєСЃРїРµСЂС‚Р°                                        |
//+------------------------------------------------------------------+
void OnTick()
{
   // РЈРІРµР»РёС‡РёРІР°РµРј СЃС‡РµС‚С‡РёРє С‚РёРєРѕРІ
   m_tick_counter++;
   
   // РћР±РЅРѕРІР»РµРЅРёРµ РїСЂРѕС„РёР»СЏ РЅР°СЃС‚СЂРѕРµРє РІ СЃРѕРѕС‚РІРµС‚СЃС‚РІРёРё СЃ С‚РµРєСѓС‰РµР№ СЃРµСЃСЃРёРµР№
   if(m_use_session_profiles && m_tick_counter % 10 == 0) // РџСЂРѕРІРµСЂСЏРµРј РєР°Р¶РґС‹Рµ 10 С‚РёРєРѕРІ
   {
      UpdateSessionSettings();
   }
   
   // РџСЂРѕРІРµСЂРєР° РЅР° РєСЂРёС‚РёС‡РµСЃРєСѓСЋ РїСЂРѕСЃР°РґРєСѓ Рё РѕС‚СЂРёС†Р°С‚РµР»СЊРЅСѓСЋ РјР°СЂР¶Сѓ
   double free_margin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
   double equity = AccountInfoDouble(ACCOUNT_EQUITY);
   double balance = AccountInfoDouble(ACCOUNT_BALANCE);
   
   // Р•СЃР»Рё РјР°СЂР¶Р° СЃРµСЂСЊРµР·РЅРѕ РѕС‚СЂРёС†Р°С‚РµР»СЊРЅР°СЏ - Р°РєС‚РёРІРёСЂСѓРµРј СЂРµР¶РёРј СЌРєСЃС‚СЂРµРЅРЅРѕРіРѕ РІС‹С…РѕРґР°
   bool emergency_exit_mode = false;
   
   if(free_margin < 0 && MathAbs(free_margin) > balance * 0.5)
   {
      emergency_exit_mode = true;
      if(!m_exit_mode)
      {
         m_exit_mode = true;
         Print("Р’РќРРњРђРќРР•! РђРєС‚РёРІРёСЂРѕРІР°РЅ СЂРµР¶РёРј СЌРєСЃС‚СЂРµРЅРЅРѕРіРѕ РІС‹С…РѕРґР° РёР·-Р·Р° РєСЂРёС‚РёС‡РµСЃРєРѕР№ РѕС‚СЂРёС†Р°С‚РµР»СЊРЅРѕР№ РјР°СЂР¶Рё: ", 
               DoubleToString(free_margin, 2));
      }
   }
   
   // РћР±РЅРѕРІР»РµРЅРёРµ РёРЅС„РѕСЂРјР°С†РёРё Рѕ С…РµРґР¶РёСЂСѓС‰РёС… РїРѕР·РёС†РёСЏС…
   int hedge_count = UpdateHedgingInfo();
   
   // РћР±РЅРѕРІР»РµРЅРёРµ РёРЅРґРёРєР°С‚РѕСЂРѕРІ
   UpdateIndicators();
   
   // РџРѕР»СѓС‡РµРЅРёРµ РёРЅС„РѕСЂРјР°С†РёРё Рѕ РїРѕР·РёС†РёСЏС…
   GetPositionsInfo();
   
   // Р•СЃР»Рё Р°РєС‚РёРІРёСЂРѕРІР°РЅ СЂРµР¶РёРј СЌРєСЃС‚СЂРµРЅРЅРѕРіРѕ РІС‹С…РѕРґР° - Р·Р°РєСЂС‹РІР°РµРј РІСЃРµ РїРѕР·РёС†РёРё
   if(emergency_exit_mode)
   {
      EmergencyCloseAllPositions();
      // РћС‚РѕР±СЂР°Р¶РµРЅРёРµ РёРЅС„РѕСЂРјР°С†РёРё Рѕ С‚РѕСЂРіРѕРІР»Рµ
      ShowTradeInfo();
      return; // РџСЂРµРєСЂР°С‰Р°РµРј РІС‹РїРѕР»РЅРµРЅРёРµ РґСЂСѓРіРёС… РѕРїРµСЂР°С†РёР№
   }
   
      // РћРїСЂРµРґРµР»РµРЅРёРµ С‚РµРєСѓС‰РµРіРѕ С‚СЂРµРЅРґР° СЂС‹РЅРєР°
      m_current_market_type = DetectMarketTrend();
   
   // РћРїСЂРµРґРµР»РµРЅРёРµ Р°РєС‚РёРІРЅС‹С… СЃР»РѕРµРІ
      DetermineActiveLayers();
   
   // РћРїСЂРµРґРµР»РµРЅРёРµ СЌРєСЃС‚СЂРµРјР°Р»СЊРЅС‹С… С†РµРЅ РґР»СЏ 3-РіРѕ СЃР»РѕСЏ - РІС‹РїРѕР»РЅСЏРµРј С‚РѕР»СЊРєРѕ РµСЃР»Рё РЅРµ РІ СЂРµР¶РёРјРµ РѕРїС‚РёРјРёР·РёСЂРѕРІР°РЅРЅРѕРіРѕ С‚РµСЃС‚РёСЂРѕРІР°РЅРёСЏ
   if(!m_optimize_for_testing || !(bool)MQLInfoInteger(MQL_OPTIMIZATION))
   {
      DetectExtremePrices();
}
   
   // Р Р°СЃС‡РµС‚ СѓСЂРѕРІРЅРµР№ РѕС‚РєСЂС‹С‚РёСЏ Рё Р·Р°РєСЂС‹С‚РёСЏ РїРѕР·РёС†РёР№
   CalculateLevels();
   
   // РћР±СЂР°Р±РѕС‚РєР° С‚РѕСЂРіРѕРІС‹С… РѕРїРµСЂР°С†РёР№
   ProcessTrading();
   
   // РЈРїСЂР°РІР»РµРЅРёРµ СѓРјРЅС‹Рј РІС‹С…РѕРґРѕРј РёР· РїСЂРѕСЃР°РґРєРё
   ManageSmartExit();
   
   // РћР±СЂР°Р±РѕС‚РєР° С‚СЂРµР№Р»РёРЅРіР°
   ProcessTrailing();
   
   // РћС‚РѕР±СЂР°Р¶РµРЅРёРµ РёРЅС„РѕСЂРјР°С†РёРё Рѕ С‚РѕСЂРіРѕРІР»Рµ - РІ СЂРµР¶РёРјРµ РІРёР·СѓР°Р»СЊРЅРѕРіРѕ С‚РµСЃС‚РёСЂРѕРІР°РЅРёСЏ РёР»Рё СЂРµР°Р»СЊРЅРѕР№ С‚РѕСЂРіРѕРІР»Рё
   if(!m_optimize_for_testing || !(bool)MQLInfoInteger(MQL_OPTIMIZATION))
   {
   ShowTradeInfo();
   }
   
   // РћС‚РѕР±СЂР°Р¶РµРЅРёРµ С‚Р°Р±Р»РёС†С‹ РїСЂРё С‚РµСЃС‚РёСЂРѕРІР°РЅРёРё - С‚РѕР»СЊРєРѕ РІ РІРёР·СѓР°Р»СЊРЅРѕРј СЂРµР¶РёРјРµ
   if(m_show_table_on_testing && ((bool)MQLInfoInteger(MQL_TESTER) || (bool)MQLInfoInteger(MQL_VISUAL_MODE)) 
      && (!m_optimize_for_testing || !(bool)MQLInfoInteger(MQL_OPTIMIZATION)))
   {
      ShowTable();
   }
   
   // РћР±РЅРѕРІР»РµРЅРёРµ РїСЂРѕРґРІРёРЅСѓС‚РѕРіРѕ Р°РЅР°Р»РёР·Р° СЂС‹РЅРєР° - С‚РѕР»СЊРєРѕ РїРѕ СЂР°СЃРїРёСЃР°РЅРёСЋ РґР»СЏ РѕРїС‚РёРјРёР·Р°С†РёРё
   if(UseAdvancedAnalysis && (!m_optimize_for_testing || m_tick_counter % m_analysis_update_frequency == 0))
   {
      // РћР±РЅРѕРІР»РµРЅРёРµ СЂС‹РЅРѕС‡РЅРѕР№ СЃС‚СЂСѓРєС‚СѓСЂС‹
      DetectMarketStructure();
      
      // РџСЂРѕРІРµСЂРєР° РЅР°Р»РёС‡РёСЏ РЅРѕРІС‹С… РїР°С‚С‚РµСЂРЅРѕРІ
      if(UsePatternRecognition)
      {
         ENUM_PRICE_PATTERN new_pattern = DetectPricePattern();
         if(new_pattern != m_current_pattern)
         {
            m_current_pattern = new_pattern;
            m_pattern_bar = 0; // РўРµРєСѓС‰РёР№ Р±Р°СЂ
            
            // Р’С‹РІРѕРґ РёРЅС„РѕСЂРјР°С†РёРё Рѕ РЅР°Р№РґРµРЅРЅРѕРј РїР°С‚С‚РµСЂРЅ С‚РѕР»СЊРєРѕ РµСЃР»Рё РЅРµ РѕРїС‚РёРјРёР·Р°С†РёСЏ
            if(m_current_pattern != PATTERN_NONE && (!m_optimize_for_testing || !(bool)MQLInfoInteger(MQL_OPTIMIZATION)))
            {
               string pattern_names[] = {"РќРµС‚", "Р‘С‹С‡РёР№ РїРёРЅ-Р±Р°СЂ", "РњРµРґРІРµР¶РёР№ РїРёРЅ-Р±Р°СЂ", "Р‘С‹С‡СЊРµ РїРѕРіР»РѕС‰РµРЅРёРµ", "РњРµРґРІРµР¶СЊРµ РїРѕРіР»РѕС‰РµРЅРёРµ", 
                                         "Р’РЅСѓС‚СЂРµРЅРЅРёР№ Р±Р°СЂ", "Р’РЅРµС€РЅРёР№ Р±Р°СЂ", "Р”РѕРґР¶Рё", "Р‘С‹С‡РёР№ С‚СЂРµС…Р±Р°СЂРЅС‹Р№ РїР°С‚С‚РµСЂРЅ", "РњРµРґРІРµР¶РёР№ С‚СЂРµС…Р±Р°СЂРЅС‹Р№ РїР°С‚С‚РµСЂРЅ", 
                                         "РџРёРЅС†РµС‚ (РґРЅРѕ)", "РџРёРЅС†РµС‚ (РІРµСЂС€РёРЅР°)", "Р“Р°СЂС‚Р»Рё", "Р‘Р°Р±РѕС‡РєР°", "Р›РµС‚СѓС‡Р°СЏ РјС‹С€СЊ", "РљСЂР°Р±"};
               Print("РћР±РЅР°СЂСѓР¶РµРЅ РїР°С‚С‚РµСЂРЅ: ", pattern_names[m_current_pattern]);
            }
         }
      }
      
      // РћР±РЅРѕРІР»РµРЅРёРµ РєР»СЋС‡РµРІС‹С… СѓСЂРѕРІРЅРµР№ - РІС‹РїРѕР»РЅСЏРµРј СЂРµР¶Рµ РґР»СЏ РѕРїС‚РёРјРёР·Р°С†РёРё
      if(UseKeyLevelAnalysis && (!m_optimize_for_testing || m_tick_counter % (m_analysis_update_frequency * 5) == 0))
      {
         UpdateKeyLevels();
      }
      
      // РђРЅР°Р»РёР· РґР°РЅРЅС‹С… РѕР±СЉРµРјРѕРІ С‚РѕСЂРіРѕРІ - РІС‹РїРѕР»РЅСЏРµРј СЂРµР¶Рµ РґР»СЏ РѕРїС‚РёРјРёР·Р°С†РёРё
      if(!m_optimize_for_testing || m_tick_counter % (m_analysis_update_frequency * 10) == 0)
      {
         AnalyzeVolumeData();
      }
      
      // РћР±РЅРѕРІР»РµРЅРёРµ С‚РѕС‡РµРє РѕРїС‚РёРјР°Р»СЊРЅРѕРіРѕ СѓСЃСЂРµРґРЅРµРЅРёСЏ
      if(UseSmartAveraging)
      {
         // РћР±РЅРѕРІР»СЏРµРј С‚РѕС‡РєРё РІ Р·Р°РІРёСЃРёРјРѕСЃС‚Рё РѕС‚ РЅР°Р»РёС‡РёСЏ РѕС‚РєСЂС‹С‚С‹С… РїРѕР·РёС†РёР№
         if(m_buy_positions_total > 0)
            CalculateOptimalAveragingPoints(1);  // Р”Р»СЏ Buy
            
         if(m_sell_positions_total > 0)
            CalculateOptimalAveragingPoints(-1); // Р”Р»СЏ Sell
      }
      
      // РћС‚РѕР±СЂР°Р¶РµРЅРёРµ РёРЅС„РѕСЂРјР°С†РёРё Рѕ РїСЂРѕРґРІРёРЅСѓС‚РѕРј Р°РЅР°Р»РёР·Рµ - С‚РѕР»СЊРєРѕ РµСЃР»Рё РЅРµ РѕРїС‚РёРјРёР·Р°С†РёСЏ
      if(m_show_advanced_info && (!m_optimize_for_testing || !(bool)MQLInfoInteger(MQL_OPTIMIZATION)))
      {
         ShowAdvancedAnalysisInfo();
      }
   }
}

// Р¤СѓРЅРєС†РёСЏ СЌРєСЃС‚СЂРµРЅРЅРѕРіРѕ Р·Р°РєСЂС‹С‚РёСЏ РІСЃРµС… РїРѕР·РёС†РёР№
void EmergencyCloseAllPositions()
{
   // Р—Р°РєСЂС‹С‚РёРµ РІСЃРµС… РїРѕР·РёС†РёР№ Buy
   for(int layer_index = 0; layer_index < 3; layer_index++)
   {
      for(int i = 1; i <= m_buy_positions_count[layer_index]; i++)
      {
         if(m_buy_tickets[layer_index][i] > 0)
         {
            if(PositionSelectByTicket(m_buy_tickets[layer_index][i]))
            {
               m_trade.SetExpertMagicNumber(m_magic + layer_index);
               if(m_trade.PositionClose(m_buy_tickets[layer_index][i], m_slippage))
               {
                  Print("Р­РєСЃС‚СЂРµРЅРЅРѕРµ Р·Р°РєСЂС‹С‚РёРµ Buy РїРѕР·РёС†РёРё #", m_buy_tickets[layer_index][i], " СѓСЃРїРµС€РЅРѕ");
               }
               else
               {
                  Print("РћС€РёР±РєР° РїСЂРё СЌРєСЃС‚СЂРµРЅРЅРѕРј Р·Р°РєСЂС‹С‚РёРё Buy РїРѕР·РёС†РёРё #", m_buy_tickets[layer_index][i], 
                        ": ", m_trade.ResultRetcode());
               }
            }
         }
      }
   }
   
   // Р—Р°РєСЂС‹С‚РёРµ РІСЃРµС… РїРѕР·РёС†РёР№ Sell
   for(int layer_index = 0; layer_index < 3; layer_index++)
   {
      for(int i = 1; i <= m_sell_positions_count[layer_index]; i++)
      {
         if(m_sell_tickets[layer_index][i] > 0)
         {
            if(PositionSelectByTicket(m_sell_tickets[layer_index][i]))
            {
               m_trade.SetExpertMagicNumber(m_magic + layer_index);
               if(m_trade.PositionClose(m_sell_tickets[layer_index][i], m_slippage))
               {
                  Print("Р­РєСЃС‚СЂРµРЅРЅРѕРµ Р·Р°РєСЂС‹С‚РёРµ Sell РїРѕР·РёС†РёРё #", m_sell_tickets[layer_index][i], " СѓСЃРїРµС€РЅРѕ");
               }
               else
               {
                  Print("РћС€РёР±РєР° РїСЂРё СЌРєСЃС‚СЂРµРЅРЅРѕРј Р·Р°РєСЂС‹С‚РёРё Sell РїРѕР·РёС†РёРё #", m_sell_tickets[layer_index][i], 
                        ": ", m_trade.ResultRetcode());
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| РћР±РЅРѕРІР»РµРЅРёРµ РґР°РЅРЅС‹С… РёРЅРґРёРєР°С‚РѕСЂРѕРІ                                     |
//+------------------------------------------------------------------+
void UpdateIndicators()
{
   // РћР±РЅРѕРІР»СЏРµРј РґР°РЅРЅС‹Рµ РёРЅРґРёРєР°С‚РѕСЂРѕРІ, РµСЃР»Рё РёСЃРїРѕР»СЊР·СѓРµС‚СЃСЏ С„РёР»СЊС‚СЂ С‚СЂРµРЅРґР°
   if(m_use_trend_filter)
   {
      if(m_trend_detection_method == 0 || m_trend_detection_method == 2) // ADX РёР»Рё РѕР±Р° РјРµС‚РѕРґР°
      {
         // РљРѕРїРёСЂРѕРІР°РЅРёРµ РґР°РЅРЅС‹С… ADX
         if(CopyBuffer(m_adx_handle, 0, 0, 3, m_adx_main) <= 0 ||
            CopyBuffer(m_adx_handle, 1, 0, 3, m_plus_di) <= 0 ||
            CopyBuffer(m_adx_handle, 2, 0, 3, m_minus_di) <= 0)
         {
            Print("РћС€РёР±РєР° РїРѕР»СѓС‡РµРЅРёСЏ РґР°РЅРЅС‹С… ADX: ", GetLastError());
            return;
         }
      }
      
      if(m_trend_detection_method == 1 || m_trend_detection_method == 2) // MA РёР»Рё РѕР±Р° РјРµС‚РѕРґР°
      {
         // РљРѕРїРёСЂРѕРІР°РЅРёРµ РґР°РЅРЅС‹С… MA
         if(CopyBuffer(m_ma_fast_handle, 0, 0, 3, m_ma_fast) <= 0 ||
            CopyBuffer(m_ma_slow_handle, 0, 0, 3, m_ma_slow) <= 0)
         {
            Print("РћС€РёР±РєР° РїРѕР»СѓС‡РµРЅРёСЏ РґР°РЅРЅС‹С… MA: ", GetLastError());
            return;
         }
      }
   }
   
   // РћР±РЅРѕРІР»СЏРµРј РґР°РЅРЅС‹Рµ ATR, РµСЃР»Рё РёСЃРїРѕР»СЊР·СѓРµС‚СЃСЏ Р°РґР°РїС‚РёРІРЅС‹Р№ СЂР°Р·РјРµСЂ СЃРµС‚РєРё
   if(m_use_adaptive_grid && m_use_atr_for_grid)
   {
      double atr_buffer[];
      if(CopyBuffer(m_atr_handle, 0, 0, 1, atr_buffer) <= 0)
      {
         Print("РћС€РёР±РєР° РїРѕР»СѓС‡РµРЅРёСЏ РґР°РЅРЅС‹С… ATR: ", GetLastError());
         return;
      }
   }
}

//+------------------------------------------------------------------+
//| РџРѕР»СѓС‡РµРЅРёРµ РёРЅС„РѕСЂРјР°С†РёРё Рѕ РїРѕР·РёС†РёСЏС…                                  |
//+------------------------------------------------------------------+
void GetPositionsInfo()
{
   // РЎР±СЂРѕСЃ РјР°СЃСЃРёРІРѕРІ РїРµСЂРµРґ Р·Р°РїРѕР»РЅРµРЅРёРµРј
   for(int layer_index = 0; layer_index < 3; layer_index++)
   {
      m_buy_positions_count[layer_index] = 0;
      m_sell_positions_count[layer_index] = 0;
      m_buy_lots_total[layer_index] = 0.0;
      m_sell_lots_total[layer_index] = 0.0;
   }
   
   // РћР±РЅРѕРІР»СЏРµРј РѕР±С‰РµРµ РєРѕР»РёС‡РµСЃС‚РІРѕ РїРѕР·РёС†РёР№
   UpdatePositionsTotal();
   
   // РџСЂРѕРІРµСЂРєР° РІСЃРµС… РѕС‚РєСЂС‹С‚С‹С… РїРѕР·РёС†РёР№
   int positions_total = PositionsTotal();
   
   for(int i = 0; i < positions_total; i++)
   {
      ulong ticket = PositionGetTicket(i);
      
      if(ticket > 0)
      {
         if(PositionGetTicket(i) == ticket)
         {
            string position_symbol = PositionGetString(POSITION_SYMBOL);
            long position_magic = PositionGetInteger(POSITION_MAGIC);
            long position_type = PositionGetInteger(POSITION_TYPE);
            double position_volume = PositionGetDouble(POSITION_VOLUME);
            double position_price = PositionGetDouble(POSITION_PRICE_OPEN);
            double position_sl = PositionGetDouble(POSITION_SL);
            double position_tp = PositionGetDouble(POSITION_TP);
            
            // РџСЂРѕРІРµСЂРєР°, РїСЂРёРЅР°РґР»РµР¶РёС‚ Р»Рё РїРѕР·РёС†РёСЏ СЌС‚РѕРјСѓ СЃРѕРІРµС‚РЅРёРєСѓ
            if(position_symbol == m_symbol)
            {
               for(int layer_index = 0; layer_index < 3; layer_index++)
               {
                  // РџСЂРѕРІРµСЂРєР° magic-РЅРѕРјРµСЂР° СЃР»РѕСЏ
                  if(position_magic == m_magic + layer_index)
                  {
                     if(position_type == POSITION_TYPE_BUY)
                     {
                        // РЈРІРµР»РёС‡РµРЅРёРµ СЃС‡РµС‚С‡РёРєР° Buy РїРѕР·РёС†РёР№ РґР»СЏ СЃР»РѕСЏ
                        m_buy_positions_count[layer_index]++;
                        
                        // РЎРѕС…СЂР°РЅРµРЅРёРµ РёРЅС„РѕСЂРјР°С†РёРё Рѕ РїРѕР·РёС†РёРё
                        m_buy_tickets[layer_index][m_buy_positions_count[layer_index]] = ticket;
                        m_buy_levels[layer_index][m_buy_positions_count[layer_index]] = position_price;
                        m_buy_lots[layer_index][m_buy_positions_count[layer_index]] = position_volume;
                        m_buy_sl[layer_index][m_buy_positions_count[layer_index]] = position_sl;
                        m_buy_tp[layer_index][m_buy_positions_count[layer_index]] = position_tp;
                        
                        // РЈС‡РµС‚ РѕР±С‰РµРіРѕ РѕР±СЉРµРјР° Buy РїРѕР·РёС†РёР№ РґР»СЏ СЃР»РѕСЏ
                        m_buy_lots_total[layer_index] += position_volume;
                     }
                     else if(position_type == POSITION_TYPE_SELL)
                     {
                        // РЈРІРµР»РёС‡РµРЅРёРµ СЃС‡РµС‚С‡РёРєР° Sell РїРѕР·РёС†РёР№ РґР»СЏ СЃР»РѕСЏ
                        m_sell_positions_count[layer_index]++;
                        
                        // РЎРѕС…СЂР°РЅРµРЅРёРµ РёРЅС„РѕСЂРјР°С†РёРё Рѕ РїРѕР·РёС†РёРё
                        m_sell_tickets[layer_index][m_sell_positions_count[layer_index]] = ticket;
                        m_sell_levels[layer_index][m_sell_positions_count[layer_index]] = position_price;
                        m_sell_lots[layer_index][m_sell_positions_count[layer_index]] = position_volume;
                        m_sell_sl[layer_index][m_sell_positions_count[layer_index]] = position_sl;
                        m_sell_tp[layer_index][m_sell_positions_count[layer_index]] = position_tp;
                        
                        // РЈС‡РµС‚ РѕР±С‰РµРіРѕ РѕР±СЉРµРјР° Sell РїРѕР·РёС†РёР№ РґР»СЏ СЃР»РѕСЏ
                        m_sell_lots_total[layer_index] += position_volume;
                     }
                  }
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| РћРїСЂРµРґРµР»РµРЅРёРµ СЌРєСЃС‚СЂРµРјР°Р»СЊРЅС‹С… С†РµРЅ РґР»СЏ 3-РіРѕ СЃР»РѕСЏ                       |
//+------------------------------------------------------------------+
void DetectExtremePrices()
{
   // РђРЅР°Р»РёР· С‚РѕР»СЊРєРѕ РµСЃР»Рё РІРєР»СЋС‡РµРЅ 3-Р№ СЃР»РѕР№
   if(!m_mode_enable_3_sloy)
      return;
      
   m_max_price = 0.0;
   m_min_price = 999999.0;
   m_max_price_index = 0;
   m_min_price_index = 0;
   
   // РџРѕРёСЃРє РјР°РєСЃРёРјР°Р»СЊРЅРѕР№ Рё РјРёРЅРёРјР°Р»СЊРЅРѕР№ С†РµРЅС‹ Р·Р° РїРѕСЃР»РµРґРЅРёРµ N Р±Р°СЂРѕРІ
   int history_bars = 100; // РљРѕР»РёС‡РµСЃС‚РІРѕ Р°РЅР°Р»РёР·РёСЂСѓРµРјС‹С… Р±Р°СЂРѕРІ
   
   for(int i = 0; i < history_bars; i++)
   {
      double high = iHigh(m_symbol, PERIOD_H1, i);
      double low = iLow(m_symbol, PERIOD_H1, i);
      
      if(high > m_max_price)
      {
         m_max_price = high;
         m_max_price_index = i;
      }
      
      if(low < m_min_price)
      {
         m_min_price = low;
         m_min_price_index = i;
      }
   }
   
   // Р Р°СЃС‡РµС‚ С‚РѕСЂРіРѕРІС‹С… СѓСЂРѕРІРЅРµР№ РґР»СЏ 3-РіРѕ СЃР»РѕСЏ
   double price_range = m_max_price - m_min_price;
   double trade_zone_height = price_range * m_pr_h_3_sl / 100.0;
   
   m_trade_high = m_min_price + trade_zone_height;
   m_trade_low = m_max_price - trade_zone_height;
   
   // РЎРѕР·РґР°РЅРёРµ РїСЂСЏРјРѕСѓРіРѕР»СЊРЅРёРєР° С‚РѕСЂРіРѕРІРѕР№ Р·РѕРЅС‹ 3-РіРѕ СЃР»РѕСЏ
   if(m_symbol == Symbol())
   {
      // РЈРґР°Р»РµРЅРёРµ СЃСѓС‰РµСЃС‚РІСѓСЋС‰РµРіРѕ РѕР±СЉРµРєС‚Р°
      ObjectDelete(0, m_trade_area_name);
      
      // РЎРѕР·РґР°РЅРёРµ РЅРѕРІРѕРіРѕ РїСЂСЏРјРѕСѓРіРѕР»СЊРЅРёРєР°
      ObjectCreate(0, m_trade_area_name, OBJ_RECTANGLE, 0, 
                  iTime(m_symbol, PERIOD_H1, history_bars - 1), m_trade_high, 
                  iTime(m_symbol, PERIOD_H1, 0), m_trade_low);
                  
      // РЈСЃС‚Р°РЅРѕРІРєР° СЃРІРѕР№СЃС‚РІ РѕР±СЉРµРєС‚Р°
      ObjectSetInteger(0, m_trade_area_name, OBJPROP_COLOR, m_color_trade_area_of_3rd_layer);
      ObjectSetInteger(0, m_trade_area_name, OBJPROP_FILL, true);
      ObjectSetInteger(0, m_trade_area_name, OBJPROP_BACK, true);
      ObjectSetInteger(0, m_trade_area_name, OBJPROP_WIDTH, 1);
      ObjectSetInteger(0, m_trade_area_name, OBJPROP_STYLE, STYLE_SOLID);
      ObjectSetString(0, m_trade_area_name, OBJPROP_TEXT, "Trade Area 3rd Layer");
   }
}

//+------------------------------------------------------------------+
//| РџРѕРёСЃРє РёРЅРґРµРєСЃР° РјР°РєСЃРёРјР°Р»СЊРЅРѕР№ С†РµРЅС‹                                  |
//+------------------------------------------------------------------+
int FindMaxPriceIndex(int layer_index, int dir)
{
   int result_index = 0;
   double max_price = 0.0;
   
   // РџРѕРёСЃРє РјР°РєСЃРёРјР°Р»СЊРЅРѕР№ С†РµРЅС‹ РІ РјР°СЃСЃРёРІРµ СѓСЂРѕРІРЅРµР№
   for(int i = 1; i <= (dir == 0 ? m_buy_positions_count[layer_index] : m_sell_positions_count[layer_index]); i++)
   {
      if(max_price <= (dir == 0 ? m_buy_levels[layer_index][i] : m_sell_levels[layer_index][i]))
      {
         max_price = (dir == 0 ? m_buy_levels[layer_index][i] : m_sell_levels[layer_index][i]);
         result_index = i;
      }
   }
   
   return result_index;
}

//+------------------------------------------------------------------+
//| РџРѕРёСЃРє РёРЅРґРµРєСЃР° РјРёРЅРёРјР°Р»СЊРЅРѕР№ С†РµРЅС‹                                   |
//+------------------------------------------------------------------+
int FindMinPriceIndex(int layer_index, int dir)
{
   int result_index = 0;
   double min_price = 1000000.0;
   
   // РџРѕРёСЃРє РјРёРЅРёРјР°Р»СЊРЅРѕР№ С†РµРЅС‹ РІ РјР°СЃСЃРёРІРµ СѓСЂРѕРІРЅРµР№
   for(int i = 1; i <= (dir == 0 ? m_buy_positions_count[layer_index] : m_sell_positions_count[layer_index]); i++)
   {
      if(min_price >= (dir == 0 ? m_buy_levels[layer_index][i] : m_sell_levels[layer_index][i]))
      {
         min_price = (dir == 0 ? m_buy_levels[layer_index][i] : m_sell_levels[layer_index][i]);
         result_index = i;
      }
   }
   
   return result_index;
}

//+------------------------------------------------------------------+
//| РџРѕР»СѓС‡РµРЅРёРµ СЃСЂРµРґРЅРµР№ С†РµРЅС‹ РґР»СЏ Р·Р°РґР°РЅРЅРѕРіРѕ РєРѕР»РёС‡РµСЃС‚РІР° РїРѕР·РёС†РёР№          |
//+------------------------------------------------------------------+
double GetAveragePrice(int layer_index, int dir)
{
   double summ_price = 0.0;
   double summ_lots = 0.0;
   
   // Р Р°СЃС‡РµС‚ СЃСЂРµРґРЅРµРІР·РІРµС€РµРЅРЅРѕР№ С†РµРЅС‹ РїРѕ РѕР±СЉРµРјСѓ Р»РѕС‚РѕРІ
   for(int i = 1; i <= (dir == 0 ? m_buy_positions_count[layer_index] : m_sell_positions_count[layer_index]); i++)
   {
      summ_price += (dir == 0 ? m_buy_levels[layer_index][i] * m_buy_lots[layer_index][i] : 
                               m_sell_levels[layer_index][i] * m_sell_lots[layer_index][i]);
      summ_lots += (dir == 0 ? m_buy_lots[layer_index][i] : m_sell_lots[layer_index][i]);
   }
   
   // РџСЂРµРґРѕС‚РІСЂР°С‰РµРЅРёРµ РґРµР»РµРЅРёСЏ РЅР° РЅРѕР»СЊ
   if(summ_lots == 0.0)
      return 0.0;
      
   return summ_price / summ_lots;
}

//+------------------------------------------------------------------+
//| РџРѕР»СѓС‡РµРЅРёРµ СЃР±Р°Р»Р°РЅСЃРёСЂРѕРІР°РЅРЅРѕР№ С†РµРЅС‹ РјРµР¶РґСѓ Buy Рё Sell РїРѕР·РёС†РёСЏРјРё       |
//+------------------------------------------------------------------+
double GetBalancedPrice(int layer_index)
{
   double buy_price_sum = 0.0;
   double buy_lots_sum = 0.0;
   double sell_price_sum = 0.0;
   double sell_lots_sum = 0.0;
   
   // Р Р°СЃС‡РµС‚ СЃСѓРјРј РґР»СЏ Buy РїРѕР·РёС†РёР№
   for(int i = 1; i <= m_buy_positions_count[layer_index]; i++)
   {
      buy_price_sum += m_buy_levels[layer_index][i] * m_buy_lots[layer_index][i];
      buy_lots_sum += m_buy_lots[layer_index][i];
   }
   
   // Р Р°СЃС‡РµС‚ СЃСѓРјРј РґР»СЏ Sell РїРѕР·РёС†РёР№
   for(int i = 1; i <= m_sell_positions_count[layer_index]; i++)
   {
      sell_price_sum += m_sell_levels[layer_index][i] * m_sell_lots[layer_index][i];
      sell_lots_sum += m_sell_lots[layer_index][i];
   }
   
   // РџСЂРµРґРѕС‚РІСЂР°С‰РµРЅРёРµ РґРµР»РµРЅРёСЏ РЅР° РЅРѕР»СЊ
   if(buy_lots_sum - sell_lots_sum == 0.0)
      return 0.0;
      
   return (buy_price_sum - sell_price_sum) / (buy_lots_sum - sell_lots_sum);
}

//+------------------------------------------------------------------+
//| РЎРѕР·РґР°РЅРёРµ Р»РёРЅРёРё СѓСЂРѕРІРЅСЏ                                            |
//+------------------------------------------------------------------+
void CreateLayerLine(string name, double price, color clr, int width = 1, int style = STYLE_SOLID)
{
   // Р’ СЂРµР¶РёРјРµ РѕРїС‚РёРјРёР·Р°С†РёРё РЅРµ СЃРѕР·РґР°РµРј РіСЂР°С„РёС‡РµСЃРєРёРµ РѕР±СЉРµРєС‚С‹ РґР»СЏ СѓСЃРєРѕСЂРµРЅРёСЏ
   if(m_optimize_for_testing && (bool)MQLInfoInteger(MQL_OPTIMIZATION))
      return;
      
   // РЈРґР°Р»РµРЅРёРµ РѕР±СЉРµРєС‚Р°, РµСЃР»Рё РѕРЅ СЃСѓС‰РµСЃС‚РІСѓРµС‚
   ObjectDelete(0, name);
   
   // РЎРѕР·РґР°РЅРёРµ РЅРѕРІРѕРіРѕ РіРѕСЂРёР·РѕРЅС‚Р°Р»СЊРЅРѕР№ Р»РёРЅРёРё
   ObjectCreate(0, name, OBJ_HLINE, 0, 0, price);
   ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
   ObjectSetInteger(0, name, OBJPROP_WIDTH, width);
   ObjectSetInteger(0, name, OBJPROP_STYLE, style);
}

//+------------------------------------------------------------------+
//| РЈРґР°Р»РµРЅРёРµ Р»РёРЅРёРё СѓСЂРѕРІРЅСЏ                                            |
//+------------------------------------------------------------------+
void DeleteLayerLine(string name)
{
   ObjectDelete(0, name);
}

//+------------------------------------------------------------------+
//| РћРїСЂРµРґРµР»РµРЅРёРµ Р°РєС‚РёРІРЅС‹С… СЃР»РѕРµРІ                                        |
//+------------------------------------------------------------------+
void DetermineActiveLayers()
{
   // Р Р°СЃС‡РµС‚ РїСЂРµРґРµР»Р° СЃРІРѕР±РѕРґРЅРѕР№ РјР°СЂР¶Рё
   m_free_margin_limit = AccountInfoDouble(ACCOUNT_BALANCE) * m_min_proc_sv_sr / 100.0;
   bool margin_limit_reached = (m_min_proc_sv_sr < 100.0 && AccountInfoDouble(ACCOUNT_MARGIN_FREE) < m_free_margin_limit);
   
   // РџРѕР»СѓС‡РµРЅРёРµ С‚РµРєСѓС‰РёС… С†РµРЅ
   double ask = SymbolInfoDouble(m_symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(m_symbol, SYMBOL_BID);
   m_mid_price = (ask + bid) / 2.0;
   
   // РЎР±СЂРѕСЃ Р°РєС‚РёРІРЅС‹С… СЃР»РѕРµРІ
   ArrayInitialize(m_active_layers, 0);
   
   // Р•СЃР»Рё РЅРµС‚ РѕС‚РєСЂС‹С‚С‹С… РїРѕР·РёС†РёР№ Рё РЅРµ РІ СЂРµР¶РёРјРµ РІС‹С…РѕРґР°, Р°РєС‚РёРІРёСЂСѓРµРј СЃР»РѕРё СЃРѕРіР»Р°СЃРЅРѕ m_torg_sloy
   if(m_buy_positions_count[0] == 0 && m_sell_positions_count[0] == 0 && 
      m_buy_positions_count[1] == 0 && m_sell_positions_count[1] == 0 && 
      m_buy_positions_count[2] == 0 && m_sell_positions_count[2] == 0 && 
      !m_exit_mode)
   {
      // РђРєС‚РёРІРёСЂСѓРµРј СЃР»РѕРё СЃРѕРіР»Р°СЃРЅРѕ РЅР°СЃС‚СЂРѕР№РєРµ РєРѕР»РёС‡РµСЃС‚РІР° С‚РѕСЂРіРѕРІС‹С… СЃР»РѕРµРІ
      m_active_layers[0] = 2; // Р’СЃРµРіРґР° Р°РєС‚РёРІРёСЂСѓРµРј РїРµСЂРІС‹Р№ СЃР»РѕР№
      
      if(m_torg_sloy >= 2) // Р•СЃР»Рё РёСЃРїРѕР»СЊР·СѓРµС‚СЃСЏ 2 РёР»Рё Р±РѕР»РµРµ СЃР»РѕРµРІ
         m_active_layers[1] = 2;
         
      if(m_torg_sloy >= 3) // Р•СЃР»Рё РёСЃРїРѕР»СЊР·СѓРµС‚СЃСЏ 3 СЃР»РѕСЏ
         m_active_layers[2] = 2;
      
      // Р•СЃР»Рё РёСЃРїРѕР»СЊР·СѓРµРј С„РёР»СЊС‚СЂ С‚СЂРµРЅРґР° Рё С‚РѕСЂРіСѓРµРј С‚РѕР»СЊРєРѕ РїРѕ С‚СЂРµРЅРґСѓ
      if(m_use_trend_filter && m_trade_only_with_trend && m_current_market_type != 0)
      {
         if(m_current_market_type == 1) // Р’РѕСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ
         {
            // Р Р°Р·СЂРµС€Р°РµРј С‚РѕР»СЊРєРѕ Buy РїРѕР·РёС†РёРё РЅР° РІСЃРµС… Р°РєС‚РёРІРЅС‹С… СЃР»РѕСЏС…
            for(int i=0; i<3; i++)
               if(m_active_layers[i] > 0)
                  m_active_layers[i] = 2;
         }
         else if(m_current_market_type == -1) // РќРёСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ
         {
            // Р Р°Р·СЂРµС€Р°РµРј С‚РѕР»СЊРєРѕ Sell РїРѕР·РёС†РёРё РЅР° РІСЃРµС… Р°РєС‚РёРІРЅС‹С… СЃР»РѕСЏС…
            for(int i=0; i<3; i++)
               if(m_active_layers[i] > 0)
                  m_active_layers[i] = 3;
         }
      }
   }
   else
   {
      // РђРєС‚РёРІР°С†РёСЏ СЃР»РѕРµРІ РЅР° РѕСЃРЅРѕРІРµ РЅР°Р»РёС‡РёСЏ РѕС‚РєСЂС‹С‚С‹С… РїРѕР·РёС†РёР№
      if(m_buy_positions_count[0] > 0 || m_sell_positions_count[0] > 0) m_active_layers[0] = 1;
      if(m_buy_positions_count[1] > 0 || m_sell_positions_count[1] > 0) m_active_layers[1] = 1;
      if(m_buy_positions_count[2] > 0 || m_sell_positions_count[2] > 0) m_active_layers[2] = 1;
      
      // РђРєС‚РёРІРёСЂСѓРµРј СЃР»РѕРё, РєРѕС‚РѕСЂС‹Рµ СЂР°Р·СЂРµС€РµРЅС‹ РЅР°СЃС‚СЂРѕР№РєРѕР№, РґР°Р¶Рµ РµСЃР»Рё Сѓ РЅРёС… РЅРµС‚ РїРѕР·РёС†РёР№
      if(m_active_layers[0] == 0 && m_torg_sloy >= 1) m_active_layers[0] = 2;
      if(m_active_layers[1] == 0 && m_torg_sloy >= 2) m_active_layers[1] = 2;
      if(m_active_layers[2] == 0 && m_torg_sloy >= 3) m_active_layers[2] = 2;
      
      // РћРіСЂР°РЅРёС‡РµРЅРёРµ РѕС‚РєСЂС‹С‚РёСЏ РЅРѕРІС‹С… РїРѕР·РёС†РёР№ РїСЂРѕС‚РёРІ С‚СЂРµРЅРґР°
      if(m_use_trend_filter && m_trade_only_with_trend)
      {
         for(int layer_index = 0; layer_index < 3; layer_index++)
         {
            if(m_active_layers[layer_index] > 0)
            {
               if(m_current_market_type == 1) // Р’РѕСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ
               {
                  // Р Р°Р·СЂРµС€Р°РµРј С‚РѕР»СЊРєРѕ Buy РїРѕР·РёС†РёРё, Р·Р°РїСЂРµС‰Р°РµРј Sell
                  if(m_buy_positions_count[layer_index] == 0 && m_sell_positions_count[layer_index] > 0)
                     m_active_layers[layer_index] = 3; // РўРѕР»СЊРєРѕ Р·Р°РєСЂС‹С‚РёРµ Sell
                  else
                     m_active_layers[layer_index] = 2; // Р Р°Р·СЂРµС€РµРЅРѕ РѕС‚РєСЂС‹С‚РёРµ Buy
               }
               else if(m_current_market_type == -1) // РќРёСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ
               {
                  // Р Р°Р·СЂРµС€Р°РµРј С‚РѕР»СЊРєРѕ Sell РїРѕР·РёС†РёРё, Р·Р°РїСЂРµС‰Р°РµРј Buy
                  if(m_buy_positions_count[layer_index] > 0 && m_sell_positions_count[layer_index] == 0)
                     m_active_layers[layer_index] = 2; // РўРѕР»СЊРєРѕ Р·Р°РєСЂС‹С‚РёРµ Buy
                  else
                     m_active_layers[layer_index] = 3; // Р Р°Р·СЂРµС€РµРЅРѕ РѕС‚РєСЂС‹С‚РёРµ Sell
               }
            }
         }
      }
   }
   
   // РџСЂРѕРІРµСЂРєР° РґРѕСЃС‚Р°С‚РѕС‡РЅРѕСЃС‚Рё РјР°СЂР¶Рё
   if(margin_limit_reached)
   {
      // РћС‚РєР»СЋС‡РµРЅРёРµ СЃР»РѕРµРІ РїСЂРё РЅРµС…РІР°С‚РєРµ РјР°СЂР¶Рё
      if(m_torg_sloy == 1)
      {
         if(MathAbs(m_buy_lots_total[0] - m_sell_lots_total[0]) > 0.0) m_active_layers[0] = 0;
         if(MathAbs(m_buy_lots_total[1] - m_sell_lots_total[1]) > 0.0) m_active_layers[1] = 0;
         if(MathAbs(m_buy_lots_total[2] - m_sell_lots_total[2]) > 0.0) m_active_layers[2] = 0;
      }
   }
   
   // РЈРґР°Р»РµРЅРёРµ РЅРµР°РєС‚РёРІРЅС‹С… Р»РёРЅРёР№ СѓСЂРѕРІРЅРµР№
   if((m_buy_positions_count[0] == 0 && m_sell_positions_count[0] == 0) || m_active_layers[0] == 0)
   {
      DeleteLayerLine("NextBUY_0");
      DeleteLayerLine("NextSELL_0");
   }
   
   if((m_buy_positions_count[1] == 0 && m_sell_positions_count[1] == 0) || m_active_layers[1] == 0)
   {
      DeleteLayerLine("NextBUY_1");
      DeleteLayerLine("NextSELL_1");
   }
   
   if((m_buy_positions_count[2] == 0 && m_sell_positions_count[2] == 0) || m_active_layers[2] == 0)
   {
      DeleteLayerLine("NextBUY_2");
      DeleteLayerLine("NextSELL_2");
   }
}

//+------------------------------------------------------------------+
//| Р Р°СЃС‡РµС‚ СѓСЂРѕРІРЅРµР№ РѕС‚РєСЂС‹С‚РёСЏ Рё Р·Р°РєСЂС‹С‚РёСЏ РїРѕР·РёС†РёР№                        |
//+------------------------------------------------------------------+
void CalculateLevels()
{
   // РџСЂРѕРІРµСЂРєР° РІСЃРµС… СЃР»РѕРµРІ
   for(int layer_index = 0; layer_index < 3; layer_index++)
   {
      if(m_active_layers[layer_index] > 0)
      {
         // Р Р°СЃС‡РµС‚ Р°РґР°РїС‚РёРІРЅРѕРіРѕ СЂР°Р·РјРµСЂР° СЃРµС‚РєРё, РµСЃР»Рё РІРєР»СЋС‡РµРЅР° СЃРѕРѕС‚РІРµС‚СЃС‚РІСѓСЋС‰Р°СЏ РѕРїС†РёСЏ
         double buy_grid_step, sell_grid_step;
         
         if(m_use_adaptive_grid)
         {
            // РџРѕР»СѓС‡РµРЅРёРµ Р°РґР°РїС‚РёРІРЅРѕРіРѕ С€Р°РіР° СЃРµС‚РєРё
            CalcAdaptiveGridStep();
            
            // РСЃРїРѕР»СЊР·РѕРІР°РЅРёРµ СЂР°СЃСЃС‡РёС‚Р°РЅРЅРѕРіРѕ С€Р°РіР° СЃРµС‚РєРё
            buy_grid_step = m_current_grid_step;
            sell_grid_step = m_current_grid_step;
         }
         else
         {
            // РСЃРїРѕР»СЊР·РѕРІР°РЅРёРµ СЃС‚Р°РЅРґР°СЂС‚РЅС‹С… РЅР°СЃС‚СЂРѕРµРє С€Р°РіР° СЃРµС‚РєРё
            // Р‘Р°Р·РѕРІС‹Р№ С€Р°Рі СЃРµС‚РєРё РІ Р·Р°РІРёСЃРёРјРѕСЃС‚Рё РѕС‚ С‚РёРїР° СЂС‹РЅРєР°
            if(m_current_market_type == 0) // Р¤Р»СЌС‚
            {
               buy_grid_step = m_flat_mode_setka * m_point * m_point_digits;
               sell_grid_step = m_flat_mode_setka * m_point * m_point_digits;
            }
            else // РўСЂРµРЅРґ
            {
               buy_grid_step = m_trend_mode_setka * m_point * m_point_digits;
               sell_grid_step = m_trend_mode_setka * m_point * m_point_digits;
            }
            
            // Р Р°СЃС‡РµС‚ С€Р°РіР° СЃРµС‚РєРё СЃ СѓС‡РµС‚РѕРј СЂРµР¶РёРјР° СѓРІРµР»РёС‡РµРЅРёСЏ
            if(m_uvel_hsetky == 1) // Р РµР¶РёРј СѓРІРµР»РёС‡РµРЅРёСЏ С€Р°РіР° 1
            {
               if(m_buy_positions_count[layer_index] > 1)
                  buy_grid_step = (m_hsetky + m_shag_uvel_hsetky * (m_buy_positions_count[layer_index] - 1)) * m_point * m_point_digits;
                  
               if(m_sell_positions_count[layer_index] > 1)
                  sell_grid_step = (m_hsetky + m_shag_uvel_hsetky * (m_sell_positions_count[layer_index] - 1)) * m_point * m_point_digits;
            }
            else if(m_uvel_hsetky == 2) // Р РµР¶РёРј СѓРІРµР»РёС‡РµРЅРёСЏ С€Р°РіР° 2
            {
               if(m_buy_positions_count[layer_index] > 1)
                  buy_grid_step = (m_hsetky - m_shag_uvel_hsetky * (m_buy_positions_count[layer_index] - 1)) * m_point * m_point_digits;
                  
               if(m_sell_positions_count[layer_index] > 1)
                  sell_grid_step = (m_hsetky - m_shag_uvel_hsetky * (m_sell_positions_count[layer_index] - 1)) * m_point * m_point_digits;
            }
         }
         
         // РЈСЃС‚Р°РЅРѕРІРєР° РјРёРЅРёРјР°Р»СЊРЅРѕРіРѕ С€Р°РіР° СЃРµС‚РєРё
         if(buy_grid_step < 10 * m_point_digits * m_point) buy_grid_step = 10 * m_point_digits * m_point;
         if(sell_grid_step < 10 * m_point_digits * m_point) sell_grid_step = 10 * m_point_digits * m_point;
         
         // РЎРѕР·РґР°РЅРёРµ Р»РёРЅРёР№ СѓСЂРѕРІРЅРµР№ РґР»СЏ РѕС‚РєСЂС‹С‚РёСЏ РЅРѕРІС‹С… РїРѕР·РёС†РёР№
         color line_colors[3][2] = {
            {clrRed, clrBlue},
            {clrMagenta, clrTeal},
            {clrMaroon, clrNavy}
         };
         
         string line_names[3][2] = {
            {"NextBUY_0", "NextSELL_0"},
            {"NextBUY_1", "NextSELL_1"},
            {"NextBUY_2", "NextSELL_2"}
         };
         
         // РћС‚РѕР±СЂР°Р¶РµРЅРёРµ Р»РёРЅРёР№ СѓСЂРѕРІРЅРµР№ РІ Р·Р°РІРёСЃРёРјРѕСЃС‚Рё РѕС‚ РєРѕР»РёС‡РµСЃС‚РІР° РїРѕР·РёС†РёР№
         if(m_buy_positions_count[layer_index] > 0 || m_sell_positions_count[layer_index] > 0)
         {
            int buy_min_index = FindMinPriceIndex(layer_index, 0);
            int buy_max_index = FindMaxPriceIndex(layer_index, 0);
            int sell_min_index = FindMinPriceIndex(layer_index, 1);
            int sell_max_index = FindMaxPriceIndex(layer_index, 1);
            
            // РЎРѕР·РґР°РµРј Р»РёРЅРёРё СѓСЂРѕРІРЅРµР№ РѕС‚РєСЂС‹С‚РёСЏ РІ Р·Р°РІРёСЃРёРјРѕСЃС‚Рё РѕС‚ СЃРѕРѕС‚РЅРѕС€РµРЅРёСЏ РєРѕР»РёС‡РµСЃС‚РІР° РїРѕР·РёС†РёР№
            if(m_buy_positions_count[layer_index] > m_sell_positions_count[layer_index])
            {
               if(m_active_layers[layer_index] != 3) // РќРµ Р·Р°РїСЂРµС‰РµРЅРѕ РѕС‚РєСЂС‹С‚РёРµ Buy
               {
                  CreateLayerLine(line_names[layer_index][0], m_buy_levels[layer_index][buy_min_index] - buy_grid_step, 
                                  line_colors[layer_index][0], 1, 7);
               }
               
               if(m_active_layers[layer_index] != 2) // РќРµ Р·Р°РїСЂРµС‰РµРЅРѕ РѕС‚РєСЂС‹С‚РёРµ Sell
               {
                  CreateLayerLine(line_names[layer_index][1], m_buy_levels[layer_index][buy_max_index] + sell_grid_step, 
                                  line_colors[layer_index][1], 1, 7);
               }
            }
            else if(m_buy_positions_count[layer_index] < m_sell_positions_count[layer_index])
            {
               if(m_active_layers[layer_index] != 3) // РќРµ Р·Р°РїСЂРµС‰РµРЅРѕ РѕС‚РєСЂС‹С‚РёРµ Buy
               {
                  CreateLayerLine(line_names[layer_index][0], m_sell_levels[layer_index][sell_min_index] - buy_grid_step, 
                                  line_colors[layer_index][0], 1, 7);
               }
               
               if(m_active_layers[layer_index] != 2) // РќРµ Р·Р°РїСЂРµС‰РµРЅРѕ РѕС‚РєСЂС‹С‚РёРµ Sell
               {
                  CreateLayerLine(line_names[layer_index][1], m_sell_levels[layer_index][sell_max_index] + sell_grid_step, 
                                  line_colors[layer_index][1], 1, 7);
               }
            }
            else if(m_buy_positions_count[layer_index] == m_sell_positions_count[layer_index] && 
                    m_buy_positions_count[layer_index] > 0)
            {
               if(m_active_layers[layer_index] != 3) // РќРµ Р·Р°РїСЂРµС‰РµРЅРѕ РѕС‚РєСЂС‹С‚РёРµ Buy
               {
                  CreateLayerLine(line_names[layer_index][0], m_buy_levels[layer_index][buy_min_index] - buy_grid_step, 
                                  line_colors[layer_index][0], 1, 7);
               }
               
               if(m_active_layers[layer_index] != 2) // РќРµ Р·Р°РїСЂРµС‰РµРЅРѕ РѕС‚РєСЂС‹С‚РёРµ Sell
               {
                  CreateLayerLine(line_names[layer_index][1], m_sell_levels[layer_index][sell_max_index] + sell_grid_step, 
                                  line_colors[layer_index][1], 1, 7);
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Р Р°СЃС‡РµС‚ Р°РґР°РїС‚РёРІРЅРѕРіРѕ СЂР°Р·РјРµСЂР° СЃРµС‚РєРё                                  |
//+------------------------------------------------------------------+
void CalcAdaptiveGridStep()
{
   // Р‘Р°Р·РѕРІС‹Р№ СЂР°Р·РјРµСЂ СЃРµС‚РєРё
   double base_grid_step = m_hsetky * m_point * m_point_digits;
   
   // РљРѕСЌС„С„РёС†РёРµРЅС‚ РґР»СЏ Р°РґР°РїС‚РёРІРЅРѕРіРѕ СЂР°Р·РјРµСЂР° СЃРµС‚РєРё
   double adaptive_multiplier = 1.0;
   
   // 1. РЈС‡РµС‚ РІРѕР»Р°С‚РёР»СЊРЅРѕСЃС‚Рё (ATR)
   if(m_use_atr_for_grid)
   {
      double atr_buffer[];
      
      // РџРѕР»СѓС‡РµРЅРёРµ Р·РЅР°С‡РµРЅРёСЏ ATR
      if(CopyBuffer(m_atr_handle, 0, 0, 1, atr_buffer) > 0)
      {
         double atr_value = atr_buffer[0];
         
         // Р•СЃР»Рё ATR СѓСЃРїРµС€РЅРѕ РїРѕР»СѓС‡РµРЅ, РёСЃРїРѕР»СЊР·СѓРµРј РµРіРѕ РґР»СЏ РєРѕСЂСЂРµРєС‚РёСЂРѕРІРєРё С€Р°РіР° СЃРµС‚РєРё
         if(atr_value > 0)
         {
            adaptive_multiplier *= (atr_value * m_atr_multiplier) / (m_point * m_point_digits);
         }
      }
   }
   
   // 2. РЈС‡РµС‚ С‚РµРєСѓС‰РµР№ С‚РѕСЂРіРѕРІРѕР№ СЃРµСЃСЃРёРё
   if(m_session_based_settings)
   {
      // РћР±РЅРѕРІР»РµРЅРёРµ РёРЅС„РѕСЂРјР°С†РёРё Рѕ С‚РµРєСѓС‰РµР№ СЃРµСЃСЃРёРё
      m_current_session = (ENUM_MARKET_SESSION)DetectCurrentSession();
      
      switch(m_current_session)
      {
         case SESSION_ASIAN:
            adaptive_multiplier *= m_asian_session_multiplier;
            break;
         case SESSION_EUROPEAN:
            adaptive_multiplier *= m_european_session_multiplier;
            break;
         case SESSION_AMERICAN:
            adaptive_multiplier *= m_american_session_multiplier;
            break;
      }
   }
   
   // 3. РЈС‡РµС‚ С‚РёРїР° РёРЅСЃС‚СЂСѓРјРµРЅС‚Р°
   if(m_auto_adjust_for_symbol)
   {
      switch((ENUM_SYMBOL_TYPE)m_symbol_type)
      {
         case TYPE_FOREX_MAJOR:
            adaptive_multiplier *= m_forex_major_multiplier;
            break;
         case TYPE_FOREX_CROSS:
            adaptive_multiplier *= m_forex_cross_multiplier;
            break;
         case TYPE_COMMODITY:
            adaptive_multiplier *= m_commodity_multiplier;
            break;
         case TYPE_INDEX:
            adaptive_multiplier *= m_index_multiplier;
            break;
      }
   }
   
   // 4. РЈС‡РµС‚ С‚РёРїР° СЂС‹РЅРєР° (С‚СЂРµРЅРґ/С„Р»СЌС‚)
   if(m_current_market_type != 0) // Р•СЃР»Рё С‚СЂРµРЅРґ
   {
      base_grid_step = m_trend_mode_setka * m_point * m_point_digits;
   }
   else // Р•СЃР»Рё С„Р»СЌС‚
   {
      base_grid_step = m_flat_mode_setka * m_point * m_point_digits;
   }
   
   // Р Р°СЃС‡РµС‚ РёС‚РѕРіРѕРІРѕРіРѕ Р°РґР°РїС‚РёРІРЅРѕРіРѕ С€Р°РіР° СЃРµС‚РєРё
   m_current_grid_step = base_grid_step * adaptive_multiplier;
   
   // РћРіСЂР°РЅРёС‡РµРЅРёРµ РјРёРЅРёРјР°Р»СЊРЅРѕРіРѕ С€Р°РіР° СЃРµС‚РєРё
   if(m_current_grid_step < 10 * m_point_digits * m_point)
      m_current_grid_step = 10 * m_point_digits * m_point;
}

//+------------------------------------------------------------------+
//| РћРїСЂРµРґРµР»РµРЅРёРµ СЃРёР»С‹ С‚РѕСЂРіРѕРІРѕРіРѕ СЃРёРіРЅР°Р»Р°                               |
//+------------------------------------------------------------------+
double DetermineSignalStrength(int direction)
{
   // Р‘Р°Р·РѕРІР°СЏ СЃРёР»Р° СЃРёРіРЅР°Р»Р° (РѕС‚ 0.0 РґРѕ 1.0)
   double signal_strength = 0.5;
   
   // 1. РЈС‡РµС‚ С‚СЂРµРЅРґР°
   if(m_use_trend_filter && m_current_market_type != 0)
   {
      if((m_current_market_type == 1 && direction == 0) || // Р’РѕСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ РїСЂРё РїРѕРєСѓРїРєРµ
         (m_current_market_type == -1 && direction == 1))  // РќРёСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ РїСЂРё РїСЂРѕРґР°Р¶Рµ
      {
         // РўРѕСЂРіРѕРІР»СЏ РїРѕ С‚СЂРµРЅРґСѓ - СѓРІРµР»РёС‡РёРІР°РµРј СЃРёР»Сѓ СЃРёРіРЅР°Р»Р°
         signal_strength += 0.2 * (m_market_structure.trend_strength / 100.0);
      }
      else
      {
         // РўРѕСЂРіРѕРІР»СЏ РїСЂРѕС‚РёРІ С‚СЂРµРЅРґР° - СѓРјРµРЅСЊС€Р°РµРј СЃРёР»Сѓ СЃРёРіРЅР°Р»Р°
         signal_strength -= 0.1 * (m_market_structure.trend_strength / 100.0);
      }
   }
   
   // 2. РЈС‡РµС‚ РІРѕР»Р°С‚РёР»СЊРЅРѕСЃС‚Рё
   if(m_use_adaptive_grid && m_use_atr_for_grid && m_atr_handle != INVALID_HANDLE)
   {
      double atr_buffer[];
      if(CopyBuffer(m_atr_handle, 0, 0, 1, atr_buffer) > 0)
      {
         double avg_atr = atr_buffer[0];
         double atr_normalized = avg_atr / (m_point * m_point_digits * 10); // РЅРѕСЂРјР°Р»РёР·Р°С†РёСЏ Рє "СЃС‚Р°РЅРґР°СЂС‚РЅС‹Рј" СѓСЃР»РѕРІРёСЏРј
         
         if(atr_normalized > 1.5) // Р’С‹СЃРѕРєР°СЏ РІРѕР»Р°С‚РёР»СЊРЅРѕСЃС‚СЊ
         {
            signal_strength -= 0.1; // РЎРЅРёР¶Р°РµРј СЃРёР»Сѓ СЃРёРіРЅР°Р»Р° РїСЂРё РІС‹СЃРѕРєРѕР№ РІРѕР»Р°С‚РёР»СЊРЅРѕСЃС‚Рё
         }
         else if(atr_normalized < 0.7) // РќРёР·РєР°СЏ РІРѕР»Р°С‚РёР»СЊРЅРѕСЃС‚СЊ
         {
            signal_strength += 0.1; // РЈРІРµР»РёС‡РёРІР°РµРј СЃРёР»Сѓ СЃРёРіРЅР°Р»Р° РїСЂРё РЅРёР·РєРѕР№ РІРѕР»Р°С‚РёР»СЊРЅРѕСЃС‚Рё
         }
      }
   }
   
   // 3. РЈС‡РµС‚ СЃС‚СЂСѓРєС‚СѓСЂС‹ СЂС‹РЅРєР°
   if(m_use_market_structure)
   {
      switch(m_market_structure.type)
      {
         case STRUCTURE_UPTREND:
            if(direction == 0) signal_strength += 0.15; // РЈСЃРёР»РµРЅРёРµ СЃРёРіРЅР°Р»Р° РїРѕРєСѓРїРєРё РІ РІРѕСЃС…РѕРґСЏС‰РµРј С‚СЂРµРЅРґРµ
            else signal_strength -= 0.15; // РћСЃР»Р°Р±Р»РµРЅРёРµ СЃРёРіРЅР°Р»Р° РїСЂРѕРґР°Р¶Рё РІ РІРѕСЃС…РѕРґСЏС‰РµРј С‚СЂРµРЅРґРµ
            break;
            
         case STRUCTURE_DOWNTREND:
            if(direction == 1) signal_strength += 0.15; // РЈСЃРёР»РµРЅРёРµ СЃРёРіРЅР°Р»Р° РїСЂРѕРґР°Р¶Рё РІ РЅРёСЃС…РѕРґСЏС‰РµРј С‚СЂРµРЅРґРµ
            else signal_strength -= 0.15; // РћСЃР»Р°Р±Р»РµРЅРёРµ СЃРёРіРЅР°Р»Р° РїРѕРєСѓРїРєРё РІ РЅРёСЃС…РѕРґСЏС‰РµРј С‚СЂРµРЅРґРµ
            break;
            
         case STRUCTURE_RANGING:
            signal_strength += 0.05; // РќРµР±РѕР»СЊС€РѕРµ СѓСЃРёР»РµРЅРёРµ Р»СЋР±С‹С… СЃРёРіРЅР°Р»РѕРІ РІ Р±РѕРєРѕРІРёРєРµ
            break;
            
         case STRUCTURE_REVERSAL_UP:
            if(direction == 0) signal_strength += 0.25; // Р—РЅР°С‡РёС‚РµР»СЊРЅРѕРµ СѓСЃРёР»РµРЅРёРµ СЃРёРіРЅР°Р»Р° РїРѕРєСѓРїРєРё РїСЂРё СЂР°Р·РІРѕСЂРѕС‚Рµ РІРІРµСЂС…
            break;
            
         case STRUCTURE_REVERSAL_DOWN:
            if(direction == 1) signal_strength += 0.25; // Р—РЅР°С‡РёС‚РµР»СЊРЅРѕРµ СѓСЃРёР»РµРЅРёРµ СЃРёРіРЅР°Р»Р° РїСЂРѕРґР°Р¶Рё РїСЂРё СЂР°Р·РІРѕСЂРѕС‚Рµ РІРЅРёР·
            break;
      }
   }
   
   // 4. РЈС‡РµС‚ РїР°С‚С‚РµСЂРЅРѕРІ
   if(UsePatternRecognition && m_current_pattern != PATTERN_NONE)
   {
      // РЈСЃРёР»РµРЅРёРµ СЃРёРіРЅР°Р»Р° РїСЂРё РЅР°Р»РёС‡РёРё РїРѕРґС‚РІРµСЂР¶РґР°СЋС‰РµРіРѕ РїР°С‚С‚РµСЂРЅР°
      switch(m_current_pattern)
      {
         case PATTERN_PINBAR_BULL:
         case PATTERN_PINBAR_BEAR:
         case PATTERN_ENGULFING_BULL:
         case PATTERN_ENGULFING_BEAR:
         case PATTERN_INSIDE_BAR:
         case PATTERN_OUTSIDE_BAR:
         case PATTERN_DOJI:
         case PATTERN_THREE_BAR_BULL:
         case PATTERN_THREE_BAR_BEAR:
         case PATTERN_TWEEZER_BOTTOM:
         case PATTERN_TWEEZER_TOP:
            signal_strength += 0.25;
            break;
      }
   }
   
   // 5. РЈС‡РµС‚ РїРѕРґС‚РІРµСЂР¶РґРµРЅРёР№ СЃ РґСЂСѓРіРёС… С‚Р°Р№РјС„СЂРµР№РјРѕРІ
   if(UseMultiTimeframeAnalysis)
   {
      // РџСЂРѕРІРµСЂСЏРµРј РїРѕРґС‚РІРµСЂР¶РґРµРЅРёРµ СЃРёРіРЅР°Р»Р° РЅР° СЂР°Р·РЅС‹С… С‚Р°Р№РјС„СЂРµР№РјР°С…
      int mtf_confirmations = 0;
      
      // РџРѕРёСЃРє С‚РѕС‡РєРё СѓСЃСЂРµРґРЅРµРЅРёСЏ РёР»Рё РєР»СЋС‡РµРІРѕРіРѕ СѓСЂРѕРІРЅСЏ, Р±Р»РёР·РєРѕРіРѕ Рє С‚РµРєСѓС‰РµР№ С†РµРЅРµ
      double current_price = (direction == 0) ? 
                           SymbolInfoDouble(m_symbol, SYMBOL_ASK) : 
                           SymbolInfoDouble(m_symbol, SYMBOL_BID);
                           
      for(int i = 0; i < m_max_averaging_points; i++)
      {
         if(m_averaging_points[i].price > 0)
         {
            double price_distance = MathAbs(current_price - m_averaging_points[i].price);
            double price_zone = 30 * m_point * m_point_digits; // Р—РѕРЅР° РІ 30 РїСѓРЅРєС‚РѕРІ
            
            if(price_distance <= price_zone)
            {
               // РЈС‡РµС‚ РїРѕРґС‚РІРµСЂР¶РґРµРЅРёР№ СЃ СЂР°Р·РЅС‹С… С‚Р°Р№РјС„СЂРµР№РјРѕРІ
               if(m_averaging_points[i].confirmed_h1) mtf_confirmations++;
               if(m_averaging_points[i].confirmed_h4) mtf_confirmations++;
               if(m_averaging_points[i].confirmed_d1) mtf_confirmations++;
               
               // РЈСЃРёР»РµРЅРёРµ СЃРёРіРЅР°Р»Р° РЅР° РѕСЃРЅРѕРІРµ РєРѕР»РёС‡РµСЃС‚РІР° РїРѕРґС‚РІРµСЂР¶РґРµРЅРёР№
               signal_strength += mtf_confirmations * 0.1;
               break; // РСЃРїРѕР»СЊР·СѓРµРј РїРµСЂРІСѓСЋ РЅР°Р№РґРµРЅРЅСѓСЋ РїРѕРґС…РѕРґСЏС‰СѓСЋ С‚РѕС‡РєСѓ
            }
         }
      }
   }
   
   // РћРіСЂР°РЅРёС‡РµРЅРёРµ СЃРёР»С‹ СЃРёРіРЅР°Р»Р° РІ РїСЂРµРґРµР»Р°С… [0.1, 1.0]
   if(signal_strength < 0.1) signal_strength = 0.1;
   if(signal_strength > 1.0) signal_strength = 1.0;
   
   return signal_strength;
}

//+------------------------------------------------------------------+
//| Р Р°СЃС‡РµС‚ СѓРјРЅРѕРіРѕ Р»РѕС‚ РЅР° РѕСЃРЅРѕРІРµ СЃРёР»С‹ СЃРёРіРЅР°Р»Р°                     |
//+------------------------------------------------------------------+
double CalculateAdaptiveLot(int direction, int layer)
{
   if(!m_use_adaptive_mm)
      return m_lot; // Р•СЃР»Рё Р°РґР°РїС‚РёРІРЅС‹Р№ MM РѕС‚РєР»СЋС‡РµРЅ, РІРѕР·РІСЂР°С‰Р°РµРј Р±Р°Р·РѕРІС‹Р№ Р»РѕС‚
      
   double base_lot = m_lot;
   double adaptive_lot = base_lot;
   
   // РџСЂРёРјРµРЅРµРЅРёРµ РЅР°СЃС‚СЂРѕРµРє РїСЂРѕС„РёР»СЏ С‚РµРєСѓС‰РµР№ СЃРµСЃСЃРёРё
   if(m_use_session_profiles)
   {
      // РџСЂРёРјРµРЅСЏРµРј РјРЅРѕР¶РёС‚РµР»СЊ Р»РѕС‚Р° С‚РµРєСѓС‰РµР№ СЃРµСЃСЃРёРё
      adaptive_lot *= m_current_profile.lot_multiplier;
   }
   
   // РџРѕР»СѓС‡Р°РµРј С‚РµРєСѓС‰РёРµ С†РµРЅС‹
   double current_price = (direction == 0) ? 
                         SymbolInfoDouble(m_symbol, SYMBOL_ASK) : 
                         SymbolInfoDouble(m_symbol, SYMBOL_BID);
   
   // РћРїСЂРµРґРµР»РµРЅРёРµ СЃРёР»С‹ СЃРёРіРЅР°Р»Р°
   double signal_strength = DetermineSignalStrength(direction);
   
   // 1. РљРѕСЂСЂРµРєС‚РёСЂСѓРµРј Р»РѕС‚ РїСЂРё С‚РѕСЂРіРѕРІР»Рµ РїСЂРѕС‚РёРІ С‚СЂРµРЅРґР°
   if(m_use_trend_filter && m_current_market_type != 0)
   {
      // Р•СЃР»Рё РЅР°РїСЂР°РІР»РµРЅРёРµ С‚РѕСЂРіРѕРІР»Рё РїСЂРѕС‚РёРІ С‚СЂРµРЅРґР°, СѓРјРµРЅСЊС€Р°РµРј Р»РѕС‚
      if((m_current_market_type == 1 && direction == 1) || // Р’РѕСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ, РїС‹С‚Р°РµРјСЃСЏ РїСЂРѕРґР°РІР°С‚СЊ
         (m_current_market_type == -1 && direction == 0))  // РќРёСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ, РїС‹С‚Р°РµРјСЃСЏ РїРѕРєСѓРїР°С‚СЊ
      {
         adaptive_lot *= m_trend_against_reduction;
         Print("РЈРјРµРЅСЊС€РµРЅРёРµ Р»РѕС‚Р° РёР·-Р·Р° С‚РѕСЂРіРѕРІР»Рё РїСЂРѕС‚РёРІ С‚СЂРµРЅРґР°: ", DoubleToString(adaptive_lot, 2));
      }
   }
   
   // 2. РС‰РµРј С‚РѕС‡РєРё СѓСЃСЂРµРґРЅРµРЅРёСЏ СЂСЏРґРѕРј СЃ С‚РµРєСѓС‰РµР№ С†РµРЅРѕР№
   double best_probability = 0.0;
   double best_lot_coef = 1.0;
   bool signal_found = false;
   
   for(int i = 0; i < m_max_averaging_points; i++)
   {
      if(m_averaging_points[i].price > 0 && m_averaging_points[i].probability >= m_min_probability_for_entry)
      {
         // РџСЂРѕРІРµСЂСЏРµРј, РЅР°С…РѕРґРёС‚СЃСЏ Р»Рё С‚РѕС‡РєР° СѓСЃСЂРµРґРЅРµРЅРёСЏ РІ Р·РѕРЅРµ С‚РµРєСѓС‰РµР№ С†РµРЅС‹
         double price_distance = MathAbs(current_price - m_averaging_points[i].price);
         double price_zone = 50 * m_point * m_point_digits; // Р—РѕРЅР° РІ 50 РїСѓРЅРєС‚РѕРІ РѕС‚ С‚РµРєСѓС‰РµР№ С†РµРЅС‹
         
         if(price_distance <= price_zone)
         {
            // Р•СЃР»Рё СЌС‚Рѕ С‚РѕС‡РєР° РґР»СЏ РїРѕРєСѓРїРєРё (РЅРёР¶Рµ С‚РµРєСѓС‰РµР№ С†РµРЅС‹) РёР»Рё РґР»СЏ РїСЂРѕРґР°Р¶Рё (РІС‹С€Рµ С‚РµРєСѓС‰РµР№ С†РµРЅС‹)
            if((direction == 0 && m_averaging_points[i].price <= current_price) || 
               (direction == 1 && m_averaging_points[i].price >= current_price))
            {
               if(m_averaging_points[i].probability > best_probability)
               {
                  best_probability = m_averaging_points[i].probability;
                  best_lot_coef = m_averaging_points[i].lot_coef;
                  signal_found = true;
               }
            }
         }
      }
   }
   
   // 3. РџСЂРёРјРµРЅРµРЅРёРµ СЃРёР»С‹ СЃРёРіРЅР°Р»Р° Рє СЂР°Р·РјРµСЂСѓ Р»РѕС‚Р°
   adaptive_lot *= signal_strength;
   
   // 4. Р”РѕРїРѕР»РЅРёС‚РµР»СЊРЅР°СЏ РєРѕСЂСЂРµРєС‚РёСЂРѕРІРєР°, РµСЃР»Рё РЅР°Р№РґРµРЅ СЃРёР»СЊРЅС‹Р№ СЃРёРіРЅР°Р»
   if(signal_found && best_probability >= m_min_probability_for_entry)
   {
      // Р‘Р°Р·РѕРІР°СЏ РєРѕСЂСЂРµРєС‚РёСЂРѕРІРєР° РЅР° РѕСЃРЅРѕРІРµ РєРѕСЌС„С„РёС†РёРµРЅС‚Р° РёР· С‚РѕС‡РєРё СѓСЃСЂРµРґРЅРµРЅРёСЏ
      adaptive_lot *= best_lot_coef;
      
      // Р”РѕРїРѕР»РЅРёС‚РµР»СЊРЅР°СЏ РєРѕСЂСЂРµРєС‚РёСЂРѕРІРєР° РЅР° РѕСЃРЅРѕРІРµ РІРµСЂРѕСЏС‚РЅРѕСЃС‚Рё
      double probability_boost = (best_probability - m_min_probability_for_entry) / 10.0 * m_probability_lot_boost;
      adaptive_lot *= (1.0 + probability_boost);
      
      Print("РђРґР°РїС‚РёРІРЅС‹Р№ MM: СЃРёР»Р° СЃРёРіРЅР°Р»Р° ", DoubleToString(signal_strength, 2),
            ", РІРµСЂРѕСЏС‚РЅРѕСЃС‚СЊ ", DoubleToString(best_probability, 1), 
            "%, РєРѕСЌС„. Р»РѕС‚Р° ", DoubleToString(best_lot_coef, 2), 
            ", РёС‚РѕРіРѕРІС‹Р№ Р»РѕС‚ ", DoubleToString(adaptive_lot, 2));
   }
   else
   {
      // Р•СЃР»Рё РЅРµ РЅР°Р№РґРµРЅ СЃРёР»СЊРЅС‹Р№ СЃРёРіРЅР°Р», РєРѕСЂСЂРµРєС‚РёСЂСѓРµРј С‚РѕР»СЊРєРѕ РЅР° РѕСЃРЅРѕРІРµ СЃРёР»С‹ СЃРёРіРЅР°Р»Р°
      Print("РђРґР°РїС‚РёРІРЅС‹Р№ MM: СЃРёР»Р° СЃРёРіРЅР°Р»Р° ", DoubleToString(signal_strength, 2),
            ", РЅРµ РЅР°Р№РґРµРЅ СЃРёР»СЊРЅС‹Р№ СЃРёРіРЅР°Р», РёС‚РѕРіРѕРІС‹Р№ Р»РѕС‚ ", DoubleToString(adaptive_lot, 2));
   }
   
   // 5. Р•СЃР»Рё РѕС‚РєСЂС‹РІР°РµРј РїРµСЂРІСѓСЋ РїРѕР·РёС†РёСЋ РІ СЃР»РѕРµ, РјРѕР¶РµРј РЅРµРјРЅРѕРіРѕ СѓРІРµР»РёС‡РёС‚СЊ Р»РѕС‚
   if((direction == 0 && m_buy_positions_count[layer] == 0) ||
      (direction == 1 && m_sell_positions_count[layer] == 0))
   {
      adaptive_lot *= 1.1;
      Print("РђРґР°РїС‚РёРІРЅС‹Р№ MM: СѓРІРµР»РёС‡РµРЅРёРµ Р»РѕС‚Р° РґР»СЏ РїРµСЂРІРѕР№ РїРѕР·РёС†РёРё РІ СЃР»РѕРµ: ", DoubleToString(adaptive_lot, 2));
   }
   
   // 6. РџСЂРёРјРµРЅРµРЅРёРµ РіР»РѕР±Р°Р»СЊРЅРѕРіРѕ РјРЅРѕР¶РёС‚РµР»СЏ СЃРёР»С‹ СЃРёРіРЅР°Р»Р° РґР»СЏ РєРѕРЅС‚СЂРѕР»СЏ РѕР±С‰РµРіРѕ СЌС„С„РµРєС‚Р°
   adaptive_lot = base_lot + (adaptive_lot - base_lot) * m_signal_strength_multiplier;
   
   // 7. РќРѕСЂРјР°Р»РёР·Р°С†РёСЏ Р»РѕС‚Р° РІ СЃРѕРѕС‚РІРµС‚СЃС‚РІРёРё СЃ РѕРіСЂР°РЅРёС‡РµРЅРёСЏРјРё
   double volume_step = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_STEP);
   adaptive_lot = NormalizeDouble(MathRound(adaptive_lot / volume_step) * volume_step, 8);
   
   // РџСЂРѕРІРµСЂРєР° РЅР° РјРёРЅРёРјР°Р»СЊРЅС‹Р№/РјР°РєСЃРёРјР°Р»СЊРЅС‹Р№ Р»РѕС‚
   if(adaptive_lot < m_min_lot)
      adaptive_lot = m_min_lot;
      
   if(adaptive_lot > m_max_lot)
      adaptive_lot = m_max_lot;
   
   return adaptive_lot;
}

//+------------------------------------------------------------------+
//| РћС‚РєСЂС‹С‚РёРµ РїРѕР·РёС†РёРё                                                 |
//+------------------------------------------------------------------+
bool OpenPosition(string symbol, int type, double lot, double price, double sl, double tp, int magic, color clr)
{
   // РџРѕР»СѓС‡Р°РµРј РїР°СЂР°РјРµС‚СЂС‹ РѕР±СЉРµРј РґР»СЏ РґР°РЅРЅРѕРіРѕ РёРЅСЃС‚СЂСѓРјРµРЅС‚Р°
   double volume_step = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);
   double min_volume = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
   double max_volume = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MAX);
   
   // РљРѕСЂСЂРµРєС‚РёСЂРѕРІРєР° СЂР°Р·РјРµСЂР° Р»РѕС‚Р° РІ СЃРѕРѕС‚РІРµС‚СЃС‚РІРёРё СЃ С‚СЂРµР±РѕРІР°РЅРёСЏРјРё РёРЅСЃС‚СЂСѓРјРµРЅС‚Р°
   lot = NormalizeDouble(MathRound(lot / volume_step) * volume_step, 8);
   
   // РџСЂРѕРІРµСЂРєР° РЅР° РјРёРЅРёРјР°Р»СЊРЅС‹Р№ РѕР±СЉРµРј
   if(lot < min_volume)
      lot = min_volume;
   
   // РџСЂРѕРІРµСЂРєР° РЅР° РјР°РєСЃРёРјР°Р»СЊРЅС‹Р№ РѕР±СЉРµРј
   if(lot > max_volume)
      lot = max_volume;
   
   // РџСЂРѕРІРµСЂРєР°, С‡С‚Рѕ РѕР±СЉРµРј РЅРµ СЂР°РІРµРЅ РЅСѓР»СЋ
   if(lot <= 0)
      return false;
   
   // РџСЂРѕРІРµСЂРєР° РѕР±СЉРµРјР° РїРµСЂРµРґ РѕС‚РїСЂР°РІРєРѕР№ РѕСЂРґРµСЂР°
   if(lot < min_volume || lot > max_volume || 
      MathAbs(lot - MathRound(lot / volume_step) * volume_step) > 0.0000001)
   {
      Print("РќРµРІРѕР·РјРѕР¶РЅРѕ РѕС‚РєСЂС‹С‚СЊ РїРѕР·РёС†РёСЋ СЃ РѕР±СЉРµРјРѕРј ", lot, 
            " РґР»СЏ ", symbol, ". РњРёРЅРёРјСѓРј: ", min_volume, 
            ", РњР°РєСЃРёРјСѓРј: ", max_volume, ", РЁР°Рі: ", volume_step);
      return false;
   }

   // РџСЂРѕРІРµСЂРєР° РґРѕСЃС‚Р°С‚РѕС‡РЅРѕСЃС‚Рё РјР°СЂР¶Рё РїРµСЂРµРґ РѕС‚РєСЂС‹С‚РёРµРј РїРѕР·РёС†РёРё
   double margin_required = 0.0;
   
   // Р Р°СЃС‡РµС‚ С‚СЂРµР±СѓРµРјРѕР№ РјР°СЂР¶Рё РґР»СЏ РЅРѕРІРѕР№ РїРѕР·РёС†РёРё
   if(!OrderCalcMargin((type == 0 ? ORDER_TYPE_BUY : ORDER_TYPE_SELL), symbol, lot, (type == 0 ? SymbolInfoDouble(symbol, SYMBOL_ASK) : SymbolInfoDouble(symbol, SYMBOL_BID)), margin_required))
   {
      Print("РћС€РёР±РєР° СЂР°СЃС‡РµС‚Р° РјР°СЂР¶Рё РґР»СЏ ", symbol, " СЃ РѕР±СЉРµРјРѕРј ", DoubleToString(lot, 8));
      return false;
   }
   
   // РџСЂРѕРІРµСЂРєР° РґРѕСЃС‚Р°С‚РѕС‡РЅРѕСЃС‚Рё СЃРІРѕР±РѕРґРЅРѕР№ РјР°СЂР¶Рё
   double free_margin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
   if(free_margin < margin_required * 1.2) // Р”РѕР±Р°РІР»СЏРµРј 20% Р·Р°РїР°СЃ РґР»СЏ Р±РµР·РѕРїР°СЃРЅРѕСЃС‚Рё
   {
      Print("РќРµРґРѕСЃС‚Р°С‚РѕС‡РЅРѕ СЃРІРѕР±РѕРґРЅРѕР№ РјР°СЂР¶Рё РґР»СЏ РѕС‚РєСЂС‹С‚РёСЏ РїРѕР·РёС†РёРё ", symbol, " СЃ РѕР±СЉРµРјРѕРј ", 
            DoubleToString(lot, 8), ". РўСЂРµР±СѓРµС‚СЃСЏ: ", DoubleToString(margin_required, 2), 
            ", Р”РѕСЃС‚СѓРїРЅРѕ: ", DoubleToString(free_margin, 2));
      return false;
   }

   // РљРѕСЂСЂРµРєС‚РёСЂРѕРІРєР° СЃС‚РѕРї-Р»РѕСЃСЃР° Рё С‚РµР№Рє-РїСЂРѕС„РёС‚Р° СЃ СѓС‡РµС‚РѕРј РѕСЃРѕР±РµРЅРЅРѕСЃС‚РµР№ РёРЅСЃС‚СЂСѓРјРµРЅС‚Р°
   if(sl != 0.0 || tp != 0.0)
   {
      // РџРѕР»СѓС‡РµРЅРёРµ С‚РµРєСѓС‰РёС… С†РµРЅ Рё РїР°СЂР°РјРµС‚СЂРѕРІ РёРЅСЃС‚СЂСѓРјРµРЅС‚Р°
      double ask = SymbolInfoDouble(symbol, SYMBOL_ASK);
      double bid = SymbolInfoDouble(symbol, SYMBOL_BID);
      int digits = (int)SymbolInfoInteger(symbol, SYMBOL_DIGITS);
      double point = SymbolInfoDouble(symbol, SYMBOL_POINT);
      double stops_level = SymbolInfoInteger(symbol, SYMBOL_TRADE_STOPS_LEVEL) * point;
      
      // РљРѕСЂСЂРµРєС‚РёСЂРѕРІРєР° СЃС‚РѕРї-Р»РѕСЃСЃР°
      if(sl != 0.0)
      {
         if(type == 0) // Buy
         {
            double min_sl = bid - stops_level;
            if(sl > min_sl)
               sl = min_sl;
         }
         else // Sell
         {
            double min_sl = ask + stops_level;
            if(sl < min_sl)
               sl = min_sl;
         }
         sl = NormalizeDouble(sl, digits);
      }
      
      // РљРѕСЂСЂРµРєС‚РёСЂРѕРІРєР° С‚РµР№Рє-РїСЂРѕС„РёС‚Р°
      if(tp != 0.0)
      {
         if(type == 0) // Buy
         {
            double min_tp = ask + stops_level;
            if(tp < min_tp)
               tp = min_tp;
         }
         else // Sell
         {
            double min_tp = bid - stops_level;
            if(tp > min_tp)
               tp = min_tp;
         }
         tp = NormalizeDouble(tp, digits);
      }
   }

   // РќР°СЃС‚СЂРѕР№РєР° РѕР±СЉРµРєС‚Р° РґР»СЏ С‚РѕСЂРіРѕРІС‹С… РѕРїРµСЂР°С†РёР№
   m_trade.SetExpertMagicNumber(magic);
   m_trade.SetDeviationInPoints(m_slippage);
   m_trade.SetTypeFillingBySymbol(m_symbol);
   
   bool result = false;
   
   if(type == 0) // Buy
   {
      result = m_trade.Buy(lot, symbol, price, sl, tp, symbol + " MGK " + IntegerToString(magic) + 
               " - СЃР»РѕР№. в„– " + IntegerToString(magic - m_magic));
   }
   else if(type == 1) // Sell
   {
      result = m_trade.Sell(lot, symbol, price, sl, tp, symbol + " MGK " + IntegerToString(magic) + 
                " - СЃР»РѕР№. в„– " + IntegerToString(magic - m_magic));
   }
   
   // РРЅС„РѕСЂРјР°С†РёСЏ Рѕ СЂРµР·СѓР»СЊС‚Р°С‚Рµ РѕРїРµСЂР°С†РёРё
   if(result)
   {
      Print("РћС‚РєСЂС‹С‚Р° РїРѕР·РёС†РёСЏ ", (type == 0 ? "Buy" : "Sell"), " РґР»СЏ ", symbol, 
            ", Lot: ", DoubleToString(lot, 8), ", Magic: ", magic);
   }
   else
   {
      Print("РћС€РёР±РєР° РїСЂРё РѕС‚РєСЂС‹С‚РёРё РїРѕР·РёС†РёРё: ", m_trade.ResultRetcode(), 
            " РґР»СЏ ", symbol, " СЃ РѕР±СЉРµРјРѕРј ", DoubleToString(lot, 8));
   }
   
   // РћР¶РёРґР°РЅРёРµ РѕР±СЂР°Р±РѕС‚РєРё РѕСЂРґРµСЂР°
   Sleep(1000);
   
   return result;
}

//+------------------------------------------------------------------+
//| Р—Р°РєСЂС‹С‚РёРµ РїРѕР·РёС†РёР№ РѕРїСЂРµРґРµР»РµРЅРЅС‹С… СЃР»РѕСЏ Рё РЅР°РїСЂР°РІР»РµРЅРёСЏ                |
//+------------------------------------------------------------------+
void ClosePositions(int layer_index, int direction)
{
   if(direction == 0) // Buy РїРѕР·РёС†РёРё
   {
      for(int i = 1; i <= m_buy_positions_count[layer_index]; i++)
      {
         if(m_buy_tickets[layer_index][i] > 0)
         {
            if(PositionSelectByTicket(m_buy_tickets[layer_index][i]))
            {
               m_trade.PositionClose(m_buy_tickets[layer_index][i], m_slippage);
            }
         }
      }
   }
   else if(direction == 1) // Sell РїРѕР·РёС†РёРё
   {
      for(int i = 1; i <= m_sell_positions_count[layer_index]; i++)
      {
         if(m_sell_tickets[layer_index][i] > 0)
         {
            if(PositionSelectByTicket(m_sell_tickets[layer_index][i]))
            {
               m_trade.PositionClose(m_sell_tickets[layer_index][i], m_slippage);
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| РћР±СЂР°Р±РѕС‚РєР° С‚РѕСЂРіРѕРІР»Рё                                               |
//+------------------------------------------------------------------+
void ProcessTrading()
{
   // РџСЂРѕРІРµСЂРєР° РІСЃРµС… СЃР»РѕРµРІ
   for(int layer_index = 0; layer_index < 3; layer_index++)
   {
      if(m_active_layers[layer_index] > 0)
      {
         // РџРѕР»СѓС‡РµРЅРёРµ С‚РµРєСѓС‰РёС… С†РµРЅ
         double ask = SymbolInfoDouble(m_symbol, SYMBOL_ASK);
         double bid = SymbolInfoDouble(m_symbol, SYMBOL_BID);
         double current_price = (ask + bid) / 2.0;
         
         // РћРїСЂРµРґРµР»РµРЅРёРµ РёРЅРґРµРєСЃРѕРІ РјР°РєСЃРёРјР°Р»СЊРЅС‹С… Рё РјРёРЅРёРјР°Р»СЊРЅС‹С… С†РµРЅ
         int buy_max_index = 0;
         int buy_min_index = 0;
         int sell_max_index = 0;
         int sell_min_index = 0;
         
         if(m_buy_positions_count[layer_index] > 0)
         {
            buy_max_index = FindMaxPriceIndex(layer_index, 0);
            buy_min_index = FindMinPriceIndex(layer_index, 0);
         }
         
         if(m_sell_positions_count[layer_index] > 0)
         {
            sell_max_index = FindMaxPriceIndex(layer_index, 1);
            sell_min_index = FindMinPriceIndex(layer_index, 1);
         }
         
         // Р¤Р»Р°РіРё РґР»СЏ Р·Р°РєСЂС‹С‚РёСЏ Рё РѕС‚РєСЂС‹С‚РёСЏ РїРѕР·РёС†РёР№
         bool close_buy = false;
         bool close_sell = false;
         bool open_buy = false;
         bool open_sell = false;
         
         // Р Р°СЃС‡РµС‚ С€Р°РіР° СЃРµС‚РєРё
         double buy_grid_step = m_hsetky * m_point * m_point_digits;
         double sell_grid_step = m_hsetky * m_point * m_point_digits;
         
         // Р Р°СЃС‡РµС‚ С€Р°РіР° СЃРµС‚РєРё СЃ СѓС‡РµС‚РѕРј СЂРµР¶РёРјР° СѓРІРµР»РёС‡РµРЅРёСЏ
         if(m_uvel_hsetky == 1) // Р РµР¶РёРј СѓРІРµР»РёС‡РµРЅРёСЏ С€Р°РіР° 1
         {
            if(m_buy_positions_count[layer_index] < 2) buy_grid_step = m_hsetky * m_point * m_point_digits;
            if(m_sell_positions_count[layer_index] < 2) sell_grid_step = m_hsetky * m_point * m_point_digits;
            
            if(m_buy_positions_count[layer_index] > 1)
               buy_grid_step = (m_hsetky + m_shag_uvel_hsetky * (m_buy_positions_count[layer_index] - 1)) * m_point * m_point_digits;
               
            if(m_sell_positions_count[layer_index] > 1)
               sell_grid_step = (m_hsetky + m_shag_uvel_hsetky * (m_sell_positions_count[layer_index] - 1)) * m_point * m_point_digits;
         }
         else if(m_uvel_hsetky == 2) // Р РµР¶РёРј СѓРІРµР»РёС‡РµРЅРёСЏ С€Р°РіР° 2
         {
            if(m_buy_positions_count[layer_index] < 2) buy_grid_step = m_hsetky * m_point * m_point_digits;
            if(m_sell_positions_count[layer_index] < 2) sell_grid_step = m_hsetky * m_point * m_point_digits;
            
            if(m_buy_positions_count[layer_index] > 1)
               buy_grid_step = (m_hsetky - m_shag_uvel_hsetky * (m_buy_positions_count[layer_index] - 1)) * m_point * m_point_digits;
               
            if(m_sell_positions_count[layer_index] > 1)
               sell_grid_step = (m_hsetky - m_shag_uvel_hsetky * (m_sell_positions_count[layer_index] - 1)) * m_point * m_point_digits;
         }
         
         // РњРёРЅРёРјР°Р»СЊРЅС‹Р№ С€Р°Рі СЃРµС‚РєРё РЅРµ РјРµРЅРµРµ 10 РїСѓРЅРєС‚РѕРІ
         if(buy_grid_step < 10 * m_point_digits * m_point) buy_grid_step = 10 * m_point_digits * m_point;
         if(sell_grid_step < 10 * m_point_digits * m_point) sell_grid_step = 10 * m_point_digits * m_point;
         
         // РџСЂРѕРІРµСЂРєР° СѓСЃР»РѕРІРёР№ РґР»СЏ Р·Р°РєСЂС‹С‚РёСЏ Buy РїРѕР·РёС†РёР№
         if(m_buy_positions_count[layer_index] > 1 && m_sell_positions_count[layer_index] > 1 && 
            ask > m_buy_levels[layer_index][buy_max_index] + buy_grid_step)
         {
            close_buy = true;
         }
         
         // РџСЂРѕРІРµСЂРєР° СѓСЃР»РѕРІРёР№ РґР»СЏ Р·Р°РєСЂС‹С‚РёСЏ Sell РїРѕР·РёС†РёР№
         if(m_sell_positions_count[layer_index] > 1 && m_buy_positions_count[layer_index] > 1 && 
            bid < m_sell_levels[layer_index][sell_min_index] - sell_grid_step)
         {
            close_sell = true;
         }
         
         // Р—Р°РєСЂС‹С‚РёРµ Buy РїРѕР·РёС†РёР№
         if(close_buy || m_exit_mode)
         {
            ClosePositions(layer_index, 0); // 0 - Buy РїРѕР·РёС†РёРё
         }
         
         // Р—Р°РєСЂС‹С‚РёРµ Sell РїРѕР·РёС†РёР№
         if(close_sell || m_exit_mode)
         {
            ClosePositions(layer_index, 1); // 1 - Sell РїРѕР·РёС†РёРё
         }
         
         // РЎРїРµС†РёР°Р»СЊРЅР°СЏ РѕР±СЂР°Р±РѕС‚РєР° РґР»СЏ С‚СЂРµС‚СЊРµРіРѕ СЃР»РѕСЏ
         if(layer_index == 2 && m_mode_enable_3_sloy)
         {
            // РџСЂРѕРІРµСЂРєР° РЅР°С…РѕР¶РґРµРЅРёСЏ С†РµРЅС‹ РІ С‚РѕСЂРіРѕРІРѕР№ Р·РѕРЅРµ С‚СЂРµС‚СЊРµРіРѕ СЃР»РѕСЏ
            bool in_trade_zone = (current_price >= m_trade_low && current_price <= m_trade_high);
            
            // РџСЂРѕРІРµСЂРєР° РЅР°Р»РёС‡РёСЏ РѕСЂРґРµСЂРѕРІ РІ РїРµСЂРІС‹С… РґРІСѓС… СЃР»РѕСЏС…
            bool has_layer0_orders = (m_buy_positions_count[0] > 0 || m_sell_positions_count[0] > 0);
            bool has_layer1_orders = (m_buy_positions_count[1] > 0 || m_sell_positions_count[1] > 0);
            
            // Р•СЃР»Рё С†РµРЅР° РЅРµ РІ С‚РѕСЂРіРѕРІРѕР№ Р·РѕРЅРµ РёР»Рё РЅРµС‚ РѕСЂРґРµСЂРѕРІ РІ РґСЂСѓРіРёС… СЃР»РѕСЏС…, Р·Р°РїСЂРµС‰Р°РµРј РѕС‚РєСЂС‹С‚РёРµ РїРѕР·РёС†РёР№
            if(!in_trade_zone || (!has_layer0_orders && !has_layer1_orders))
            {
               open_buy = false;
               open_sell = false;
            }
            else
            {
               // РћРїСЂРµРґРµР»РµРЅРёРµ РїСЂРµРѕР±Р»Р°РґР°СЋС‰РµРіРѕ РЅР°РїСЂР°РІР»РµРЅРёСЏ РїРµСЂРІС‹С… РґРІСѓС… СЃР»РѕРµРІ
               int buy_count = m_buy_positions_count[0] + m_buy_positions_count[1];
               int sell_count = m_sell_positions_count[0] + m_sell_positions_count[1];
               
               // Р•СЃР»Рё РІ РїРµСЂРІС‹С… РґРІСѓС… СЃР»РѕСЏС… РїСЂРµРѕР±Р»Р°РґР°СЋС‚ Buy, С‚Рѕ РІ С‚СЂРµС‚СЊРµРј РѕС‚РєСЂС‹РІР°РµРј Sell
               if(buy_count > sell_count)
               {
                  open_buy = false;
                  open_sell = true;
               }
               // Р•СЃР»Рё РІ РїРµСЂРІС‹С… РґРІСѓС… СЃР»РѕСЏС… РїСЂРµРѕР±Р»Р°РґР°СЋС‚ Sell, С‚Рѕ РІ С‚СЂРµС‚СЊРµРј РѕС‚РєСЂС‹РІР°РµРј Buy
               else if(buy_count < sell_count)
               {
                  open_buy = true;
                  open_sell = false;
               }
            }
         }
         
         // РџСЂРѕРІРµСЂРєР° СѓСЃР»РѕРІРёР№ РґР»СЏ РѕС‚РєСЂС‹С‚РёСЏ РїРѕР·РёС†РёР№
         if(m_buy_positions_count[layer_index] == 0 && m_sell_positions_count[layer_index] == 0)
         {
            open_buy = true;
            open_sell = true;
            
            // РџСЂРѕРІРµСЂРєР° С‚СЂРµРЅРґР° РґР»СЏ РѕС‚РєСЂС‹С‚РёСЏ РїРѕР·РёС†РёР№
            if(m_use_trend_filter && m_trade_only_with_trend)
            {
               if(m_current_market_type == 1) // Р’РѕСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ
               {
                  open_sell = false; // Р—Р°РїСЂРµС‰Р°РµРј РѕС‚РєСЂС‹С‚РёРµ Sell РїРѕР·РёС†РёР№ РїСЂРѕС‚РёРІ С‚СЂРµРЅРґР°
               }
               else if(m_current_market_type == -1) // РќРёСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ
               {
                  open_buy = false; // Р—Р°РїСЂРµС‰Р°РµРј РѕС‚РєСЂС‹С‚РёРµ Buy РїРѕР·РёС†РёР№ РїСЂРѕС‚РёРІ С‚СЂРµРЅРґР°
               }
            }
         }
         
         if(m_buy_positions_count[layer_index] == 1 && m_sell_positions_count[layer_index] == 1)
         {
            if(ask < m_buy_levels[layer_index][buy_min_index] - buy_grid_step)
               open_buy = true;
               
            if(bid > m_sell_levels[layer_index][sell_max_index] + sell_grid_step)
               open_sell = true;
               
            // РџСЂРѕРІРµСЂРєР° С‚СЂРµРЅРґР° РґР»СЏ РѕС‚РєСЂС‹С‚РёСЏ РїРѕР·РёС†РёР№
            if(m_use_trend_filter && m_trade_only_with_trend)
            {
               if(m_current_market_type == 1) // Р’РѕСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ
               {
                  open_sell = false; // Р—Р°РїСЂРµС‰Р°РµРј РѕС‚РєСЂС‹С‚РёРµ Sell РїРѕР·РёС†РёР№ РїСЂРѕС‚РёРІ С‚СЂРµРЅРґР°
               }
               else if(m_current_market_type == -1) // РќРёСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ
               {
                  open_buy = false; // Р—Р°РїСЂРµС‰Р°РµРј РѕС‚РєСЂС‹С‚РёРµ Buy РїРѕР·РёС†РёР№ РїСЂРѕС‚РёРІ С‚СЂРµРЅРґР°
               }
            }
         }
         
         if(m_buy_positions_count[layer_index] > 1 && m_sell_positions_count[layer_index] > 0)
         {
            if(bid < m_buy_levels[layer_index][buy_min_index] - buy_grid_step)
               open_buy = true;
               
            // РџСЂРѕРІРµСЂРєР° С‚СЂРµРЅРґР°
            if(m_use_trend_filter && m_trade_only_with_trend && m_current_market_type == -1)
               open_buy = false; // Р—Р°РїСЂРµС‰Р°РµРј Buy РІ РЅРёСЃС…РѕРґСЏС‰РµРј С‚СЂРµРЅРґРµ
         }
         
         if(m_sell_positions_count[layer_index] > 1 && m_buy_positions_count[layer_index] > 0)
         {
            if(bid > m_sell_levels[layer_index][sell_max_index] + sell_grid_step)
               open_sell = true;
               
            // РџСЂРѕРІРµСЂРєР° С‚СЂРµРЅРґР°
            if(m_use_trend_filter && m_trade_only_with_trend && m_current_market_type == 1)
               open_sell = false; // Р—Р°РїСЂРµС‰Р°РµРј Sell РІ РІРѕСЃС…РѕРґСЏС‰РµРј С‚СЂРµРЅРґРµ
         }
         
         if(m_buy_positions_count[layer_index] > 0 && m_sell_positions_count[layer_index] == 0)
         {
            if(bid < m_buy_levels[layer_index][buy_min_index] - buy_grid_step)
               open_buy = true;
               
            // РџСЂРѕРІРµСЂРєР° С‚СЂРµРЅРґР°
            if(m_use_trend_filter && m_trade_only_with_trend && m_current_market_type == -1)
               open_buy = false; // Р—Р°РїСЂРµС‰Р°РµРј Buy РІ РЅРёСЃС…РѕРґСЏС‰РµРј С‚СЂРµРЅРґРµ
         }
         
         if(m_buy_positions_count[layer_index] == 0 && m_sell_positions_count[layer_index] > 0)
         {
            if(bid > m_sell_levels[layer_index][sell_max_index] + sell_grid_step)
               open_sell = true;
               
            // РџСЂРѕРІРµСЂРєР° С‚СЂРµРЅРґР°
            if(m_use_trend_filter && m_trade_only_with_trend && m_current_market_type == 1)
               open_sell = false; // Р—Р°РїСЂРµС‰Р°РµРј Sell РІ РІРѕСЃС…РѕРґСЏС‰РµРј С‚СЂРµРЅРґРµ
         }
         
         // РћС‚РєСЂС‹С‚РёРµ Buy РїРѕР·РёС†РёРё, РµСЃР»Рё СѓСЃР»РѕРІРёСЏ РІС‹РїРѕР»РЅРµРЅС‹
         if(open_buy && m_active_layers[layer_index] != 3 && !m_exit_mode)
         {
            double new_lot = m_lot;
            
            // Р Р°СЃС‡РµС‚ СЂР°Р·РјРµСЂР° Р»РѕС‚Р° РґР»СЏ РЅРѕРІРѕР№ РїРѕР·РёС†РёРё
            if(m_use_adaptive_mm)
            {
               // РСЃРїРѕР»СЊР·СѓРµРј Р°РґР°РїС‚РёРІРЅС‹Р№ СЂР°СЃС‡РµС‚ Р»РѕС‚Р° РЅР° РѕСЃРЅРѕРІРµ СЃРёР»С‹ СЃРёРіРЅР°Р»Р°
               new_lot = CalculateAdaptiveLot(0, layer_index); // 0 - РґР»СЏ Buy
            }
            else if(m_buy_positions_count[layer_index] > 0)
            {
               new_lot = NormalizeDouble(m_buy_lots[layer_index][buy_min_index] * m_lot_multiplicator, 2);
            }
            
            // Р”Р»СЏ С‚СЂРµС‚СЊРµРіРѕ СЃР»РѕСЏ СѓРІРµР»РёС‡РёРІР°РµРј СЂР°Р·РјРµСЂ Р»РѕС‚Р° СЃ РїРѕРјРѕС‰СЊСЋ РјРЅРѕР¶РёС‚РµР»СЏ DD
            if(layer_index == 2 && m_mode_enable_3_sloy)
            {
               new_lot = NormalizeDouble(new_lot * m_dd_lot, 2);
            }
               
            if(new_lot < m_min_lot) new_lot = m_min_lot;
            if(new_lot > m_max_lot) new_lot = m_max_lot;
            
            // РћС‚РєСЂС‹С‚РёРµ Buy РїРѕР·РёС†РёРё
            OpenPosition(m_symbol, 0, new_lot, ask, 0, 0, m_magic + layer_index, clrRed);
         }
         
         // РћС‚РєСЂС‹С‚РёРµ Sell РїРѕР·РёС†РёРё, РµСЃР»Рё СѓСЃР»РѕРІРёСЏ РІС‹РїРѕР»РЅРµРЅС‹
         if(open_sell && m_active_layers[layer_index] != 2 && !m_exit_mode)
         {
            double new_lot = m_lot;
            
            // Р Р°СЃС‡РµС‚ СЂР°Р·РјРµСЂР° Р»РѕС‚Р° РґР»СЏ РЅРѕРІРѕР№ РїРѕР·РёС†РёРё
            if(m_use_adaptive_mm)
            {
               // РСЃРїРѕР»СЊР·СѓРµРј Р°РґР°РїС‚РёРІРЅС‹Р№ СЂР°СЃС‡РµС‚ Р»РѕС‚Р° РЅР° РѕСЃРЅРѕРІРµ СЃРёР»С‹ СЃРёРіРЅР°Р»Р°
               new_lot = CalculateAdaptiveLot(1, layer_index); // 1 - РґР»СЏ Sell
            }
            else if(m_sell_positions_count[layer_index] > 0)
            {
               new_lot = NormalizeDouble(m_sell_lots[layer_index][sell_max_index] * m_lot_multiplicator, 2);
            }
            
            // Р”Р»СЏ С‚СЂРµС‚СЊРµРіРѕ СЃР»РѕСЏ СѓРІРµР»РёС‡РёРІР°РµРј СЂР°Р·РјРµСЂ Р»РѕС‚Р° СЃ РїРѕРјРѕС‰СЊСЋ РјРЅРѕР¶РёС‚РµР»СЏ DD
            if(layer_index == 2 && m_mode_enable_3_sloy)
            {
               new_lot = NormalizeDouble(new_lot * m_dd_lot, 2);
            }
               
            if(new_lot < m_min_lot) new_lot = m_min_lot;
            if(new_lot > m_max_lot) new_lot = m_max_lot;
            
            // РћС‚РєСЂС‹С‚РёРµ Sell РїРѕР·РёС†РёРё
            OpenPosition(m_symbol, 1, new_lot, bid, 0, 0, m_magic + layer_index, clrBlue);
         }
      }
   }
}

//+------------------------------------------------------------------+
//| РћР±СЂР°Р±РѕС‚РєР° С‚СЂРµР№Р»РёРЅРіР°                                              |
//+------------------------------------------------------------------+
void ProcessTrailing()
{
   for(int layer_index = 0; layer_index < 3; layer_index++)
   {
      if(m_active_layers[layer_index] > 0)
      {
         double ask = SymbolInfoDouble(m_symbol, SYMBOL_ASK);
         double bid = SymbolInfoDouble(m_symbol, SYMBOL_BID);
         int spread = (int)SymbolInfoInteger(m_symbol, SYMBOL_SPREAD);
         
         // РќР°СЃС‚СЂРѕР№РєР° С‚СЂРµР№Р»РёРЅРіР° РІ Р·Р°РІРёСЃРёРјРѕСЃС‚Рё РѕС‚ РїСЂРѕС„РёР»СЏ СЃРµСЃСЃРёРё
         int current_trailing_stop = m_tralling_stop;
         
         // Р•СЃР»Рё РІРєР»СЋС‡РµРЅС‹ РїСЂРѕС„РёР»Рё СЃРµСЃСЃРёР№, РёСЃРїРѕР»СЊР·СѓРµРј Р·РЅР°С‡РµРЅРёРµ РёР· С‚РµРєСѓС‰РµРіРѕ РїСЂРѕС„РёР»СЏ
         if(m_use_session_profiles)
         {
            current_trailing_stop = m_current_profile.trailing_activation;
         }
         
         // РўСЂРµР№Р»РёРЅРі РґР»СЏ Buy РїРѕР·РёС†РёР№
         if(m_buy_positions_count[layer_index] > 0)
         {
            double buy_trailing_level = NormalizeDouble(GetAveragePrice(layer_index, 0) + m_trall_tp * m_point * m_point_digits, m_digits);
            
            // РЎРѕР·РґР°РЅРёРµ Р»РёРЅРёР№ С‚СЂРµР№Р»РёРЅРіР° РґР»СЏ Buy
            if(m_symbol == Symbol())
            {
               CreateLayerLine("UBB_" + IntegerToString(layer_index), buy_trailing_level, clrRed, 2);
               CreateLayerLine("SLB_" + IntegerToString(layer_index), bid - current_trailing_stop * m_point * m_point_digits, clrRed, 1, STYLE_DASHDOT);
            }
            
            // РЈСЃС‚Р°РЅРѕРІРєР° СЃС‚РѕРї-Р»РѕСЃСЃР° РґР»СЏ Buy
            if(buy_trailing_level < bid - current_trailing_stop * m_point * m_point_digits)
            {
               for(int i = 1; i <= m_buy_positions_count[layer_index]; i++)
               {
                  if(m_buy_tickets[layer_index][i] > 0)
                  {
                     if(PositionSelectByTicket(m_buy_tickets[layer_index][i]))
                     {
                        double current_sl = PositionGetDouble(POSITION_SL);
                        double new_sl = bid - current_trailing_stop * m_point * m_point_digits;
                        
                        // Р’Р°Р»РёРґР°С†РёСЏ СЃС‚РѕРї-Р»РѕСЃСЃР°
                        new_sl = ValidateStopLoss(m_symbol, 0, bid, new_sl);
                        
                        if((new_sl > current_sl || current_sl == 0) && new_sl > 0)
                        {
                           m_trade.SetExpertMagicNumber(m_magic + layer_index);
                           
                           if(!m_trade.PositionModify(m_buy_tickets[layer_index][i], new_sl, 0))
                           {
                              Print("РћС€РёР±РєР° СѓСЃС‚Р°РЅРѕРІРєРё С‚СЂРµР№Р»РёРЅРі-СЃС‚РѕРїР° РґР»СЏ Buy #", m_buy_tickets[layer_index][i], 
                                    ": ", m_trade.ResultRetcode(), ", sl: ", DoubleToString(new_sl, m_digits));
                           }
                        }
                     }
                  }
               }
            }
         }
         else
         {
            // РЈРґР°Р»РµРЅРёРµ Р»РёРЅРёР№
            DeleteLayerLine("UBB_" + IntegerToString(layer_index));
            DeleteLayerLine("SLB_" + IntegerToString(layer_index));
         }
         
         // РўСЂРµР№Р»РёРЅРі РґР»СЏ Sell РїРѕР·РёС†РёР№
         if(m_sell_positions_count[layer_index] > 0)
         {
            double sell_trailing_level = NormalizeDouble(GetAveragePrice(layer_index, 1) - m_trall_tp * m_point * m_point_digits, m_digits);
            
            // РЎРѕР·РґР°РЅРёРµ Р»РёРЅРёР№ С‚СЂРµР№Р»РёРЅРіР° РґР»СЏ Sell
            if(m_symbol == Symbol())
            {
               CreateLayerLine("UBS_" + IntegerToString(layer_index), sell_trailing_level, clrBlue, 2);
               CreateLayerLine("SLS_" + IntegerToString(layer_index), ask + current_trailing_stop * m_point * m_point_digits, clrBlue, 1, STYLE_DASHDOT);
            }
            
            // РЈСЃС‚Р°РЅРѕРІРєР° СЃС‚РѕРї-Р»РѕСЃСЃР° РґР»СЏ Sell
            if(sell_trailing_level > ask + current_trailing_stop * m_point * m_point_digits)
            {
               for(int i = 1; i <= m_sell_positions_count[layer_index]; i++)
               {
                  if(m_sell_tickets[layer_index][i] > 0)
                  {
                     if(PositionSelectByTicket(m_sell_tickets[layer_index][i]))
                     {
                        double current_sl = PositionGetDouble(POSITION_SL);
                        double new_sl = ask + current_trailing_stop * m_point * m_point_digits;
                        
                        // Р’Р°Р»РёРґР°С†РёСЏ СЃС‚РѕРї-Р»РѕСЃСЃР°
                        new_sl = ValidateStopLoss(m_symbol, 1, ask, new_sl);
                        
                        if((new_sl < current_sl || current_sl == 0) && new_sl > 0)
                        {
                           m_trade.SetExpertMagicNumber(m_magic + layer_index);
                           
                           if(!m_trade.PositionModify(m_sell_tickets[layer_index][i], new_sl, 0))
                           {
                              Print("РћС€РёР±РєР° СѓСЃС‚Р°РЅРѕРІРєРё С‚СЂРµР№Р»РёРЅРі-СЃС‚РѕРїР° РґР»СЏ Sell #", m_sell_tickets[layer_index][i], 
                                    ": ", m_trade.ResultRetcode(), ", sl: ", DoubleToString(new_sl, m_digits));
                           }
                        }
                     }
                  }
               }
            }
         }
         else
         {
            // РЈРґР°Р»РµРЅРёРµ Р»РёРЅРёР№
            DeleteLayerLine("UBS_" + IntegerToString(layer_index));
            DeleteLayerLine("SLS_" + IntegerToString(layer_index));
         }
      }
   }
}
   
   ObjectSetString(0, name, OBJPROP_TEXT, "РџСЂРёР±С‹Р»СЊ РїРѕР·Р°РІС‡РµСЂР°: " + DoubleToString(profit_before_yesterday, 2));
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, m_text_size);
   ObjectSetString(0, name, OBJPROP_FONT, "Courier New");
   ObjectSetInteger(0, name, OBJPROP_COLOR, m_color_table_on_testing);
   
   // Р‘Р°Р»Р°РЅСЃ
   name = m_prefix + "4";
   
   if(ObjectFind(0, name) == -1)
   {
      ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, name, OBJPROP_CORNER, 1);
      ObjectSetInteger(0, name, OBJPROP_XDISTANCE, 10);
      ObjectSetInteger(0, name, OBJPROP_YDISTANCE, 76);
   }
   
   ObjectSetString(0, name, OBJPROP_TEXT, "Р‘Р°Р»Р°РЅСЃ : " + DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2));
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, m_text_size);
   ObjectSetString(0, name, OBJPROP_FONT, "Courier New");
   ObjectSetInteger(0, name, OBJPROP_COLOR, m_color_table_on_testing);
   
   // РЎРІРѕР±РѕРґРЅР°СЏ РјР°СЂР¶Р°
   name = m_prefix + "5";
   
   if(ObjectFind(0, name) == -1)
   {
      ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, name, OBJPROP_CORNER, 1);
      ObjectSetInteger(0, name, OBJPROP_XDISTANCE, 10);
      ObjectSetInteger(0, name, OBJPROP_YDISTANCE, 96);
   }
   
   ObjectSetString(0, name, OBJPROP_TEXT, "РЎРІРѕР±. СЃСЂРµРґСЃС‚РІР°: " + DoubleToString(AccountInfoDouble(ACCOUNT_MARGIN_FREE), 2));
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, m_text_size);
   ObjectSetString(0, name, OBJPROP_FONT, "Courier New");
   ObjectSetInteger(0, name, OBJPROP_COLOR, m_color_table_on_testing);
   
   // РЎРѕР·РґР°РЅРёРµ Р»РѕРіРѕС‚РёРїР°
   CreateLogoObjects();
}

//+------------------------------------------------------------------+
//| РЎРѕР·РґР°РЅРёРµ РѕР±СЉРµРєС‚РѕРІ Р»РѕРіРѕС‚РёРїР°                                       |
//+------------------------------------------------------------------+
void CreateLogoObjects()
{
   string name = m_prefix + "L_1";
   
   if(ObjectFind(0, name) == -1)
   {
      ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, name, OBJPROP_CORNER, 0);
      ObjectSetInteger(0, name, OBJPROP_XDISTANCE, 390);
      ObjectSetInteger(0, name, OBJPROP_YDISTANCE, 10);
   }
   
   ObjectSetString(0, name, OBJPROP_TEXT, "F O R E X");
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 28);
   ObjectSetString(0, name, OBJPROP_FONT, "Arial");
   ObjectSetInteger(0, name, OBJPROP_COLOR, m_color_logotip_name);
   
   name = m_prefix + "L_2";
   
   if(ObjectFind(0, name) == -1)
   {
      ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, name, OBJPROP_CORNER, 0);
      ObjectSetInteger(0, name, OBJPROP_XDISTANCE, 382);
      ObjectSetInteger(0, name, OBJPROP_YDISTANCE, 50);
   }
   
   ObjectSetString(0, name, OBJPROP_TEXT, "I  N  V  E  S  T  O  R");
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 16);
   ObjectSetString(0, name, OBJPROP_FONT, "Arial");
   ObjectSetInteger(0, name, OBJPROP_COLOR, m_color_logotip_name);
   
   name = m_prefix + "L_3";
   
   if(ObjectFind(0, name) == -1)
   {
      ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, name, OBJPROP_CORNER, 0);
      ObjectSetInteger(0, name, OBJPROP_XDISTANCE, 397);
      ObjectSetInteger(0, name, OBJPROP_YDISTANCE, 75);
   }
   
   ObjectSetString(0, name, OBJPROP_TEXT, "www.forex-investor.net");
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 12);
   ObjectSetString(0, name, OBJPROP_FONT, "Arial");
   ObjectSetInteger(0, name, OBJPROP_COLOR, m_color_logotip_site);
}

//+------------------------------------------------------------------+
//| Р Р°СЃС‡РµС‚ РїСЂРёР±С‹Р»Рё Р·Р° РѕРїСЂРµРґРµР»РµРЅРЅС‹Р№ РґРµРЅСЊ                              |
//+------------------------------------------------------------------+
double CalculateProfit(int days_ago)
{
   double profit = 0.0;
   
   // РћРїСЂРµРґРµР»РµРЅРёРµ РЅР°С‡Р°Р»Р° Рё РєРѕРЅС†Р° РґРЅСЏ
   datetime day_start = iTime(m_symbol, PERIOD_D1, days_ago);
   datetime day_end = day_start + 86399; // +23:59:59
   
   // РџСЂРѕРІРµСЂРєР° РІСЃРµС… СЃРґРµР»РѕРє РІ РёСЃС‚РѕСЂРёРё
   int history_deals = HistoryDealsTotal();
   
   for(int i = 0; i < history_deals; i++)
   {
      ulong deal_ticket = HistoryDealGetTicket(i);
      
      if(deal_ticket > 0 && HistoryDealSelect(deal_ticket))
      {
         // РџСЂРѕРІРµСЂРєР° РїСЂРёРЅР°РґР»РµР¶РЅРѕСЃС‚Рё СЃРґРµР»РєРё Рє СЃРѕРІРµС‚РЅРёРєСѓ Рё СѓРєР°Р·Р°РЅРЅРѕРјСѓ РїРµСЂРёРѕРґСѓ
         datetime deal_time = (datetime)HistoryDealGetInteger(deal_ticket, DEAL_TIME);
         
         if(deal_time >= day_start && deal_time <= day_end)
         {
            string deal_symbol = HistoryDealGetString(deal_ticket, DEAL_SYMBOL);
            ulong deal_magic = HistoryDealGetInteger(deal_ticket, DEAL_MAGIC);
            
            if(deal_symbol == m_symbol && 
               (deal_magic == m_magic || deal_magic == m_magic + 1 || deal_magic == m_magic + 2))
            {
               // Р”РѕР±Р°РІР»РµРЅРёРµ РїСЂРёР±С‹Р»Рё Рё РєРѕРјРёСЃСЃРёРё
               profit += HistoryDealGetDouble(deal_ticket, DEAL_PROFIT);
               profit += HistoryDealGetDouble(deal_ticket, DEAL_COMMISSION);
               profit += HistoryDealGetDouble(deal_ticket, DEAL_SWAP);
            }
         }
      }
   }
   
   return profit;
} 

//+------------------------------------------------------------------+
//| РћРїСЂРµРґРµР»РµРЅРёРµ С‚РµРєСѓС‰РµРіРѕ С‚СЂРµРЅРґР°                                |
//+------------------------------------------------------------------+
int DetectMarketTrend()
{
   // РџРѕ СѓРјРѕР»С‡Р°РЅРёСЋ: 0 - С„Р»СЌС‚, 1 - РІРѕСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ, -1 - РќРёСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ
   int trend = 0;
   
   // Р•СЃР»Рё С„РёР»СЊС‚СЂ С‚СЂРµРЅРґР° РѕС‚РєР»СЋС‡РµРЅ, РІСЃРµРіРґР° СЃС‡РёС‚Р°РµРј СЂС‹РЅРѕРє С„Р»СЌС‚РѕРІС‹Рј
   if(!m_use_trend_filter)
      return 0;
   
   // Р”Р»СЏ РјРµС‚РѕРґР° ADX
   if(m_trend_detection_method == 0 || m_trend_detection_method == 2)
   {
      double adx_main[], plus_di[], minus_di[];
      
      // РљРѕРїРёСЂРѕРІР°РЅРёРµ Р·РЅР°С‡РµРЅРёР№ РёРЅРґРёРєР°С‚РѕСЂР° ADX
      if(CopyBuffer(m_adx_handle, 0, 0, 3, adx_main) <= 0 ||
         CopyBuffer(m_adx_handle, 1, 0, 3, plus_di) <= 0 ||
         CopyBuffer(m_adx_handle, 2, 0, 3, minus_di) <= 0)
      {
         Print("РћС€РёР±РєР° РєРѕРїРёСЂРѕРІР°РЅРёСЏ РґР°РЅРЅС‹С… РёРЅРґРёРєР°С‚РѕСЂР° ADX");
         return 0;
      }
      
      // РџСЂРѕРІРµСЂРєР° СЃРёР»С‹ С‚СЂРµРЅРґР° РїРѕ ADX
      if(adx_main[m_ma_signal_bar] >= m_adx_trend_level)
      {
         // Р•СЃР»Рё РёСЃРїРѕР»СЊР·СѓРµРј С„РёР»СЊС‚СЂ DI+ Рё DI-
         if(m_adx_use_di_filter)
         {
            if(plus_di[m_ma_signal_bar] > minus_di[m_ma_signal_bar])
               trend = 1;  // Р’РѕСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ
            else
               trend = -1; // РќРёСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ
         }
         else
         {
            // РџСЂРѕСЃС‚Рѕ СЃРёР»СЊРЅС‹Р№ С‚СЂРµРЅРґ, РЅР°РїСЂР°РІР»РµРЅРёРµ РѕРїСЂРµРґРµР»СЏРµРј РїРѕ С†РµРЅР°Рј
            double close_prices[];
            if(CopyClose(m_symbol, PERIOD_CURRENT, 0, 3, close_prices) > 0)
            {
               if(close_prices[1] > close_prices[0])
                  trend = 1;  // Р’РѕСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ
               else
                  trend = -1; // РќРёСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ
            }
         }
      }
   }
   
   // Р”Р»СЏ РјРµС‚РѕРґР° MA
   if(m_trend_detection_method == 1 || m_trend_detection_method == 2)
   {
      double ma_fast[], ma_slow[];
      
      // РљРѕРїРёСЂРѕРІР°РЅРёРµ Р·РЅР°С‡РµРЅРёР№ РёРЅРґРёРєР°С‚РѕСЂРѕРІ MA
      if(CopyBuffer(m_ma_fast_handle, 0, 0, 3, ma_fast) <= 0 ||
         CopyBuffer(m_ma_slow_handle, 0, 0, 3, ma_slow) <= 0)
      {
         Print("РћС€РёР±РєР° РєРѕРїРёСЂРѕРІР°РЅРёСЏ РґР°РЅРЅС‹С… РёРЅРґРёРєР°С‚РѕСЂРѕРІ MA");
         return 0;
      }
      
      // РћРїСЂРµРґРµР»РµРЅРёРµ С‚СЂРµРЅРґР° РїРѕ MA
      int ma_trend = 0;
      
      if(ma_fast[m_ma_signal_bar] > ma_slow[m_ma_signal_bar])
         ma_trend = 1;  // Р’РѕСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ
      else if(ma_fast[m_ma_signal_bar] < ma_slow[m_ma_signal_bar])
         ma_trend = -1; // РќРёСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ
      
      // Р•СЃР»Рё РёСЃРїРѕР»СЊР·СѓРµРј РѕР±Р° РјРµС‚РѕРґР°, С‚СЂРµР±СѓРµС‚СЃСЏ РїРѕРґС‚РІРµСЂР¶РґРµРЅРёРµ РѕР±РѕРёРјРё РјРµС‚РѕРґР°РјРё
      if(m_trend_detection_method == 2)
      {
         // Р•СЃР»Рё trenРґ Рё ma_trend РёРјРµСЋС‚ РѕРґРёРЅР°РєРѕРІРѕРµ РЅР°РїСЂР°РІР»РµРЅРёРµ РёР»Рё РѕРґРёРЅ РёР· РЅРёС… 0
         if(trend == ma_trend || trend == 0)
            trend = ma_trend;
         else if(ma_trend == 0)
            trend = trend;
         else
            trend = 0; // Р Р°Р·РЅРѕРіР»Р°СЃРёРµ РјРµР¶РґСѓ РјРµС‚РѕРґР°РјРё, СЃС‡РёС‚Р°РµРј С„Р»СЌС‚РѕРј
      }
      else if(m_trend_detection_method == 1)
      {
         trend = ma_trend;
      }
   }
   
   return trend;
}

//+------------------------------------------------------------------+
//| РџРѕР»СѓС‡РµРЅРёРµ СЃС‚Р°С‚СѓСЃР° СЃР»РѕСЏ РґР»СЏ РѕС‚РѕР±СЂР°Р¶РµРЅРёСЏ                           |
//+------------------------------------------------------------------+
string GetLayerStatus(int layer_index)
{
   if(m_active_layers[layer_index] == 0)
      return "РЎР›РћР™ РћРўРљР›Р®Р§Р•Рќ";
      
   if(m_active_layers[layer_index] == 1)
      return "РђРљРўРР’Р•Рќ";
      
   if(m_active_layers[layer_index] == 2)
      return "РўРћР›Р¬РљРћ BUY";
      
   if(m_active_layers[layer_index] == 3)
      return "РўРћР›Р¬РљРћ SELL";
      
   return "РќР•РР—Р’Р•РЎРўРќРћ";
}

//+------------------------------------------------------------------+
//| Р’СЃРїРѕРјРѕРіР°С‚РµР»СЊРЅС‹Рµ С„СѓРЅРєС†РёРё РґР»СЏ СѓРјРЅРѕРіРѕ РІС‹С…РѕРґР° РёР· РїСЂРѕСЃР°РґРєРё             |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| РћРїСЂРµРґРµР»РµРЅРёРµ РЅР°Р»РёС‡РёСЏ РїСЂРѕСЃР°РґРєРё                                     |
//+------------------------------------------------------------------+
bool DetectDrawdown()
{
   double total_profit = 0.0;
   int total_positions = 0;
   
   // РџРѕРґСЃС‡РµС‚ РѕР±С‰РµРіРѕ РїСЂРѕС„РёС‚Р° РїРѕ РІСЃРµРј РїРѕР·РёС†РёСЏРј
   for(int layer = 0; layer < 3; layer++)
   {
      // РЈС‡РµС‚ РїСЂРѕС„РёС‚Р°/СѓР±С‹С‚РєР° РїРѕ Buy РїРѕР·РёС†РёСЏРј
      for(int i = 0; i < m_buy_positions_count[layer]; i++)
      {
         if(PositionSelectByTicket(m_buy_tickets[layer][i]))
         {
            total_profit += PositionGetDouble(POSITION_PROFIT);
            total_positions++;
         }
      }
      
      // РЈС‡РµС‚ РїСЂРѕС„РёС‚Р°/СѓР±С‹С‚РєР° РїРѕ Sell РїРѕР·РёС†РёСЏРј
      for(int i = 0; i < m_sell_positions_count[layer]; i++)
      {
         if(PositionSelectByTicket(m_sell_tickets[layer][i]))
         {
            total_profit += PositionGetDouble(POSITION_PROFIT);
            total_positions++;
         }
      }
   }
   
   // РњРµС‚ РїРѕР·РёС†РёР№ - РЅРµС‚ РїСЂРѕСЃР°РґРєРё
   if(total_positions == 0)
   {
      m_drawdown_detected = false;
      m_current_drawdown_percent = 0.0;
      return false;
   }
   
   // Р Р°СЃС‡РµС‚ РїСЂРѕСЃР°РґРєР° РІ РїСЂРѕС†РµРЅС‚Р°С… РѕС‚ РґРµРїРѕР·РёС‚Р°
   double balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double drawdown_percent = -total_profit / balance * 100.0;
   
   // РћР±РЅРѕРІР»РµРЅРёРµ С‚РµРєСѓС‰РµР№ РїСЂРѕСЃР°РґРєРё
   m_current_drawdown_percent = drawdown_percent;
   
   // Р•СЃР»Рё РїСЂРѕС„РёС‚ РїРѕР»РѕР¶РёС‚РµР»СЊРЅС‹Р№ - РЅРµС‚ РїСЂРѕСЃР°РґРєРё
   if(total_profit >= 0)
   {
      m_drawdown_detected = false;
      return false;
   }
   
   // Р•СЃР»Рё РїСЂРѕСЃР°РґРєР° РїСЂРµРІС‹С€Р°РµС‚ РїСЂРѕС‚РѕРіРѕРІРѕРµ Р·РЅР°С‡РµРЅРёРµ - С„РёРєСЃРёСЂСѓРµРј РЅР°С‡Р°Р»Рѕ РїСЂРѕСЃР°РґРєРё
   // Р•СЃР»Рё РїСЂРѕСЃР°РґРєР° РїСЂРµРІС‹С€Р°РµС‚ РїРѕСЂРѕРіРѕРІРѕРµ Р·РЅР°С‡РµРЅРёРµ - С„РёРєСЃРёСЂСѓРµРј РЅР°С‡Р°Р»Рѕ РїСЂРѕСЃР°РґРєРё
   if(drawdown_percent > m_drawdown_max_percent && !m_drawdown_detected)
   {
      m_drawdown_detected = true;
      m_drawdown_start_time = TimeCurrent();
      m_drawdown_max_value = drawdown_percent;
      Print("РћР±РЅР°СЂСѓР¶РµРЅР° РїСЂРѕСЃР°РґРєР°: ", DoubleToString(drawdown_percent, 2), "% РѕС‚ РґРµРїРѕР·РёС‚Р°");
      return true;
   }
   
   // РћР±РЅРѕРІР»РµРЅРёРµ РјР°РєСЃРёРјР°Р»СЊРЅРѕРіРѕ Р·РЅР°С‡РµРЅРёСЏ РїСЂРѕСЃР°РґРєРё
   if(m_drawdown_detected && drawdown_percent > m_drawdown_max_value)
   {
      m_drawdown_max_value = drawdown_percent;
   }
   
   return m_drawdown_detected;
}

//+------------------------------------------------------------------+
//| Р Р°СЃС‡РµС‚ РґР»РёС‚РµР»СЊРЅРѕСЃС‚Рё РїСЂРѕСЃР°РґРєРё РІ С‡Р°СЃР°С…                              |
//+------------------------------------------------------------------+
int GetDrawdownDuration()
{
   if(!m_drawdown_detected)
      return 0;
      
   datetime current_time = TimeCurrent();
   int duration_seconds = (int)(current_time - m_drawdown_start_time);
   
   return duration_seconds / 3600; // РљРѕРЅРІРµСЂС‚Р°С†РёСЏ РІ С‡Р°СЃС‹
}

//+------------------------------------------------------------------+
//| Р Р°СЃС‡РµС‚ РїСЂРѕС†РµРЅС‚Р° РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёСЏ                                    |
//+------------------------------------------------------------------+
double CalculateRecoveryPercent()
{
   if(!m_drawdown_detected || m_drawdown_max_value == 0.0)
      return 100.0;
      
   // Р•СЃР»Рё С‚РµРєСѓС‰Р°СЏ РїСЂРѕСЃР°РґРєР° РјРµРЅСЊС€Рµ РјР°РєСЃРёРјР°Р»СЊРЅРѕР№, Р·РЅР°С‡РёС‚ РёРґРµС‚ РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёРµ
   double recovery = (m_drawdown_max_value - m_current_drawdown_percent) / m_drawdown_max_value * 100.0;
   
   // РћРіСЂР°РЅРёС‡РµРЅРёРµ Р·РЅР°С‡РµРЅРёСЏ РѕС‚ 0 РґРѕ 100
   if(recovery < 0.0) recovery = 0.0;
   if(recovery > 100.0) recovery = 100.0;
   
   m_recovery_percent = recovery;
   return recovery;
}

//+------------------------------------------------------------------+
//| Р Р°СЃС‡РµС‚ РѕРїС‚РёРјР°Р»СЊРЅС‹С… С‚РѕС‡РµРє СѓСЃСЂРµРґРЅРµРЅРёСЏ                               |
//+------------------------------------------------------------------+
double CalculateOptimalAveragingPoint(int layer, int direction, int current_positions_count)
{
   // РќР°РїСЂР°РІР»РµРЅРёРµ: 0 - Buy, 1 - Sell
   
   // Р‘Р°Р·РѕРІС‹Р№ С€Р°Рі СЃРµС‚РєРё РІ Р·Р°РІРёСЃРёРјРѕСЃС‚Рё РѕС‚ С‚РµРєСѓС‰РµРіРѕ С‚РёРїР° СЂС‹РЅРєР°
   double base_step = (m_current_market_type != 0) ? m_trend_mode_setka : m_flat_mode_setka;
   
   // РЈС‡РµС‚ РІРѕР»Р°С‚РёР»СЊРЅРѕСЃС‚Рё, РµСЃР»Рё РёСЃРїРѕР»СЊР·СѓРµС‚СЃСЏ Р°РґР°РїС‚РёРІРЅС‹Р№ СЂР°Р·РјРµСЂ СЃРµС‚РєРё
   double volatility_factor = m_use_atr_for_grid ? m_current_grid_step / (m_hsetky * m_point) : 1.0;
   
   // РџСЂРѕРіСЂРµСЃСЃРёРІРЅРѕРµ СѓРІРµР»РёС‡РµРЅРёРµ С€Р°РіР° РґР»СЏ РїРѕСЃР»РµРґСѓСЋС‰РёС… РїРѕР·РёС†РёР№
   double progression_factor = 1.0 + 0.15 * current_positions_count;
   
   // РЈС‡РµС‚ СЃР»РѕСЏ РІ СЂР°СЃС‡РµС‚Рµ (РїРµСЂРІС‹Р№ СЃР»РѕР№ - Р±РѕР»РµРµ Р°РіСЂРµСЃСЃРёРІРЅРѕРµ СѓСЃСЂРµРґРЅРµРЅРёРµ)
   double layer_factor = (layer == 0) ? 0.85 : ((layer == 1) ? 1.0 : 1.2);
   
   double step = base_step * volatility_factor * progression_factor * layer_factor;
   
   // РћРїСЂРµРґРµР»РµРЅРёРµ С‚РµРєСѓС‰РµР№ С†РµРЅС‹ (РїРѕСЃР»РµРґРЅРµР№ РїРѕР·РёС†РёРё РёР»Рё СЂС‹РЅРѕС‡РЅРѕР№)
   double current_price;
   
   if(direction == 0) // Buy
   {
      if(m_buy_positions_count[layer] > 0)
         current_price = m_buy_levels[layer][m_buy_positions_count[layer] - 1];
      else
         current_price = SymbolInfoDouble(m_symbol, SYMBOL_ASK);
         
      // Р”Р»СЏ Buy РїРѕР·РёС†РёР№ С‚РѕС‡РєР° СѓСЃСЂРµРґРЅРµРЅРёСЏ РЅРёР¶Рµ С‚РµРєСѓС‰РµР№ С†РµРЅС‹
      return current_price - step * m_point * m_point_digits;
   }
   else // Sell
   {
      if(m_sell_positions_count[layer] > 0)
         current_price = m_sell_levels[layer][m_sell_positions_count[layer] - 1];
      else
         current_price = SymbolInfoDouble(m_symbol, SYMBOL_BID);
         
      // Р”Р»СЏ Sell РїРѕР·РёС†РёР№ С‚РѕС‡РєР° СѓСЃСЂРµРґРЅРµРЅРёСЏ РІС‹С€Рµ С‚РµРєСѓС‰РµР№ С†РµРЅС‹
      return current_price + step * m_point * m_point_digits;
   }
}

//+------------------------------------------------------------------+
//| Р Р°СЃС‡РµС‚ РѕРїС‚РёРјР°Р»СЊРЅРѕРіРѕ СЂР°Р·РјРµСЂР° Р»РѕС‚Р° РґР»СЏ СѓСЃСЂРµРґРЅРµРЅРёСЏ                    |
//+------------------------------------------------------------------+
double CalculateRecoveryLot(int layer, int direction)
{
   // РќР°РїСЂР°РІР»РµРЅРёРµ: 0 - Buy, 1 - Sell
   
   double total_lots = 0.0;
   double avg_price = 0.0;
   double total_weighted = 0.0;
   
   // Р Р°СЃС‡РµС‚ С‚РµРєСѓС‰РµР№ СЃСЂРµРґРЅРµРІР·РІРµС€РµРЅРЅРѕР№ С†РµРЅС‹ Рё РѕР±С‰РµРіРѕ РѕР±СЉРµРјР°
   if(direction == 0) // Buy
   {
      for(int i = 0; i < m_buy_positions_count[layer]; i++)
      {
         total_lots += m_buy_lots[layer][i];
         total_weighted += m_buy_levels[layer][i] * m_buy_lots[layer][i];
      }
   }
   else // Sell
   {
      for(int i = 0; i < m_sell_positions_count[layer]; i++)
      {
         total_lots += m_sell_lots[layer][i];
         total_weighted += m_sell_levels[layer][i] * m_sell_lots[layer][i];
      }
   }
   
   // Р•СЃР»Рё РЅРµС‚ РїРѕР·РёС†РёР№, РёСЃРїРѕР»СЊР·СѓРµРј СЃС‚Р°РЅРґР°СЂС‚РЅС‹Р№ СЂР°Р·РјРµСЂ Р»РѕС‚Р°
   if(total_lots == 0.0)
   {
      if(m_lot_const)
         return m_lot;
      else
      {
         double balance = AccountInfoDouble(ACCOUNT_BALANCE);
         return NormalizeDouble(balance * m_risk_percent / 1000000.0, 2);
      }
   }
   
   // РЎСЂРµРґРЅРµРІР·РІРµС€РµРЅРЅР°СЏ С†РµРЅР°
   avg_price = total_weighted / total_lots;
   
   // Р‘Р°Р·РѕРІС‹Р№ СЂР°Р·РјРµСЂ Р»РѕС‚Р° РґР»СЏ РЅРѕРІРѕР№ РїРѕР·РёС†РёРё
   double base_lot = total_lots * m_smart_lot_multiplier;
   
   // РџСЂРѕРІРµСЂРєР° РЅР° РјРёРЅРёРјР°Р»СЊРЅС‹Р№/РјР°РєСЃРёРјР°Р»СЊРЅС‹Р№ СЂР°Р·РјРµСЂ Р»РѕС‚Р°
   if(base_lot < m_min_lot)
      base_lot = m_min_lot;
   if(base_lot > m_max_lot)
      base_lot = m_max_lot;
   
   return NormalizeDouble(base_lot, 2);
}

//+------------------------------------------------------------------+
//| Р Р°СЃС‡РµС‚ РѕРїС‚РёРјР°Р»СЊРЅРѕРіРѕ РѕР±СЉРµРјР° С…РµРґР¶РёСЂСѓСЋС‰РµР№ РїРѕР·РёС†РёРё                   |
//+------------------------------------------------------------------+
double CalculateOptimalHedgeLot(int layer, int direction)
{
   // direction: 0 - Buy hedge (РїСЂРѕС‚РёРІ Sell), 1 - Sell hedge (РїСЂРѕС‚РёРІ Buy)
   // РџРѕР»СѓС‡Р°РµРј РѕР±С‰РёР№ РѕР±СЉРµРј РѕС‚РєСЂС‹С‚С‹С… РїРѕР·РёС†РёР№ РІ РїСЂРѕС‚РёРІРѕРїРѕР»РѕР¶РЅРѕРј РЅР°РїСЂР°РІР»РµРЅРёРё
   double main_lots = 0.0;
   if(direction == 0) // РҐРµРґР¶РёСЂСѓРµРј Sell РїРѕР·РёС†РёРё (РѕС‚РєСЂС‹РІР°РµРј Buy С…РµРґР¶)
   {
      for(int i = 0; i < m_sell_positions_count[layer]; i++)
         main_lots += m_sell_lots[layer][i];
   }
   else // РҐРµРґР¶РёСЂСѓРµРј Buy РїРѕР·РёС†РёРё (РѕС‚РєСЂС‹РІР°РµРј Sell С…РµРґР¶)
   {
      for(int i = 0; i < m_buy_positions_count[layer]; i++)
         main_lots += m_buy_lots[layer][i];
   }

   // Р•СЃР»Рё РЅРµС‚ РїРѕР·РёС†РёР№ РґР»СЏ С…РµРґР¶РёСЂРѕРІР°РЅРёСЏ, РІРѕР·РІСЂР°С‰Р°РµРј 0
   if(main_lots <= 0.0)
      return 0.0;

   // Р‘Р°Р·РѕРІС‹Р№ СЂР°СЃС‡РµС‚ РѕР±СЉРµРјР° С…РµРґР¶Р° РїРѕ РѕРїС‚РёРјР°Р»СЊРЅРѕРјСѓ СЃРѕРѕС‚РЅРѕС€РµРЅРёСЋ
   double hedge_lot = main_lots * OptimalHedgeRatio;

   // РљРѕСЂСЂРµРєС‚РёСЂРѕРІРєР° РЅР° РІРѕР»Р°С‚РёР»СЊРЅРѕСЃС‚СЊ (РµСЃР»Рё РІРєР»СЋС‡РµРЅРѕ)
   if(HedgingVolatilityFactor > 0.0)
   {
      int atr_handle = iATR(m_symbol, PERIOD_CURRENT, 14);
      double atr_buffer[];
      ArraySetAsSeries(atr_buffer, true);
      CopyBuffer(atr_handle, 0, 0, 1, atr_buffer);
      double atr = atr_buffer[0];
      double base_atr = 0.001; // Р‘Р°Р·РѕРІРѕРµ Р·РЅР°С‡РµРЅРёРµ ATR РґР»СЏ РЅРѕСЂРјР°Р»РёР·Р°С†РёРё (РјРѕР¶РЅРѕ СЃРєРѕСЂСЂРµРєС‚РёСЂРѕРІР°С‚СЊ)
      if(atr > 0.0)
         hedge_lot *= (1.0 + (atr / base_atr - 1.0) * (HedgingVolatilityFactor - 1.0));
   }

   // РљРѕСЂСЂРµРєС‚РёСЂРѕРІРєР° РЅР° РєРѕСЂСЂРµР»СЏС†РёСЋ (РµСЃР»Рё РґРѕСЃС‚СѓРїРЅР°)
   // Р—РґРµСЃСЊ РјРѕР¶РЅРѕ РґРѕР±Р°РІРёС‚СЊ СЂР°СЃС‡РµС‚ РєРѕСЂСЂРµР»СЏС†РёРё СЃ РґСЂСѓРіРёРј РёРЅСЃС‚СЂСѓРјРµРЅС‚РѕРј, РµСЃР»Рё С‚СЂРµР±СѓРµС‚СЃСЏ
   // double correlation = ...;
   // hedge_lot *= MathAbs(correlation);

   // РћРіСЂР°РЅРёС‡РµРЅРёСЏ РїРѕ РјРёРЅРёРјР°Р»СЊРЅРѕРјСѓ Рё РјР°РєСЃРёРјР°Р»СЊРЅРѕРјСѓ РѕР±СЉРµРјСѓ
   double volume_step = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_STEP);
   double min_volume = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MIN);
   double max_volume = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MAX);

   hedge_lot = NormalizeDouble(MathRound(hedge_lot / volume_step) * volume_step, 8);
   if(hedge_lot < min_volume)
      hedge_lot = min_volume;
   if(hedge_lot > max_volume)
      hedge_lot = max_volume;

   // РџСЂРѕРІРµСЂРєР°, С‡С‚Рѕ РѕР±СЉРµРј РЅРµ РїСЂРµРІС‹С€Р°РµС‚ РѕСЃРЅРѕРІРЅРѕР№ РѕР±СЉРµРј
   if(hedge_lot > main_lots)
      hedge_lot = main_lots;

   // Р’РѕР·РІСЂР°С‰Р°РµРј СЂР°СЃСЃС‡РёС‚Р°РЅРЅС‹Р№ РѕР±СЉРµРј С…РµРґР¶Р°
   return hedge_lot;
}

//+------------------------------------------------------------------+
//| РџРѕРёСЃРє РѕРїС‚РёРјР°Р»СЊРЅРѕР№ С‚РѕС‡РєРё РІС…РѕРґР° РґР»СЏ С…РµРґР¶РёСЂСѓСЋС‰РµРіРѕ РѕСЂРґРµСЂР°           |
//+------------------------------------------------------------------+
double FindOptimalHedgeEntry(int layer, int direction)
{
   // direction: 0 - Buy hedge (РїСЂРѕС‚РёРІ Sell), 1 - Sell hedge (РїСЂРѕС‚РёРІ Buy)
   // Р‘Р°Р·РѕРІР°СЏ С‚РѕС‡РєР° вЂ” С‚РµРєСѓС‰Р°СЏ С†РµРЅР°
   double entry_price = (direction == 0) ? SymbolInfoDouble(m_symbol, SYMBOL_ASK)
                                         : SymbolInfoDouble(m_symbol, SYMBOL_BID);

   // РЈС‡РµС‚ РІРѕР»Р°С‚РёР»СЊРЅРѕСЃС‚Рё (ATR)
   int atr_handle = iATR(m_symbol, PERIOD_CURRENT, 14);
   double atr_buffer[];
   ArraySetAsSeries(atr_buffer, true);
   CopyBuffer(atr_handle, 0, 0, 1, atr_buffer);
   double atr = atr_buffer[0];
   if(atr <= 0.0)
      atr = SymbolInfoDouble(m_symbol, SYMBOL_POINT) * 100; // fallback

   // РџРѕРёСЃРє Р±Р»РёР¶Р°Р№С€РёС… РєР»СЋС‡РµРІС‹С… СѓСЂРѕРІРЅРµР№ (KeyLevel)
   double best_level = 0.0;
   double min_distance = DBL_MAX;
   for(int i = 0; i < m_key_levels_count; i++)
   {
      double level_price = m_key_levels[i].price;
      double distance = MathAbs(level_price - entry_price);
      if(distance < min_distance && m_key_levels[i].strength > 50.0)
      {
         min_distance = distance;
         best_level = level_price;
      }
   }

   // Р•СЃР»Рё РЅР°Р№РґРµРЅ СЃРёР»СЊРЅС‹Р№ СѓСЂРѕРІРµРЅСЊ СЂСЏРґРѕРј, РёСЃРїРѕР»СЊР·СѓРµРј РµРіРѕ РєР°Рє С‚РѕС‡РєСѓ РІС…РѕРґР°
   if(best_level > 0.0 && min_distance < 2.0 * atr)
      entry_price = best_level;

   // РЈС‡РµС‚ РїР°С‚С‚РµСЂРЅРѕРІ (PatternStrength)
   for(int i = 0; i < m_pattern_count; i++)
   {
      if(m_current_patterns[i].strength > 60.0 && m_current_patterns[i].on_key_level)
      {
         // Р•СЃР»Рё РїР°С‚С‚РµСЂРЅ РїРѕРґС‚РІРµСЂР¶РґС‘РЅ Рё РЅР° СѓСЂРѕРІРЅРµ, РєРѕСЂСЂРµРєС‚РёСЂСѓРµРј С‚РѕС‡РєСѓ РІС…РѕРґР°
         entry_price = m_current_patterns[i].price_level;
         break;
      }
   }

   // РЈС‡РµС‚ СЃС‚СЂСѓРєС‚СѓСЂС‹ СЂС‹РЅРєР° (MarketStructureInfo)
   if(m_market_structure.type == STRUCTURE_REVERSAL_UP && direction == 0)
   {
      // Р”Р»СЏ Buy С…РµРґР¶Р° вЂ” РёС‰РµРј РІС…РѕРґ РїРѕСЃР»Рµ СЂР°Р·РІРѕСЂРѕС‚Р° РІРІРµСЂС…
      entry_price -= atr * 0.5;
   }
   else if(m_market_structure.type == STRUCTURE_REVERSAL_DOWN && direction == 1)
   {
      // Р”Р»СЏ Sell С…РµРґР¶Р° вЂ” РёС‰РµРј РІС…РѕРґ РїРѕСЃР»Рµ СЂР°Р·РІРѕСЂРѕС‚Р° РІРЅРёР·
      entry_price += atr * 0.5;
   }

   // РљРѕСЂСЂРµРєС‚РёСЂРѕРІРєР° РЅР° РјРёРЅРёРјР°Р»СЊРЅРѕРµ СЂР°СЃСЃС‚РѕСЏРЅРёРµ РґР»СЏ С…РµРґР¶Р°
   double min_dist = MinDistanceForHedge * SymbolInfoDouble(m_symbol, SYMBOL_POINT);
   if(direction == 0 && entry_price > SymbolInfoDouble(m_symbol, SYMBOL_ASK) - min_dist)
      entry_price = SymbolInfoDouble(m_symbol, SYMBOL_ASK) - min_dist;
   if(direction == 1 && entry_price < SymbolInfoDouble(m_symbol, SYMBOL_BID) + min_dist)
      entry_price = SymbolInfoDouble(m_symbol, SYMBOL_BID) + min_dist;

   // Р’РѕР·РІСЂР°С‰Р°РµРј СЂР°СЃСЃС‡РёС‚Р°РЅРЅСѓСЋ С‚РѕС‡РєСѓ РІС…РѕРґР°
   return entry_price;
}

// РЈРґР°Р»РµРЅР° РґСѓР±Р»РёСЂСѓСЋС‰Р°СЏСЃСЏ С„СѓРЅРєС†РёСЏ OpenHedgeOrder

//+------------------------------------------------------------------+
//| РџСЂРѕРіСЂРµСЃСЃРёРІРЅРѕРµ С‡Р°СЃС‚РёС‡РЅРѕРµ Р·Р°РєСЂС‹С‚РёРµ С…РµРґР¶РёСЂСѓСЋС‰РёС… РїРѕР·РёС†РёР№               |
//+------------------------------------------------------------------+
void PartialHedgeClose(int layer)
{
   // РњР°СЃСЃРёРІ РґР»СЏ С…СЂР°РЅРµРЅРёСЏ РёРЅС„РѕСЂРјР°С†РёРё Рѕ С…РµРґР¶РёСЂСѓСЋС‰РёС… РїРѕР·РёС†РёСЏС…
   static HedgingInfo hedge_info[50]; // Р”Рѕ 50 С…РµРґР¶РёСЂСѓСЋС‰РёС… РїРѕР·РёС†РёР№
   static int hedge_count = 0;
   
   // РћР±РЅРѕРІР»РµРЅРёРµ РёРЅС„РѕСЂРјР°С†РёРё Рѕ С…РµРґР¶РёСЂСѓСЋС‰РёС… РїРѕР·РёС†РёСЏС…
   hedge_count = UpdateHedgingInfo();
   
   // РџСЂРѕРІРµСЂРєР° РЅР°Р»РёС‡РёСЏ С…РµРґР¶РёСЂСѓСЋС‰РёС… РїРѕР·РёС†РёР№
   if(hedge_count == 0)
      return;
   
   // РќР°С…РѕРґРёРј СЃР°РјСѓСЋ РєСЂСѓРїРЅСѓСЋ Р°РєС‚РёРІРЅСѓСЋ С…РµРґР¶РёСЂСѓСЋС‰СѓСЋ РїРѕР·РёС†РёСЋ
   double max_hedge_lot = 0.0;
   int max_hedge_index = -1;
   for(int i = 0; i < hedge_count; i++)
   {
      if(hedge_info[i].is_active && hedge_info[i].hedge_lot > max_hedge_lot)
      {
         max_hedge_lot = hedge_info[i].hedge_lot;
         max_hedge_index = i;
      }
   }
   
   // Р•СЃР»Рё РЅРµ РЅР°Р№РґРµРЅРѕ Р°РєС‚РёРІРЅС‹С… С…РµРґР¶РёСЂСѓСЋС‰РёС… РїРѕР·РёС†РёР№, РІС‹С…РѕРґРёРј
   if(max_hedge_index == -1)
      return;
   
   // Р Р°СЃС‡РµС‚ РѕР±СЉРµРјР° РґР»СЏ С‡Р°СЃС‚РёС‡РЅРѕРіРѕ Р·Р°РєСЂС‹С‚РёСЏ
   double close_lot = max_hedge_lot * PartialHedgeClosePercent / 100.0;
   close_lot = NormalizeDouble(close_lot, 2);
   
   // РџСЂРѕРІРµСЂРєР° РЅР° РјРёРЅРёРјР°Р»СЊРЅС‹Р№ Рё РјР°РєСЃРёРјР°Р»СЊРЅС‹Р№ Р»РѕС‚
   double volume_step = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_STEP);
   double min_volume = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MIN);
   double max_volume = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MAX);
   if(close_lot < min_volume)
      close_lot = min_volume;
   if(close_lot > max_volume)
      close_lot = max_volume;
   
   // РџСЂРѕРІРµСЂРєР°, С‡С‚Рѕ РѕР±СЉРµРј РЅРµ РїСЂРµРІС‹С€Р°РµС‚ РѕСЃС‚Р°РІС€РёР№СЃСЏ РѕР±СЉРµРј С…РµРґР¶Р°
   if(close_lot > hedge_info[max_hedge_index].hedge_lot)
      close_lot = hedge_info[max_hedge_index].hedge_lot;
   
   // Р’С‹РїРѕР»РЅРµРЅРёРµ С‡Р°СЃС‚РёС‡РЅРѕРіРѕ Р·Р°РєСЂС‹С‚РёСЏ
   m_trade.SetExpertMagicNumber(m_magic + layer);
   bool result = m_trade.PositionClosePartial(hedge_info[max_hedge_index].hedge_ticket, close_lot, m_slippage);
   if(!result)
   {
      Print("РћС€РёР±РєР° С‡Р°СЃС‚РёС‡РЅРѕРіРѕ Р·Р°РєСЂС‹С‚РёСЏ С…РµРґР¶РёСЂСѓСЋС‰РµР№ РїРѕР·РёС†РёРё: ", m_trade.ResultRetcode());
      return;
   }
   
   // РћР±РЅРѕРІР»РµРЅРёРµ РёРЅС„РѕСЂРјР°С†РёРё Рѕ С…РµРґР¶РёСЂСѓСЋС‰РёС… РїРѕР·РёС†РёСЏС…
   UpdateHedgingInfo();
}

//+------------------------------------------------------------------+
//| РћР±РЅРѕРІР»РµРЅРёРµ РёРЅС„РѕСЂРјР°С†РёРё Рѕ С…РµРґР¶РёСЂСѓСЋС‰РёС… РїРѕР·РёС†РёСЏС…                     |
//+------------------------------------------------------------------+
int UpdateHedgingInfo()
{
   // РњР°СЃСЃРёРІ РґР»СЏ С…СЂР°РЅРµРЅРёСЏ РёРЅС„РѕСЂРјР°С†РёРё Рѕ С…РµРґР¶РёСЂСѓСЋС‰РёС… РїРѕР·РёС†РёСЏС…
   static HedgingInfo hedge_info[50]; // Р”Рѕ 50 С…РµРґР¶РёСЂСѓСЋС‰РёС… РїРѕР·РёС†РёР№
   static int hedge_count = 0;
   
   // РћС‡РёС‰Р°РµРј РёРЅС„РѕСЂРјР°С†РёСЋ Рѕ С…РµРґР¶РёСЂСѓСЋС‰РёС… РїРѕР·РёС†РёСЏС…
   for(int i = 0; i < hedge_count; i++)
      hedge_info[i].is_active = false;
      
   hedge_count = 0;
   
   // РЎРѕР±РёСЂР°РµРј РёРЅС„РѕСЂРјР°С†РёСЋ Рѕ РІСЃРµС… РїРѕР·РёС†РёСЏС…
   for(int layer = 0; layer < 3; layer++)
   {
      // РџСЂРѕРІРµСЂСЏРµРј Buy РїРѕР·РёС†РёРё
      for(int i = 0; i < m_buy_positions_count[layer]; i++)
      {
         if(PositionSelectByTicket(m_buy_tickets[layer][i]))
         {
            string comment = PositionGetString(POSITION_COMMENT);
            if(StringFind(comment, "Hedge") >= 0)
            {
               // Р­С‚Рѕ С…РµРґР¶РёСЂСѓСЋС‰Р°СЏ РїРѕР·РёС†РёСЏ
               hedge_info[hedge_count].is_active = true;
               hedge_info[hedge_count].hedge_ticket = m_buy_tickets[layer][i];
               hedge_info[hedge_count].hedge_lot = PositionGetDouble(POSITION_VOLUME);
               hedge_info[hedge_count].hedge_price = PositionGetDouble(POSITION_PRICE_OPEN);
               hedge_info[hedge_count].created_time = (datetime)PositionGetInteger(POSITION_TIME);
               hedge_info[hedge_count].hedge_direction = 0; // Buy hedge
               hedge_info[hedge_count].related_layer = layer;
               hedge_info[hedge_count].profit_at_hedge = PositionGetDouble(POSITION_PROFIT);
               hedge_info[hedge_count].optimal_ratio = OptimalHedgeRatio; // РСЃРїРѕР»СЊР·РѕРІР°С‚СЊ РЅР°СЃС‚СЂРѕР№РєСѓ
               
               // Р Р°СЃС‡РµС‚ С‚РµРєСѓС‰РµР№ РєРѕСЂСЂРµР»СЏС†РёРё (РјРѕР¶РЅРѕ СЂРµР°Р»РёР·РѕРІР°С‚СЊ Р±РѕР»РµРµ СЃР»РѕР¶РЅС‹Р№ Р°Р»РіРѕСЂРёС‚Рј)
               hedge_info[hedge_count].current_correlation = 0.0;
               
               hedge_count++;
               if(hedge_count >= 50) break; // РћРіСЂР°РЅРёС‡РµРЅРёРµ РЅР° РєРѕР»РёС‡РµСЃС‚РІРѕ РѕС‚СЃР»РµР¶РёРІР°РµРјС‹С… РїРѕР·РёС†РёР№
            }
         }
      }
      
      // РџСЂРѕРІРµСЂСЏРµРј Sell РїРѕР·РёС†РёРё
      for(int i = 0; i < m_sell_positions_count[layer]; i++)
      {
         if(PositionSelectByTicket(m_sell_tickets[layer][i]))
         {
            string comment = PositionGetString(POSITION_COMMENT);
            if(StringFind(comment, "Hedge") >= 0)
            {
               // Р­С‚Рѕ С…РµРґР¶РёСЂСѓСЋС‰Р°СЏ РїРѕР·РёС†РёСЏ
               hedge_info[hedge_count].is_active = true;
               hedge_info[hedge_count].hedge_ticket = m_sell_tickets[layer][i];
               hedge_info[hedge_count].hedge_lot = PositionGetDouble(POSITION_VOLUME);
               hedge_info[hedge_count].hedge_price = PositionGetDouble(POSITION_PRICE_OPEN);
               hedge_info[hedge_count].created_time = (datetime)PositionGetInteger(POSITION_TIME);
               hedge_info[hedge_count].hedge_direction = 1; // Sell hedge
               hedge_info[hedge_count].related_layer = layer;
               hedge_info[hedge_count].profit_at_hedge = PositionGetDouble(POSITION_PROFIT);
               hedge_info[hedge_count].optimal_ratio = OptimalHedgeRatio; // РСЃРїРѕР»СЊР·РѕРІР°С‚СЊ РЅР°СЃС‚СЂРѕР№РєСѓ
               
               // Р Р°СЃС‡РµС‚ С‚РµРєСѓС‰РµР№ РєРѕСЂСЂРµР»СЏС†РёРё (РјРѕР¶РЅРѕ СЂРµР°Р»РёР·РѕРІР°С‚СЊ Р±РѕР»РµРµ СЃР»РѕР¶РЅС‹Р№ Р°Р»РіРѕСЂРёС‚Рј)
               hedge_info[hedge_count].current_correlation = 0.0;
               
               hedge_count++;
               if(hedge_count >= 50) break; // РћРіСЂР°РЅРёС‡РµРЅРёРµ РЅР° РєРѕР»РёС‡РµСЃС‚РІРѕ РѕС‚СЃР»РµР¶РёРІР°РµРјС‹С… РїРѕР·РёС†РёР№
            }
         }
      }
   }
   
   return hedge_count;
}

//+------------------------------------------------------------------+
//| РћС‚РєСЂС‹С‚РёРµ С…РµРґР¶РёСЂСѓСЋС‰РµРіРѕ РѕСЂРґРµСЂР°                                    |
//+------------------------------------------------------------------+
bool OpenHedgeOrder(int layer, int direction)
{
   // direction: 0 - Buy hedge (РїСЂРѕС‚РёРІ Sell), 1 - Sell hedge (РїСЂРѕС‚РёРІ Buy)
   // 1. Р Р°СЃС‡С‘С‚ РѕР±СЉС‘РјР° С…РµРґР¶Р°
   double hedge_lot = CalculateOptimalHedgeLot(layer, direction);
   if(hedge_lot <= 0.0)
   {
      Print("[HEDGE] РќРµС‚ РїРѕР·РёС†РёР№ РґР»СЏ С…РµРґР¶РёСЂРѕРІР°РЅРёСЏ РёР»Рё РѕР±СЉС‘Рј С…РµРґР¶Р° = 0");
      return false;
   }

   // 2. РџРѕРёСЃРє РѕРїС‚РёРјР°Р»СЊРЅРѕР№ С‚РѕС‡РєРё РІС…РѕРґР°
   double entry_price = FindOptimalHedgeEntry(layer, direction);

   // 3. РџСЂРѕРІРµСЂРєР° РјР°СЂР¶Рё
   double margin_required = 0.0;
   if(!OrderCalcMargin((direction == 0 ? ORDER_TYPE_BUY : ORDER_TYPE_SELL), m_symbol, hedge_lot, entry_price, margin_required))
   {
      Print("[HEDGE] РћС€РёР±РєР° СЂР°СЃС‡С‘С‚Р° РјР°СЂР¶Рё РґР»СЏ С…РµРґР¶Р°: ", m_symbol, ", Р»РѕС‚: ", DoubleToString(hedge_lot, 8));
      return false;
   }
   double free_margin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
   if(free_margin < margin_required * 1.2)
   {
      Print("[HEDGE] РќРµРґРѕСЃС‚Р°С‚РѕС‡РЅРѕ СЃРІРѕР±РѕРґРЅРѕР№ РјР°СЂР¶Рё РґР»СЏ С…РµРґР¶Р°: С‚СЂРµР±СѓРµС‚СЃСЏ ", DoubleToString(margin_required, 2), ", РґРѕСЃС‚СѓРїРЅРѕ: ", DoubleToString(free_margin, 2));
      return false;
   }

   // 4. РџСЂРѕРІРµСЂРєР° Р»РёРјРёС‚РѕРІ РїРѕ РєРѕР»РёС‡РµСЃС‚РІСѓ С…РµРґР¶РµР№
   int hedge_count = 0;
   // Р—РґРµСЃСЊ РјРѕР¶РЅРѕ СЂРµР°Р»РёР·РѕРІР°С‚СЊ РїРѕРґСЃС‡С‘С‚ СѓР¶Рµ РѕС‚РєСЂС‹С‚С‹С… С…РµРґР¶РёСЂСѓСЋС‰РёС… РїРѕР·РёС†РёР№ (РїРѕ HedgingInfo)
   // ...
   if(hedge_count >= MaxHedgePositions)
   {
      Print("[HEDGE] Р”РѕСЃС‚РёРіРЅСѓС‚ Р»РёРјРёС‚ РїРѕ РєРѕР»РёС‡РµСЃС‚РІСѓ С…РµРґР¶РёСЂСѓСЋС‰РёС… РїРѕР·РёС†РёР№");
      return false;
   }

   // 5. Р—Р°РґРµСЂР¶РєР° РїРµСЂРµРґ РѕС‚РєСЂС‹С‚РёРµРј С…РµРґР¶Р° (РµСЃР»Рё С‚СЂРµР±СѓРµС‚СЃСЏ)
   if(HedgeOpenDelaySeconds > 0)
      Sleep(HedgeOpenDelaySeconds * 1000);

   // 6. РћС‚РєСЂС‹С‚РёРµ РѕСЂРґРµСЂР°
   m_trade.SetExpertMagicNumber(m_magic + layer);
   bool result = false;
   if(direction == 0)
   {
      result = m_trade.Buy(hedge_lot, m_symbol, entry_price, 0.0, 0.0, "Hedge Buy");
   }
   else
   {
      result = m_trade.Sell(hedge_lot, m_symbol, entry_price, 0.0, 0.0, "Hedge Sell");
   }

   if(result)
   {
      Print("[HEDGE] РћС‚РєСЂС‹С‚ С…РµРґР¶РёСЂСѓСЋС‰РёР№ РѕСЂРґРµСЂ: ", (direction == 0 ? "Buy" : "Sell"), ", Р›РѕС‚: ", DoubleToString(hedge_lot, 8), ", Р¦РµРЅР°: ", DoubleToString(entry_price, m_digits));
      // Р—РґРµСЃСЊ РјРѕР¶РЅРѕ РґРѕР±Р°РІРёС‚СЊ Р·Р°РїРёСЃСЊ РІ РјР°СЃСЃРёРІ HedgingInfo
      // ...
      return true;
   }
   else
   {
      Print("[HEDGE] РћС€РёР±РєР° РѕС‚РєСЂС‹С‚РёСЏ С…РµРґР¶РёСЂСѓСЋС‰РµРіРѕ РѕСЂРґРµСЂР°: ", m_trade.ResultRetcode());
      return false;
   }
}

//+------------------------------------------------------------------+
//| Р§Р°СЃС‚РёС‡РЅРѕРµ Р·Р°РєСЂС‹С‚РёРµ С…РµРґР¶РёСЂСѓСЋС‰РёС… РїРѕР·РёС†РёР№ (РїСЂРѕРіСЂРµСЃСЃРёРІРЅРѕРµ)           |
//+------------------------------------------------------------------+
bool PartialHedgeClose(int layer, int direction)
{
   // direction: 0 - Buy hedge, 1 - Sell hedge
   // РџРѕРёСЃРє СЃР°РјРѕР№ РєСЂСѓРїРЅРѕР№ Р°РєС‚РёРІРЅРѕР№ С…РµРґР¶РёСЂСѓСЋС‰РµР№ РїРѕР·РёС†РёРё
   ulong hedge_ticket = 0;
   double max_lot = 0.0;
   for(int i = 0; i < m_buy_positions_count[layer]; i++)
   {
      if(direction == 0 && PositionSelectByTicket(m_buy_tickets[layer][i]))
      {
         double lot = PositionGetDouble(POSITION_VOLUME);
         if(lot > max_lot)
         {
            max_lot = lot;
            hedge_ticket = m_buy_tickets[layer][i];
         }
      }
   }
   for(int i = 0; i < m_sell_positions_count[layer]; i++)
   {
      if(direction == 1 && PositionSelectByTicket(m_sell_tickets[layer][i]))
      {
         double lot = PositionGetDouble(POSITION_VOLUME);
         if(lot > max_lot)
         {
            max_lot = lot;
            hedge_ticket = m_sell_tickets[layer][i];
         }
      }
   }
   if(hedge_ticket == 0 || max_lot <= 0.0)
   {
      Print("[HEDGE] РќРµС‚ Р°РєС‚РёРІРЅС‹С… С…РµРґР¶РёСЂСѓСЋС‰РёС… РїРѕР·РёС†РёР№ РґР»СЏ С‡Р°СЃС‚РёС‡РЅРѕРіРѕ Р·Р°РєСЂС‹С‚РёСЏ");
      return false;
   }

   // Р Р°СЃС‡РµС‚ РѕР±СЉРµРјР° РґР»СЏ С‡Р°СЃС‚РёС‡РЅРѕРіРѕ Р·Р°РєСЂС‹С‚РёСЏ
   double close_volume = max_lot * PartialHedgeClosePercent / 100.0;
   double volume_step = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_STEP);
   double min_volume = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MIN);
   double max_volume = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MAX);
   close_volume = NormalizeDouble(MathRound(close_volume / volume_step) * volume_step, 8);
   if(close_volume < min_volume)
      close_volume = min_volume;
   if(close_volume > max_volume)
      close_volume = max_volume;
   if(close_volume >= max_lot)
      close_volume = max_lot;
   if(close_volume <= 0)
   {
      Print("[HEDGE] РћР±СЉРµРј РґР»СЏ С‡Р°СЃС‚РёС‡РЅРѕРіРѕ Р·Р°РєСЂС‹С‚РёСЏ С…РµРґР¶Р° РЅРµРєРѕСЂСЂРµРєС‚РµРЅ");
      return false;
   }

   // Р’С‹РїРѕР»РЅРµРЅРёРµ С‡Р°СЃС‚РёС‡РЅРѕРіРѕ Р·Р°РєСЂС‹С‚РёСЏ
   m_trade.SetExpertMagicNumber(m_magic + layer);
   if(m_trade.PositionClosePartial(hedge_ticket, close_volume, m_slippage))
   {
      Print("[HEDGE] Р§Р°СЃС‚РёС‡РЅРѕ Р·Р°РєСЂС‹С‚Р° С…РµРґР¶РёСЂСѓСЋС‰Р°СЏ РїРѕР·РёС†РёСЏ: Ticket ", hedge_ticket, ", Volume: ", DoubleToString(close_volume, 8));
      return true;
   }
   else
   {
      Print("[HEDGE] РћС€РёР±РєР° РїСЂРё С‡Р°СЃС‚РёС‡РЅРѕРј Р·Р°РєСЂС‹С‚РёРё С…РµРґР¶РёСЂСѓСЋС‰РµР№ РїРѕР·РёС†РёРё: ", m_trade.ResultRetcode());
      return false;
   }
}

//+------------------------------------------------------------------+
//| РџСЂРёРјРµРЅРµРЅРёРµ РёРЅС‚РµР»Р»РµРєС‚СѓР°Р»СЊРЅРѕРіРѕ СѓСЃСЂРµРґРЅРµРЅРёСЏ                           |
//+------------------------------------------------------------------+
bool ApplySmartAveraging(int layer)
{
   // РџСЂРѕРІРµСЂРєР° РєРѕР»РёС‡РµСЃС‚РІР° СѓР¶Рµ РґРѕР±Р°РІР»РµРЅРЅС‹С… РїРѕР·РёС†РёР№ РґР»СЏ РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёСЏ
   if(m_recovery_positions_added >= m_max_recovery_positions)
      return false;
   
   // РћРїСЂРµРґРµР»РµРЅРёРµ РЅР°РїСЂР°РІР»РµРЅРёСЏ РѕСЃРЅРѕРІРЅС‹С… РїРѕР·РёС†РёР№ СЃР»РѕСЏ
   int direction = -1; // -1 - РЅРµ РѕРїСЂРµРґРµР»РµРЅРѕ, 0 - Buy, 1 - Sell
   
   if(m_buy_positions_count[layer] > m_sell_positions_count[layer])
      direction = 0; // РџСЂРµРѕР±Р»Р°РґР°СЋС‚ Buy РїРѕР·РёС†РёРё
   else if(m_sell_positions_count[layer] > m_buy_positions_count[layer])
      direction = 1; // РџСЂРµРѕР±Р»Р°РґР°СЋС‚ Sell РїРѕР·РёС†РёРё
   else if(m_buy_positions_count[layer] > 0 && m_sell_positions_count[layer] > 0)
      direction = (m_buy_lots_total[layer] > m_sell_lots_total[layer]) ? 0 : 1; // РџРѕ РѕР±С‰РµРјСѓ РѕР±СЉРµРјСѓ
   else
      return false; // РќРµС‚ РѕС‚РєСЂС‹С‚С‹С… РїРѕР·РёС†РёР№
   
   // Р Р°СЃС‡РµС‚ С‚РѕС‡РєРё РІС…РѕРґР° Рё СЂР°Р·РјРµСЂР° Р»РѕС‚Р°
   double entry_price = CalculateOptimalAveragingPoint(layer, direction, m_recovery_positions_added);
   double lot_size = CalculateRecoveryLot(layer, direction);
   
   // РџРѕР»СѓС‡Р°РµРј РїР°СЂР°РјРµС‚СЂС‹ РѕР±СЉРµРјР° РґР»СЏ СЃРёРјРІРѕР»Р°
   double volume_step = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_STEP);
   double min_volume = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MIN);
   double max_volume = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MAX);
   
   // РљРѕСЂСЂРµРєС‚РёСЂРѕРІРєР° СЂР°Р·РјРµСЂР° Р»РѕС‚Р° РІ СЃРѕРѕС‚РІРµС‚СЃС‚РІРёРё СЃ С‚СЂРµР±РѕРІР°РЅРёСЏРјРё РёРЅСЃС‚СЂСѓРјРµРЅС‚Р°
   lot_size = NormalizeDouble(MathRound(lot_size / volume_step) * volume_step, 8);
   
   // РџСЂРѕРІРµСЂРєР° РЅР° РјРёРЅРёРјР°Р»СЊРЅС‹Р№ РѕР±СЉРµРј
   if(lot_size < min_volume)
      lot_size = min_volume;
   
   // РџСЂРѕРІРµСЂРєР° РЅР° РјР°РєСЃРёРјР°Р»СЊРЅС‹Р№ РѕР±СЉРµРј
   if(lot_size > max_volume)
      lot_size = max_volume;
   
   // РџСЂРѕРІРµСЂРєР°, С‡С‚Рѕ РѕР±СЉРµРј РЅРµ СЂР°РІРµРЅ РЅСѓР»СЋ
   if(lot_size <= 0)
      return false;
   
   // РџСЂРѕРІРµСЂРєР° РѕР±СЉРµРјР° РїРµСЂРµРґ РѕС‚РїСЂР°РІРєРѕР№ РѕСЂРґРµСЂР°
   if(lot_size < min_volume || lot_size > max_volume || 
      MathAbs(lot_size - MathRound(lot_size / volume_step) * volume_step) > 0.0000001)
   {
      Print("РќРµРІРѕР·РјРѕР¶РЅРѕ РѕС‚РєСЂС‹С‚СЊ РїРѕР·РёС†РёСЋ СЃ РѕР±СЉРµРјРѕРј ", lot_size, 
            " РґР»СЏ ", m_symbol, ". РњРёРЅРёРјСѓРј: ", min_volume, 
            ", РњР°РєСЃРёРјСѓРј: ", max_volume, ", РЁР°Рі: ", volume_step);
      return false;
   }
   
   // РџСЂРѕРІРµСЂРєР° РґРѕСЃС‚Р°С‚РѕС‡РЅРѕСЃС‚Рё РјР°СЂР¶Рё РїРµСЂРµРґ РѕС‚РєСЂС‹С‚РёРµРј РїРѕР·РёС†РёРё
   double margin_required = 0.0;
   
   // Р Р°СЃС‡РµС‚ С‚СЂРµР±СѓРµРјРѕР№ РјР°СЂР¶Рё РґР»СЏ РЅРѕРІРѕР№ РїРѕР·РёС†РёРё
   if(!OrderCalcMargin((direction == 0 ? ORDER_TYPE_BUY : ORDER_TYPE_SELL), m_symbol, lot_size, 
                    (direction == 0 ? SymbolInfoDouble(m_symbol, SYMBOL_ASK) : SymbolInfoDouble(m_symbol, SYMBOL_BID)), 
                    margin_required))
   {
      Print("РћС€РёР±РєР° СЂР°СЃС‡РµС‚Р° РјР°СЂР¶Рё РґР»СЏ ", m_symbol, " СЃ РѕР±СЉРµРјРѕРј ", DoubleToString(lot_size, 8));
      return false;
   }
   
   // РџСЂРѕРІРµСЂРєР° РґРѕСЃС‚Р°С‚РѕС‡РЅРѕСЃС‚Рё СЃРІРѕР±РѕРґРЅРѕР№ РјР°СЂР¶Рё
   double free_margin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
   if(free_margin < margin_required * 1.2) // Р”РѕР±Р°РІР»СЏРµРј 20% Р·Р°РїР°СЃ РґР»СЏ Р±РµР·РѕРїР°СЃРЅРѕСЃС‚Рё
   {
      Print("РќРµРґРѕСЃС‚Р°С‚РѕС‡РЅРѕ СЃРІРѕР±РѕРґРЅРѕР№ РјР°СЂР¶Рё РґР»СЏ РѕС‚РєСЂС‹С‚РёСЏ РїРѕР·РёС†РёРё РґР»СЏ РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёСЏ ", m_symbol, 
           " СЃ РѕР±СЉРµРјРѕРј ", DoubleToString(lot_size, 8), ". РўСЂРµР±СѓРµС‚СЃСЏ: ", 
           DoubleToString(margin_required, 2), ", Р”РѕСЃС‚СѓРїРЅРѕ: ", DoubleToString(free_margin, 2));
      // Р’ СЃР»СѓС‡Р°Рµ РЅРµРґРѕСЃС‚Р°С‚РєР° СЃСЂРµРґСЃС‚РІ, СѓРјРµРЅСЊС€Р°РµРј Р»РѕС‚ РµС‰Рµ СЂР°Р·, РЅРѕ РїСЂРѕРІРµСЂСЏРµРј РјРёРЅРёРјР°Р»СЊРЅС‹Р№ Р»РѕС‚
      lot_size = NormalizeDouble(lot_size * 0.5, 8);
      if(lot_size < min_volume)
      {
         Print("РќРµРІРѕР·РјРѕР¶РЅРѕ РґР°Р»СЊС€Рµ СѓРјРµРЅСЊС€РёС‚СЊ Р»РѕС‚ РґР»СЏ РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёСЏ. РњРёРЅРёРјР°Р»СЊРЅС‹Р№ Р»РѕС‚: ", min_volume);
         return false;
      }
      
      // РџРµСЂРµСЃС‡РёС‚С‹РІР°РµРј С‚СЂРµР±СѓРµРјСѓСЋ РјР°СЂР¶Сѓ РґР»СЏ СѓРјРµРЅСЊС€РµРЅРЅРѕРіРѕ Р»РѕС‚Р°
      if(!OrderCalcMargin((direction == 0 ? ORDER_TYPE_BUY : ORDER_TYPE_SELL), m_symbol, lot_size, 
                       (direction == 0 ? SymbolInfoDouble(m_symbol, SYMBOL_ASK) : SymbolInfoDouble(m_symbol, SYMBOL_BID)), 
                       margin_required))
      {
         Print("РћС€РёР±РєР° СЂР°СЃС‡РµС‚Р° РјР°СЂР¶Рё РґР»СЏ СѓРјРµРЅСЊС€РµРЅРЅРѕРіРѕ Р»РѕС‚Р° ", m_symbol, " СЃ РѕР±СЉРµРјРѕРј ", DoubleToString(lot_size, 8));
         return false;
      }
      
      // РџСЂРѕРІРµСЂСЏРµРј РµС‰Рµ СЂР°Р· РґРѕСЃС‚Р°С‚РѕС‡РЅРѕСЃС‚СЊ РјР°СЂР¶Рё
      if(free_margin < margin_required * 1.2)
      {
         Print("РќРµРґРѕСЃС‚Р°С‚РѕС‡РЅРѕ СЃРІРѕР±РѕРґРЅРѕР№ РјР°СЂР¶Рё РґР°Р¶Рµ РґР»СЏ СѓРјРµРЅСЊС€РµРЅРЅРѕРіРѕ Р»РѕС‚Р°. РўСЂРµР±СѓРµС‚СЃСЏ: ", 
               DoubleToString(margin_required, 2), ", Р”РѕСЃС‚СѓРїРЅРѕ: ", DoubleToString(free_margin, 2));
         return false;
      }
      
      Print("Р›РѕС‚ СѓРјРµРЅСЊС€РµРЅ РґРѕ ", DoubleToString(lot_size, 8), " РёР·-Р·Р° РЅРµРґРѕСЃС‚Р°С‚РєР° РјР°СЂР¶Рё");
   }
   
   // РЈСЃС‚Р°РЅРѕРІРєР° РѕСЂРґРµСЂР°
   ulong ticket = 0;
   m_trade.SetExpertMagicNumber(m_magic + layer);
   
   if(direction == 0) // Buy
   {
      if(m_trade.Buy(lot_size, m_symbol, 0.0, 0.0, 0.0, 
         "Recovery Buy #" + IntegerToString(m_recovery_positions_added + 1)))
      {
         ticket = m_trade.ResultOrder();
         m_recovery_position_tickets[m_recovery_positions_added] = (int)ticket;
         m_recovery_positions_added++;
         Print("Р”РѕР±Р°РІР»РµРЅР° РїРѕР·РёС†РёСЏ РґР»СЏ РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёСЏ: Buy, Symbol: ", m_symbol, 
               ", Lot: ", DoubleToString(lot_size, 8));
         return true;
      }
   }
   else // Sell
   {
      if(m_trade.Sell(lot_size, m_symbol, 0.0, 0.0, 0.0, 
         "Recovery Sell #" + IntegerToString(m_recovery_positions_added + 1)))
      {
         ticket = m_trade.ResultOrder();
         m_recovery_position_tickets[m_recovery_positions_added] = (int)ticket;
         m_recovery_positions_added++;
         Print("Р”РѕР±Р°РІР»РµРЅР° РїРѕР·РёС†РёСЏ РґР»СЏ РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёСЏ: Sell, Symbol: ", m_symbol, 
               ", Lot: ", DoubleToString(lot_size, 8));
         return true;
      }
   }
   
   Print("РћС€РёР±РєР° РїСЂРё РґРѕР±Р°РІР»РµРЅРёРё РїРѕР·РёС†РёРё РґР»СЏ РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёСЏ: ", m_trade.ResultRetcode(), 
         " РґР»СЏ ", m_symbol, " СЃ РѕР±СЉРµРјРѕРј ", DoubleToString(lot_size, 8));
   return false;
}

//+------------------------------------------------------------------+
//| Р§Р°СЃС‚РёС‡РЅРѕРµ Р·Р°РєСЂС‹С‚РёРµ РїРѕР·РёС†РёР№                                        |
//+------------------------------------------------------------------+
bool ExecutePartialClose(int layer)
{
   // РџСЂРѕРІРµСЂРєР° РЅР°Р»РёС‡РёСЏ РїРѕР·РёС†РёР№
   if(m_buy_positions_count[layer] == 0 && m_sell_positions_count[layer] == 0)
      return false;
   
   // РћРїСЂРµРґРµР»РµРЅРёРµ РЅР°РїСЂР°РІР»РµРЅРёСЏ Рё РЅР°РёР±РѕР»СЊС€РµРіРѕ СѓР±С‹С‚РєР°
   int direction = -1; // -1 - РЅРµ РѕРїСЂРµРґРµР»РµРЅРѕ, 0 - Buy, 1 - Sell
   int worst_pos_index = -1;
   double worst_profit = 0.0;
   ulong worst_ticket = 0;
   
   // РџСЂРѕРІРµСЂРєР° Buy РїРѕР·РёС†РёР№
   for(int i = 0; i < m_buy_positions_count[layer]; i++)
   {
      if(PositionSelectByTicket(m_buy_tickets[layer][i]))
      {
         double profit = PositionGetDouble(POSITION_PROFIT);
         if(profit < worst_profit)
         {
            worst_profit = profit;
            worst_pos_index = i;
            worst_ticket = m_buy_tickets[layer][i];
            direction = 0;
         }
      }
   }
   
   // РџСЂРѕРІРµСЂРєР° Sell РїРѕР·РёС†РёР№
   for(int i = 0; i < m_sell_positions_count[layer]; i++)
   {
      if(PositionSelectByTicket(m_sell_tickets[layer][i]))
      {
         double profit = PositionGetDouble(POSITION_PROFIT);
         if(profit < worst_profit)
         {
            worst_profit = profit;
            worst_pos_index = i;
            worst_ticket = m_sell_tickets[layer][i];
            direction = 1;
         }
      }
   }
   
   // Р•СЃР»Рё РЅРµ РЅР°Р№РґРµРЅРѕ СѓР±С‹С‚РѕС‡РЅС‹С… РїРѕР·РёС†РёР№
   if(worst_pos_index == -1 || worst_ticket == 0)
      return false;
   
   // Р’С‹Р±РѕСЂ РїРѕР·РёС†РёРё СЃ РЅР°РёР±РѕР»СЊС€РёРј СѓР±С‹С‚РєРѕРј
   if(!PositionSelectByTicket(worst_ticket))
      return false;
   
   // РџРѕР»СѓС‡Р°РµРј РёРЅС„РѕСЂРјР°С†РёСЋ Рѕ СЃРёРјРІРѕР»Рµ РґР»СЏ РєРѕСЂСЂРµРєС‚РЅРѕР№ СЂР°Р±РѕС‚С‹ СЃ РѕР±СЉРµРјР°РјРё
   string symbol = PositionGetString(POSITION_SYMBOL);
   double pos_volume = PositionGetDouble(POSITION_VOLUME);
   
   // РџРѕР»СѓС‡Р°РµРј РїР°СЂР°РјРµС‚СЂС‹ РѕР±СЉРµРјР° РґР»СЏ РґР°РЅРЅРѕРіРѕ РёРЅСЃС‚СЂСѓРјРµРЅС‚Р°
   double volume_step = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);
   double min_volume = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
   double max_volume = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MAX);
   
   // Р Р°СЃС‡РµС‚ РѕР±СЉРµРјР° РґР»СЏ С‡Р°СЃС‚РёС‡РЅРѕРіРѕ Р·Р°РєСЂС‹С‚РёСЏ
   double close_volume = pos_volume * m_partial_close_percent / 100.0;
   
   // РљРѕСЂСЂРµРєС‚РёСЂРѕРІРєР° РѕР±СЉРµРјР° РІ СЃРѕРѕС‚РІРµС‚СЃС‚РІРёРё СЃ С€Р°РіРѕРј РѕР±СЉРµРјР° РёРЅСЃС‚СЂСѓРјРµРЅС‚Р°
   close_volume = NormalizeDouble(MathRound(close_volume / volume_step) * volume_step, 8);
   
   // РџСЂРѕРІРµСЂРєР° РЅР° РјРёРЅРёРјР°Р»СЊРЅС‹Р№ РѕР±СЉРµРј
   if(close_volume < min_volume)
      close_volume = min_volume;
   
   // РџСЂРѕРІРµСЂРєР° РЅР° РјР°РєСЃРёРјР°Р»СЊРЅС‹Р№ РѕР±СЉРµРј
   if(close_volume > max_volume)
      close_volume = max_volume;
   
   // РџСЂРѕРІРµСЂРєР°, С‡С‚РѕР±С‹ РѕР±СЉРµРј Р·Р°РєСЂС‹С‚РёСЏ РЅРµ РїСЂРµРІС‹С€Р°Р» РѕР±СЉРµРј РїРѕР·РёС†РёРё
   if(close_volume >= pos_volume)
      close_volume = pos_volume;
   
   // РџСЂРѕРІРµСЂРєР°, С‡С‚Рѕ РѕР±СЉРµРј РЅРµ СЂР°РІРµРЅ РЅСѓР»СЋ
   if(close_volume <= 0)
      return false;
   
   // РџСЂРѕРІРµСЂРєР° РѕР±СЉРµРјР° РїРµСЂРµРґ РѕС‚РїСЂР°РІРєРѕР№ РѕСЂРґРµСЂР°
   if(close_volume < min_volume || close_volume > max_volume || 
      MathAbs(close_volume - MathRound(close_volume / volume_step) * volume_step) > 0.0000001)
   {
      Print("РќРµРІРѕР·РјРѕР¶РЅРѕ Р·Р°РєСЂС‹С‚СЊ РїРѕР·РёС†РёСЋ СЃ РѕР±СЉРµРјРѕРј ", close_volume, 
            " РґР»СЏ ", symbol, ". РњРёРЅРёРјСѓРј: ", min_volume, 
            ", РњР°РєСЃРёРјСѓРј: ", max_volume, ", РЁР°Рі: ", volume_step);
      return false;
   }
   
   // Р§Р°СЃС‚РёС‡РЅРѕРµ Р·Р°РєСЂС‹С‚РёРµ РїРѕР·РёС†РёРё
   m_trade.SetExpertMagicNumber(m_magic + layer);
   
   if(m_trade.PositionClosePartial(worst_ticket, close_volume, m_slippage))
   {
      m_positions_closed++;
      Print("Р§Р°СЃС‚РёС‡РЅРѕРµ Р·Р°РєСЂС‹С‚РёРµ РїРѕР·РёС†РёРё: Ticket ", worst_ticket, ", Symbol: ", symbol, 
            ", Volume: ", DoubleToString(close_volume, 8));
      return true;
   }
   
   Print("РћС€РёР±РєР° РїСЂРё С‡Р°СЃС‚РёС‡РЅРѕРј Р·Р°РєСЂС‹С‚РёРё РїРѕР·РёС†РёРё: ", m_trade.ResultRetcode(), 
         " РґР»СЏ ", symbol, " СЃ РѕР±СЉРµРјРѕРј ", DoubleToString(close_volume, 8));
   return false;
}

//+------------------------------------------------------------------+
//| РџСЂРёРјРµРЅРµРЅРёРµ РїСЂРѕРіСЂРµСЃСЃРёРІРЅРѕРіРѕ С‚СЂРµР№Р»РёРЅРіР°                               |
//+------------------------------------------------------------------+
void ApplyProgressiveTrailing(int layer)
{
   // Р Р°СЃС‡РµС‚ РїСЂРѕС†РµРЅС‚Р° РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёСЏ
   double recovery_percent = CalculateRecoveryPercent();
   
   // Р•СЃР»Рё РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёРµ РјРµРЅРµРµ СѓРєР°Р·Р°РЅРЅРѕРіРѕ СѓСЂРѕРІРЅСЏ, РЅРµ РїСЂРёРјРµРЅСЏРµРј С‚СЂРµР№Р»РёРЅРі
   if(recovery_percent < m_trailing_activation_level * 100.0)
      return;
   
   // РђРґР°РїС‚РёРІРЅР°СЏ РЅР°СЃС‚СЂРѕР№РєР° С‚СЂРµР№Р»РёРЅРіР° РІ Р·Р°РІРёСЃРёРјРѕСЃС‚Рё РѕС‚ РїСЂРѕС†РµРЅС‚Р° РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёСЏ
   double trailing_points = m_tralling_stop;
   
   if(recovery_percent > 25.0)
      trailing_points *= 0.8;
   if(recovery_percent > 50.0)
      trailing_points *= 0.7;
   if(recovery_percent > 75.0)
      trailing_points *= 0.5;
   if(recovery_percent > 90.0)
      trailing_points *= 0.3;
   
   // РЈСЃС‚Р°РЅРѕРІРєР° С‚СЂРµР№Р»РёРЅРіР° РґР»СЏ Buy РїРѕР·РёС†РёР№
   for(int i = 0; i < m_buy_positions_count[layer]; i++)
   {
      if(!PositionSelectByTicket(m_buy_tickets[layer][i]))
         continue;
      
      double current_sl = PositionGetDouble(POSITION_SL);
      double current_price = SymbolInfoDouble(m_symbol, SYMBOL_BID);
      
      // РџРѕР»СѓС‡РµРЅРёРµ РїР°СЂР°РјРµС‚СЂРѕРІ РґР»СЏ РєРѕСЂСЂРµРєС‚РЅРѕР№ СѓСЃС‚Р°РЅРѕРІРєРё СЃС‚РѕРї-Р»РѕСЃСЃР°
      int digits = (int)SymbolInfoInteger(m_symbol, SYMBOL_DIGITS);
      double point = SymbolInfoDouble(m_symbol, SYMBOL_POINT);
      double stops_level = SymbolInfoInteger(m_symbol, SYMBOL_TRADE_STOPS_LEVEL) * point;
      
      // Р Р°СЃС‡РµС‚ РЅРѕРІРѕРіРѕ СЃС‚РѕРї-Р»РѕСЃСЃР°
      double new_sl = NormalizeDouble(current_price - trailing_points * m_point * m_point_digits, digits);
      
      // РџСЂРѕРІРµСЂРєР° РЅР° РјРёРЅРёРјР°Р»СЊРЅРѕРµ СЂР°СЃСЃС‚РѕСЏРЅРёРµ СЃС‚РѕРї-Р»РѕСЃСЃР° РѕС‚ С‚РµРєСѓС‰РµР№ С†РµРЅС‹
      if(current_price - new_sl < stops_level)
         new_sl = NormalizeDouble(current_price - stops_level - point, digits);
      
      // Р•СЃР»Рё РЅРѕРІС‹Р№ СЃС‚РѕРї-Р»РѕСЃСЃ РІС‹С€Рµ С‚РµРєСѓС‰РµРіРѕ РёР»Рё СЃС‚РѕРї-Р»РѕСЃСЃ РЅРµ СѓСЃС‚Р°РЅРѕРІР»РµРЅ
      if((new_sl > current_sl || current_sl == 0.0) && new_sl > 0.0)
      {
         m_trade.SetExpertMagicNumber(m_magic + layer);
         
         // РџСЂРѕРІРµСЂРєР° СЂРµР·СѓР»СЊС‚Р°С‚Р° РјРѕРґРёС„РёРєР°С†РёРё
         if(!m_trade.PositionModify(m_buy_tickets[layer][i], new_sl, 0))
         {
            Print("РћС€РёР±РєР° РјРѕРґРёС„РёРєР°С†РёРё СЃС‚РѕРї-Р»РѕСЃСЃР° РґР»СЏ Buy #", m_buy_tickets[layer][i], 
                  ": ", m_trade.ResultRetcode(), ", sl: ", DoubleToString(new_sl, digits));
         }
      }
   }
   
   // РЈСЃС‚Р°РЅРѕРІРєР° С‚СЂРµР№Р»РёРЅРіР° РґР»СЏ Sell РїРѕР·РёС†РёР№
   for(int i = 0; i < m_sell_positions_count[layer]; i++)
   {
      if(!PositionSelectByTicket(m_sell_tickets[layer][i]))
         continue;
      
      double current_sl = PositionGetDouble(POSITION_SL);
      double current_price = SymbolInfoDouble(m_symbol, SYMBOL_ASK);
      
      // РџРѕР»СѓС‡РµРЅРёРµ РїР°СЂР°РјРµС‚СЂРѕРІ РґР»СЏ РєРѕСЂСЂРµРєС‚РЅРѕР№ СѓСЃС‚Р°РЅРѕРІРєРё СЃС‚РѕРї-Р»РѕСЃСЃР°
      int digits = (int)SymbolInfoInteger(m_symbol, SYMBOL_DIGITS);
      double point = SymbolInfoDouble(m_symbol, SYMBOL_POINT);
      double stops_level = SymbolInfoInteger(m_symbol, SYMBOL_TRADE_STOPS_LEVEL) * point;
      
      // Р Р°СЃС‡РµС‚ РЅРѕРІРѕРіРѕ СЃС‚РѕРї-Р»РѕСЃСЃР°
      double new_sl = NormalizeDouble(current_price + trailing_points * m_point * m_point_digits, digits);
      
      // РџСЂРѕРІРµСЂРєР° РЅР° РјРёРЅРёРјР°Р»СЊРЅРѕРµ СЂР°СЃСЃС‚РѕСЏРЅРёРµ СЃС‚РѕРї-Р»РѕСЃСЃР° РѕС‚ С‚РµРєСѓС‰РµР№ С†РµРЅС‹
      if(new_sl - current_price < stops_level)
         new_sl = NormalizeDouble(current_price + stops_level + point, digits);
      
      // Р•СЃР»Рё РЅРѕРІС‹Р№ СЃС‚РѕРї-Р»РѕСЃСЃ РЅРёР¶Рµ С‚РµРєСѓС‰РµРіРѕ РёР»Рё СЃС‚РѕРї-Р»РѕСЃСЃ РЅРµ СѓСЃС‚Р°РЅРѕРІР»РµРЅ
      if((new_sl < current_sl || current_sl == 0.0) && new_sl > 0.0)
      {
         m_trade.SetExpertMagicNumber(m_magic + layer);
         
         // РџСЂРѕРІРµСЂРєР° СЂРµР·СѓР»СЊС‚Р°С‚Р° РјРѕРґРёС„РёРєР°С†РёРё
         if(!m_trade.PositionModify(m_sell_tickets[layer][i], new_sl, PositionGetDouble(POSITION_TP)))
         {
            Print("РћС€РёР±РєР° РјРѕРґРёС„РёРєР°С†РёРё СЃС‚РѕРї-Р»РѕСЃСЃР° РґР»СЏ Sell #", m_sell_tickets[layer][i], 
                  ": ", m_trade.ResultRetcode(), ", sl: ", DoubleToString(new_sl, digits));
         }
      }
   }
}

//+------------------------------------------------------------------+
//| РЈРїСЂР°РІР»РµРЅРёРµ СЃРјР°СЂС‚-РІС‹С…РѕРґРѕРј РёР· РїСЂРѕСЃР°РґРєРё                              |
//+------------------------------------------------------------------+
void ManageSmartExit()
{
   // РџСЂРѕРІРµСЂРєР° РІРєР»СЋС‡РµРЅРёСЏ СѓРјРЅРѕРіРѕ РІС‹С…РѕРґР°
   if(!m_use_smart_exit)
      return;
   
   // РџСЂРѕРІРµСЂРєР° РїР°СѓР·С‹ РїРѕСЃР»Рµ РІС‹С…РѕРґР°
   if(m_drawdown_pause_until > 0 && TimeCurrent() < m_drawdown_pause_until)
      return;
   
   // РџСЂРѕРІРµСЂРєР° РєСЂРёС‚РёС‡РµСЃРєРѕР№ РјР°СЂР¶Рё
   double free_margin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
   double balance = AccountInfoDouble(ACCOUNT_BALANCE);
   
   // Р•СЃР»Рё РјР°СЂР¶Р° СЃРёР»СЊРЅРѕ РѕС‚СЂРёС†Р°С‚РµР»СЊРЅР°СЏ, Р°РєС‚РёРІРёСЂСѓРµРј РїСЂРёРЅСѓРґРёС‚РµР»СЊРЅРѕРµ РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёРµ
   if(free_margin < 0 && MathAbs(free_margin) > balance * 0.3 && !m_recovery_active)
   {
      m_recovery_active = true;
      m_drawdown_detected = true;
      m_recovery_positions_added = 0;
      m_positions_closed = 0;
      m_drawdown_start_time = TimeCurrent();
      m_drawdown_max_value = MathAbs(free_margin) / balance * 100.0;
      m_current_drawdown_percent = m_drawdown_max_value;
      
      Print("Р’РќРРњРђРќРР•! РђРєС‚РёРІРёСЂРѕРІР°РЅРѕ РїСЂРёРЅСѓРґРёС‚РµР»СЊРЅРѕРµ РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёРµ РёР·-Р·Р° РѕС‚СЂРёС†Р°С‚РµР»СЊРЅРѕР№ РјР°СЂР¶Рё: ", 
            DoubleToString(free_margin, 2), ", РџСЂРѕСЃР°РґРєР°: ", DoubleToString(m_current_drawdown_percent, 2), "%");
      
      // Р—Р°РєСЂС‹РІР°РµРј С‡Р°СЃС‚СЊ СѓР±С‹С‚РѕС‡РЅС‹С… РїРѕР·РёС†РёР№ РґР»СЏ СѓР»СѓС‡С€РµРЅРёСЏ РјР°СЂР¶Рё
      ReducePositionsForMarginImprovement();
      
      return;
   }
   
   // РЎС‚Р°РЅРґР°СЂС‚РЅРѕРµ РѕРїСЂРµРґРµР»РµРЅРёРµ РЅР°Р»РёС‡РёСЏ РїСЂРѕСЃР°РґРєРё
   bool is_drawdown = DetectDrawdown();
   
   // Р•СЃР»Рё РїСЂРѕСЃР°РґРєР° РЅРµ РѕР±РЅР°СЂСѓР¶РµРЅР°, РЅРѕ Р°РєС‚РёРІРЅРѕ РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёРµ - РїСЂРѕРІРµСЂСЏРµРј Р·Р°РІРµСЂС€РµРЅРёРµ
   if(!is_drawdown && m_recovery_active)
   {
      // Р Р°СЃС‡РµС‚ РїСЂРѕС†РµРЅС‚Р° РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёСЏ
      double recovery = CalculateRecoveryPercent();
      
      // Р•СЃР»Рё РїРѕР»РЅРѕСЃС‚СЊСЋ РІРѕСЃСЃС‚Р°РЅРѕРІРёР»РёСЃСЊ РёР»Рё РґР°Р¶Рµ РІС‹С€Р»Рё РІ РїР»СЋСЃ
      if(recovery >= 100.0 || m_current_drawdown_percent <= 0)
      {
         m_recovery_active = false;
         m_drawdown_detected = false;
         m_recovery_positions_added = 0;
         m_positions_closed = 0;
         
         // РЈСЃС‚Р°РЅРѕРІРєР° РїР°СѓР·С‹ РїРѕСЃР»Рµ СѓСЃРїРµС€РЅРѕРіРѕ РІС‹С…РѕРґР°
         if(m_drawdown_pause_after_exit > 0)
            m_drawdown_pause_until = TimeCurrent() + m_drawdown_pause_after_exit * 3600;
         
         Print("РЈСЃРїРµС€РЅРѕРµ РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёРµ РёР· РїСЂРѕСЃР°РґРєРё!");
      }
      else
      {
         // РџСЂРёРјРµРЅРµРЅРёРµ РїСЂРѕРіСЂРµСЃСЃРёРІРЅРѕРіРѕ С‚СЂРµР№Р»РёРЅРіР° РґР»СЏ РІСЃРµС… СЃР»РѕРµРІ
         for(int layer = 0; layer < 3; layer++)
         {
            if(m_active_layers[layer] > 0)
               ApplyProgressiveTrailing(layer);
         }
      }
      
      return;
   }
   
   // Р•СЃР»Рё РїСЂРѕСЃР°РґРєР° РѕР±РЅР°СЂСѓР¶РµРЅР°
   if(is_drawdown)
   {
      // РџСЂРѕРІРµСЂРєР° РґР»РёС‚РµР»СЊРЅРѕСЃС‚Рё РїСЂРѕСЃР°РґРєРё
      int drawdown_hours = GetDrawdownDuration();
      
      // Р•СЃР»Рё РїСЂРµРІС‹С€РµРЅРѕ РјР°РєСЃРёРјР°Р»СЊРЅРѕРµ РІСЂРµРјСЏ РІ РїСЂРѕСЃР°РґРєРµ РёР»Рё РґРѕСЃС‚РёРіРЅСѓС‚ РјР°РєСЃРёРјР°Р»СЊРЅС‹Р№ РїСЂРѕС†РµРЅС‚ РїСЂРѕСЃР°РґРєРё
      if((m_drawdown_max_time > 0 && drawdown_hours >= m_drawdown_max_time) ||
         (m_current_drawdown_percent > m_drawdown_max_percent))
      {
         // Р•СЃР»Рё РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёРµ РµС‰Рµ РЅРµ Р°РєС‚РёРІРЅРѕ
         if(!m_recovery_active)
         {
            m_recovery_active = true;
            m_recovery_positions_added = 0;
            m_positions_closed = 0;
            Print("РќР°С‡Р°С‚Рѕ РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёРµ РёР· РїСЂРѕСЃР°РґРєРё. РўРµРєСѓС‰Р°СЏ РїСЂРѕСЃР°РґРєР°: ", 
                  DoubleToString(m_current_drawdown_percent, 2), "%, Р”Р»РёС‚РµР»СЊРЅРѕСЃС‚СЊ: ", drawdown_hours, " С‡Р°СЃРѕРІ");
         }
         
         // РџСЂРёРјРµРЅРµРЅРёРµ СЃС‚СЂР°С‚РµРіРёРё РІРѕСЃСЃС‚Р°РЅРѕРІР»РµРЅРёСЏ РІ Р·Р°РІРёСЃРёРјРѕСЃС‚Рё РѕС‚ РІС‹Р±СЂР°РЅРЅРѕРіРѕ РјРµС‚РѕРґР°
         for(int layer = 0; layer < 3; layer++)
         {
            if(m_active_layers[layer] <= 0)
               continue;
               
            switch(m_recovery_method)
            {
               case RECOVERY_AVERAGING:
                  ApplySmartAveraging(layer);
                  break;
                  
               case RECOVERY_PARTIAL_CLOSE:
                  ExecutePartialClose(layer);
                  break;
                  
               case RECOVERY_HEDGING:
               {
                  // Р РµР°Р»РёР·Р°С†РёСЏ СѓРјРЅРѕРіРѕ С…РµРґР¶РёСЂРѕРІР°РЅРёСЏ
                  if(!UseSmartHedging)
                  {
                     Print("[HEDGE] РЈРјРЅРѕРµ С…РµРґР¶РёСЂРѕРІР°РЅРёРµ РѕС‚РєР»СЋС‡РµРЅРѕ РІ РЅР°СЃС‚СЂРѕР№РєР°С…");
                     break;
                  }
                     
                  // РћРїСЂРµРґРµР»РµРЅРёРµ РЅР°РїСЂР°РІР»РµРЅРёСЏ РѕСЃРЅРѕРІРЅС‹С… РїРѕР·РёС†РёР№ СЃР»РѕСЏ
                  int hedge_direction = -1; // -1 - РЅРµ РѕРїСЂРµРґРµР»РµРЅРѕ, 0 - Buy С…РµРґР¶, 1 - Sell С…РµРґР¶
                  
                  if(m_buy_positions_count[layer] > m_sell_positions_count[layer])
                     hedge_direction = 1; // РњРЅРѕРіРѕ Buy РїРѕР·РёС†РёР№ - С…РµРґР¶РёСЂСѓРµРј Sell
                  else if(m_sell_positions_count[layer] > m_buy_positions_count[layer])
                     hedge_direction = 0; // РњРЅРѕРіРѕ Sell РїРѕР·РёС†РёР№ - С…РµРґР¶РёСЂСѓРµРј Buy
                  else if(m_buy_positions_count[layer] > 0 && m_sell_positions_count[layer] > 0)
                     hedge_direction = (m_buy_lots_total[layer] > m_sell_lots_total[layer]) ? 1 : 0; // РџРѕ РѕР±С‰РµРјСѓ РѕР±СЉРµРјСѓ
                  else
                     break; // РќРµС‚ РѕС‚РєСЂС‹С‚С‹С… РїРѕР·РёС†РёР№
                   
                  // РџСЂРѕРІРµСЂРєР° СѓСЂРѕРІРЅСЏ РїСЂРѕСЃР°РґРєРё РґР»СЏ Р°РєС‚РёРІР°С†РёРё С…РµРґР¶РёСЂРѕРІР°РЅРёСЏ
                  if(m_current_drawdown_percent >= HedgingThreshold)
                  {
                     // РЎРЅР°С‡Р°Р»Р° РѕР±РЅРѕРІР»СЏРµРј РёРЅС„РѕСЂРјР°С†РёСЋ Рѕ СЃСѓС‰РµСЃС‚РІСѓСЋС‰РёС… С…РµРґР¶Р°С…
                     int hedge_count = UpdateHedgingInfo();
                     
                     // РџСЂРѕРІРµСЂСЏРµРј, РЅРµ РїСЂРµРІС‹С€РµРЅРѕ Р»Рё РјР°РєСЃРёРјР°Р»СЊРЅРѕРµ РєРѕР»РёС‡РµСЃС‚РІРѕ С…РµРґР¶РёСЂСѓСЋС‰РёС… РїРѕР·РёС†РёР№
                     if(hedge_count < MaxHedgePositions)
                     {
                        // РћС‚РєСЂС‹РІР°РµРј С…РµРґР¶РёСЂСѓСЋС‰РёР№ РѕСЂРґРµСЂ
                        if(OpenHedgeOrder(layer, hedge_direction))
                        {
                           Print("[HEDGE] РҐРµРґР¶РёСЂСѓСЋС‰РёР№ РѕСЂРґРµСЂ СѓСЃРїРµС€РЅРѕ РѕС‚РєСЂС‹С‚. РџСЂРѕСЃР°РґРєР°: ", 
                                DoubleToString(m_current_drawdown_percent, 2), "%");
                        }
                        else
                        {
                           Print("[HEDGE] РќРµ СѓРґР°Р»РѕСЃСЊ РѕС‚РєСЂС‹С‚СЊ С…РµРґР¶РёСЂСѓСЋС‰РёР№ РѕСЂРґРµСЂ. РџСЂРѕР±СѓРµРј С‡Р°СЃС‚РёС‡РЅРѕРµ Р·Р°РєСЂС‹С‚РёРµ.");
                           // Р•СЃР»Рё РЅРµ РїРѕР»СѓС‡РёР»РѕСЃСЊ РѕС‚РєСЂС‹С‚СЊ С…РµРґР¶, РїСЂРѕР±СѓРµРј С‡Р°СЃС‚РёС‡РЅРѕРµ Р·Р°РєСЂС‹С‚РёРµ
                           ExecutePartialClose(layer);
                        }
                     }
                     else
                     {
                        // Р•СЃР»Рё РґРѕСЃС‚РёРіРЅСѓС‚Рѕ РјР°РєСЃРёРјР°Р»СЊРЅРѕРµ РєРѕР»РёС‡РµСЃС‚РІРѕ С…РµРґР¶РµР№, РёСЃРїРѕР»СЊР·СѓРµРј С‡Р°СЃС‚РёС‡РЅРѕРµ Р·Р°РєСЂС‹С‚РёРµ
                        Print("[HEDGE] Р”РѕСЃС‚РёРіРЅСѓС‚Рѕ РјР°РєСЃРёРјР°Р»СЊРЅРѕРµ РєРѕР»РёС‡РµСЃС‚РІРѕ С…РµРґР¶РёСЂСѓСЋС‰РёС… РїРѕР·РёС†РёР№ (", 
                             IntegerToString(MaxHedgePositions), "). РСЃРїРѕР»СЊР·СѓРµРј С‡Р°СЃС‚РёС‡РЅРѕРµ Р·Р°РєСЂС‹С‚РёРµ.");
                        ExecutePartialClose(layer);
                     }
                  }
                  else
                  {
                     // Р•СЃР»Рё РїСЂРѕСЃР°РґРєР° РЅРµР±РѕР»СЊС€Р°СЏ, РїСЂРѕРІРµСЂСЏРµРј, РјРѕР¶РЅРѕ Р»Рё С‡Р°СЃС‚РёС‡РЅРѕ Р·Р°РєСЂС‹С‚СЊ СЃСѓС‰РµСЃС‚РІСѓСЋС‰РёРµ С…РµРґР¶Рё
                     // РґР»СЏ С„РёРєСЃР°С†РёРё РїСЂРёР±С‹Р»Рё
                     double recovery_level = m_recovery_percent;
                     if(recovery_level >= 50.0) // Р•СЃР»Рё РІРѕСЃСЃС‚Р°РЅРѕРІРёР»РёСЃСЊ РЅР° 50% Рё Р±РѕР»РµРµ
                     {
                        PartialHedgeClose(layer);
                     }
                  }
                  break;
               }
                  
               case RECOVERY_COMBINED:
                  // РљРѕРјР±РёРЅРёСЂРѕРІР°РЅРЅС‹Р№ РїРѕРґС…РѕРґ
                  if(m_current_drawdown_percent > m_drawdown_max_percent * 1.5)
                     ExecutePartialClose(layer); // РџСЂРё РіР»СѓР±РѕРєРѕР№ РїСЂРѕСЃР°РґРєРµ - С‡Р°СЃС‚РёС‡РЅРѕРµ Р·Р°РєСЂС‹С‚РёРµ
                  else
                     ApplySmartAveraging(layer); // РџСЂРё РјРµРЅСЊС€РµР№ РїСЂРѕСЃР°РґРєРµ - СѓСЃСЂРµРґРЅРµРЅРёРµ
                  break;
            }
            
            // Р’ Р»СЋР±РѕРј СЃР»СѓС‡Р°Рµ РїСЂРёРјРµРЅСЏРµРј РїСЂРѕРіСЂРµСЃСЃРёРІРЅС‹Р№ С‚СЂРµР№Р»РёРЅРі
            ApplyProgressiveTrailing(layer);
         }
      }
   }
}

// Р—Р°РєСЂС‹С‚РёРµ С‡Р°СЃС‚Рё РїРѕР·РёС†РёР№ РґР»СЏ СѓР»СѓС‡С€РµРЅРёСЏ РјР°СЂР¶РёРЅР°Р»СЊРЅРѕР№ СЃРёС‚СѓР°С†РёРё
void ReducePositionsForMarginImprovement()
{
   // РЎРѕР±РёСЂР°РµРј РёРЅС„РѕСЂРјР°С†РёСЋ Рѕ РІСЃРµС… РїРѕР·РёС†РёСЏС… Рё СЃРѕСЂС‚РёСЂСѓРµРј РёС… РїРѕ СѓР±С‹С‚РѕС‡РЅРѕСЃС‚Рё
   int total_positions = 0;
   ulong all_tickets[300];
   double all_profits[300];
   
   // РЎРѕР±РёСЂР°РµРј РІСЃРµ РѕС‚РєСЂС‹С‚С‹Рµ РїРѕР·РёС†РёРё
   for(int layer_index = 0; layer_index < 3; layer_index++)
   {
      // РЎРѕР±РёСЂР°РµРј РїРѕР·РёС†РёРё Buy
      for(int i = 1; i <= m_buy_positions_count[layer_index]; i++)
      {
         if(m_buy_tickets[layer_index][i] > 0 && PositionSelectByTicket(m_buy_tickets[layer_index][i]))
         {
            all_tickets[total_positions] = m_buy_tickets[layer_index][i];
            all_profits[total_positions] = PositionGetDouble(POSITION_PROFIT);
            total_positions++;
         }
      }
      
      // РЎРѕР±РёСЂР°РµРј РїРѕР·РёС†РёРё Sell
      for(int i = 1; i <= m_sell_positions_count[layer_index]; i++)
      {
         if(m_sell_tickets[layer_index][i] > 0 && PositionSelectByTicket(m_sell_tickets[layer_index][i]))
         {
            all_tickets[total_positions] = m_sell_tickets[layer_index][i];
            all_profits[total_positions] = PositionGetDouble(POSITION_PROFIT);
            total_positions++;
         }
      }
   }
   
   // РЎРѕСЂС‚РёСЂСѓРµРј РїРѕР·РёС†РёРё РїРѕ СѓР±С‹С‚РѕС‡РЅРѕСЃС‚Рё (СЃР°РјС‹Рµ СѓР±С‹С‚РѕС‡РЅС‹Рµ РІ РЅР°С‡Р°Р»Рµ)
   for(int i = 0; i < total_positions - 1; i++)
   {
      for(int j = i + 1; j < total_positions; j++)
      {
         if(all_profits[i] > all_profits[j])
         {
            // РћР±РјРµРЅ Р·РЅР°С‡РµРЅРёСЏРјРё
            double temp_profit = all_profits[i];
            ulong temp_ticket = all_tickets[i];
            
            all_profits[i] = all_profits[j];
            all_tickets[i] = all_tickets[j];
            
            all_profits[j] = temp_profit;
            all_tickets[j] = temp_ticket;
         }
      }
   }
   
   // Р—Р°РєСЂС‹РІР°РµРј СЃР°РјС‹Рµ СѓР±С‹С‚РѕС‡РЅС‹Рµ РїРѕР·РёС†РёРё (РїСЂРёРјРµСЂРЅРѕ 30% РѕС‚ РѕР±С‰РµРіРѕ РєРѕР»РёС‡РµСЃС‚РІР°)
   int positions_to_close = (int)MathCeil(total_positions * 0.3);
   Print("РџРѕРїС‹С‚РєР° Р·Р°РєСЂС‹С‚СЊ ", positions_to_close, " РїРѕР·РёС†РёР№ РґР»СЏ СѓР»СѓС‡С€РµРЅРёСЏ РјР°СЂР¶Рё");
   
   for(int i = 0; i < positions_to_close && i < total_positions; i++)
   {
      if(PositionSelectByTicket(all_tickets[i]))
      {
         int magic = (int)PositionGetInteger(POSITION_MAGIC);
         m_trade.SetExpertMagicNumber(magic);
         
         if(m_trade.PositionClose(all_tickets[i], m_slippage))
         {
            Print("Р—Р°РєСЂС‹С‚РёРµ СѓР±С‹С‚РѕС‡РЅРѕР№ РїРѕР·РёС†РёРё #", all_tickets[i], 
                  " СЃ РїСЂРёР±С‹Р»СЊСЋ ", DoubleToString(all_profits[i], 2), " РґР»СЏ СѓР»СѓС‡С€РµРЅРёСЏ РјР°СЂР¶Рё");
            m_positions_closed++;
         }
         else
         {
            Print("РћС€РёР±РєР° РїСЂРё Р·Р°РєСЂС‹С‚РёРё СѓР±С‹С‚РѕС‡РЅРѕР№ РїРѕР·РёС†РёРё #", all_tickets[i], 
                  ": ", m_trade.ResultRetcode());
         }
      }
   }
}

//+------------------------------------------------------------------+
//| РРґРµРЅС‚РёС„РёРєР°С†РёСЏ РєР»СЋС‡РµРІС‹С… СѓСЂРѕРІРЅРµР№ РїРѕРґРґРµСЂР¶РєРё Рё СЃРѕРїСЂРѕС‚РёРІР»РµРЅРёСЏ          |
//+------------------------------------------------------------------+
void IdentifyKeyLevels()
{
   if(!UseAdvancedAnalysis || !UseKeyLevelAnalysis)
      return;
      
   // РћРїС‚РёРјРёР·РёСЂРѕРІР°РЅРЅР°СЏ РІРµСЂСЃРёСЏ РґР»СЏ Р±С‹СЃС‚СЂРѕРіРѕ Р°РЅР°Р»РёР·Р°
      
   // РћС‡РёС‰Р°РµРј РјР°СЃСЃРёРІ СѓСЂРѕРІРЅРµР№
   for(int i = 0; i < m_max_key_levels; i++)
   {
      m_key_levels[i].price = 0;
      m_key_levels[i].strength = 0;
      m_key_levels[i].touches = 0;
      m_key_levels[i].created = 0;
      m_key_levels[i].last_touch = 0;
      m_key_levels[i].is_support = false;
      m_key_levels[i].volume_activity = 0;
      m_key_levels[i].rejection_strength = 0;
      m_key_levels[i].is_order_cluster = false;
   }
   
   // РСЃРїРѕР»СЊР·СѓРµРј РјРµРЅСЊС€РµРµ РєРѕР»РёС‡РµСЃС‚РІРѕ Р±Р°СЂРѕРІ РїСЂРё РѕРїС‚РёРјРёР·Р°С†РёРё
   int bars_to_analyze = m_optimize_for_testing && (bool)MQLInfoInteger(MQL_OPTIMIZATION) ? 
                         MathMin(100, HistoryBarsAnalysis) : HistoryBarsAnalysis;
   
   // РњР°СЃСЃРёРІС‹ РґР»СЏ С…СЂР°РЅРµРЅРёСЏ РґР°РЅРЅС‹С…
   double highs[], lows[], closes[];
   datetime times[];
   
   ArrayResize(highs, bars_to_analyze);
   ArrayResize(lows, bars_to_analyze);
   ArrayResize(closes, bars_to_analyze);
   ArrayResize(times, bars_to_analyze);
   
   // РџРѕР»СѓС‡РµРЅРёРµ РґР°РЅРЅС‹С… Рѕ С†РµРЅР°С…
   for(int i = 0; i < bars_to_analyze; i++)
   {
      highs[i] = iHigh(m_symbol, PERIOD_CURRENT, i);
      lows[i] = iLow(m_symbol, PERIOD_CURRENT, i);
      closes[i] = iClose(m_symbol, PERIOD_CURRENT, i);
      times[i] = iTime(m_symbol, PERIOD_CURRENT, i);
   }
   
   // РџРѕРёСЃРє Р»РѕРєР°Р»СЊРЅС‹С… РјР°РєСЃРёРјСѓРјРѕРІ Рё РјРёРЅРёРјСѓРјРѕРІ (swing points)
   int swing_points_count = 0;
   double swing_points[];
   bool is_support[];
   datetime swing_times[];
   
   // Р’СЂРµРјРµРЅРЅС‹Р№ РјР°СЃСЃРёРІ РґР»СЏ С…СЂР°РЅРµРЅРёСЏ swing points
   ArrayResize(swing_points, bars_to_analyze / 4);
   ArrayResize(is_support, bars_to_analyze / 4);
   ArrayResize(swing_times, bars_to_analyze / 4);
   
   // РќР°С…РѕРґРёРј Р»РѕРєР°Р»СЊРЅС‹Рµ РјР°РєСЃРёРјСѓРјС‹ Рё РјРёРЅРёРјСѓРјС‹ (СѓРїСЂРѕС‰РµРЅРЅС‹Р№ Р°Р»РіРѕСЂРёС‚Рј)
   for(int i = 3; i < bars_to_analyze - 3; i++)
   {
      // РџСЂРѕРІРµСЂРєР° РЅР° Р»РѕРєР°Р»СЊРЅС‹Р№ РјР°РєСЃРёРјСѓРј
      if(highs[i] > highs[i-1] && highs[i] > highs[i-2] && 
         highs[i] > highs[i+1] && highs[i] > highs[i+2])
      {
         swing_points[swing_points_count] = highs[i];
         is_support[swing_points_count] = false; // Р­С‚Рѕ СѓСЂРѕРІРµРЅСЊ СЃРѕРїСЂРѕС‚РёРІР»РµРЅРёСЏ
         swing_times[swing_points_count] = times[i];
         swing_points_count++;
      }
      
      // РџСЂРѕРІРµСЂРєР° РЅР° Р»РѕРєР°Р»СЊРЅС‹Р№ РјРёРЅРёРјСѓРј
      if(lows[i] < lows[i-1] && lows[i] < lows[i-2] && 
         lows[i] < lows[i+1] && lows[i] < lows[i+2])
      {
         swing_points[swing_points_count] = lows[i];
         is_support[swing_points_count] = true; // Р­С‚Рѕ СѓСЂРѕРІРµРЅСЊ РїРѕРґРґРµСЂР¶РєРё
         swing_times[swing_points_count] = times[i];
         swing_points_count++;
      }
   }
   
   // РћР±СЉРµРґРёРЅСЏРµРј Р±Р»РёР·РєРёРµ СѓСЂРѕРІРЅРё
   double zone_size = KeyLevelTouchZone * m_point * m_point_digits;
   
   // Р’СЂРµРјРµРЅРЅС‹Рµ РјР°СЃСЃРёРІС‹ РґР»СЏ С…СЂР°РЅРµРЅРёСЏ РѕР±СЉРµРґРёРЅРµРЅРЅС‹С… СѓСЂРѕРІРЅРµР№
   double merged_levels[];
   int merged_touches[];
   bool merged_is_support[];
   datetime merged_created[];
   datetime merged_last_touch[];
   
   ArrayResize(merged_levels, swing_points_count);
   ArrayResize(merged_touches, swing_points_count);
   ArrayResize(merged_is_support, swing_points_count);
   ArrayResize(merged_created, swing_points_count);
   ArrayResize(merged_last_touch, swing_points_count);
   
   int merged_count = 0;
   
   // Р“СЂСѓРїРїРёСЂСѓРµРј Р±Р»РёР·РєРёРµ СѓСЂРѕРІРЅРё
   for(int i = 0; i < swing_points_count; i++)
   {
      bool is_new_level = true;
      
      for(int j = 0; j < merged_count; j++)
      {
         if(MathAbs(swing_points[i] - merged_levels[j]) <= zone_size)
         {
            // РћР±СЉРµРґРёРЅСЏРµРј СѓСЂРѕРІРЅРё
            merged_levels[j] = (merged_levels[j] * merged_touches[j] + swing_points[i]) / (merged_touches[j] + 1);
            merged_touches[j]++;
            
            // РћР±РЅРѕРІР»СЏРµРј РІСЂРµРјСЏ РїРѕСЃР»РµРґРЅРµРіРѕ РєР°СЃР°РЅРёСЏ
            if(swing_times[i] > merged_last_touch[j])
               merged_last_touch[j] = swing_times[i];
               
            is_new_level = false;
            break;
         }
      }
      
      if(is_new_level)
      {
         merged_levels[merged_count] = swing_points[i];
         merged_touches[merged_count] = 1;
         merged_is_support[merged_count] = is_support[i];
         merged_created[merged_count] = swing_times[i];
         merged_last_touch[merged_count] = swing_times[i];
         merged_count++;
      }
   }
   
   // РЎРѕСЂС‚РёСЂСѓРµРј СѓСЂРѕРІРЅРё РїРѕ РєРѕР»РёС‡РµСЃС‚РІСѓ РєР°СЃР°РЅРёР№ (РѕС‚ Р±РѕР»СЊС€РµРіРѕ Рє РјРµРЅСЊС€РµРјСѓ)
   for(int i = 0; i < merged_count - 1; i++)
   {
      for(int j = i + 1; j < merged_count; j++)
      {
         if(merged_touches[j] > merged_touches[i])
         {
            // РњРµРЅСЏРµРј РјРµСЃС‚Р°РјРё
            double temp_level = merged_levels[i];
            int temp_touches = merged_touches[i];
            bool temp_is_support = merged_is_support[i];
            datetime temp_created = merged_created[i];
            datetime temp_last_touch = merged_last_touch[i];
            
            merged_levels[i] = merged_levels[j];
            merged_touches[i] = merged_touches[j];
            merged_is_support[i] = merged_is_support[j];
            merged_created[i] = merged_created[j];
            merged_last_touch[i] = merged_last_touch[j];
            
            merged_levels[j] = temp_level;
            merged_touches[j] = temp_touches;
            merged_is_support[j] = temp_is_support;
            merged_created[j] = temp_created;
            merged_last_touch[j] = temp_last_touch;
         }
      }
   }
   
   // Р—Р°РїРѕР»РЅСЏРµРј РјР°СЃСЃРёРІ РєР»СЋС‡РµРІС‹С… СѓСЂРѕРІРЅРµР№
   int max_count = MathMin(merged_count, m_max_key_levels);
   
   for(int i = 0; i < max_count; i++)
   {
      m_key_levels[i].price = merged_levels[i];
      m_key_levels[i].touches = merged_touches[i];
      m_key_levels[i].is_support = merged_is_support[i];
      m_key_levels[i].created = merged_created[i];
      m_key_levels[i].last_touch = merged_last_touch[i];
      
      // Р Р°СЃС‡РµС‚ СЃРёР»С‹ СѓСЂРѕРІРЅСЏ
      // Р¤Р°РєС‚РѕСЂС‹: РєРѕР»РёС‡РµСЃС‚РІРѕ РєР°СЃР°РЅРёР№, РІРѕР·СЂР°СЃС‚ СѓСЂРѕРІРЅСЏ, РІСЂРµРјСЏ РїРѕСЃР»РµРґРЅРµРіРѕ РєР°СЃР°РЅРёСЏ
      double touch_weight = MathMin(merged_touches[i] * 10, 50); // Р”Рѕ 50 Р±Р°Р»Р»РѕРІ Р·Р° РєРѕР»РёС‡РµСЃС‚РІРѕ РєР°СЃР°РЅРёР№
      
      // Р’СЂРµРјРµРЅРЅРѕР№ РІРµСЃ - С‡РµРј РЅРѕРІРµРµ, С‚РµРј СЃРёР»СЊРЅРµРµ
      double time_weight = 30 * (1.0 - (double)(TimeCurrent() - merged_last_touch[i]) / (7 * 24 * 3600));
      time_weight = MathMax(time_weight, 0); // Р”Рѕ 30 Р±Р°Р»Р»РѕРІ Р·Р° СЃРІРµР¶РµСЃС‚СЊ
      
      // Р’РµСЃ Р·Р° Р±Р»РёР·РѕСЃС‚СЊ Рє С‚РµРєСѓС‰РµР№ С†РµРЅРµ
      double current_price = (iHigh(m_symbol, PERIOD_CURRENT, 0) + iLow(m_symbol, PERIOD_CURRENT, 0)) / 2;
      double distance_weight = 20 * (1.0 - MathMin(MathAbs(current_price - merged_levels[i]) / (1000 * m_point * m_point_digits), 1.0));
      
      // РС‚РѕРіРѕРІР°СЏ СЃРёР»Р° СѓСЂРѕРІРЅСЏ
      m_key_levels[i].strength = touch_weight + time_weight + distance_weight;
   }
}

//+------------------------------------------------------------------+
//| РћРїСЂРµРґРµР»РµРЅРёРµ С‚РµРєСѓС‰РµР№ СЃС‚СЂСѓРєС‚СѓСЂС‹ СЂС‹РЅРєР°                               |
//+------------------------------------------------------------------+
void DetectMarketStructure()
{
   if(!UseAdvancedAnalysis)
      return;
      
   // РћРїС‚РёРјРёР·РёСЂРѕРІР°РЅРЅР°СЏ РІРµСЂСЃРёСЏ РґР»СЏ Р±С‹СЃС‚СЂРѕРіРѕ РѕРїСЂРµРґРµР»РµРЅРёСЏ СЃС‚СЂСѓРєС‚СѓСЂС‹ СЂС‹РЅРєР°
   
   // РџРѕР»СѓС‡Р°РµРј РґР°РЅРЅС‹Рµ Рѕ РјР°РєСЃРёРјСѓРјР°С… Рё РјРёРЅРёРјСѓРјР°С… Р·Р° РїРѕСЃР»РµРґРЅРёРµ РЅРµСЃРєРѕР»СЊРєРѕ Р±Р°СЂРѕРІ
   int bars_to_analyze = 20; // РћРїС‚РёРјРёР·РёСЂРѕРІР°РЅРЅС‹Р№ СЂР°Р·РјРµСЂ РІС‹Р±РѕСЂРєРё
   
   // РњР°СЃСЃРёРІС‹ РґР»СЏ С…СЂР°РЅРµРЅРёСЏ РґР°РЅРЅС‹С…
   double highs[], lows[], closes[];
   ArrayResize(highs, bars_to_analyze);
   ArrayResize(lows, bars_to_analyze);
   ArrayResize(closes, bars_to_analyze);
   
   for(int i = 0; i < bars_to_analyze; i++)
   {
      highs[i] = iHigh(m_symbol, PERIOD_CURRENT, i);
      lows[i] = iLow(m_symbol, PERIOD_CURRENT, i);
      closes[i] = iClose(m_symbol, PERIOD_CURRENT, i);
   }
   
   // РРЅРёС†РёР°Р»РёР·РёСЂСѓРµРј РјР°СЃСЃРёРІС‹ РґР»СЏ С…СЂР°РЅРµРЅРёСЏ Р»РѕРєР°Р»СЊРЅС‹С… СЌРєСЃС‚СЂРµРјСѓРјРѕРІ
   // РСЃРїРѕР»СЊР·СѓРµРј РїСЂРѕСЃС‚РѕР№ РїРѕРґС…РѕРґ РІРјРµСЃС‚Рѕ СЃР»РѕР¶РЅС‹С… РѕРїРµСЂР°С†РёР№ СЃРґРІРёРіР°
   double swing_highs[4] = {0, 0, 0, 0}; // РџРѕСЃР»РµРґРЅРёРµ 4 Р»РѕРєР°Р»СЊРЅС‹С… РјР°РєСЃРёРјСѓРјР°
   double swing_lows[4] = {0, 0, 0, 0};  // РџРѕСЃР»РµРґРЅРёРµ 4 Р»РѕРєР°Р»СЊРЅС‹С… РјРёРЅРёРјСѓРјР°
   
   // РќР°С…РѕРґРёРј РїРµСЂРІС‹Р№ Р»РѕРєР°Р»СЊРЅС‹Р№ РјР°РєСЃРёРјСѓРј
   for(int i = 2; i < bars_to_analyze - 2; i++)
   {
      // РџСЂРѕРІРµСЂРєР° РЅР° Р»РѕРєР°Р»СЊРЅС‹Р№ РјР°РєСЃРёРјСѓРј
      if(highs[i] > highs[i-1] && highs[i] > highs[i-2] && 
         highs[i] > highs[i+1] && highs[i] > highs[i+2])
      {
         swing_highs[0] = highs[i];
         
         // РС‰РµРј СЃР»РµРґСѓСЋС‰РёР№ Р»РѕРєР°Р»СЊРЅС‹Р№ РјР°РєСЃРёРјСѓРј РїРѕСЃР»Рµ С‚РµРєСѓС‰РµРіРѕ
         for(int j = i + 2; j < bars_to_analyze - 2; j++)
         {
            if(highs[j] > highs[j-1] && highs[j] > highs[j-2] && 
               highs[j] > highs[j+1] && highs[j] > highs[j+2])
            {
               swing_highs[1] = highs[j];
               
               // РС‰РµРј С‚СЂРµС‚РёР№ Р»РѕРєР°Р»СЊРЅС‹Р№ РјР°РєСЃРёРјСѓРј
               for(int k = j + 2; k < bars_to_analyze - 2; k++)
               {
                  if(highs[k] > highs[k-1] && highs[k] > highs[k-2] && 
                     highs[k] > highs[k+1] && highs[k] > highs[k+2])
                  {
                     swing_highs[2] = highs[k];
                     
                     // РС‰РµРј С‡РµС‚РІРµСЂС‚С‹Р№ Р»РѕРєР°Р»СЊРЅС‹Р№ РјР°РєСЃРёРјСѓРј
                     for(int l = k + 2; l < bars_to_analyze - 2; l++)
                     {
                        if(highs[l] > highs[l-1] && highs[l] > highs[l-2] && 
                           highs[l] > highs[l+1] && highs[l] > highs[l+2])
                        {
                           swing_highs[3] = highs[l];
                           break;
                        }
                     }
                     break;
                  }
               }
               break;
            }
         }
         break;
      }
   }
   
   // Р•СЃР»Рё РЅРµ РЅР°С€Р»Рё Р»РѕРєР°Р»СЊРЅС‹Р№ РјР°РєСЃРёРјСѓРј, РёСЃРїРѕР»СЊР·СѓРµРј С‚РµРєСѓС‰РёР№ РјР°РєСЃРёРјСѓРј
   if(swing_highs[0] == 0)
   {
      swing_highs[0] = highs[0];
      
      // Р—Р°РїРѕР»РЅСЏРµРј РѕСЃС‚Р°РІС€РёРµСЃСЏ РїРѕР·РёС†РёРё, РµСЃР»Рё РµСЃС‚СЊ РґР°РЅРЅС‹Рµ
      if(bars_to_analyze > 1) swing_highs[1] = highs[1];
      if(bars_to_analyze > 2) swing_highs[2] = highs[2];
      if(bars_to_analyze > 3) swing_highs[3] = highs[3];
   }
   
   // РќР°С…РѕРґРёРј РїРµСЂРІС‹Р№ Р»РѕРєР°Р»СЊРЅС‹Р№ РјРёРЅРёРјСѓРј
   for(int i = 2; i < bars_to_analyze - 2; i++)
   {
      // РџСЂРѕРІРµСЂРєР° РЅР° Р»РѕРєР°Р»СЊРЅС‹Р№ РјРёРЅРёРјСѓРј
      if(lows[i] < lows[i-1] && lows[i] < lows[i-2] && 
         lows[i] < lows[i+1] && lows[i] < lows[i+2])
      {
         swing_lows[0] = lows[i];
         
         // РС‰РµРј СЃР»РµРґСѓСЋС‰РёР№ Р»РѕРєР°Р»СЊРЅС‹Р№ РјРёРЅРёРјСѓРј РїРѕСЃР»Рµ С‚РµРєСѓС‰РµРіРѕ
         for(int j = i + 2; j < bars_to_analyze - 2; j++)
         {
            if(lows[j] < lows[j-1] && lows[j] < lows[j-2] && 
               lows[j] < lows[j+1] && lows[j] < lows[j+2])
            {
               swing_lows[1] = lows[j];
               
               // РС‰РµРј С‚СЂРµС‚РёР№ Р»РѕРєР°Р»СЊРЅС‹Р№ РјРёРЅРёРјСѓРј
               for(int k = j + 2; k < bars_to_analyze - 2; k++)
               {
                  if(lows[k] < lows[k-1] && lows[k] < lows[k-2] && 
                     lows[k] < lows[k+1] && lows[k] < lows[k+2])
                  {
                     swing_lows[2] = lows[k];
                     
                     // РС‰РµРј С‡РµС‚РІРµСЂС‚С‹Р№ Р»РѕРєР°Р»СЊРЅС‹Р№ РјРёРЅРёРјСѓРј
                     for(int l = k + 2; l < bars_to_analyze - 2; l++)
                     {
                        if(lows[l] < lows[l-1] && lows[l] < lows[l-2] && 
                           lows[l] < lows[l+1] && lows[l] < lows[l+2])
                        {
                           swing_lows[3] = lows[l];
                           break;
                        }
                     }
                     break;
                  }
               }
               break;
            }
         }
         break;
      }
   }
   
   // Р•СЃР»Рё РЅРµ РЅР°С€Р»Рё Р»РѕРєР°Р»СЊРЅС‹Р№ РјРёРЅРёРјСѓРј, РёСЃРїРѕР»СЊР·СѓРµРј С‚РµРєСѓС‰РёР№ РјРёРЅРёРјСѓРј
   if(swing_lows[0] == 0)
   {
      swing_lows[0] = lows[0];
      
      // Р—Р°РїРѕР»РЅСЏРµРј РѕСЃС‚Р°РІС€РёРµСЃСЏ РїРѕР·РёС†РёРё, РµСЃР»Рё РµСЃС‚СЊ РґР°РЅРЅС‹Рµ
      if(bars_to_analyze > 1) swing_lows[1] = lows[1];
      if(bars_to_analyze > 2) swing_lows[2] = lows[2];
      if(bars_to_analyze > 3) swing_lows[3] = lows[3];
   }
   
   // РћРїСЂРµРґРµР»СЏРµРј СЃС‚СЂСѓРєС‚СѓСЂСѓ СЂС‹РЅРєР° РЅР° РѕСЃРЅРѕРІРµ РїРѕСЃР»РµРґРѕРІР°С‚РµР»СЊРЅРѕСЃС‚Рё СЌРєСЃС‚СЂРµРјСѓРјРѕРІ
   
   // Р’РѕСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ: РїРѕСЃР»РµРґРѕРІР°С‚РµР»СЊРЅРѕСЃС‚СЊ РїРѕРІС‹С€Р°СЋС‰РёС…СЃСЏ РјРёРЅРёРјСѓРјРѕРІ Рё РјР°РєСЃРёРјСѓРјРѕРІ
   if(swing_highs[0] > swing_highs[1] && swing_lows[0] > swing_lows[1])
   {
      m_market_structure.type = STRUCTURE_UPTREND;
      m_market_structure.trend_strength = 60;
   }
   // РќРёСЃС…РѕРґСЏС‰РёР№ С‚СЂРµРЅРґ: РїРѕСЃР»РµРґРѕРІР°С‚РµР»СЊРЅРѕСЃС‚СЊ РїРѕРЅРёР¶Р°СЋС‰РёС…СЃСЏ РјРёРЅРёРјСѓРјРѕРІ Рё РјР°РєСЃРёРјСѓРјРѕРІ
   else if(swing_highs[0] < swing_highs[1] && swing_lows[0] < swing_lows[1])
   {
      m_market_structure.type = STRUCTURE_DOWNTREND;
      m_market_structure.trend_strength = 60;
   }
   // Р Р°Р·РІРѕСЂРѕС‚ РІРІРµСЂС…: РїСЂРµРґС‹РґСѓС‰РёРµ РјР°РєСЃРёРјСѓРјС‹ РїРѕРЅРёР¶Р°Р»РёСЃСЊ, РЅРѕ РїРѕСЃР»РµРґРЅРёР№ РјР°РєСЃРёРјСѓРј РІС‹С€Рµ
   else if(swing_highs[1] < swing_highs[2] && swing_highs[0] > swing_highs[1])
   {
      m_market_structure.type = STRUCTURE_REVERSAL_UP;
      m_market_structure.trend_strength = 40;
   }
   // Р Р°Р·РІРѕСЂРѕС‚ РІРЅРёР·: РїСЂРµРґС‹РґСѓС‰РёРµ РјРёРЅРёРјСѓРјС‹ РїРѕРІС‹С€Р°Р»РёСЃСЊ, РЅРѕ РїРѕСЃР»РµРґРЅРёР№ РјРёРЅРёРјСѓРј РЅРёР¶Рµ
   else if(swing_lows[1] > swing_lows[2] && swing_lows[0] < swing_lows[1])
   {
      m_market_structure.type = STRUCTURE_REVERSAL_DOWN;
      m_market_structure.trend_strength = 40;
   }
   // Р‘РѕРєРѕРІРѕРµ РґРІРёР¶РµРЅРёРµ
   else
   {
      m_market_structure.type = STRUCTURE_RANGING;
      m_market_structure.trend_strength = 20;
   }
   
   // Р”РѕРїРѕР»РЅРёС‚РµР»СЊРЅС‹Р№ Р°РЅР°Р»РёР· СЃРёР»С‹ С‚СЂРµРЅРґР° РЅР° РѕСЃРЅРѕРІРµ РЅР°РєР»РѕРЅР° СЃРєРѕР»СЊР·СЏС‰РёС… СЃСЂРµРґРЅРёС…
   if(m_use_trend_filter && (m_trend_detection_method == 1 || m_trend_detection_method == 2))
   {
      double ma_fast_current = m_ma_fast[0];
      double ma_fast_prev = m_ma_fast[1];
      double ma_slow_current = m_ma_slow[0];
      double ma_slow_prev = m_ma_slow[1];
      
      // РљСЂСѓС‚РѕР№ РЅР°РєР»РѕРЅ РњРђ СѓСЃРёР»РёРІР°РµС‚ С‚СЂРµРЅРґ
      if((m_market_structure.type == STRUCTURE_UPTREND && ma_fast_current > ma_fast_prev * 1.001) ||
         (m_market_structure.type == STRUCTURE_DOWNTREND && ma_fast_current < ma_fast_prev * 0.999))
      {
         m_market_structure.trend_strength += 15;
      }
      
      // РџРµСЂРµСЃРµС‡РµРЅРёРµ РњРђ РїРѕРґС‚РІРµСЂР¶РґР°РµС‚ СЂР°Р·РІРѕСЂРѕС‚
      if((m_market_structure.type == STRUCTURE_REVERSAL_UP && ma_fast_current > ma_slow_current && ma_fast_prev < ma_slow_prev) ||
         (m_market_structure.type == STRUCTURE_REVERSAL_DOWN && ma_fast_current < ma_slow_current && ma_fast_prev > ma_slow_prev))
      {
         m_market_structure.trend_strength += 20;
      }
   }
   
   // РћРіСЂР°РЅРёС‡РµРЅРёРµ РјР°РєСЃРёРјР°Р»СЊРЅРѕР№ СЃРёР»С‹ С‚СЂРµРЅРґР°
   if(m_market_structure.trend_strength > 100)
      m_market_structure.trend_strength = 100;
      
   // РЎРѕС…СЂР°РЅРµРЅРёРµ РїРѕСЃР»РµРґРЅРёС… СЌРєСЃС‚СЂРµРјСѓРјРѕРІ
   m_market_structure.last_high = swing_highs[0];
   m_market_structure.last_low = swing_lows[0];
   m_market_structure.prev_high = swing_highs[1];
   m_market_structure.prev_low = swing_lows[1];
}
   
  

//+------------------------------------------------------------------+
//| РћР±РЅРѕРІР»РµРЅРёРµ РєР»СЋС‡РµРІС‹С… СѓСЂРѕРІРЅРµР№                                       |
//+------------------------------------------------------------------+
void UpdateKeyLevels()
{
   if(!UseAdvancedAnalysis || !UseKeyLevelAnalysis)
      return;
      
   double current_price = (iHigh(m_symbol, PERIOD_CURRENT, 0) + iLow(m_symbol, PERIOD_CURRENT, 0)) / 2;
   double zone_size = KeyLevelTouchZone * m_point * m_point_digits;
   
   // РџСЂРѕРІРµСЂСЏРµРј, РєР°СЃР°РµС‚СЃСЏ Р»Рё С‚РµРєСѓС‰Р°СЏ С†РµРЅР° РєР°РєРѕРіРѕ-Р»РёР±Рѕ СѓСЂРѕРІРЅСЏ
   for(int i = 0; i < m_max_key_levels; i++)
   {
      // Р•СЃР»Рё СѓСЂРѕРІРµРЅСЊ СЃСѓС‰РµСЃС‚РІСѓРµС‚
      if(m_key_levels[i].price > 0)
      {
         // Р•СЃР»Рё С†РµРЅР° РІ Р·РѕРЅРµ РєР°СЃР°РЅРёСЏ СѓСЂРѕРІРЅСЏ
         if(MathAbs(current_price - m_key_levels[i].price) <= zone_size)
         {
            // РћР±РЅРѕРІР»СЏРµРј РІСЂРµРјСЏ РїРѕСЃР»РµРґРЅРµРіРѕ РєР°СЃР°РЅРёСЏ Рё РєРѕР»РёС‡РµСЃС‚РІРѕ РєР°СЃР°РЅРёР№
            if(TimeCurrent() - m_key_levels[i].last_touch > 3600) // РќРµ СЃС‡РёС‚Р°РµРј РєР°СЃР°РЅРёСЏ С‡Р°С‰Рµ, С‡РµРј СЂР°Р· РІ С‡Р°СЃ
            {
               m_key_levels[i].last_touch = TimeCurrent();
               m_key_levels[i].touches++;
               
               // РџРµСЂРµСЃС‡РёС‚С‹РІР°РµРј СЃРёР»Сѓ СѓСЂРѕРІРЅСЏ
               double touch_weight = MathMin(m_key_levels[i].touches * 10, 50);
               double time_weight = 30 * (1.0 - (double)(TimeCurrent() - m_key_levels[i].last_touch) / (7 * 24 * 3600));
               time_weight = MathMax(time_weight, 0);
               double distance_weight = 20 * (1.0 - MathMin(MathAbs(current_price - m_key_levels[i].price) / (1000 * m_point * m_point_digits), 1.0));
               
               m_key_levels[i].strength = touch_weight + time_weight + distance_weight;
            }
         }
      }
   }
   
   // РџСЂРѕРІРµСЂРєР° РЅР° РїСЂРѕР±РѕР№ СѓСЂРѕРІРЅСЏ (РґР»СЏ РІРѕР·РјРѕР¶РЅРѕРіРѕ СѓРґР°Р»РµРЅРёСЏ РёР»Рё СѓРјРµРЅСЊС€РµРЅРёСЏ СЃРёР»С‹)
   static double prev_price = 0;
   
   if(prev_price > 0)
   {
      for(int i = 0; i < m_max_key_levels; i++)
      {
         if(m_key_levels[i].price > 0)
         {
            // Р•СЃР»Рё РїСЂРѕРёР·РѕС€РµР» РїСЂРѕР±РѕР№ СѓСЂРѕРІРЅСЏ
            if((prev_price < m_key_levels[i].price && current_price > m_key_levels[i].price) ||
               (prev_price > m_key_levels[i].price && current_price < m_key_levels[i].price))
            {
               // Р•СЃР»Рё РїСЂРѕР±РѕР№ Р·РЅР°С‡РёС‚РµР»СЊРЅС‹Р№ (РЅРµ РїСЂРѕСЃС‚Рѕ РєР°СЃР°РЅРёРµ)
               if(MathAbs(current_price - m_key_levels[i].price) > zone_size * 3)
               {
                  // РЈРјРµРЅСЊС€Р°РµРј СЃРёР»Сѓ СѓСЂРѕРІРЅСЏ РїРѕСЃР»Рµ РїСЂРѕР±РѕСЏ
                  m_key_levels[i].strength *= 0.8;
                  
                  // Р•СЃР»Рё СЃРёР»Р° СѓСЂРѕРІРЅСЏ СЃС‚Р°Р»Р° РѕС‡РµРЅСЊ РјР°Р»РµРЅСЊРєРѕР№, РјРѕР¶РЅРѕ СѓРґР°Р»РёС‚СЊ СѓСЂРѕРІРµРЅСЊ
                  if(m_key_levels[i].strength < 10)
                  {
                     m_key_levels[i].price = 0;
                     m_key_levels[i].strength = 0;
                     m_key_levels[i].touches = 0;
                  }
               }
            }
         }
      }
   }
   
   prev_price = current_price;
   
   // РћРїС‚РёРјРёР·РёСЂРѕРІР°РЅРЅР°СЏ СЃРѕСЂС‚РёСЂРѕРІРєР° - СЃРѕСЂС‚РёСЂРѕРІРєР° СЂР°Р· РІ 5 РјРёРЅСѓС‚
   static datetime last_sort_time = 0;
   if(TimeCurrent() - last_sort_time > 300)
   {
      // РџРµСЂРµСЃРѕСЂС‚РёСЂРѕРІРєР° СѓСЂРѕРІРЅРµР№ РїРѕ СЃРёР»Рµ (Р±РѕР»РµРµ СЃРёР»СЊРЅС‹Рµ РІ РЅР°С‡Р°Р»Рµ РјР°СЃСЃРёРІР°)
      for(int i = 0; i < m_max_key_levels - 1; i++)
      {
         for(int j = i + 1; j < m_max_key_levels; j++)
         {
            if(m_key_levels[j].strength > m_key_levels[i].strength)
            {
               // РњРµРЅСЏРµРј РјРµСЃС‚Р°РјРё
               KeyLevel temp = m_key_levels[i];
               m_key_levels[i] = m_key_levels[j];
               m_key_levels[j] = temp;
            }
         }
      }
      last_sort_time = TimeCurrent();
   }
}

//+------------------------------------------------------------------+
//| РђРЅР°Р»РёР· РґР°РЅРЅС‹С… РѕР±СЉРµРјРѕРІ                                             |
//+------------------------------------------------------------------+
void AnalyzeVolumeData()
{
   if(!UseAdvancedAnalysis)
      return;
      
   // Р—Р°РіР»СѓС€РєР° РґР»СЏ С„СѓРЅРєС†РёРё
   Print("Р¤СѓРЅРєС†РёСЏ AnalyzeVolumeData РІС‹Р·РІР°РЅР°");
}

//+------------------------------------------------------------------+
//| РћС‚РѕР±СЂР°Р¶РµРЅРёРµ РёРЅС„РѕСЂРјР°С†РёРё Рѕ РїСЂРѕРґРІРёРЅСѓС‚РѕРј Р°РЅР°Р»РёР·Рµ                      |
//+------------------------------------------------------------------+
void ShowAdvancedAnalysisInfo()
{
   if(!UseAdvancedAnalysis || !m_show_advanced_info)
      return;
      
   // Р—Р°РіР»СѓС€РєР° РґР»СЏ С„СѓРЅРєС†РёРё
   Print("Р¤СѓРЅРєС†РёСЏ ShowAdvancedAnalysisInfo РІС‹Р·РІР°РЅР°");
}

//+------------------------------------------------------------------+
//| РћС‚РѕР±СЂР°Р¶РµРЅРёРµ РёРЅС„РѕСЂРјР°С†РёРё РѕР± СѓРјРЅРѕРј РІС‹С…РѕРґРµ                            |
//+------------------------------------------------------------------+
void ShowSmartExitInfo()
{
   if(!m_use_smart_exit)
      return;
      
   // Р—Р°РіР»СѓС€РєР° РґР»СЏ С„СѓРЅРєС†РёРё
   Print("Р¤СѓРЅРєС†РёСЏ ShowSmartExitInfo РІС‹Р·РІР°РЅР°");
}

//+------------------------------------------------------------------+
//| РћР±РЅРѕРІР»РµРЅРёРµ РѕР±С‰РµРіРѕ РєРѕР»РёС‡РµСЃС‚РІР° РїРѕР·РёС†РёР№                              |
//+------------------------------------------------------------------+
void UpdatePositionsTotal()
{
   // РЎР±СЂРѕСЃ СЃС‡РµС‚С‡РёРєРѕРІ
   m_buy_positions_total = 0;
   m_sell_positions_total = 0;
   
   // РџРѕРґСЃС‡РµС‚ РїРѕР·РёС†РёР№ РїРѕ РІСЃРµРј СЃР»РѕСЏРј
   for(int layer_index = 0; layer_index < 3; layer_index++)
   {
      m_buy_positions_total += m_buy_positions_count[layer_index];
      m_sell_positions_total += m_sell_positions_count[layer_index];
   }
}

//+------------------------------------------------------------------+
//| Р’Р°Р»РёРґР°С†РёСЏ СЃС‚РѕРї-Р»РѕСЃСЃР°                                              |
//+------------------------------------------------------------------+
double ValidateStopLoss(string symbol, int direction, double price, double sl)
{
   // РџСЂРѕРІРµСЂРєР° РЅР° РґРѕСЃС‚Р°С‚РѕС‡РЅРѕРµ СЂР°СЃСЃС‚РѕСЏРЅРёРµ РѕС‚ С‚РµРєСѓС‰РµР№ С†РµРЅС‹
   double min_distance = SymbolInfoInteger(symbol, SYMBOL_TRADE_STOPS_LEVEL) * SymbolInfoDouble(symbol, SYMBOL_POINT);
   
   if(direction == 0) // Buy
   {
      if(price - sl < min_distance)
         sl = price - min_distance;
   }
   else // Sell
   {
      if(sl - price < min_distance)
         sl = price + min_distance;
   }
   
   return sl;
}
//+------------------------------------------------------------------+
//| РћР±РЅР°СЂСѓР¶РµРЅРёРµ С†РµРЅРѕРІС‹С… РїР°С‚С‚РµСЂРЅРѕРІ                                    |
//+------------------------------------------------------------------+
ENUM_PRICE_PATTERN DetectPricePattern()
{
   // РџСЂРѕСЃС‚РѕР№ Р°Р»РіРѕСЂРёС‚Рј РѕРїСЂРµРґРµР»РµРЅРёСЏ РїР°С‚С‚РµСЂРЅРѕРІ (РїСЂРёРјРµСЂ)
   if(m_market_structure.type == STRUCTURE_UPTREND || m_market_structure.type == STRUCTURE_REVERSAL_UP)
      return PATTERN_BULL_TREND;
   else if(m_market_structure.type == STRUCTURE_DOWNTREND || m_market_structure.type == STRUCTURE_REVERSAL_DOWN)
      return PATTERN_BEAR_TREND;
   else
      return PATTERN_RANGING;
}

//+------------------------------------------------------------------+
//| Р Р°СЃС‡РµС‚ С‚РѕС‡РµРє РѕРїС‚РёРјР°Р»СЊРЅРѕРіРѕ СѓСЃСЂРµРґРЅРµРЅРёСЏ                             |
//+------------------------------------------------------------------+
void CalculateOptimalAveragingPoints(int direction) // 1 РґР»СЏ Buy, -1 РґР»СЏ Sell
{
   // РџСЂРёРјРµСЂ СЂРµР°Р»РёР·Р°С†РёРё
   if(direction == 1)  // Buy
   {
      // РџСЂРѕСЃС‚Р°СЏ СЂРµР°Р»РёР·Р°С†РёСЏ - СѓСЃСЂРµРґРЅРµРЅРёРµ РЅР° СѓСЂРѕРІРЅСЏС… Р¤РёР±РѕРЅР°С‡С‡Рё
      // Р—РґРµСЃСЊ РјРѕР¶РЅРѕ РґРѕР±Р°РІРёС‚СЊ Р±РѕР»РµРµ СЃР»РѕР¶РЅСѓСЋ Р»РѕРіРёРєСѓ
   }
   else  // Sell
   {
      // РђРЅР°Р»РѕРіРёС‡РЅР°СЏ Р»РѕРіРёРєР° РґР»СЏ Sell
   }
}

//+------------------------------------------------------------------+
//| РРЅРёС†РёР°Р»РёР·Р°С†РёСЏ РїСЂРѕС„РёР»РµР№ С‚РѕСЂРіРѕРІС‹С… СЃРµСЃСЃРёР№                           |
//+------------------------------------------------------------------+
void InitSessionProfiles()
{
   // РђР·РёР°С‚СЃРєР°СЏ СЃРµСЃСЃРёСЏ
   m_session_profiles[0].lot_multiplier = AsianSessionLotMult;
   m_session_profiles[0].grid_step_multiplier = AsianSessionGridMult;
   m_session_profiles[0].tp_multiplier = AsianSessionTPMult;
   m_session_profiles[0].sl_multiplier = AsianSessionSLMult;
   m_session_profiles[0].trailing_activation = AsianSessionTrailAct;
   m_session_profiles[0].averaging_multiplier = AsianSessionAvgMult;
   m_session_profiles[0].description = "Asian Session Settings";
   
   // Р•РІСЂРѕРїРµР№СЃРєР°СЏ СЃРµСЃСЃРёСЏ
   m_session_profiles[1].lot_multiplier = EuropeanSessionLotMult;
   m_session_profiles[1].grid_step_multiplier = EuropeanSessionGridMult;
   m_session_profiles[1].tp_multiplier = EuropeanSessionTPMult;
   m_session_profiles[1].sl_multiplier = EuropeanSessionSLMult;
   m_session_profiles[1].trailing_activation = EuropeanSessionTrailAct;
   m_session_profiles[1].averaging_multiplier = EuropeanSessionAvgMult;
   m_session_profiles[1].description = "European Session Settings";
   
   // РђРјРµСЂРёРєР°РЅСЃРєР°СЏ СЃРµСЃСЃРёСЏ
   m_session_profiles[2].lot_multiplier = AmericanSessionLotMult;
   m_session_profiles[2].grid_step_multiplier = AmericanSessionGridMult;
   m_session_profiles[2].tp_multiplier = AmericanSessionTPMult;
   m_session_profiles[2].sl_multiplier = AmericanSessionSLMult;
   m_session_profiles[2].trailing_activation = AmericanSessionTrailAct;
   m_session_profiles[2].averaging_multiplier = AmericanSessionAvgMult;
   m_session_profiles[2].description = "American Session Settings";
}

//+------------------------------------------------------------------+
//| РћР±РЅРѕРІР»РµРЅРёРµ РЅР°СЃС‚СЂРѕРµРє РІ Р·Р°РІРёСЃРёРјРѕСЃС‚Рё РѕС‚ СЃРµСЃСЃРёРё                       |
//+------------------------------------------------------------------+
void UpdateSessionSettings()
{
   if(!UseSessionProfiles)
      return;
      
   // РћРїСЂРµРґРµР»РµРЅРёРµ С‚РµРєСѓС‰РµР№ СЃРµСЃСЃРёРё
   int current_session = DetectCurrentSession();
   
   // РџСЂРёРјРµРЅРµРЅРёРµ РЅР°СЃС‚СЂРѕРµРє С‚РµРєСѓС‰РµР№ СЃРµСЃСЃРёРё
   m_current_lot_multiplier = m_session_profiles[current_session].lot_multiplier;
