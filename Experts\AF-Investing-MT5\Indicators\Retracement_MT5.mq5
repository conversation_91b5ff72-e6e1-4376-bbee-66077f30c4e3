//+------------------------------------------------------------------+
//|                                         Retracement_MT5.mq5      |
//|                         Copyright 2023, AFSID-Group.Cv           |
//|                                      https://afs-id.com          |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, AFSID-Group.Cv"
#property link      "https://afs-id.com"
#property version   "1.00"

#property indicator_chart_window
#property indicator_buffers 10
#property indicator_plots   6

// Основные линии Фибоначчи
#property indicator_label1  "Fibo 0.0"
#property indicator_type1   DRAW_LINE
#property indicator_color1  clrRed
#property indicator_style1  STYLE_DASH
#property indicator_width1  1

#property indicator_label2  "Fibo 0.236"
#property indicator_type2   DRAW_LINE
#property indicator_color2  clrBlue
#property indicator_style2  STYLE_DASH
#property indicator_width2  1

#property indicator_label3  "Fibo 0.382"
#property indicator_type3   DRAW_LINE
#property indicator_color3  clrMagenta
#property indicator_style3  STYLE_DASH
#property indicator_width3  1

#property indicator_label4  "Fibo 0.5"
#property indicator_type4   DRAW_LINE
#property indicator_color4  clrOrange
#property indicator_style4  STYLE_DASH
#property indicator_width4  1

#property indicator_label5  "Fibo 0.618"
#property indicator_type5   DRAW_LINE
#property indicator_color5  clrGreen
#property indicator_style5  STYLE_DASH
#property indicator_width5  1

#property indicator_label6  "Fibo 1.0"
#property indicator_type6   DRAW_LINE
#property indicator_color6  clrRed
#property indicator_style6  STYLE_DASH
#property indicator_width6  1

// Входные параметры
input int    InpPeriod = 20;             // Период для поиска экстремумов
input bool   InpAutomatic = true;        // Автоматический поиск экстремумов
input bool   InpShowLabels = true;       // Показывать метки уровней
input bool   InpExtendLines = true;      // Продлить линии вправо
input color  InpLabelColor = clrWhite;   // Цвет меток
input int    InpLabelFontSize = 8;       // Размер шрифта меток
input string InpPrefix = "Fibo_";        // Префикс для объектов

// Буферы индикатора
double Fibo0Buffer[];
double Fibo236Buffer[];
double Fibo382Buffer[];
double Fibo50Buffer[];
double Fibo618Buffer[];
double Fibo100Buffer[];

// Вспомогательные буферы
double HighBuffer[];
double LowBuffer[];
double HighestBuffer[];
double LowestBuffer[];

// Глобальные переменные
double fiboLevels[6] = {0.0, 0.236, 0.382, 0.5, 0.618, 1.0};
datetime lastCalculation = 0;
int lastHighBar = 0, lastLowBar = 0;
double lastHigh = 0, lastLow = 0;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   // Настройка буферов индикатора
   SetIndexBuffer(0, Fibo0Buffer, INDICATOR_DATA);
   SetIndexBuffer(1, Fibo236Buffer, INDICATOR_DATA);
   SetIndexBuffer(2, Fibo382Buffer, INDICATOR_DATA);
   SetIndexBuffer(3, Fibo50Buffer, INDICATOR_DATA);
   SetIndexBuffer(4, Fibo618Buffer, INDICATOR_DATA);
   SetIndexBuffer(5, Fibo100Buffer, INDICATOR_DATA);
   
   // Настройка вспомогательных буферов
   SetIndexBuffer(6, HighBuffer, INDICATOR_CALCULATIONS);
   SetIndexBuffer(7, LowBuffer, INDICATOR_CALCULATIONS);
   SetIndexBuffer(8, HighestBuffer, INDICATOR_CALCULATIONS);
   SetIndexBuffer(9, LowestBuffer, INDICATOR_CALCULATIONS);
   
   // Установка пустых значений для линий
   PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, 0.0);
   PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, 0.0);
   PlotIndexSetDouble(2, PLOT_EMPTY_VALUE, 0.0);
   PlotIndexSetDouble(3, PLOT_EMPTY_VALUE, 0.0);
   PlotIndexSetDouble(4, PLOT_EMPTY_VALUE, 0.0);
   PlotIndexSetDouble(5, PLOT_EMPTY_VALUE, 0.0);
   
   // Установка имени индикатора
   IndicatorSetString(INDICATOR_SHORTNAME, "Retracement MT5 (" + string(InpPeriod) + ")");
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Удаление всех графических объектов
   ObjectsDeleteAll(0, InpPrefix);
   Comment("");
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   // Проверка на достаточное количество баров
   if(rates_total < InpPeriod)
      return(0);
      
   // Вычисление начальной позиции
   int limit;
   if(prev_calculated == 0)
   {
      limit = rates_total - InpPeriod - 1;
      ArrayInitialize(Fibo0Buffer, 0.0);
      ArrayInitialize(Fibo236Buffer, 0.0);
      ArrayInitialize(Fibo382Buffer, 0.0);
      ArrayInitialize(Fibo50Buffer, 0.0);
      ArrayInitialize(Fibo618Buffer, 0.0);
      ArrayInitialize(Fibo100Buffer, 0.0);
      ArrayInitialize(HighBuffer, 0.0);
      ArrayInitialize(LowBuffer, 0.0);
      ArrayInitialize(HighestBuffer, 0.0);
      ArrayInitialize(LowestBuffer, 0.0);
   }
   else
   {
      limit = rates_total - prev_calculated;
      if(limit > 1) limit++;
   }
   
   // Основной расчет
   for(int i = limit; i >= 0; i--)
   {
      // Запоминаем максимумы и минимумы
      HighBuffer[i] = high[i];
      LowBuffer[i] = low[i];
      
      // Находим локальные экстремумы
      if(InpAutomatic)
      {
         if(i < rates_total - 1)
         {
            // Поиск максимума
            HighestBuffer[i] = 0;
            double max_value = high[i];
            int max_pos = i;
            for(int j = 1; j <= InpPeriod && i+j < rates_total; j++)
            {
               if(high[i+j] > max_value)
               {
                  max_value = high[i+j];
                  max_pos = i+j;
               }
            }
            
            // Поиск минимума
            LowestBuffer[i] = 0;
            double min_value = low[i];
            int min_pos = i;
            for(int j = 1; j <= InpPeriod && i+j < rates_total; j++)
            {
               if(low[i+j] < min_value)
               {
                  min_value = low[i+j];
                  min_pos = i+j;
               }
            }
            
            // Обновление глобальных переменных
            if(max_pos != lastHighBar || min_pos != lastLowBar)
            {
               lastHighBar = max_pos;
               lastLowBar = min_pos;
               lastHigh = max_value;
               lastLow = min_value;
               lastCalculation = time[i];
               
               // Обновление линий Фибоначчи
               if(max_pos < min_pos) // Нисходящий тренд
                  CalculateRetracement(rates_total, time, lastHigh, lastLow, false);
               else // Восходящий тренд
                  CalculateRetracement(rates_total, time, lastLow, lastHigh, true);
            }
         }
      }
      
      // Заполнение буферов для отображения уровней
      if(lastHigh > 0 && lastLow > 0)
      {
         if(lastHigh > lastLow) // Восходящий тренд
         {
            double range = lastHigh - lastLow;
            
            Fibo0Buffer[i] = lastLow;
            Fibo236Buffer[i] = lastLow + range * 0.236;
            Fibo382Buffer[i] = lastLow + range * 0.382;
            Fibo50Buffer[i] = lastLow + range * 0.5;
            Fibo618Buffer[i] = lastLow + range * 0.618;
            Fibo100Buffer[i] = lastHigh;
         }
         else // Нисходящий тренд
         {
            double range = lastLow - lastHigh;
            
            Fibo0Buffer[i] = lastHigh;
            Fibo236Buffer[i] = lastHigh + range * 0.236;
            Fibo382Buffer[i] = lastHigh + range * 0.382;
            Fibo50Buffer[i] = lastHigh + range * 0.5;
            Fibo618Buffer[i] = lastHigh + range * 0.618;
            Fibo100Buffer[i] = lastLow;
         }
      }
   }
   
   // Создание меток для уровней Фибоначчи
   if(InpShowLabels && lastHigh > 0 && lastLow > 0)
      CreateLabels(rates_total, time);
   
   return(rates_total);
}

//+------------------------------------------------------------------+
//| Создание графических уровней Фибоначчи                           |
//+------------------------------------------------------------------+
void CalculateRetracement(const int rates_total, const datetime &time[], double start_price, double end_price, bool is_uptrend)
{
   if(rates_total < 2)
      return;
      
   double range = MathAbs(end_price - start_price);
   
   // Очистка старых объектов
   ObjectsDeleteAll(0, InpPrefix);
   
   // Создание новых линий
   for(int i = 0; i < 6; i++)
   {
      string obj_name = InpPrefix + DoubleToString(fiboLevels[i], 3);
      double level_price;
      
      if(is_uptrend)
         level_price = start_price + range * fiboLevels[i];
      else
         level_price = start_price - range * fiboLevels[i];
         
      // Создание горизонтальной линии
      ObjectCreate(0, obj_name, OBJ_HLINE, 0, 0, level_price);
      
      // Настройка линии
      ObjectSetInteger(0, obj_name, OBJPROP_COLOR, GetFiboColor(i));
      ObjectSetInteger(0, obj_name, OBJPROP_STYLE, STYLE_DASH);
      ObjectSetInteger(0, obj_name, OBJPROP_WIDTH, 1);
      ObjectSetInteger(0, obj_name, OBJPROP_BACK, true);
      ObjectSetInteger(0, obj_name, OBJPROP_SELECTABLE, false);
      
      // Продление линии вправо
      if(InpExtendLines)
         ObjectSetInteger(0, obj_name, OBJPROP_RAY_RIGHT, true);
   }
}

//+------------------------------------------------------------------+
//| Создание меток для уровней Фибоначчи                             |
//+------------------------------------------------------------------+
void CreateLabels(const int rates_total, const datetime &time[])
{
   if(!InpShowLabels || rates_total < 2)
      return;
      
   for(int i = 0; i < 6; i++)
   {
      string obj_name = InpPrefix + "Label_" + DoubleToString(fiboLevels[i], 3);
      string level_text = DoubleToString(fiboLevels[i], 3);
      double level_price;
      
      string fibo_obj_name = InpPrefix + DoubleToString(fiboLevels[i], 3);
      if(ObjectFind(0, fibo_obj_name) >= 0)
         level_price = ObjectGetDouble(0, fibo_obj_name, OBJPROP_PRICE, 0);
      else
         continue;
         
      // Создание метки
      if(ObjectFind(0, obj_name) < 0)
         ObjectCreate(0, obj_name, OBJ_TEXT, 0, time[0], level_price);
      else
         ObjectMove(0, obj_name, 0, time[0], level_price);
         
      // Настройка метки
      ObjectSetString(0, obj_name, OBJPROP_TEXT, level_text);
      ObjectSetInteger(0, obj_name, OBJPROP_COLOR, InpLabelColor);
      ObjectSetInteger(0, obj_name, OBJPROP_FONTSIZE, InpLabelFontSize);
      ObjectSetInteger(0, obj_name, OBJPROP_BACK, false);
      ObjectSetInteger(0, obj_name, OBJPROP_SELECTABLE, false);
   }
}

//+------------------------------------------------------------------+
//| Получение цвета для уровня Фибоначчи                             |
//+------------------------------------------------------------------+
color GetFiboColor(int index)
{
   switch(index)
   {
      case 0: return clrRed;
      case 1: return clrBlue;
      case 2: return clrMagenta;
      case 3: return clrOrange;
      case 4: return clrGreen;
      case 5: return clrRed;
      default: return clrWhite;
   }
}
//+------------------------------------------------------------------+ 