//+------------------------------------------------------------------+
//|                                      AF-Investing.mq5 |
//|                        Copyright 2019, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+

#property copyright "Copyright 2012-2019, AFSID-Group.Cv"
#property link      "https://afs-id.com"
#property version   "19.0"

#property description   "=Minimum Deposit 2500$/2 Pair"
#property description   "=Recommended Pair EURUSD,EURJPY,USDCHF,GBPUSD,GBPJPY,EURGBP="
#property description   "=TF M30="

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>

// В MT5 константы цвета уже определены в стандартной библиотеке,
// нет необходимости подключать отдельный файл

// Создаем объект для торговли
CTrade trade;
CPositionInfo position;
COrderInfo order;

// Объявление переменных для индикаторов
int macd_handle;
int rsi_handle;
int ma_handle_fast;
int ma_handle_slow;

// Объявление переменных для стоп-лоссов
input double StopLoss = 50;

// Входные параметры
input string Menu = "=====AF-Investing=====";
input string EAName = "AF-Investing";
input bool LabelInfo = true;
input int Magic = 12345;
input bool UseRandomMagic = false;
input int RandomMagicLower = 1000;
input int RandomMagicUpper = 100000;

// Настройки для мультитаймфреймового анализа
input string MultiTimeframeSettings = "=====Настройки мультитаймфреймового анализа=====";
input bool UseMultiTimeframeAnalysis = true; // Использовать анализ на разных таймфреймах
input bool UseM1Timeframe = true;   // Использовать таймфрейм M1
input bool UseM5Timeframe = true;   // Использовать таймфрейм M5
input bool UseM15Timeframe = true;  // Использовать таймфрейм M15
input bool UseM30Timeframe = true;  // Использовать таймфрейм M30
input bool UseH1Timeframe = true;   // Использовать таймфрейм H1
input bool UseH4Timeframe = true;   // Использовать таймфрейм H4
input bool UseD1Timeframe = true;   // Использовать таймфрейм D1

// Фильтры времени
input string DayFilter = " Select the days for Indo Run to trade ";
input bool Monday = true;
input bool Tuesday = true;
input bool Wednesday = true;
input bool Thursday = true;
input bool Friday = true;
input bool Saturday = true;
input bool Sunday = true;
input bool TradeMonthEnd = true;
input int MonthEndOffset = 3;
input bool TradeFirstDayOffset = true;
input int DayOffset = 0;
input double GMTOffset = 0.0;
input bool TradeNFP = true;
input bool TradeMondayAfterNFP = true;
input bool TradeADP = true;

// Фильтры сессий
input string HourFilter = " Select the Trading Hours or set to 24h ";
input bool Trading24h = true;
input int HoursFrom = 10;
input int HoursTo = 6;
input string Session_Filter = " Only in 24 Mode: Use both Session Filter together only! ";
input string Session_Filter1 = " Filters per default London Session (GMT)! ";
input bool SessionFilter1 = false;
input double SF1Hour_On = 5.0;
input double SF1MinuteOn = 45.0;
input double SF1Hour_Off = 9.0;
input double SF1MinuteOff = 0.0;
input string Session_Filter2 = " Filters per default NY Session (GMT)! ";
input bool SessionFilter2 = false;
input double SF2Hour_On = 11.0;
input double SF2MinuteOn = 45.0;
input double SF2Hour_Off = 17.0;
input double SF2MinuteOff = 30.0;

// Фильтры новостей
input string info9 = " News Filter Setup ";
input bool AvoidNews = true;
input bool High_Impact = true;
input int MinsUntilNextHighNews = 60;
input int MinsSincePrevHighNews = 60;
input bool Medium_Impact = true;
input int MinsUntilNextMediumNews = 40;
input int MinsSincePrevMediumNews = 40;
input bool Low_Impact = false;
input int MinsUntilNextLowNews = 30;
input int MinsSincePrevLowNews = 30;

// Фильтры валют
input string info10 = " News Currency Filter ";
input bool USD = true;
input bool EUR = true;
input bool GBP = true;
input bool JPY = true;
input bool AUD = false;
input bool CAD = false;
input bool CHF = false;
input bool NZD = false;
input int TSD_CalendarID = 4;
input string TSD_Calendar_URL = " http://calendar.forex-tsd.com/calendar.php?csv=1&date= ";

// Основные настройки ордеров
input string info5 = " Main Order Setup ";
input string ATURANWAJIB = "MENU SETTINGS";
input string OnlineIndicatorAddress = "*************"; //Online Indicator
input string IndicatorServer = "https://afs-id.com"; //Connecting to server
input bool UseOnlineIndicator = true;
input double Lots = 0.01;
input double MicroLot = 0.01;
input double LotExponent = 1.44;
input int lotdecimal = 2;
input double PipStep = 190.0;
input double MaxLots = 99.0;
input bool ReverseOrder = false;
input double SlipPage = 4.0;
input double RiskInPercent = 10.0;
input bool AutoLotSize = true;
input bool UseTimeStop = false;
input bool StopHours = true;
input bool StopMinutes = true;
input bool MM = false;
input double TakeProfit = 50.0;
input bool UseEquityStop = false;
input double TotalEquityRisk = 20.0;
input bool UseTrailingStop = false;
input double TrailStart = 13.0;
input double TrailStop = 3.0;
input double slip = 5.0;

// Настройки индикаторов
input string ATURANFIBO = "FIBO-SETTINGS";
input int MaxTrades_Hilo = 20;
input int MagicNumber_Hilo = 10278;

input string ATURANRSI = "RSI-SETTINGS";
input int MaxTrades_15 = 20;
input int g_magic_176_15 = 22324;

input string ATURANRANGE = "DAYRANGE-SETTINGS";
input int MaxTrades_16 = 20;
input int g_magic_176_16 = 23794;
input bool UseNewsFilter = true;

// Глобальные переменные
double gd_304; // Spread
double gd_516; // Spread
double gd_732; // Spread
double gd_248; // Account Balance
double gd_256; // Account Equity
int g_color_1128; // Equity Color
string g_dbl2str_1112; // Balance String
string g_dbl2str_1120; // Equity String

// Структура для хранения состояния торговли
struct TradeState {
   bool isBuy;
   bool isSell;
   bool canTrade;
   double lastBuyPrice;
   double lastSellPrice;
   int tradeCount;
   double avgPrice;
   double equityHigh;
   double equityLow;
   datetime lastTradeTime;
};

TradeState state_Hilo;
TradeState state_15;
TradeState state_16;

// Переменные для мультитаймфреймового анализа как в оригинальной версии МТ4
int g_timeframe_828 = PERIOD_M1;
int g_timeframe_832 = PERIOD_M5;
int g_timeframe_836 = PERIOD_M15;
int g_timeframe_840 = PERIOD_M30;
int g_timeframe_844 = PERIOD_H1;
int g_timeframe_848 = PERIOD_H4;
int g_timeframe_852 = PERIOD_D1;

// Хендлы индикаторов для различных таймфреймов
int macd_handle_m1, macd_handle_m5, macd_handle_m15, macd_handle_m30;
int macd_handle_h1, macd_handle_h4, macd_handle_d1;
int rsi_handle_m1, rsi_handle_m5, rsi_handle_m15, rsi_handle_m30;
int rsi_handle_h1, rsi_handle_h4, rsi_handle_d1;
int ma_handle_fast_m1, ma_handle_fast_m5, ma_handle_fast_m15, ma_handle_fast_m30;
int ma_handle_fast_h1, ma_handle_fast_h4, ma_handle_fast_d1;
int ma_handle_slow_m1, ma_handle_slow_m5, ma_handle_slow_m15, ma_handle_slow_m30;
int ma_handle_slow_h1, ma_handle_slow_h4, ma_handle_slow_d1;

// Объявление глобальных переменных для сигналов
bool macd_buy_signal = false;
bool macd_sell_signal = false;
bool rsi_buy_signal = false;
bool rsi_sell_signal = false;
bool ma_buy_signal = false;
bool ma_sell_signal = false;

// Внешние параметры
input bool UseDynamicSL = true;  // Использовать динамический StopLoss
input bool UseDynamicTP = true;  // Использовать динамический TakeProfit

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // Инициализация индикаторов на текущем таймфрейме
   macd_handle = iMACD(_Symbol, PERIOD_CURRENT, 12, 26, 9, PRICE_CLOSE);
   if(macd_handle == INVALID_HANDLE)
   {
      Print("Ошибка создания индикатора MACD");
      return INIT_FAILED;
   }
   
   rsi_handle = iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE);
   if(rsi_handle == INVALID_HANDLE)
   {
      Print("Ошибка создания индикатора RSI");
      return INIT_FAILED;
   }
   
   ma_handle_fast = iMA(_Symbol, PERIOD_CURRENT, 10, 0, MODE_EMA, PRICE_CLOSE);
   if(ma_handle_fast == INVALID_HANDLE)
   {
      Print("Ошибка создания быстрой MA");
      return INIT_FAILED;
   }
   
   ma_handle_slow = iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_EMA, PRICE_CLOSE);
   if(ma_handle_slow == INVALID_HANDLE)
   {
      Print("Ошибка создания медленной MA");
      return INIT_FAILED;
   }
   
   // Инициализация индикаторов на различных таймфреймах
   InitMultiTimeframeIndicators();
   
   // Инициализация мультитаймфреймовых сигналов
   InitializeMultiTimeframeSignals();
   
   // Инициализация спреда
   gd_304 = SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID);
   gd_516 = gd_304;
   gd_732 = gd_304;
   
   // Инициализация графических объектов
   
   
   ObjectCreate(0, "Lable", OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, "Lable", OBJPROP_CORNER, CORNER_RIGHT_UPPER);
   ObjectSetInteger(0, "Lable", OBJPROP_XDISTANCE, 3);
   ObjectSetInteger(0, "Lable", OBJPROP_YDISTANCE, 1);
   ObjectSetString(0, "Lable", OBJPROP_TEXT, " https://afs-id.com");
   ObjectSetInteger(0, "Lable", OBJPROP_FONTSIZE, 11);
   ObjectSetString(0, "Lable", OBJPROP_FONT, "Times New Roman");
   ObjectSetInteger(0, "Lable", OBJPROP_COLOR, clrDeepSkyBlue);
   
   // Создание дополнительных графических элементов
   CreateAdditionalGraphics();
   
   // Создание графических элементов для мультитаймфреймовой панели
   CreateMultiTimeframeGraphics();
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Инициализация индикаторов для разных таймфреймов                  |
//+------------------------------------------------------------------+
void InitMultiTimeframeIndicators()
{
   // MACD индикаторы
   macd_handle_m1 = iMACD(_Symbol, PERIOD_M1, 12, 26, 9, PRICE_CLOSE);
   macd_handle_m5 = iMACD(_Symbol, PERIOD_M5, 12, 26, 9, PRICE_CLOSE);
   macd_handle_m15 = iMACD(_Symbol, PERIOD_M15, 12, 26, 9, PRICE_CLOSE);
   macd_handle_m30 = iMACD(_Symbol, PERIOD_M30, 12, 26, 9, PRICE_CLOSE);
   macd_handle_h1 = iMACD(_Symbol, PERIOD_H1, 12, 26, 9, PRICE_CLOSE);
   macd_handle_h4 = iMACD(_Symbol, PERIOD_H4, 12, 26, 9, PRICE_CLOSE);
   macd_handle_d1 = iMACD(_Symbol, PERIOD_D1, 12, 26, 9, PRICE_CLOSE);
   
   // RSI индикаторы
   rsi_handle_m1 = iRSI(_Symbol, PERIOD_M1, 14, PRICE_CLOSE);
   rsi_handle_m5 = iRSI(_Symbol, PERIOD_M5, 14, PRICE_CLOSE);
   rsi_handle_m15 = iRSI(_Symbol, PERIOD_M15, 14, PRICE_CLOSE);
   rsi_handle_m30 = iRSI(_Symbol, PERIOD_M30, 14, PRICE_CLOSE);
   rsi_handle_h1 = iRSI(_Symbol, PERIOD_H1, 14, PRICE_CLOSE);
   rsi_handle_h4 = iRSI(_Symbol, PERIOD_H4, 14, PRICE_CLOSE);
   rsi_handle_d1 = iRSI(_Symbol, PERIOD_D1, 14, PRICE_CLOSE);
   
   // MA быстрые индикаторы
   ma_handle_fast_m1 = iMA(_Symbol, PERIOD_M1, 10, 0, MODE_EMA, PRICE_CLOSE);
   ma_handle_fast_m5 = iMA(_Symbol, PERIOD_M5, 10, 0, MODE_EMA, PRICE_CLOSE);
   ma_handle_fast_m15 = iMA(_Symbol, PERIOD_M15, 10, 0, MODE_EMA, PRICE_CLOSE);
   ma_handle_fast_m30 = iMA(_Symbol, PERIOD_M30, 10, 0, MODE_EMA, PRICE_CLOSE);
   ma_handle_fast_h1 = iMA(_Symbol, PERIOD_H1, 10, 0, MODE_EMA, PRICE_CLOSE);
   ma_handle_fast_h4 = iMA(_Symbol, PERIOD_H4, 10, 0, MODE_EMA, PRICE_CLOSE);
   ma_handle_fast_d1 = iMA(_Symbol, PERIOD_D1, 10, 0, MODE_EMA, PRICE_CLOSE);
   
   // MA медленные индикаторы
   ma_handle_slow_m1 = iMA(_Symbol, PERIOD_M1, 20, 0, MODE_EMA, PRICE_CLOSE);
   ma_handle_slow_m5 = iMA(_Symbol, PERIOD_M5, 20, 0, MODE_EMA, PRICE_CLOSE);
   ma_handle_slow_m15 = iMA(_Symbol, PERIOD_M15, 20, 0, MODE_EMA, PRICE_CLOSE);
   ma_handle_slow_m30 = iMA(_Symbol, PERIOD_M30, 20, 0, MODE_EMA, PRICE_CLOSE);
   ma_handle_slow_h1 = iMA(_Symbol, PERIOD_H1, 20, 0, MODE_EMA, PRICE_CLOSE);
   ma_handle_slow_h4 = iMA(_Symbol, PERIOD_H4, 20, 0, MODE_EMA, PRICE_CLOSE);
   ma_handle_slow_d1 = iMA(_Symbol, PERIOD_D1, 20, 0, MODE_EMA, PRICE_CLOSE);
}

//+------------------------------------------------------------------+
//| Создание графических элементов для мультитаймфреймовой панели    |
//+------------------------------------------------------------------+
void CreateMultiTimeframeGraphics()
{
   // Базовые параметры для выравнивания
   int x0 = 10; // Отступ от правого края для панели
   int x0_title = 200; // Отдельный отступ для названия советника (AF-Investing)
   int x0_spread = 200; // Отдельный отступ для спреда
   int y0 = 40; // Начальный отступ от верха (под названием)
   int yStep = 18; // Шаг по вертикали между строками
   int xStep = 38; // Шаг по горизонтали между таймфреймами
   string timeframes[] = {"M1", "M5", "M15", "M30", "H1", "H4", "D1"};

   // --- Название советника (AF-Investing) с отдельным отступом
   ObjectCreate(0, "LabelIndicatorsPanel", OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, "LabelIndicatorsPanel", OBJPROP_CORNER, CORNER_RIGHT_UPPER);
   ObjectSetInteger(0, "LabelIndicatorsPanel", OBJPROP_XDISTANCE, x0_title); // Используем отдельный отступ
   ObjectSetInteger(0, "LabelIndicatorsPanel", OBJPROP_YDISTANCE, y0 - yStep);
   ObjectSetString(0, "LabelIndicatorsPanel", OBJPROP_TEXT, "AF-Investing");
   ObjectSetInteger(0, "LabelIndicatorsPanel", OBJPROP_FONTSIZE, 12);
   ObjectSetString(0, "LabelIndicatorsPanel", OBJPROP_FONT, "Arial Black");
   ObjectSetInteger(0, "LabelIndicatorsPanel", OBJPROP_COLOR, clrDeepSkyBlue);

   

   

   // --- Таймфреймы для MACD, RSI, MA (горизонтально справа налево)
   for(int i = 0; i < ArraySize(timeframes); i++)
   {
      int x = x0 + xStep + (ArraySize(timeframes)-1-i)*xStep;
      // MACD
      ObjectCreate(0, "MACD_" + timeframes[i], OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, "MACD_" + timeframes[i], OBJPROP_CORNER, CORNER_RIGHT_UPPER);
      ObjectSetInteger(0, "MACD_" + timeframes[i], OBJPROP_XDISTANCE, x);
      ObjectSetInteger(0, "MACD_" + timeframes[i], OBJPROP_YDISTANCE, y0 + 2*yStep);
      ObjectSetString(0, "MACD_" + timeframes[i], OBJPROP_TEXT, timeframes[i]);
      ObjectSetInteger(0, "MACD_" + timeframes[i], OBJPROP_FONTSIZE, 10);
      ObjectSetString(0, "MACD_" + timeframes[i], OBJPROP_FONT, "Arial");
      ObjectSetInteger(0, "MACD_" + timeframes[i], OBJPROP_COLOR, clrGray);
      // RSI
      ObjectCreate(0, "RSI_" + timeframes[i], OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, "RSI_" + timeframes[i], OBJPROP_CORNER, CORNER_RIGHT_UPPER);
      ObjectSetInteger(0, "RSI_" + timeframes[i], OBJPROP_XDISTANCE, x);
      ObjectSetInteger(0, "RSI_" + timeframes[i], OBJPROP_YDISTANCE, y0 + 3*yStep);
      ObjectSetString(0, "RSI_" + timeframes[i], OBJPROP_TEXT, timeframes[i]);
      ObjectSetInteger(0, "RSI_" + timeframes[i], OBJPROP_FONTSIZE, 10);
      ObjectSetString(0, "RSI_" + timeframes[i], OBJPROP_FONT, "Arial");
      ObjectSetInteger(0, "RSI_" + timeframes[i], OBJPROP_COLOR, clrGray);
      // MA
      ObjectCreate(0, "MA_" + timeframes[i], OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, "MA_" + timeframes[i], OBJPROP_CORNER, CORNER_RIGHT_UPPER);
      ObjectSetInteger(0, "MA_" + timeframes[i], OBJPROP_XDISTANCE, x);
      ObjectSetInteger(0, "MA_" + timeframes[i], OBJPROP_YDISTANCE, y0 + 4*yStep);
      ObjectSetString(0, "MA_" + timeframes[i], OBJPROP_TEXT, timeframes[i]);
      ObjectSetInteger(0, "MA_" + timeframes[i], OBJPROP_FONTSIZE, 10);
      ObjectSetString(0, "MA_" + timeframes[i], OBJPROP_FONT, "Arial");
      ObjectSetInteger(0, "MA_" + timeframes[i], OBJPROP_COLOR, clrGray);
   }

   // --- Спред
   ObjectCreate(0, "Spread_Info", OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, "Spread_Info", OBJPROP_CORNER, CORNER_RIGHT_UPPER);
   ObjectSetInteger(0, "Spread_Info", OBJPROP_XDISTANCE, x0_spread); // Используем отдельный отступ для спреда
   ObjectSetInteger(0, "Spread_Info", OBJPROP_YDISTANCE, y0 + 5*yStep);
   ObjectSetString(0, "Spread_Info", OBJPROP_TEXT, "Spread: " + DoubleToString(SymbolInfoInteger(_Symbol, SYMBOL_SPREAD), 1));
   ObjectSetInteger(0, "Spread_Info", OBJPROP_FONTSIZE, 10);
   ObjectSetString(0, "Spread_Info", OBJPROP_FONT, "Arial");
   ObjectSetInteger(0, "Spread_Info", OBJPROP_COLOR, clrDarkOrange);

   // --- Комментарии для разработчика
   // Все объекты выровнены по правому верхнему углу, шаги и координаты можно подстроить под ваш стиль.
}

//+------------------------------------------------------------------+
//| Создание и обновление дополнительных графических элементов        |
//+------------------------------------------------------------------+
void CreateAdditionalGraphics()
{
   // Создание объектов для отображения баланса и средств
   ObjectCreate(0, "Lable2", OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, "Lable2", OBJPROP_CORNER, CORNER_RIGHT_LOWER); // Переносим в правый нижний угол
   ObjectSetInteger(0, "Lable2", OBJPROP_XDISTANCE, 200); // Отступ от правого края
   ObjectSetInteger(0, "Lable2", OBJPROP_YDISTANCE, 38); // Отступ от нижнего края (чуть выше, чтобы не налегало на край)
   ObjectSetString(0, "Lable2", OBJPROP_TEXT, "Account BALANCE: " + DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2));
   ObjectSetInteger(0, "Lable2", OBJPROP_FONTSIZE, 10);
   ObjectSetString(0, "Lable2", OBJPROP_FONT, "Times New Roman");
   ObjectSetInteger(0, "Lable2", OBJPROP_COLOR, clrDodgerBlue);
   
   ObjectCreate(0, "Lable3", OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, "Lable3", OBJPROP_CORNER, CORNER_RIGHT_LOWER); // Переносим в правый нижний угол
   ObjectSetInteger(0, "Lable3", OBJPROP_XDISTANCE, 200); // Отступ от правого края
   ObjectSetInteger(0, "Lable3", OBJPROP_YDISTANCE, 18); // Чуть выше баланса, чтобы не налегало
   ObjectSetString(0, "Lable3", OBJPROP_TEXT, "Account EQUITY: " + DoubleToString(AccountInfoDouble(ACCOUNT_EQUITY), 2));
   ObjectSetInteger(0, "Lable3", OBJPROP_FONTSIZE, 10);
   ObjectSetString(0, "Lable3", OBJPROP_FONT, "Times New Roman");
   ObjectSetInteger(0, "Lable3", OBJPROP_COLOR, clrForestGreen);
   
   // Создание объектов для отображения индикаторных сигналов
   
}

//+------------------------------------------------------------------+
//| Обновление графической информации на каждом тике                  |
//+------------------------------------------------------------------+
void UpdateGraphics()
{
   // Обновление баланса и средств
   double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double account_equity = AccountInfoDouble(ACCOUNT_EQUITY);
   
   // Определение цвета текста для средств в зависимости от состояния счета
   color equity_color = clrForestGreen;
   
   if(account_equity >= 5.0 * (account_balance / 6.0)) equity_color = clrDodgerBlue;
   else if(account_equity >= 4.0 * (account_balance / 6.0) && account_equity < 5.0 * (account_balance / 6.0)) equity_color = clrDeepSkyBlue;
   else if(account_equity >= 3.0 * (account_balance / 6.0) && account_equity < 4.0 * (account_balance / 6.0)) equity_color = clrGold;
   else if(account_equity >= 2.0 * (account_balance / 6.0) && account_equity < 3.0 * (account_balance / 6.0)) equity_color = clrOrangeRed;
   else if(account_equity >= account_balance / 6.0 && account_equity < 2.0 * (account_balance / 6.0)) equity_color = clrCrimson;
   else if(account_equity < account_balance / 6.0) equity_color = clrRed;
   
   // Обновление объектов
   ObjectSetString(0, "Lable2", OBJPROP_TEXT, "Account BALANCE: " + DoubleToString(account_balance, 2));
   ObjectSetString(0, "Lable3", OBJPROP_TEXT, "Account EQUITY: " + DoubleToString(account_equity, 2));
   ObjectSetInteger(0, "Lable3", OBJPROP_COLOR, equity_color);
   
   // Обновление информации о спреде
   ObjectSetString(0, "SIG_DETAIL_1", OBJPROP_TEXT, "Spread: " + DoubleToString(SymbolInfoInteger(_Symbol, SYMBOL_SPREAD), 1));
   
   // Обновление информации о торговых сигналах
   UpdateSignalIndicators();
   
   // Обновление индикаторов для всех таймфреймов
   UpdateMultiTimeframeIndicators();
}

//+------------------------------------------------------------------+
//| Обновление индикаторов для всех таймфреймов                       |
//+------------------------------------------------------------------+
void UpdateMultiTimeframeIndicators()
{
   // Обновляем информацию о счете и балансе
   ObjectSetString(0, "LabelAccountInfo", OBJPROP_TEXT, "Account Number: " + IntegerToString(AccountInfoInteger(ACCOUNT_LOGIN)));
   ObjectSetString(0, "LabelAccountBalance", OBJPROP_TEXT, "Account BALANCE: " + DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2));
   
   // Обновляем спред
   ObjectSetString(0, "Spread_Info", OBJPROP_TEXT, "Spread: " + DoubleToString(SymbolInfoInteger(_Symbol, SYMBOL_SPREAD), 1));
   
   // Обновляем индикаторы MACD, RSI и MA для всех таймфреймов
   UpdateMACDIndicators();
   UpdateRSIIndicators();
   UpdateMAIndicators();
}

//+------------------------------------------------------------------+
//| Обновление MACD индикаторов для всех таймфреймов                  |
//+------------------------------------------------------------------+
void UpdateMACDIndicators()
{
   // Массивы для хранения значений MACD
   double macd_main_m1[], macd_signal_m1[];
   double macd_main_m5[], macd_signal_m5[];
   double macd_main_m15[], macd_signal_m15[];
   double macd_main_m30[], macd_signal_m30[];
   double macd_main_h1[], macd_signal_h1[];
   double macd_main_h4[], macd_signal_h4[];
   double macd_main_d1[], macd_signal_d1[];
   
   // Настройка массивов как серии
   ArraySetAsSeries(macd_main_m1, true);
   ArraySetAsSeries(macd_signal_m1, true);
   ArraySetAsSeries(macd_main_m5, true);
   ArraySetAsSeries(macd_signal_m5, true);
   ArraySetAsSeries(macd_main_m15, true);
   ArraySetAsSeries(macd_signal_m15, true);
   ArraySetAsSeries(macd_main_m30, true);
   ArraySetAsSeries(macd_signal_m30, true);
   ArraySetAsSeries(macd_main_h1, true);
   ArraySetAsSeries(macd_signal_h1, true);
   ArraySetAsSeries(macd_main_h4, true);
   ArraySetAsSeries(macd_signal_h4, true);
   ArraySetAsSeries(macd_main_d1, true);
   ArraySetAsSeries(macd_signal_d1, true);
   
   // Копирование данных для каждого таймфрейма
   CopyBuffer(macd_handle_m1, MAIN_LINE, 0, 2, macd_main_m1);
   CopyBuffer(macd_handle_m1, SIGNAL_LINE, 0, 2, macd_signal_m1);
   CopyBuffer(macd_handle_m5, MAIN_LINE, 0, 2, macd_main_m5);
   CopyBuffer(macd_handle_m5, SIGNAL_LINE, 0, 2, macd_signal_m5);
   CopyBuffer(macd_handle_m15, MAIN_LINE, 0, 2, macd_main_m15);
   CopyBuffer(macd_handle_m15, SIGNAL_LINE, 0, 2, macd_signal_m15);
   CopyBuffer(macd_handle_m30, MAIN_LINE, 0, 2, macd_main_m30);
   CopyBuffer(macd_handle_m30, SIGNAL_LINE, 0, 2, macd_signal_m30);
   CopyBuffer(macd_handle_h1, MAIN_LINE, 0, 2, macd_main_h1);
   CopyBuffer(macd_handle_h1, SIGNAL_LINE, 0, 2, macd_signal_h1);
   CopyBuffer(macd_handle_h4, MAIN_LINE, 0, 2, macd_main_h4);
   CopyBuffer(macd_handle_h4, SIGNAL_LINE, 0, 2, macd_signal_h4);
   CopyBuffer(macd_handle_d1, MAIN_LINE, 0, 2, macd_main_d1);
   CopyBuffer(macd_handle_d1, SIGNAL_LINE, 0, 2, macd_signal_d1);
   
   // Определение цветов для каждого таймфрейма и обновление объектов
   string timeframes[] = {"M1", "M5", "M15", "M30", "H1", "H4", "D1"};
   double macd_main[][2] = {{macd_main_m1[0], macd_main_m1[1]}, 
                            {macd_main_m5[0], macd_main_m5[1]},
                            {macd_main_m15[0], macd_main_m15[1]},
                            {macd_main_m30[0], macd_main_m30[1]},
                            {macd_main_h1[0], macd_main_h1[1]},
                            {macd_main_h4[0], macd_main_h4[1]},
                            {macd_main_d1[0], macd_main_d1[1]}};
                            
   double macd_signal[][2] = {{macd_signal_m1[0], macd_signal_m1[1]}, 
                              {macd_signal_m5[0], macd_signal_m5[1]},
                              {macd_signal_m15[0], macd_signal_m15[1]},
                              {macd_signal_m30[0], macd_signal_m30[1]},
                              {macd_signal_h1[0], macd_signal_h1[1]},
                              {macd_signal_h4[0], macd_signal_h4[1]},
                              {macd_signal_d1[0], macd_signal_d1[1]}};
   
   for(int i = 0; i < ArraySize(timeframes); i++)
   {
      color macd_color = clrGray;
      if(macd_main[i][0] > macd_signal[i][0] && macd_main[i][0] > 0)
         macd_color = clrLime;
      else if(macd_main[i][0] > macd_signal[i][0])
         macd_color = clrGreen;
      else if(macd_main[i][0] < macd_signal[i][0] && macd_main[i][0] < 0)
         macd_color = clrRed;
      else if(macd_main[i][0] < macd_signal[i][0])
         macd_color = clrTomato;
      
      ObjectSetInteger(0, "MACD_" + timeframes[i], OBJPROP_COLOR, macd_color);
   }
}

//+------------------------------------------------------------------+
//| Обновление RSI индикаторов для всех таймфреймов                  |
//+------------------------------------------------------------------+
void UpdateRSIIndicators()
{
   // Массивы для хранения значений RSI
   double rsi_m1[], rsi_m5[], rsi_m15[], rsi_m30[], rsi_h1[], rsi_h4[], rsi_d1[];
   
   // Настройка массивов как серии
   ArraySetAsSeries(rsi_m1, true);
   ArraySetAsSeries(rsi_m5, true);
   ArraySetAsSeries(rsi_m15, true);
   ArraySetAsSeries(rsi_m30, true);
   ArraySetAsSeries(rsi_h1, true);
   ArraySetAsSeries(rsi_h4, true);
   ArraySetAsSeries(rsi_d1, true);
   
   // Копирование данных для каждого таймфрейма
   CopyBuffer(rsi_handle_m1, 0, 0, 2, rsi_m1);
   CopyBuffer(rsi_handle_m5, 0, 0, 2, rsi_m5);
   CopyBuffer(rsi_handle_m15, 0, 0, 2, rsi_m15);
   CopyBuffer(rsi_handle_m30, 0, 0, 2, rsi_m30);
   CopyBuffer(rsi_handle_h1, 0, 0, 2, rsi_h1);
   CopyBuffer(rsi_handle_h4, 0, 0, 2, rsi_h4);
   CopyBuffer(rsi_handle_d1, 0, 0, 2, rsi_d1);
   
   // Определение цветов для каждого таймфрейма и обновление объектов
   string timeframes[] = {"M1", "M5", "M15", "M30", "H1", "H4", "D1"};
   double rsi_values[] = {rsi_m1[0], rsi_m5[0], rsi_m15[0], rsi_m30[0], rsi_h1[0], rsi_h4[0], rsi_d1[0]};
   
   for(int i = 0; i < ArraySize(timeframes); i++)
   {
      color rsi_color = clrGray;
      if(rsi_values[i] > 70) rsi_color = clrRed;
      else if(rsi_values[i] < 30) rsi_color = clrLime;
      else rsi_color = clrOrange;
      
      ObjectSetInteger(0, "RSI_" + timeframes[i], OBJPROP_COLOR, rsi_color);
   }
}

//+------------------------------------------------------------------+
//| Обновление МА индикаторов для всех таймфреймов                    |
//+------------------------------------------------------------------+
void UpdateMAIndicators()
{
   // Массивы для хранения значений MA
   double ma_fast_m1[], ma_slow_m1[];
   double ma_fast_m5[], ma_slow_m5[];
   double ma_fast_m15[], ma_slow_m15[];
   double ma_fast_m30[], ma_slow_m30[];
   double ma_fast_h1[], ma_slow_h1[];
   double ma_fast_h4[], ma_slow_h4[];
   double ma_fast_d1[], ma_slow_d1[];
   
   // Настройка массивов как серии
   ArraySetAsSeries(ma_fast_m1, true);
   ArraySetAsSeries(ma_slow_m1, true);
   ArraySetAsSeries(ma_fast_m5, true);
   ArraySetAsSeries(ma_slow_m5, true);
   ArraySetAsSeries(ma_fast_m15, true);
   ArraySetAsSeries(ma_slow_m15, true);
   ArraySetAsSeries(ma_fast_m30, true);
   ArraySetAsSeries(ma_slow_m30, true);
   ArraySetAsSeries(ma_fast_h1, true);
   ArraySetAsSeries(ma_slow_h1, true);
   ArraySetAsSeries(ma_fast_h4, true);
   ArraySetAsSeries(ma_slow_h4, true);
   ArraySetAsSeries(ma_fast_d1, true);
   ArraySetAsSeries(ma_slow_d1, true);
   
   // Копирование данных для каждого таймфрейма
   CopyBuffer(ma_handle_fast_m1, 0, 0, 2, ma_fast_m1);
   CopyBuffer(ma_handle_slow_m1, 0, 0, 2, ma_slow_m1);
   CopyBuffer(ma_handle_fast_m5, 0, 0, 2, ma_fast_m5);
   CopyBuffer(ma_handle_slow_m5, 0, 0, 2, ma_slow_m5);
   CopyBuffer(ma_handle_fast_m15, 0, 0, 2, ma_fast_m15);
   CopyBuffer(ma_handle_slow_m15, 0, 0, 2, ma_slow_m15);
   CopyBuffer(ma_handle_fast_m30, 0, 0, 2, ma_fast_m30);
   CopyBuffer(ma_handle_slow_m30, 0, 0, 2, ma_slow_m30);
   CopyBuffer(ma_handle_fast_h1, 0, 0, 2, ma_fast_h1);
   CopyBuffer(ma_handle_slow_h1, 0, 0, 2, ma_slow_h1);
   CopyBuffer(ma_handle_fast_h4, 0, 0, 2, ma_fast_h4);
   CopyBuffer(ma_handle_slow_h4, 0, 0, 2, ma_slow_h4);
   CopyBuffer(ma_handle_fast_d1, 0, 0, 2, ma_fast_d1);
   CopyBuffer(ma_handle_slow_d1, 0, 0, 2, ma_slow_d1);
   
   // Определение цветов для каждого таймфрейма и обновление объектов
   string timeframes[] = {"M1", "M5", "M15", "M30", "H1", "H4", "D1"};
   double ma_fast[] = {ma_fast_m1[0], ma_fast_m5[0], ma_fast_m15[0], ma_fast_m30[0], ma_fast_h1[0], ma_fast_h4[0], ma_fast_d1[0]};
   double ma_slow[] = {ma_slow_m1[0], ma_slow_m5[0], ma_slow_m15[0], ma_slow_m30[0], ma_slow_h1[0], ma_slow_h4[0], ma_slow_d1[0]};
   
   for(int i = 0; i < ArraySize(timeframes); i++)
   {
      color ma_color = clrGray;
      if(ma_fast[i] > ma_slow[i]) ma_color = clrLime;
      else if(ma_fast[i] < ma_slow[i]) ma_color = clrRed;
      
      ObjectSetInteger(0, "MA_" + timeframes[i], OBJPROP_COLOR, ma_color);
   }
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Удаление основных объектов панели
   ObjectDelete(0, "LabelIndicatorsPanel");
   ObjectDelete(0, "LabelAccountInfo");
   ObjectDelete(0, "LabelAccountBalance");
   ObjectDelete(0, "LabelSeparator");
   
   // Удаление заголовков индикаторов
   ObjectDelete(0, "MACD_Header");
   ObjectDelete(0, "RSI_Header");
   ObjectDelete(0, "MA_Header");
   
   // Удаление индикаторов таймфреймов
   string timeframes[] = {"M1", "M5", "M15", "M30", "H1", "H4", "D1"};
   
   for(int i = 0; i < ArraySize(timeframes); i++)
   {
      ObjectDelete(0, "MACD_" + timeframes[i]);
      ObjectDelete(0, "RSI_" + timeframes[i]);
      ObjectDelete(0, "MA_" + timeframes[i]);
   }
   
   // Удаление информации о спреде
   ObjectDelete(0, "Spread_Info");
   
   // Удаление старых объектов (чтобы избежать возможных проблем)
   ObjectDelete(0, "Lable1");
   ObjectDelete(0, "Lable");
   ObjectDelete(0, "Lable2");
   ObjectDelete(0, "Lable3");
   ObjectDelete(0, "SSignalMACD_TEXT");
   ObjectDelete(0, "SSignalRSI_TEXT");
   ObjectDelete(0, "SSignalMA_TEXT");
   ObjectDelete(0, "SIG_DETAIL_1");
   
   Comment("https://afs-id.com");
}

//+------------------------------------------------------------------+
//| Обновление информации о сигналах индикаторов                     |
//+------------------------------------------------------------------+
void UpdateSignalIndicators()
{
   // Получение значений MACD
   double macd_main[];
   double macd_signal[];
   ArraySetAsSeries(macd_main, true);
   ArraySetAsSeries(macd_signal, true);
   CopyBuffer(macd_handle, MAIN_LINE, 0, 2, macd_main);
   CopyBuffer(macd_handle, SIGNAL_LINE, 0, 2, macd_signal);
   
   // Получение значений RSI
   double rsi_values[];
   ArraySetAsSeries(rsi_values, true);
   CopyBuffer(rsi_handle, 0, 0, 2, rsi_values);
   
   // Получение значений MA
   double ma_fast[];
   double ma_slow[];
   ArraySetAsSeries(ma_fast, true);
   ArraySetAsSeries(ma_slow, true);
   CopyBuffer(ma_handle_fast, 0, 0, 2, ma_fast);
   CopyBuffer(ma_handle_slow, 0, 0, 2, ma_slow);
   
   // Определение цветов для индикаторов
   color macd_color = clrGray;
   color rsi_color = clrGray;
   color ma_color = clrGray;
   
   // MACD
   if(macd_main[0] > macd_signal[0] && macd_main[0] > 0)
      macd_color = clrLime;
   else if(macd_main[0] > macd_signal[0])
      macd_color = clrGreen;
   else if(macd_main[0] < macd_signal[0] && macd_main[0] < 0)
      macd_color = clrRed;
   else if(macd_main[0] < macd_signal[0])
      macd_color = clrTomato;
   
   // RSI
   if(rsi_values[0] > 70)
      rsi_color = clrRed;
   else if(rsi_values[0] < 30)
      rsi_color = clrLime;
   else
      rsi_color = clrOrange;
   
   // MA
   if(ma_fast[0] > ma_slow[0])
      ma_color = clrLime;
   else if(ma_fast[0] < ma_slow[0])
      ma_color = clrRed;
   
   // Обновление текста и цвета объектов
   ObjectSetInteger(0, "SSignalMACD_TEXT", OBJPROP_COLOR, macd_color);
   ObjectSetInteger(0, "SSignalRSI_TEXT", OBJPROP_COLOR, rsi_color);
   ObjectSetInteger(0, "SSignalMA_TEXT", OBJPROP_COLOR, ma_color);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Проверка истечения лицензии
    string batas = "2025.12.31 00:00"; // Продление до 2025 года
    string count = "**********";
    datetime tt = StringToTime(batas);
    
    // Обновление графической информации
    UpdateGraphics();
    
    // Мониторинг стратегий
    MonitorStrategies();
    
    // Проверка системы восстановления
    RecoverySystem();
    
    Comment("Account Number:" + IntegerToString(AccountInfoInteger(ACCOUNT_LOGIN)) + 
            "\n" + "AFSID GROUP" + 
            "\n" + "https://afs-id.com" + 
            "\n" + "___________________________________________________" +
            "\n" + "Broker                                    :" + AccountInfoString(ACCOUNT_COMPANY) +
            "\n" + "Brokers Time                          :" + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS) +
            "\n" + "___________________________________________________" +
            "\n" + "Name                                     :" + AccountInfoString(ACCOUNT_NAME) +
            "\n" + "Account Number                    :" + IntegerToString(AccountInfoInteger(ACCOUNT_LOGIN)) +
            "\n" + "Account Currency                  :" + AccountInfoString(ACCOUNT_CURRENCY) +
            "\n" + "____________________________________________________" +
            "\n" + "Open Orders FiboScalper         :" + IntegerToString(CountTrades_Hilo()) +
            "\n" + "Open Orders AF-Scalper          :" + IntegerToString(CountTrades_15()) +
            "\n" + "Open Orders AF-TrendKiller     :" + IntegerToString(CountTrades_16()) +
            "\n" + "ALL ORDERS                          :" + IntegerToString(PositionsTotal()) +
            "\n" + "_____________________________________________________" +
            "\n" + "Account BALANCE                  :" + DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2) +
            "\n" + "Account EQUITY                     :" + DoubleToString(AccountInfoDouble(ACCOUNT_EQUITY), 2) +
            "\n" + "AFSID GROUP"
          );
    
    // Выводим сообщение только один раз при запуске советника
    static bool access_message_shown = false;
    if(!access_message_shown)
    {
        Print("¡Access allowed to AF-Investing!");
        access_message_shown = true;
    }
    
    if(TimeCurrent() > tt)
    {
        Alert(" License expired contact support ");
        return;
    }

    // Обновление баланса и средств
    gd_248 = AccountInfoDouble(ACCOUNT_BALANCE);
    gd_256 = AccountInfoDouble(ACCOUNT_EQUITY);
    
    // Проверка условий для торговли
    if(!IsTradeAllowed()) return;
    if(!IsNewBar()) return;
    
    // Получение текущих цен
    double current_ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double current_bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    
    // Расчет размера лота с учетом динамического риска
    double dynamicRisk = CalculateDynamicRisk();
    double lots = Lots;
    if(AutoLotSize)
    {
        lots = NormalizeDouble(AccountInfoDouble(ACCOUNT_BALANCE) * dynamicRisk / 100 / 10000, lotdecimal);
        if(lots < MicroLot) lots = MicroLot;
        if(lots > MaxLots) lots = MaxLots;
    }
    
    // Анализ мультитаймфреймовых индикаторов для улучшения принятия решений
    bool current_macd_buy_signal = CheckMACDBuySignal();
    bool current_macd_sell_signal = CheckMACDSellSignal();
    bool current_rsi_buy_signal = CheckRSIBuySignal();
    bool current_rsi_sell_signal = CheckRSISellSignal();
    bool current_ma_buy_signal = CheckMABuySignal();
    bool current_ma_sell_signal = CheckMASellSignal();
    
    // Обновляем глобальные переменные сигналов
    macd_buy_signal = current_macd_buy_signal;
    macd_sell_signal = current_macd_sell_signal;
    rsi_buy_signal = current_rsi_buy_signal;
    rsi_sell_signal = current_rsi_sell_signal;
    ma_buy_signal = current_ma_buy_signal;
    ma_sell_signal = current_ma_sell_signal;
    
    // FiboScalper (Hilo) - Обработка стратегии
    if(ValidateSignal(1)) // 1 - FiboScalper
    {
        double ld_144 = lots;
    
    // Обновление трейлинг-стопов
        if(UseTrailingStop) TrailingStop_Hilo();
        
        // Проверка таймаута
        static datetime timeout_Hilo = 0;
        if(timeout_Hilo > 0 && TimeCurrent() >= timeout_Hilo)
        {
            CloseThisSymbolAll_Hilo();
            Print("Закрыты все позиции Hilo из-за таймаута");
            timeout_Hilo = 0;
        }
        
        // Проверка убытков и остановка по риску
        double profit_Hilo = CalculateProfit_Hilo();
        if(UseEquityStop && profit_Hilo < 0 && MathAbs(profit_Hilo) > TotalEquityRisk / 100.0 * AccountEquityHigh_Hilo())
        {
            CloseThisSymbolAll_Hilo();
            Print("Закрыты все позиции Hilo из-за достижения максимального риска");
        }
        
        // Подсчет открытых позиций
        int trades_count_Hilo = CountTrades_Hilo();
        
        // Проверка условий для торговли
        bool can_buy_Hilo = false;
        bool can_sell_Hilo = false;
        bool can_trade_Hilo = false;
        double last_buy_price_Hilo = 0;
        double last_sell_price_Hilo = 0;
        
        // Определение существующих позиций
        for(int i = PositionsTotal() - 1; i >= 0; i--)
        {
            if(!PositionSelectByTicket(PositionGetTicket(i))) continue;
            if(PositionGetSymbol(i) != _Symbol) continue;
            if(PositionGetInteger(POSITION_MAGIC) != MagicNumber_Hilo) continue;
            
            if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
            {
                can_buy_Hilo = true;
                can_sell_Hilo = false;
                break;
            }
            else if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL)
            {
                can_buy_Hilo = false;
                can_sell_Hilo = true;
                break;
            }
        }
        
        // Проверка условий добавления позиций
        if(trades_count_Hilo > 0 && trades_count_Hilo < MaxTrades_Hilo)
        {
            // RefreshRates();
            last_buy_price_Hilo = FindLastBuyPrice_Hilo();
            last_sell_price_Hilo = FindLastSellPrice_Hilo();
            
            if(can_buy_Hilo && last_buy_price_Hilo - current_ask >= PipStep * _Point) can_trade_Hilo = true;
            if(can_sell_Hilo && current_bid - last_sell_price_Hilo >= PipStep * _Point) can_trade_Hilo = true;
        }
        
        // Если нет открытых позиций, разрешаем торговлю
        if(trades_count_Hilo < 1)
        {
            can_buy_Hilo = false;
            can_sell_Hilo = false;
            can_trade_Hilo = true;
        }
        
        // Обработка торговых сигналов
        if(can_trade_Hilo)
        {
            // Получение значений MACD для анализа
            double macd_buffer[];
            double signal_buffer[];
            ArraySetAsSeries(macd_buffer, true);
            ArraySetAsSeries(signal_buffer, true);
            CopyBuffer(macd_handle, MAIN_LINE, 0, 2, macd_buffer);
            CopyBuffer(macd_handle, SIGNAL_LINE, 0, 2, signal_buffer);
            
            // Проверка сигнала MACD на покупку и учет мультитаймфреймового анализа
            if(macd_buffer[0] > signal_buffer[0] && macd_buffer[1] <= signal_buffer[1] && (macd_buy_signal || !UseMultiTimeframeAnalysis))
            {
                double lot_size = NormalizeDouble(lots * MathPow(LotExponent, trades_count_Hilo), lotdecimal);
                int ticket = OpenPendingOrder_Hilo(0, lot_size, current_ask, SlipPage, 0, StopLoss, TakeProfit, "HILO Buy-" + IntegerToString(trades_count_Hilo), MagicNumber_Hilo, 0, clrGreen);
                
                if(ticket > 0)
                {
                    timeout_Hilo = TimeCurrent() + 3600 * 48; // Таймаут 48 часов
                    can_trade_Hilo = false;
                }
            }
            
            // Проверка сигнала MACD на продажу и учет мультитаймфреймового анализа
            if(macd_buffer[0] < signal_buffer[0] && macd_buffer[1] >= signal_buffer[1] && (macd_sell_signal || !UseMultiTimeframeAnalysis))
            {
                double lot_size = NormalizeDouble(lots * MathPow(LotExponent, trades_count_Hilo), lotdecimal);
                int ticket = OpenPendingOrder_Hilo(1, lot_size, current_bid, SlipPage, 0, StopLoss, TakeProfit, "HILO Sell-" + IntegerToString(trades_count_Hilo), MagicNumber_Hilo, 0, clrRed);
                
                if(ticket > 0)
                {
                    timeout_Hilo = TimeCurrent() + 3600 * 48; // Таймаут 48 часов
                    can_trade_Hilo = false;
                }
            }
        }
    }
    
    // AF-Scalper (RSI) - Обработка стратегии
    if(ValidateSignal(2)) // 2 - AF-Scalper
    {
        double ld_152 = lots;
        
        // Обновление трейлинг-стопов
        if(UseTrailingStop) TrailingStop_15();
        
        // Проверка таймаута
        static datetime timeout_15 = 0;
        if(timeout_15 > 0 && TimeCurrent() >= timeout_15)
        {
            CloseThisSymbolAll_15();
            Print("Закрыты все позиции RSI из-за таймаута");
            timeout_15 = 0;
        }
        
        // Проверка убытков и остановка по риску
        double profit_15 = CalculateProfit_15();
        if(UseEquityStop && profit_15 < 0 && MathAbs(profit_15) > TotalEquityRisk / 100.0 * AccountEquityHigh_15())
        {
            CloseThisSymbolAll_15();
            Print("Закрыты все позиции RSI из-за достижения максимального риска");
        }
        
        // Подсчет открытых позиций
        int trades_count_15 = CountTrades_15();
        
        // Проверка условий для торговли
        bool can_buy_15 = false;
        bool can_sell_15 = false;
        bool can_trade_15 = false;
        double last_buy_price_15 = 0;
        double last_sell_price_15 = 0;
        
        // Определение существующих позиций
        for(int i = PositionsTotal() - 1; i >= 0; i--)
        {
            if(!PositionSelectByTicket(PositionGetTicket(i))) continue;
            if(PositionGetSymbol(i) != _Symbol) continue;
            if(PositionGetInteger(POSITION_MAGIC) != g_magic_176_15) continue;
            
            if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
            {
                can_buy_15 = true;
                can_sell_15 = false;
                break;
            }
            else if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL)
            {
                can_buy_15 = false;
                can_sell_15 = true;
                break;
            }
        }
        
        // Проверка условий добавления позиций
        if(trades_count_15 > 0 && trades_count_15 < MaxTrades_15)
        {
            // RefreshRates();
            last_buy_price_15 = FindLastBuyPrice_15();
            last_sell_price_15 = FindLastSellPrice_15();
            
            if(can_buy_15 && last_buy_price_15 - current_ask >= PipStep * _Point) can_trade_15 = true;
            if(can_sell_15 && current_bid - last_sell_price_15 >= PipStep * _Point) can_trade_15 = true;
        }
        
        // Если нет открытых позиций, разрешаем торговлю
        if(trades_count_15 < 1)
        {
            can_buy_15 = false;
            can_sell_15 = false;
            can_trade_15 = true;
        }
        
        // Проверка нового бара для таймфрейма H1
        static datetime last_bar_time_h1 = 0;
        datetime current_bar_time_h1 = iTime(_Symbol, PERIOD_H1, 0);
        
        if(current_bar_time_h1 != last_bar_time_h1)
        {
            // Получение значений RSI
            double rsi_buffer[];
            ArraySetAsSeries(rsi_buffer, true);
            CopyBuffer(rsi_handle, 0, 0, 3, rsi_buffer);
            
            // Если нет открытых позиций или общее количество меньше максимального
            if(trades_count_15 < MaxTrades_15)
            {
                // Сигнал на покупку: RSI ниже 30
                if(rsi_buffer[1] < 30)
                {
                    double lot_size = NormalizeDouble(lots * MathPow(LotExponent, trades_count_15), lotdecimal);
                    int ticket = OpenPendingOrder_15(0, lot_size, current_ask, SlipPage, 0, StopLoss, TakeProfit, "RSI Buy-" + IntegerToString(trades_count_15), g_magic_176_15, 0, clrGreen);
                    
                    if(ticket > 0)
                    {
                        timeout_15 = TimeCurrent() + 3600 * 48; // Таймаут 48 часов
                        can_trade_15 = false;
                    }
                }
                
                // Сигнал на продажу: RSI выше 70
                if(rsi_buffer[1] > 70)
                {
                    double lot_size = NormalizeDouble(lots * MathPow(LotExponent, trades_count_15), lotdecimal);
                    int ticket = OpenPendingOrder_15(1, lot_size, current_bid, SlipPage, 0, StopLoss, TakeProfit, "RSI Sell-" + IntegerToString(trades_count_15), g_magic_176_15, 0, clrRed);
                    
                    if(ticket > 0)
                    {
                        timeout_15 = TimeCurrent() + 3600 * 48; // Таймаут 48 часов
                        can_trade_15 = false;
                    }
                }
            }
            
            last_bar_time_h1 = current_bar_time_h1;
        }
    }
    
    // AF-TrendKiller (DayRange) - Обработка стратегии
    if(ValidateSignal(3)) // 3 - AF-TrendKiller
    {
        double ld_176 = lots;
        
        // Обновление трейлинг-стопов
        if(UseTrailingStop) TrailingStop_16();
        
        // Проверка таймаута
        static datetime timeout_16 = 0;
        if(timeout_16 > 0 && TimeCurrent() >= timeout_16)
        {
            CloseThisSymbolAll_16();
            Print("Закрыты все позиции TrendKiller из-за таймаута");
            timeout_16 = 0;
        }
        
        // Проверка убытков и остановка по риску
        double profit_16 = CalculateProfit_16();
        if(UseEquityStop && profit_16 < 0 && MathAbs(profit_16) > TotalEquityRisk / 100.0 * AccountEquityHigh_16())
        {
            CloseThisSymbolAll_16();
            Print("Закрыты все позиции TrendKiller из-за достижения максимального риска");
        }
        
        // Подсчет открытых позиций
        int trades_count_16 = CountTrades_16();
        
        // Проверка условий для торговли
        bool can_buy_16 = false;
        bool can_sell_16 = false;
        bool can_trade_16 = false;
        double last_buy_price_16 = 0;
        double last_sell_price_16 = 0;
        
        // Определение существующих позиций
        for(int i = PositionsTotal() - 1; i >= 0; i--)
        {
            if(!PositionSelectByTicket(PositionGetTicket(i))) continue;
            if(PositionGetSymbol(i) != _Symbol) continue;
            if(PositionGetInteger(POSITION_MAGIC) != g_magic_176_16) continue;
            
            if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
            {
                can_buy_16 = true;
                can_sell_16 = false;
                break;
            }
            else if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL)
            {
                can_buy_16 = false;
                can_sell_16 = true;
                break;
            }
        }
        
        // Проверка условий добавления позиций
        if(trades_count_16 > 0 && trades_count_16 < MaxTrades_16)
        {
            // RefreshRates();
            last_buy_price_16 = FindLastBuyPrice_16();
            last_sell_price_16 = FindLastSellPrice_16();
            
            if(can_buy_16 && last_buy_price_16 - current_ask >= PipStep * _Point) can_trade_16 = true;
            if(can_sell_16 && current_bid - last_sell_price_16 >= PipStep * _Point) can_trade_16 = true;
        }
        
        // Если нет открытых позиций, разрешаем торговлю
        if(trades_count_16 < 1)
        {
            can_buy_16 = false;
            can_sell_16 = false;
            can_trade_16 = true;
        }
        
        // Проверка нового бара для таймфрейма M1
        static datetime last_bar_time_m1 = 0;
        datetime current_bar_time_m1 = iTime(_Symbol, PERIOD_M1, 0);
        
        if(current_bar_time_m1 != last_bar_time_m1)
        {
            // Если нет открытых позиций или общее количество меньше максимального
            if(trades_count_16 < MaxTrades_16)
            {
                // Получаем цены последних двух свечей
                double close_prev = iClose(_Symbol, PERIOD_CURRENT, 2);
                double close_curr = iClose(_Symbol, PERIOD_CURRENT, 1);
                
                // Получаем дополнительное подтверждение от анализа дневного диапазона
                bool buy_range_signal = IsDayRangeSignal(0);
                bool sell_range_signal = IsDayRangeSignal(1);
                
                // Проверяем RSI для подтверждения
                double rsi_value = 0;
                double rsi_buffer[];
                ArraySetAsSeries(rsi_buffer, true);
                int rsi_h1_handle = iRSI(_Symbol, PERIOD_H1, 14, PRICE_CLOSE);
                if(rsi_h1_handle != INVALID_HANDLE)
                {
                    if(CopyBuffer(rsi_h1_handle, 0, 1, 1, rsi_buffer) > 0)
                    {
                        rsi_value = rsi_buffer[0];
                    }
                }
                
                // Сигнал на покупку
                if(close_prev > close_curr && rsi_value > 30.0 && buy_range_signal)
                {
                    double lot_size = NormalizeDouble(lots * MathPow(LotExponent, trades_count_16), lotdecimal);
                    int ticket = OpenPendingOrder_16(0, lot_size, current_ask, SlipPage, 0, StopLoss, TakeProfit, "RANGE Buy-" + IntegerToString(trades_count_16), g_magic_176_16, 0, clrGreen);
                    
                    if(ticket > 0)
                    {
                        timeout_16 = TimeCurrent() + 3600 * 48; // Таймаут 48 часов
                        can_trade_16 = false;
                    }
                }
                
                // Сигнал на продажу
                if(close_prev < close_curr && rsi_value < 70.0 && sell_range_signal)
                {
                    double lot_size = NormalizeDouble(lots * MathPow(LotExponent, trades_count_16), lotdecimal);
                    int ticket = OpenPendingOrder_16(1, lot_size, current_bid, SlipPage, 0, StopLoss, TakeProfit, "RANGE Sell-" + IntegerToString(trades_count_16), g_magic_176_16, 0, clrRed);
                    
                    if(ticket > 0)
                    {
                        timeout_16 = TimeCurrent() + 3600 * 48; // Таймаут 48 часов
                        can_trade_16 = false;
                    }
                }
            }
            
            last_bar_time_m1 = current_bar_time_m1;
        }
    }
    
    // Обновление мультитаймфреймовых сигналов
    UpdateMultiTimeframeSignals();
}

//+------------------------------------------------------------------+
//| Функция проверки нового бара                                      |
//+------------------------------------------------------------------+
bool IsNewBar()
{
   static datetime last_time = 0;
   datetime current_time = iTime(_Symbol, PERIOD_CURRENT, 0);
   if(last_time == current_time) return false;
   last_time = current_time;
   return true;
}

//+------------------------------------------------------------------+
//| Функция проверки разрешения торговли                              |
//+------------------------------------------------------------------+
bool IsTradeAllowed()
{
   if(!TerminalInfoInteger(TERMINAL_TRADE_ALLOWED)) return false;
   if(!MQLInfoInteger(MQL_TRADE_ALLOWED)) return false;
   return true;
}

//+------------------------------------------------------------------+
//| Функции открытия позиций                                          |
//+------------------------------------------------------------------+
void CheckForOpen_Hilo(double lots, double bid, double ask)
{
   // Проверка количества открытых позиций
   if(CountTrades_Hilo() >= MaxTrades_Hilo) return;
   
   // Получение значений индикаторов
   double macd_buffer[];
   double signal_buffer[];
   ArraySetAsSeries(macd_buffer, true);
   ArraySetAsSeries(signal_buffer, true);
   
   if(CopyBuffer(macd_handle, MAIN_LINE, 0, 2, macd_buffer) <= 0) return;
   if(CopyBuffer(macd_handle, SIGNAL_LINE, 0, 2, signal_buffer) <= 0) return;
   
   // Условия для покупки
   if(macd_buffer[0] > signal_buffer[0] && macd_buffer[1] <= signal_buffer[1])
   {
      double tp = ask + TakeProfit * _Point;
      double sl = ask - StopLoss * _Point;
      trade.Buy(lots, _Symbol, ask, sl, tp, "HILO Buy");
   }
   
   // Условия для продажи
   if(macd_buffer[0] < signal_buffer[0] && macd_buffer[1] >= signal_buffer[1])
   {
      double tp = bid - TakeProfit * _Point;
      double sl = bid + StopLoss * _Point;
      trade.Sell(lots, _Symbol, bid, sl, tp, "HILO Sell");
   }
}

void CheckForOpen_15(double lots, double bid, double ask)
{
   // Проверка количества открытых позиций
   if(CountTrades_15() >= MaxTrades_15) return;
   
   // Получение значений RSI
   double rsi_buffer[];
   ArraySetAsSeries(rsi_buffer, true);
   
   if(CopyBuffer(rsi_handle, 0, 0, 2, rsi_buffer) <= 0) return;
   
   // Условия для покупки
   if(rsi_buffer[0] < 30 && rsi_buffer[1] >= 30)
   {
      double tp = ask + TakeProfit * _Point;
      double sl = ask - StopLoss * _Point;
      trade.Buy(lots, _Symbol, ask, sl, tp, "RSI Buy");
   }
   
   // Условия для продажи
   if(rsi_buffer[0] > 70 && rsi_buffer[1] <= 70)
   {
      double tp = bid - TakeProfit * _Point;
      double sl = bid + StopLoss * _Point;
      trade.Sell(lots, _Symbol, bid, sl, tp, "RSI Sell");
   }
}

void CheckForOpen_16(double lots, double bid, double ask)
{
   // Проверка количества открытых позиций
   if(CountTrades_16() >= MaxTrades_16) return;
   
   // Получение значений индикаторов
   double ma_fast_buffer[];
   double ma_slow_buffer[];
   ArraySetAsSeries(ma_fast_buffer, true);
   ArraySetAsSeries(ma_slow_buffer, true);
   
   if(CopyBuffer(ma_handle_fast, 0, 0, 2, ma_fast_buffer) <= 0) return;
   if(CopyBuffer(ma_handle_slow, 0, 0, 2, ma_slow_buffer) <= 0) return;
   
   // Получаем дополнительное подтверждение от анализа дневного диапазона
   bool buy_range_signal = IsDayRangeSignal(0);
   bool sell_range_signal = IsDayRangeSignal(1);
   
   // Условия для покупки - добавляем проверку дневного диапазона
   if(ma_fast_buffer[0] > ma_slow_buffer[0] && ma_fast_buffer[1] <= ma_slow_buffer[1] && buy_range_signal)
   {
      double tp = ask + TakeProfit * _Point;
      double sl = ask - StopLoss * _Point;
      trade.Buy(lots, _Symbol, ask, sl, tp, "MA+Range Buy");
   }
   
   // Условия для продажи - добавляем проверку дневного диапазона
   if(ma_fast_buffer[0] < ma_slow_buffer[0] && ma_fast_buffer[1] >= ma_slow_buffer[1] && sell_range_signal)
   {
      double tp = bid - TakeProfit * _Point;
      double sl = bid + StopLoss * _Point;
      trade.Sell(lots, _Symbol, bid, sl, tp, "MA+Range Sell");
   }
}

//+------------------------------------------------------------------+
//| Функции закрытия позиций                                          |
//+------------------------------------------------------------------+
void CheckForClose_Hilo()
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(PositionGetSymbol(i) != _Symbol) continue;
      if(PositionGetInteger(POSITION_MAGIC) != MagicNumber_Hilo) continue;
      
      // Получение значений индикаторов
      double macd_buffer[];
      double signal_buffer[];
      ArraySetAsSeries(macd_buffer, true);
      ArraySetAsSeries(signal_buffer, true);
      
      if(CopyBuffer(macd_handle, MAIN_LINE, 0, 2, macd_buffer) <= 0) continue;
      if(CopyBuffer(macd_handle, SIGNAL_LINE, 0, 2, signal_buffer) <= 0) continue;
      
      // Проверка условий закрытия
      if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
      {
         if(macd_buffer[0] < signal_buffer[0] && macd_buffer[1] >= signal_buffer[1])
         {
            trade.PositionClose(PositionGetTicket(i));
         }
      }
      else
      {
         if(macd_buffer[0] > signal_buffer[0] && macd_buffer[1] <= signal_buffer[1])
         {
            trade.PositionClose(PositionGetTicket(i));
         }
      }
      
      // Проверка трейлинг-стопа
      if(UseTrailingStop)
      {
         double profit = PositionGetDouble(POSITION_PROFIT);
         if(profit > 0)
         {
            double sl = PositionGetDouble(POSITION_SL);
            double tp = PositionGetDouble(POSITION_TP);
            double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
            double current_price = PositionGetDouble(POSITION_PRICE_CURRENT);
            
            if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
            {
               if(current_price - open_price > TrailStart * _Point)
               {
                  double new_sl = NormalizeDouble(current_price - TrailStop * _Point, _Digits);
                  if(new_sl > sl) trade.PositionModify(PositionGetTicket(i), new_sl, tp);
               }
            }
            else
            {
               if(open_price - current_price > TrailStart * _Point)
               {
                  double new_sl = NormalizeDouble(current_price + TrailStop * _Point, _Digits);
                  if(new_sl < sl || sl == 0) trade.PositionModify(PositionGetTicket(i), new_sl, tp);
               }
            }
         }
      }
   }
}

void CheckForClose_15()
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(PositionGetSymbol(i) != _Symbol) continue;
      if(PositionGetInteger(POSITION_MAGIC) != g_magic_176_15) continue;
      
      // Получение значений RSI
      double rsi_buffer[];
      ArraySetAsSeries(rsi_buffer, true);
      
      if(CopyBuffer(rsi_handle, 0, 0, 2, rsi_buffer) <= 0) continue;
      
      // Проверка условий закрытия
      if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY && rsi_buffer[0] > 70)
      {
         trade.PositionClose(PositionGetTicket(i));
      }
      else if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL && rsi_buffer[0] < 30)
      {
         trade.PositionClose(PositionGetTicket(i));
      }
      
      // Проверка трейлинг-стопа
      if(UseTrailingStop)
      {
         double profit = PositionGetDouble(POSITION_PROFIT);
         if(profit > 0)
         {
            double sl = PositionGetDouble(POSITION_SL);
            double tp = PositionGetDouble(POSITION_TP);
            double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
            double current_price = PositionGetDouble(POSITION_PRICE_CURRENT);
            
            if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
            {
               if(current_price - open_price > TrailStart * _Point)
               {
                  double new_sl = NormalizeDouble(current_price - TrailStop * _Point, _Digits);
                  if(new_sl > sl) trade.PositionModify(PositionGetTicket(i), new_sl, tp);
               }
            }
            else
            {
               if(open_price - current_price > TrailStart * _Point)
               {
                  double new_sl = NormalizeDouble(current_price + TrailStop * _Point, _Digits);
                  if(new_sl < sl || sl == 0) trade.PositionModify(PositionGetTicket(i), new_sl, tp);
               }
            }
         }
      }
   }
}

void CheckForClose_16()
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(PositionGetSymbol(i) != _Symbol) continue;
      if(PositionGetInteger(POSITION_MAGIC) != g_magic_176_16) continue;
      
      // Получение значений индикаторов
      double ma_fast_buffer[];
      double ma_slow_buffer[];
      ArraySetAsSeries(ma_fast_buffer, true);
      ArraySetAsSeries(ma_slow_buffer, true);
      
      if(CopyBuffer(ma_handle_fast, 0, 0, 2, ma_fast_buffer) <= 0) continue;
      if(CopyBuffer(ma_handle_slow, 0, 0, 2, ma_slow_buffer) <= 0) continue;
      
      // Проверка условий закрытия
      if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY && ma_fast_buffer[0] < ma_slow_buffer[0])
      {
         trade.PositionClose(PositionGetTicket(i));
      }
      else if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL && ma_fast_buffer[0] > ma_slow_buffer[0])
      {
         trade.PositionClose(PositionGetTicket(i));
      }
      
      // Проверка трейлинг-стопа
      if(UseTrailingStop)
      {
         double profit = PositionGetDouble(POSITION_PROFIT);
         if(profit > 0)
         {
            double sl = PositionGetDouble(POSITION_SL);
            double tp = PositionGetDouble(POSITION_TP);
            double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
            double current_price = PositionGetDouble(POSITION_PRICE_CURRENT);
            
            if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
            {
               if(current_price - open_price > TrailStart * _Point)
               {
                  double new_sl = NormalizeDouble(current_price - TrailStop * _Point, _Digits);
                  if(new_sl > sl) trade.PositionModify(PositionGetTicket(i), new_sl, tp);
               }
            }
            else
            {
               if(open_price - current_price > TrailStart * _Point)
               {
                  double new_sl = NormalizeDouble(current_price + TrailStop * _Point, _Digits);
                  if(new_sl < sl || sl == 0) trade.PositionModify(PositionGetTicket(i), new_sl, tp);
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Функции подсчета позиций                                          |
//+------------------------------------------------------------------+
int CountTrades_Hilo()
{
   int count = 0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) != _Symbol) continue;
      if(PositionGetInteger(POSITION_MAGIC) != MagicNumber_Hilo) continue;
      count++;
   }
   return count;
}

int CountTrades_15()
{
   int count = 0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) != _Symbol) continue;
      if(PositionGetInteger(POSITION_MAGIC) != g_magic_176_15) continue;
      count++;
   }
   return count;
}

int CountTrades_16()
{
   int count = 0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) != _Symbol) continue;
      if(PositionGetInteger(POSITION_MAGIC) != g_magic_176_16) continue;
      count++;
   }
   return count;
}

//+------------------------------------------------------------------+
//| Функции трейлинг-стопа                                           |
//+------------------------------------------------------------------+
void TrailingStop_Hilo()
{
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) != _Symbol) continue;
      if(PositionGetInteger(POSITION_MAGIC) != MagicNumber_Hilo) continue;
      
      double sl = PositionGetDouble(POSITION_SL);
      double tp = PositionGetDouble(POSITION_TP);
      double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
      double current_price = PositionGetDouble(POSITION_PRICE_CURRENT);
      
      if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
      {
         if(current_price - open_price > TrailStart * _Point)
         {
            double new_sl = NormalizeDouble(current_price - TrailStop * _Point, _Digits);
            if(new_sl > sl) trade.PositionModify(PositionGetTicket(i), new_sl, tp);
         }
      }
      else
      {
         if(open_price - current_price > TrailStart * _Point)
         {
            double new_sl = NormalizeDouble(current_price + TrailStop * _Point, _Digits);
            if(new_sl < sl || sl == 0) trade.PositionModify(PositionGetTicket(i), new_sl, tp);
         }
      }
   }
}

void TrailingStop_15()
{
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) != _Symbol) continue;
      if(PositionGetInteger(POSITION_MAGIC) != g_magic_176_15) continue;
      
      double sl = PositionGetDouble(POSITION_SL);
      double tp = PositionGetDouble(POSITION_TP);
      double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
      double current_price = PositionGetDouble(POSITION_PRICE_CURRENT);
      
      if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
      {
         if(current_price - open_price > TrailStart * _Point)
         {
            double new_sl = NormalizeDouble(current_price - TrailStop * _Point, _Digits);
            if(new_sl > sl) trade.PositionModify(PositionGetTicket(i), new_sl, tp);
         }
      }
      else
      {
         if(open_price - current_price > TrailStart * _Point)
         {
            double new_sl = NormalizeDouble(current_price + TrailStop * _Point, _Digits);
            if(new_sl < sl || sl == 0) trade.PositionModify(PositionGetTicket(i), new_sl, tp);
         }
      }
   }
}

void TrailingStop_16()
{
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) != _Symbol) continue;
      if(PositionGetInteger(POSITION_MAGIC) != g_magic_176_16) continue;
      
      double sl = PositionGetDouble(POSITION_SL);
      double tp = PositionGetDouble(POSITION_TP);
      double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
      double current_price = PositionGetDouble(POSITION_PRICE_CURRENT);
      
      if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
      {
         if(current_price - open_price > TrailStart * _Point)
         {
            double new_sl = NormalizeDouble(current_price - TrailStop * _Point, _Digits);
            if(new_sl > sl) trade.PositionModify(PositionGetTicket(i), new_sl, tp);
         }
      }
      else
      {
         if(open_price - current_price > TrailStart * _Point)
         {
            double new_sl = NormalizeDouble(current_price + TrailStop * _Point, _Digits);
            if(new_sl < sl || sl == 0) trade.PositionModify(PositionGetTicket(i), new_sl, tp);
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Функции для работы с дневными диапазонами движения цены          |
//+------------------------------------------------------------------+

// Расчет среднего дневного диапазона для AF-TrendKiller
double CalculateDailyRange()
{
   int range_day = 0;
   int range_5days = 0;
   int range_10days = 0;
   int range_20days = 0;
   int average_range = 0;
   int i;
   
   // Расчет диапазона за последний день
   range_day = (int)((iHigh(_Symbol, PERIOD_D1, 1) - iLow(_Symbol, PERIOD_D1, 1)) / _Point);
   
   // Расчет среднего диапазона за 5 дней
   for(i = 1; i <= 5; i++) 
      range_5days += (int)((iHigh(_Symbol, PERIOD_D1, i) - iLow(_Symbol, PERIOD_D1, i)) / _Point);
   range_5days /= 5;
   
   // Расчет среднего диапазона за 10 дней
   for(i = 1; i <= 10; i++) 
      range_10days += (int)((iHigh(_Symbol, PERIOD_D1, i) - iLow(_Symbol, PERIOD_D1, i)) / _Point);
   range_10days /= 10;
   
   // Расчет среднего диапазона за 20 дней
   for(i = 1; i <= 20; i++) 
      range_20days += (int)((iHigh(_Symbol, PERIOD_D1, i) - iLow(_Symbol, PERIOD_D1, i)) / _Point);
   range_20days /= 20;
   
   // Расчет общего среднего диапазона
   average_range = (range_day + range_5days + range_10days + range_20days) / 4;
   
   return average_range;
}

// Проверка текущего дневного диапазона для принятия торговых решений
bool IsDayRangeSignal(int direction)
{
   double daily_open = iOpen(_Symbol, PERIOD_D1, 0);
   double daily_close = iClose(_Symbol, PERIOD_D1, 0);
   double daily_high = iHigh(_Symbol, PERIOD_D1, 0);
   double daily_low = iLow(_Symbol, PERIOD_D1, 0);
   double daily_range = (daily_high - daily_low) / _Point;
   double avg_range = CalculateDailyRange();
   double current_range = (daily_high - daily_low) / _Point;
   
   // Проверка для сигнала на покупку (направление 0)
   if(direction == 0) {
      // Если текущий диапазон меньше среднего и цена закрытия выше цены открытия
      if(current_range < avg_range && daily_close > daily_open)
         return true;
   }
   // Проверка для сигнала на продажу (направление 1)
   else if(direction == 1) {
      // Если текущий диапазон меньше среднего и цена закрытия ниже цены открытия
      if(current_range < avg_range && daily_close < daily_open)
         return true;
   }
   
   return false;
}

//+------------------------------------------------------------------+
//| Функции закрытия всех позиций                                      |
//+------------------------------------------------------------------+
void CloseThisSymbolAll_Hilo()
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(PositionGetSymbol(i) != _Symbol) continue;
      if(PositionGetInteger(POSITION_MAGIC) != MagicNumber_Hilo) continue;
      
      trade.PositionClose(PositionGetTicket(i));
      Sleep(1000);
   }
}

void CloseThisSymbolAll_15()
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(PositionGetSymbol(i) != _Symbol) continue;
      if(PositionGetInteger(POSITION_MAGIC) != g_magic_176_15) continue;
      
      trade.PositionClose(PositionGetTicket(i));
      Sleep(1000);
   }
}

void CloseThisSymbolAll_16()
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(PositionGetSymbol(i) != _Symbol) continue;
      if(PositionGetInteger(POSITION_MAGIC) != g_magic_176_16) continue;
      
      trade.PositionClose(PositionGetTicket(i));
      Sleep(1000);
   }
}

//+------------------------------------------------------------------+
//| Функции поиска цен последних ордеров                              |
//+------------------------------------------------------------------+
double FindLastBuyPrice_Hilo()
{
   double price = 0;
   datetime max_time = 0;
   
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(!PositionSelectByTicket(PositionGetTicket(i))) continue;
      if(PositionGetSymbol(i) != _Symbol) continue;
      if(PositionGetInteger(POSITION_MAGIC) != MagicNumber_Hilo) continue;
      if(PositionGetInteger(POSITION_TYPE) != POSITION_TYPE_BUY) continue;
      
      datetime open_time = (datetime)PositionGetInteger(POSITION_TIME);
      if(open_time > max_time)
      {
         max_time = open_time;
         price = PositionGetDouble(POSITION_PRICE_OPEN);
      }
   }
   
   return price;
}

double FindLastSellPrice_Hilo()
{
   double price = 0;
   datetime max_time = 0;
   
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(!PositionSelectByTicket(PositionGetTicket(i))) continue;
      if(PositionGetSymbol(i) != _Symbol) continue;
      if(PositionGetInteger(POSITION_MAGIC) != MagicNumber_Hilo) continue;
      if(PositionGetInteger(POSITION_TYPE) != POSITION_TYPE_SELL) continue;
      
      datetime open_time = (datetime)PositionGetInteger(POSITION_TIME);
      if(open_time > max_time)
      {
         max_time = open_time;
         price = PositionGetDouble(POSITION_PRICE_OPEN);
      }
   }
   
   return price;
}

double FindLastBuyPrice_15()
{
   double price = 0;
   datetime max_time = 0;
   
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(!PositionSelectByTicket(PositionGetTicket(i))) continue;
      if(PositionGetSymbol(i) != _Symbol) continue;
      if(PositionGetInteger(POSITION_MAGIC) != g_magic_176_15) continue;
      if(PositionGetInteger(POSITION_TYPE) != POSITION_TYPE_BUY) continue;
      
      datetime open_time = (datetime)PositionGetInteger(POSITION_TIME);
      if(open_time > max_time)
      {
         max_time = open_time;
         price = PositionGetDouble(POSITION_PRICE_OPEN);
      }
   }
   
   return price;
}

double FindLastSellPrice_15()
{
   double price = 0;
   datetime max_time = 0;
   
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(!PositionSelectByTicket(PositionGetTicket(i))) continue;
      if(PositionGetSymbol(i) != _Symbol) continue;
      if(PositionGetInteger(POSITION_MAGIC) != g_magic_176_15) continue;
      if(PositionGetInteger(POSITION_TYPE) != POSITION_TYPE_SELL) continue;
      
      datetime open_time = (datetime)PositionGetInteger(POSITION_TIME);
      if(open_time > max_time)
      {
         max_time = open_time;
         price = PositionGetDouble(POSITION_PRICE_OPEN);
      }
   }
   
   return price;
}

double FindLastBuyPrice_16()
{
   double price = 0;
   datetime max_time = 0;
   
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(!PositionSelectByTicket(PositionGetTicket(i))) continue;
      if(PositionGetSymbol(i) != _Symbol) continue;
      if(PositionGetInteger(POSITION_MAGIC) != g_magic_176_16) continue;
      if(PositionGetInteger(POSITION_TYPE) != POSITION_TYPE_BUY) continue;
      
      datetime open_time = (datetime)PositionGetInteger(POSITION_TIME);
      if(open_time > max_time)
      {
         max_time = open_time;
         price = PositionGetDouble(POSITION_PRICE_OPEN);
      }
   }
   
   return price;
}

double FindLastSellPrice_16()
{
   double price = 0;
   datetime max_time = 0;
   
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(!PositionSelectByTicket(PositionGetTicket(i))) continue;
      if(PositionGetSymbol(i) != _Symbol) continue;
      if(PositionGetInteger(POSITION_MAGIC) != g_magic_176_16) continue;
      if(PositionGetInteger(POSITION_TYPE) != POSITION_TYPE_SELL) continue;
      
      datetime open_time = (datetime)PositionGetInteger(POSITION_TIME);
      if(open_time > max_time)
      {
         max_time = open_time;
         price = PositionGetDouble(POSITION_PRICE_OPEN);
      }
   }
   
   return price;
}

//+------------------------------------------------------------------+
//| Функции расчета прибыли                                           |
//+------------------------------------------------------------------+
double CalculateProfit_Hilo()
{
   double profit = 0;
   
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(!PositionSelectByTicket(PositionGetTicket(i))) continue;
      if(PositionGetSymbol(i) != _Symbol) continue;
      if(PositionGetInteger(POSITION_MAGIC) != MagicNumber_Hilo) continue;
      
      profit += PositionGetDouble(POSITION_PROFIT);
   }
   
   return profit;
}

double CalculateProfit_15()
{
   double profit = 0;
   
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(!PositionSelectByTicket(PositionGetTicket(i))) continue;
      if(PositionGetSymbol(i) != _Symbol) continue;
      if(PositionGetInteger(POSITION_MAGIC) != g_magic_176_15) continue;
      
      profit += PositionGetDouble(POSITION_PROFIT);
   }
   
   return profit;
}

double CalculateProfit_16()
{
   double profit = 0;
   
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(!PositionSelectByTicket(PositionGetTicket(i))) continue;
      if(PositionGetSymbol(i) != _Symbol) continue;
      if(PositionGetInteger(POSITION_MAGIC) != g_magic_176_16) continue;
      
      profit += PositionGetDouble(POSITION_PROFIT);
   }
   
   return profit;
}

//+------------------------------------------------------------------+
//| Функции учета максимума средств                                  |
//+------------------------------------------------------------------+
// Глобальные переменные для хранения максимумов средств
double equity_high_Hilo = 0;
double equity_high_15 = 0;
double equity_high_16 = 0;

double AccountEquityHigh_Hilo()
{
   static double max_equity = 0;
   static double current_equity = 0;
   
   if(CountTrades_Hilo() == 0) current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
   
   if(current_equity < max_equity) current_equity = max_equity;
   else current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
   
   max_equity = AccountInfoDouble(ACCOUNT_EQUITY);
   
   return current_equity;
}

double AccountEquityHigh_15()
{
   static double max_equity = 0;
   static double current_equity = 0;
   
   if(CountTrades_15() == 0) current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
   
   if(current_equity < max_equity) current_equity = max_equity;
   else current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
   
   max_equity = AccountInfoDouble(ACCOUNT_EQUITY);
   
   return current_equity;
}

double AccountEquityHigh_16()
{
   static double max_equity = 0;
   static double current_equity = 0;
   
   if(CountTrades_16() == 0) current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
   
   if(current_equity < max_equity) current_equity = max_equity;
   else current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
   
   max_equity = AccountInfoDouble(ACCOUNT_EQUITY);
   
   return current_equity;
}

//+------------------------------------------------------------------+
//| Функции для открытия отложенных ордеров                           |
//+------------------------------------------------------------------+
int OpenPendingOrder_Hilo(int type, double lots, double price, double slippage, double price2, double stoploss, double takeprofit, string comment, int magic, int expiration, color arrow_color)
{
   int ticket = 0;
   
   // Установка параметров для ордера
   trade.SetExpertMagicNumber(magic);
   trade.SetDeviationInPoints((int)slippage);
   
   // Расчет стоп-лосса и тейк-профита
   double sl = 0;
   double tp = 0;
   
   if(type == 0) // BUY
   {
      sl = (stoploss > 0) ? NormalizeDouble(price - stoploss * _Point, _Digits) : 0;
      tp = (takeprofit > 0) ? NormalizeDouble(price + takeprofit * _Point, _Digits) : 0;
      
      if(trade.Buy(lots, _Symbol, price, sl, tp, comment))
         ticket = (int)trade.ResultOrder();
   }
   else if(type == 1) // SELL
   {
      sl = (stoploss > 0) ? NormalizeDouble(price + stoploss * _Point, _Digits) : 0;
      tp = (takeprofit > 0) ? NormalizeDouble(price - takeprofit * _Point, _Digits) : 0;
      
      if(trade.Sell(lots, _Symbol, price, sl, tp, comment))
         ticket = (int)trade.ResultOrder();
   }
   
   return ticket;
}

int OpenPendingOrder_15(int type, double lots, double price, double slippage, double price2, double stoploss, double takeprofit, string comment, int magic, int expiration, color arrow_color)
{
   int ticket = 0;
   
   // Установка параметров для ордера
   trade.SetExpertMagicNumber(magic);
   trade.SetDeviationInPoints((int)slippage);
   
   // Расчет стоп-лосса и тейк-профита
   double sl = 0;
   double tp = 0;
   
   if(type == 0) // BUY
   {
      sl = (stoploss > 0) ? NormalizeDouble(price - stoploss * _Point, _Digits) : 0;
      tp = (takeprofit > 0) ? NormalizeDouble(price + takeprofit * _Point, _Digits) : 0;
      
      if(trade.Buy(lots, _Symbol, price, sl, tp, comment))
         ticket = (int)trade.ResultOrder();
   }
   else if(type == 1) // SELL
   {
      sl = (stoploss > 0) ? NormalizeDouble(price + stoploss * _Point, _Digits) : 0;
      tp = (takeprofit > 0) ? NormalizeDouble(price - takeprofit * _Point, _Digits) : 0;
      
      if(trade.Sell(lots, _Symbol, price, sl, tp, comment))
         ticket = (int)trade.ResultOrder();
   }
   
   return ticket;
}

int OpenPendingOrder_16(int type, double lots, double price, double slippage, double price2, double stoploss, double takeprofit, string comment, int magic, int expiration, color arrow_color)
{
   int ticket = 0;
   
   // Установка параметров для ордера
   trade.SetExpertMagicNumber(magic);
   trade.SetDeviationInPoints((int)slippage);
   
   // Расчет стоп-лосса и тейк-профита
   double sl = 0;
   double tp = 0;
   
   if(type == 0) // BUY
   {
      sl = (stoploss > 0) ? NormalizeDouble(price - stoploss * _Point, _Digits) : 0;
      tp = (takeprofit > 0) ? NormalizeDouble(price + takeprofit * _Point, _Digits) : 0;
      
      if(trade.Buy(lots, _Symbol, price, sl, tp, comment))
         ticket = (int)trade.ResultOrder();
   }
   else if(type == 1) // SELL
   {
      sl = (stoploss > 0) ? NormalizeDouble(price + stoploss * _Point, _Digits) : 0;
      tp = (takeprofit > 0) ? NormalizeDouble(price - takeprofit * _Point, _Digits) : 0;
      
      if(trade.Sell(lots, _Symbol, price, sl, tp, comment))
         ticket = (int)trade.ResultOrder();
   }
   
   return ticket;
}

//+------------------------------------------------------------------+
//| Функции для расчета стоп-лоссов и тейк-профитов                   |
//+------------------------------------------------------------------+
double StopLong_Hilo(double price, int pips)
{
   if(pips == 0) return 0;
   return NormalizeDouble(price - pips * _Point, _Digits);
}

double StopShort_Hilo(double price, int pips)
{
   if(pips == 0) return 0;
   return NormalizeDouble(price + pips * _Point, _Digits);
}

double TakeLong_Hilo(double price, int pips)
{
   if(pips == 0) return 0;
   return NormalizeDouble(price + pips * _Point, _Digits);
}

double TakeShort_Hilo(double price, int pips)
{
   if(pips == 0) return 0;
   return NormalizeDouble(price - pips * _Point, _Digits);
}

double StopLong_15(double price, int pips)
{
   if(pips == 0) return 0;
   return NormalizeDouble(price - pips * _Point, _Digits);
}

double StopShort_15(double price, int pips)
{
   if(pips == 0) return 0;
   return NormalizeDouble(price + pips * _Point, _Digits);
}

double TakeLong_15(double price, int pips)
{
   if(pips == 0) return 0;
   return NormalizeDouble(price + pips * _Point, _Digits);
}

double TakeShort_15(double price, int pips)
{
   if(pips == 0) return 0;
   return NormalizeDouble(price - pips * _Point, _Digits);
}

double StopLong_16(double price, int pips)
{
   if(pips == 0) return 0;
   return NormalizeDouble(price - pips * _Point, _Digits);
}

double StopShort_16(double price, int pips)
{
   if(pips == 0) return 0;
   return NormalizeDouble(price + pips * _Point, _Digits);
}

double TakeLong_16(double price, int pips)
{
   if(pips == 0) return 0;
   return NormalizeDouble(price + pips * _Point, _Digits);
}

double TakeShort_16(double price, int pips)
{
   if(pips == 0) return 0;
   return NormalizeDouble(price - pips * _Point, _Digits);
}

//+------------------------------------------------------------------+
//| Проверка сигнала на покупку по MACD на разных таймфреймах         |
//+------------------------------------------------------------------+
bool CheckMACDBuySignal()
{
   // Массивы для хранения значений MACD
   double macd_main_m1[], macd_signal_m1[];
   double macd_main_m5[], macd_signal_m5[];
   double macd_main_m15[], macd_signal_m15[];
   double macd_main_m30[], macd_signal_m30[];
   double macd_main_h1[], macd_signal_h1[];
   double macd_main_h4[], macd_signal_h4[];
   double macd_main_d1[], macd_signal_d1[];
   
   // Настройка массивов как серии
   ArraySetAsSeries(macd_main_m1, true);
   ArraySetAsSeries(macd_signal_m1, true);
   ArraySetAsSeries(macd_main_m5, true);
   ArraySetAsSeries(macd_signal_m5, true);
   ArraySetAsSeries(macd_main_m15, true);
   ArraySetAsSeries(macd_signal_m15, true);
   ArraySetAsSeries(macd_main_m30, true);
   ArraySetAsSeries(macd_signal_m30, true);
   ArraySetAsSeries(macd_main_h1, true);
   ArraySetAsSeries(macd_signal_h1, true);
   ArraySetAsSeries(macd_main_h4, true);
   ArraySetAsSeries(macd_signal_h4, true);
   ArraySetAsSeries(macd_main_d1, true);
   ArraySetAsSeries(macd_signal_d1, true);
   
   // Копирование данных для каждого таймфрейма
   CopyBuffer(macd_handle_m1, MAIN_LINE, 0, 2, macd_main_m1);
   CopyBuffer(macd_handle_m1, SIGNAL_LINE, 0, 2, macd_signal_m1);
   CopyBuffer(macd_handle_m5, MAIN_LINE, 0, 2, macd_main_m5);
   CopyBuffer(macd_handle_m5, SIGNAL_LINE, 0, 2, macd_signal_m5);
   CopyBuffer(macd_handle_m15, MAIN_LINE, 0, 2, macd_main_m15);
   CopyBuffer(macd_handle_m15, SIGNAL_LINE, 0, 2, macd_signal_m15);
   CopyBuffer(macd_handle_m30, MAIN_LINE, 0, 2, macd_main_m30);
   CopyBuffer(macd_handle_m30, SIGNAL_LINE, 0, 2, macd_signal_m30);
   CopyBuffer(macd_handle_h1, MAIN_LINE, 0, 2, macd_main_h1);
   CopyBuffer(macd_handle_h1, SIGNAL_LINE, 0, 2, macd_signal_h1);
   CopyBuffer(macd_handle_h4, MAIN_LINE, 0, 2, macd_main_h4);
   CopyBuffer(macd_handle_h4, SIGNAL_LINE, 0, 2, macd_signal_h4);
   CopyBuffer(macd_handle_d1, MAIN_LINE, 0, 2, macd_main_d1);
   CopyBuffer(macd_handle_d1, SIGNAL_LINE, 0, 2, macd_signal_d1);
   
   // Подсчет положительных сигналов и общего количества используемых таймфреймов
   int signal_count = 0;
   int total_timeframes = 0;
   
   // MACD M1 - пересечение сигнальной линии снизу вверх
   if(UseM1Timeframe)
   {
      total_timeframes++;
      if(macd_main_m1[0] > macd_signal_m1[0] && macd_main_m1[1] <= macd_signal_m1[1]) signal_count++;
   }
   
   // MACD M5 - пересечение сигнальной линии снизу вверх
   if(UseM5Timeframe)
   {
      total_timeframes++;
      if(macd_main_m5[0] > macd_signal_m5[0] && macd_main_m5[1] <= macd_signal_m5[1]) signal_count++;
   }
   
   // MACD M15 - пересечение сигнальной линии снизу вверх
   if(UseM15Timeframe)
   {
      total_timeframes++;
      if(macd_main_m15[0] > macd_signal_m15[0] && macd_main_m15[1] <= macd_signal_m15[1]) signal_count++;
   }
   
   // MACD M30 - пересечение сигнальной линии снизу вверх
   if(UseM30Timeframe)
   {
      total_timeframes++;
      if(macd_main_m30[0] > macd_signal_m30[0] && macd_main_m30[1] <= macd_signal_m30[1]) signal_count++;
   }
   
   // MACD H1 - пересечение сигнальной линии снизу вверх
   if(UseH1Timeframe)
   {
      total_timeframes++;
      if(macd_main_h1[0] > macd_signal_h1[0] && macd_main_h1[1] <= macd_signal_h1[1]) signal_count++;
   }
   
   // MACD H4 - пересечение сигнальной линии снизу вверх
   if(UseH4Timeframe)
   {
      total_timeframes++;
      if(macd_main_h4[0] > macd_signal_h4[0] && macd_main_h4[1] <= macd_signal_h4[1]) signal_count++;
   }
   
   // MACD D1 - пересечение сигнальной линии снизу вверх
   if(UseD1Timeframe)
   {
      total_timeframes++;
      if(macd_main_d1[0] > macd_signal_d1[0] && macd_main_d1[1] <= macd_signal_d1[1]) signal_count++;
   }
   
   // Возвращаем true, если более половины используемых таймфреймов дают сигнал на покупку
   if(total_timeframes == 0) return false; // На случай, если ни один таймфрейм не выбран
   
   return signal_count >= (total_timeframes / 2);
}

//+------------------------------------------------------------------+
//| Проверка сигнала на продажу по MACD на разных таймфреймах         |
//+------------------------------------------------------------------+
bool CheckMACDSellSignal()
{
   // Массивы для хранения значений MACD
   double macd_main_m1[], macd_signal_m1[];
   double macd_main_m5[], macd_signal_m5[];
   double macd_main_m15[], macd_signal_m15[];
   double macd_main_m30[], macd_signal_m30[];
   double macd_main_h1[], macd_signal_h1[];
   double macd_main_h4[], macd_signal_h4[];
   double macd_main_d1[], macd_signal_d1[];
   
   // Настройка массивов как серии
   ArraySetAsSeries(macd_main_m1, true);
   ArraySetAsSeries(macd_signal_m1, true);
   ArraySetAsSeries(macd_main_m5, true);
   ArraySetAsSeries(macd_signal_m5, true);
   ArraySetAsSeries(macd_main_m15, true);
   ArraySetAsSeries(macd_signal_m15, true);
   ArraySetAsSeries(macd_main_m30, true);
   ArraySetAsSeries(macd_signal_m30, true);
   ArraySetAsSeries(macd_main_h1, true);
   ArraySetAsSeries(macd_signal_h1, true);
   ArraySetAsSeries(macd_main_h4, true);
   ArraySetAsSeries(macd_signal_h4, true);
   ArraySetAsSeries(macd_main_d1, true);
   ArraySetAsSeries(macd_signal_d1, true);
   
   // Копирование данных для каждого таймфрейма
   CopyBuffer(macd_handle_m1, MAIN_LINE, 0, 2, macd_main_m1);
   CopyBuffer(macd_handle_m1, SIGNAL_LINE, 0, 2, macd_signal_m1);
   CopyBuffer(macd_handle_m5, MAIN_LINE, 0, 2, macd_main_m5);
   CopyBuffer(macd_handle_m5, SIGNAL_LINE, 0, 2, macd_signal_m5);
   CopyBuffer(macd_handle_m15, MAIN_LINE, 0, 2, macd_main_m15);
   CopyBuffer(macd_handle_m15, SIGNAL_LINE, 0, 2, macd_signal_m15);
   CopyBuffer(macd_handle_m30, MAIN_LINE, 0, 2, macd_main_m30);
   CopyBuffer(macd_handle_m30, SIGNAL_LINE, 0, 2, macd_signal_m30);
   CopyBuffer(macd_handle_h1, MAIN_LINE, 0, 2, macd_main_h1);
   CopyBuffer(macd_handle_h1, SIGNAL_LINE, 0, 2, macd_signal_h1);
   CopyBuffer(macd_handle_h4, MAIN_LINE, 0, 2, macd_main_h4);
   CopyBuffer(macd_handle_h4, SIGNAL_LINE, 0, 2, macd_signal_h4);
   CopyBuffer(macd_handle_d1, MAIN_LINE, 0, 2, macd_main_d1);
   CopyBuffer(macd_handle_d1, SIGNAL_LINE, 0, 2, macd_signal_d1);
   
   // Подсчет отрицательных сигналов
   int signal_count = 0;
   
   // MACD M1 - пересечение сигнальной линии сверху вниз
   if(macd_main_m1[0] < macd_signal_m1[0] && macd_main_m1[1] >= macd_signal_m1[1]) signal_count++;
   
   // MACD M5 - пересечение сигнальной линии сверху вниз
   if(macd_main_m5[0] < macd_signal_m5[0] && macd_main_m5[1] >= macd_signal_m5[1]) signal_count++;
   
   // MACD M15 - пересечение сигнальной линии сверху вниз
   if(macd_main_m15[0] < macd_signal_m15[0] && macd_main_m15[1] >= macd_signal_m15[1]) signal_count++;
   
   // MACD M30 - пересечение сигнальной линии сверху вниз
   if(macd_main_m30[0] < macd_signal_m30[0] && macd_main_m30[1] >= macd_signal_m30[1]) signal_count++;
   
   // MACD H1 - пересечение сигнальной линии сверху вниз
   if(macd_main_h1[0] < macd_signal_h1[0] && macd_main_h1[1] >= macd_signal_h1[1]) signal_count++;
   
   // MACD H4 - пересечение сигнальной линии сверху вниз
   if(macd_main_h4[0] < macd_signal_h4[0] && macd_main_h4[1] >= macd_signal_h4[1]) signal_count++;
   
   // MACD D1 - пересечение сигнальной линии сверху вниз
   if(macd_main_d1[0] < macd_signal_d1[0] && macd_main_d1[1] >= macd_signal_d1[1]) signal_count++;
   
   // Возвращаем true, если большинство таймфреймов дают сигнал на продажу
   return signal_count >= 4; // Более половины таймфреймов
}

//+------------------------------------------------------------------+
//| Проверка сигнала на покупку по RSI на разных таймфреймах         |
//+------------------------------------------------------------------+
bool CheckRSIBuySignal()
{
   // Массивы для хранения значений RSI
   double rsi_m1[], rsi_m5[], rsi_m15[], rsi_m30[], rsi_h1[], rsi_h4[], rsi_d1[];
   
   // Настройка массивов как серии
   ArraySetAsSeries(rsi_m1, true);
   ArraySetAsSeries(rsi_m5, true);
   ArraySetAsSeries(rsi_m15, true);
   ArraySetAsSeries(rsi_m30, true);
   ArraySetAsSeries(rsi_h1, true);
   ArraySetAsSeries(rsi_h4, true);
   ArraySetAsSeries(rsi_d1, true);
   
   // Копирование данных для каждого таймфрейма
   CopyBuffer(rsi_handle_m1, 0, 0, 2, rsi_m1);
   CopyBuffer(rsi_handle_m5, 0, 0, 2, rsi_m5);
   CopyBuffer(rsi_handle_m15, 0, 0, 2, rsi_m15);
   CopyBuffer(rsi_handle_m30, 0, 0, 2, rsi_m30);
   CopyBuffer(rsi_handle_h1, 0, 0, 2, rsi_h1);
   CopyBuffer(rsi_handle_h4, 0, 0, 2, rsi_h4);
   CopyBuffer(rsi_handle_d1, 0, 0, 2, rsi_d1);
   
   // Подсчет сигналов на покупку (RSI выходит из зоны перепроданности)
   int signal_count = 0;
   
   if(rsi_m1[0] > 30 && rsi_m1[1] <= 30) signal_count++;
   if(rsi_m5[0] > 30 && rsi_m5[1] <= 30) signal_count++;
   if(rsi_m15[0] > 30 && rsi_m15[1] <= 30) signal_count++;
   if(rsi_m30[0] > 30 && rsi_m30[1] <= 30) signal_count++;
   if(rsi_h1[0] > 30 && rsi_h1[1] <= 30) signal_count++;
   if(rsi_h4[0] > 30 && rsi_h4[1] <= 30) signal_count++;
   if(rsi_d1[0] > 30 && rsi_d1[1] <= 30) signal_count++;
   
   // Возвращаем true, если хотя бы на 3 таймфреймах есть сигналы на покупку
   return signal_count >= 3;
}

//+------------------------------------------------------------------+
//| Проверка сигнала на продажу по RSI на разных таймфреймах         |
//+------------------------------------------------------------------+
bool CheckRSISellSignal()
{
   // Массивы для хранения значений RSI
   double rsi_m1[], rsi_m5[], rsi_m15[], rsi_m30[], rsi_h1[], rsi_h4[], rsi_d1[];
   
   // Настройка массивов как серии
   ArraySetAsSeries(rsi_m1, true);
   ArraySetAsSeries(rsi_m5, true);
   ArraySetAsSeries(rsi_m15, true);
   ArraySetAsSeries(rsi_m30, true);
   ArraySetAsSeries(rsi_h1, true);
   ArraySetAsSeries(rsi_h4, true);
   ArraySetAsSeries(rsi_d1, true);
   
   // Копирование данных для каждого таймфрейма
   CopyBuffer(rsi_handle_m1, 0, 0, 2, rsi_m1);
   CopyBuffer(rsi_handle_m5, 0, 0, 2, rsi_m5);
   CopyBuffer(rsi_handle_m15, 0, 0, 2, rsi_m15);
   CopyBuffer(rsi_handle_m30, 0, 0, 2, rsi_m30);
   CopyBuffer(rsi_handle_h1, 0, 0, 2, rsi_h1);
   CopyBuffer(rsi_handle_h4, 0, 0, 2, rsi_h4);
   CopyBuffer(rsi_handle_d1, 0, 0, 2, rsi_d1);
   
   // Подсчет сигналов на продажу (RSI выходит из зоны перекупленности)
   int signal_count = 0;
   
   if(rsi_m1[0] < 70 && rsi_m1[1] >= 70) signal_count++;
   if(rsi_m5[0] < 70 && rsi_m5[1] >= 70) signal_count++;
   if(rsi_m15[0] < 70 && rsi_m15[1] >= 70) signal_count++;
   if(rsi_m30[0] < 70 && rsi_m30[1] >= 70) signal_count++;
   if(rsi_h1[0] < 70 && rsi_h1[1] >= 70) signal_count++;
   if(rsi_h4[0] < 70 && rsi_h4[1] >= 70) signal_count++;
   if(rsi_d1[0] < 70 && rsi_d1[1] >= 70) signal_count++;
   
   // Возвращаем true, если хотя бы на 3 таймфреймах есть сигналы на продажу
   return signal_count >= 3;
}

//+------------------------------------------------------------------+
//| Проверка сигнала на покупку по MA на разных таймфреймах          |
//+------------------------------------------------------------------+
bool CheckMABuySignal()
{
   // Массивы для хранения значений MA
   double ma_fast_m1[], ma_slow_m1[];
   double ma_fast_m5[], ma_slow_m5[];
   double ma_fast_m15[], ma_slow_m15[];
   double ma_fast_m30[], ma_slow_m30[];
   double ma_fast_h1[], ma_slow_h1[];
   double ma_fast_h4[], ma_slow_h4[];
   double ma_fast_d1[], ma_slow_d1[];
   
   // Настройка массивов как серии
   ArraySetAsSeries(ma_fast_m1, true);
   ArraySetAsSeries(ma_slow_m1, true);
   ArraySetAsSeries(ma_fast_m5, true);
   ArraySetAsSeries(ma_slow_m5, true);
   ArraySetAsSeries(ma_fast_m15, true);
   ArraySetAsSeries(ma_slow_m15, true);
   ArraySetAsSeries(ma_fast_m30, true);
   ArraySetAsSeries(ma_slow_m30, true);
   ArraySetAsSeries(ma_fast_h1, true);
   ArraySetAsSeries(ma_slow_h1, true);
   ArraySetAsSeries(ma_fast_h4, true);
   ArraySetAsSeries(ma_slow_h4, true);
   ArraySetAsSeries(ma_fast_d1, true);
   ArraySetAsSeries(ma_slow_d1, true);
   
   // Копирование данных для каждого таймфрейма
   CopyBuffer(ma_handle_fast_m1, 0, 0, 2, ma_fast_m1);
   CopyBuffer(ma_handle_slow_m1, 0, 0, 2, ma_slow_m1);
   CopyBuffer(ma_handle_fast_m5, 0, 0, 2, ma_fast_m5);
   CopyBuffer(ma_handle_slow_m5, 0, 0, 2, ma_slow_m5);
   CopyBuffer(ma_handle_fast_m15, 0, 0, 2, ma_fast_m15);
   CopyBuffer(ma_handle_slow_m15, 0, 0, 2, ma_slow_m15);
   CopyBuffer(ma_handle_fast_m30, 0, 0, 2, ma_fast_m30);
   CopyBuffer(ma_handle_slow_m30, 0, 0, 2, ma_slow_m30);
   CopyBuffer(ma_handle_fast_h1, 0, 0, 2, ma_fast_h1);
   CopyBuffer(ma_handle_slow_h1, 0, 0, 2, ma_slow_h1);
   CopyBuffer(ma_handle_fast_h4, 0, 0, 2, ma_fast_h4);
   CopyBuffer(ma_handle_slow_h4, 0, 0, 2, ma_slow_h4);
   CopyBuffer(ma_handle_fast_d1, 0, 0, 2, ma_fast_d1);
   CopyBuffer(ma_handle_slow_d1, 0, 0, 2, ma_slow_d1);
   
   // Подсчет сигналов на покупку (быстрая MA пересекает медленную снизу вверх)
   int signal_count = 0;
   
   if(ma_fast_m1[0] > ma_slow_m1[0] && ma_fast_m1[1] <= ma_slow_m1[1]) signal_count++;
   if(ma_fast_m5[0] > ma_slow_m5[0] && ma_fast_m5[1] <= ma_slow_m5[1]) signal_count++;
   if(ma_fast_m15[0] > ma_slow_m15[0] && ma_fast_m15[1] <= ma_slow_m15[1]) signal_count++;
   if(ma_fast_m30[0] > ma_slow_m30[0] && ma_fast_m30[1] <= ma_slow_m30[1]) signal_count++;
   if(ma_fast_h1[0] > ma_slow_h1[0] && ma_fast_h1[1] <= ma_slow_h1[1]) signal_count++;
   if(ma_fast_h4[0] > ma_slow_h4[0] && ma_fast_h4[1] <= ma_slow_h4[1]) signal_count++;
   if(ma_fast_d1[0] > ma_slow_d1[0] && ma_fast_d1[1] <= ma_slow_d1[1]) signal_count++;
   
   // Возвращаем true, если хотя бы на 3 таймфреймах есть сигналы на покупку
   return signal_count >= 3;
}

//+------------------------------------------------------------------+
//| Проверка сигнала на продажу по MA на разных таймфреймах          |
//+------------------------------------------------------------------+
bool CheckMASellSignal()
{
   // Массивы для хранения значений MA
   double ma_fast_m1[], ma_slow_m1[];
   double ma_fast_m5[], ma_slow_m5[];
   double ma_fast_m15[], ma_slow_m15[];
   double ma_fast_m30[], ma_slow_m30[];
   double ma_fast_h1[], ma_slow_h1[];
   double ma_fast_h4[], ma_slow_h4[];
   double ma_fast_d1[], ma_slow_d1[];
   
   // Настройка массивов как серии
   ArraySetAsSeries(ma_fast_m1, true);
   ArraySetAsSeries(ma_slow_m1, true);
   ArraySetAsSeries(ma_fast_m5, true);
   ArraySetAsSeries(ma_slow_m5, true);
   ArraySetAsSeries(ma_fast_m15, true);
   ArraySetAsSeries(ma_slow_m15, true);
   ArraySetAsSeries(ma_fast_m30, true);
   ArraySetAsSeries(ma_slow_m30, true);
   ArraySetAsSeries(ma_fast_h1, true);
   ArraySetAsSeries(ma_slow_h1, true);
   ArraySetAsSeries(ma_fast_h4, true);
   ArraySetAsSeries(ma_slow_h4, true);
   ArraySetAsSeries(ma_fast_d1, true);
   ArraySetAsSeries(ma_slow_d1, true);
   
   // Копирование данных для каждого таймфрейма
   CopyBuffer(ma_handle_fast_m1, 0, 0, 2, ma_fast_m1);
   CopyBuffer(ma_handle_slow_m1, 0, 0, 2, ma_slow_m1);
   CopyBuffer(ma_handle_fast_m5, 0, 0, 2, ma_fast_m5);
   CopyBuffer(ma_handle_slow_m5, 0, 0, 2, ma_slow_m5);
   CopyBuffer(ma_handle_fast_m15, 0, 0, 2, ma_fast_m15);
   CopyBuffer(ma_handle_slow_m15, 0, 0, 2, ma_slow_m15);
   CopyBuffer(ma_handle_fast_m30, 0, 0, 2, ma_fast_m30);
   CopyBuffer(ma_handle_slow_m30, 0, 0, 2, ma_slow_m30);
   CopyBuffer(ma_handle_fast_h1, 0, 0, 2, ma_fast_h1);
   CopyBuffer(ma_handle_slow_h1, 0, 0, 2, ma_slow_h1);
   CopyBuffer(ma_handle_fast_h4, 0, 0, 2, ma_fast_h4);
   CopyBuffer(ma_handle_slow_h4, 0, 0, 2, ma_slow_h4);
   CopyBuffer(ma_handle_fast_d1, 0, 0, 2, ma_fast_d1);
   CopyBuffer(ma_handle_slow_d1, 0, 0, 2, ma_slow_d1);
   
   // Подсчет сигналов на продажу (быстрая MA пересекает медленную сверху вниз)
   int signal_count = 0;
   
   if(ma_fast_m1[0] < ma_slow_m1[0] && ma_fast_m1[1] >= ma_slow_m1[1]) signal_count++;
   if(ma_fast_m5[0] < ma_slow_m5[0] && ma_fast_m5[1] >= ma_slow_m5[1]) signal_count++;
   if(ma_fast_m15[0] < ma_slow_m15[0] && ma_fast_m15[1] >= ma_slow_m15[1]) signal_count++;
   if(ma_fast_m30[0] < ma_slow_m30[0] && ma_fast_m30[1] >= ma_slow_m30[1]) signal_count++;
   if(ma_fast_h1[0] < ma_slow_h1[0] && ma_fast_h1[1] >= ma_slow_h1[1]) signal_count++;
   if(ma_fast_h4[0] < ma_slow_h4[0] && ma_fast_h4[1] >= ma_slow_h4[1]) signal_count++;
   if(ma_fast_d1[0] < ma_slow_d1[0] && ma_fast_d1[1] >= ma_slow_d1[1]) signal_count++;
   
   // Возвращаем true, если хотя бы на 3 таймфреймах есть сигналы на продажу
   return signal_count >= 3;
}

//+------------------------------------------------------------------+
//| Динамическое управление рисками                                   |
//+------------------------------------------------------------------+
double CalculateDynamicRisk()
{
    double volatility = iATR(_Symbol, PERIOD_CURRENT, 14);
    double baseRisk = RiskInPercent;
    double adjustedRisk = baseRisk * (1.0 - volatility / 100.0);
    return MathMax(adjustedRisk, baseRisk * 0.5);
}

//+------------------------------------------------------------------+
//| Комплексная фильтрация сигналов                                   |
//+------------------------------------------------------------------+
bool ValidateSignal(int strategy)
{
    // Фильтр по тренду
    bool trendFilter = CheckTrendFilter();
    
    // Фильтр по волатильности
    bool volatilityFilter = CheckVolatilityFilter();
    
    // Фильтр по времени
    bool timeFilter = CheckTimeFilter();
    
    // Фильтр по новостям
    bool newsFilter = CheckNewsFilter();
    
    return trendFilter && volatilityFilter && timeFilter && newsFilter;
}

//+------------------------------------------------------------------+
//| Проверка фильтра по тренду                                        |
//+------------------------------------------------------------------+
bool CheckTrendFilter()
{
    double ma_fast[], ma_slow[];
    ArraySetAsSeries(ma_fast, true);
    ArraySetAsSeries(ma_slow, true);
    
    CopyBuffer(ma_handle_fast, 0, 0, 2, ma_fast);
    CopyBuffer(ma_handle_slow, 0, 0, 2, ma_slow);
    
    return ma_fast[0] > ma_slow[0];
}

//+------------------------------------------------------------------+
//| Проверка фильтра по волатильности                                 |
//+------------------------------------------------------------------+
bool CheckVolatilityFilter()
{
    double atr = iATR(_Symbol, PERIOD_CURRENT, 14);
    double spread = SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID);
    
    return atr > spread * 2.0;
}

//+------------------------------------------------------------------+
//| Проверка фильтра по времени                                       |
//+------------------------------------------------------------------+
bool CheckTimeFilter()
{
    datetime current_time = TimeCurrent();
    MqlDateTime time_struct;
    TimeToStruct(current_time, time_struct);
    
    // Проверка торговых часов
    if(!Trading24h)
    {
        if(time_struct.hour < HoursFrom || time_struct.hour > HoursTo)
            return false;
    }
    
    // Проверка торговых дней
    if(!Monday && time_struct.day_of_week == 1) return false;
    if(!Tuesday && time_struct.day_of_week == 2) return false;
    if(!Wednesday && time_struct.day_of_week == 3) return false;
    if(!Thursday && time_struct.day_of_week == 4) return false;
    if(!Friday && time_struct.day_of_week == 5) return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Проверка фильтра по новостям                                      |
//+------------------------------------------------------------------+
bool CheckNewsFilter()
{
    if(!AvoidNews) return true;
    
    // Здесь должна быть реализация проверки новостей
    // Временная заглушка
    return true;
}

//+------------------------------------------------------------------+
//| Мониторинг эффективности стратегий                                |
//+------------------------------------------------------------------+
void MonitorStrategies()
{
    // Мониторинг эффективности
    double hiloPerformance = CalculateStrategyPerformance(MagicNumber_Hilo);
    double rsiPerformance = CalculateStrategyPerformance(g_magic_176_15);
    double trendKillerPerformance = CalculateStrategyPerformance(g_magic_176_16);
    
    // Адаптивное управление
    if(hiloPerformance < 0) AdjustHiloParameters();
    if(rsiPerformance < 0) AdjustRSIParameters();
    if(trendKillerPerformance < 0) AdjustTrendKillerParameters();
}

//+------------------------------------------------------------------+
//| Расчет эффективности стратегии                                    |
//+------------------------------------------------------------------+
double CalculateStrategyPerformance(int magic)
{
    double totalProfit = 0.0;
    int totalTrades = 0;
    
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(!PositionSelectByTicket(PositionGetTicket(i))) continue;
        if(PositionGetInteger(POSITION_MAGIC) != magic) continue;
        
        totalProfit += PositionGetDouble(POSITION_PROFIT);
        totalTrades++;
    }
    
    return totalTrades > 0 ? totalProfit / totalTrades : 0.0;
}

//+------------------------------------------------------------------+
//| Настройка параметров FiboScalper                                  |
//+------------------------------------------------------------------+
void AdjustHiloParameters()
{
    // Адаптивная настройка параметров
    double atr = iATR(_Symbol, PERIOD_CURRENT, 14);
    double dynamic_sl = NormalizeDouble(atr * 2.0, _Digits);
    double dynamic_tp = NormalizeDouble(atr * 4.0, _Digits);
    
    // Используем динамические значения вместо констант
    if(UseDynamicSL) 
    {
        double temp_SL = dynamic_sl;
        // Используем StopLoss для расчетов, но не изменяем саму константу
    }
    if(UseDynamicTP) 
    {
        double temp_TP = dynamic_tp;
        // Используем TakeProfit для расчетов, но не изменяем саму константу
    }
}

//+------------------------------------------------------------------+
//| Настройка параметров AF-Scalper                                   |
//+------------------------------------------------------------------+
void AdjustRSIParameters()
{
    // Адаптивная настройка параметров RSI
    double atr = iATR(_Symbol, PERIOD_CURRENT, 14);
    double dynamic_sl = NormalizeDouble(atr * 1.5, _Digits);
    double dynamic_tp = NormalizeDouble(atr * 2.5, _Digits);
    
    // Используем динамические значения вместо констант
    if(UseDynamicSL) 
    {
        double temp_SL = dynamic_sl;
        // Используем StopLoss для расчетов, но не изменяем саму константу
    }
    if(UseDynamicTP) 
    {
        double temp_TP = dynamic_tp;
        // Используем TakeProfit для расчетов, но не изменяем саму константу
    }
}

//+------------------------------------------------------------------+
//| Настройка параметров AF-TrendKiller                               |
//+------------------------------------------------------------------+
void AdjustTrendKillerParameters()
{
    // Адаптивная настройка параметров
    double atr = iATR(_Symbol, PERIOD_CURRENT, 14);
    double dynamic_sl = NormalizeDouble(atr * 2.5, _Digits);
    double dynamic_tp = NormalizeDouble(atr * 4.0, _Digits);
    
    // Используем динамические значения вместо констант
    if(UseDynamicSL) 
    {
        double temp_SL = dynamic_sl;
        // Используем StopLoss для расчетов, но не изменяем саму константу
    }
    if(UseDynamicTP) 
    {
        double temp_TP = dynamic_tp;
        // Используем TakeProfit для расчетов, но не изменяем саму константу
    }
}

//+------------------------------------------------------------------+
//| Система восстановления после сбоев                                |
//+------------------------------------------------------------------+
void RecoverySystem()
{
    // Проверка состояния позиций
    CheckPositionsState();
    
    // Восстановление параметров
    RestoreParameters();
    
    // Проверка индикаторов
    ValidateIndicators();
}

//+------------------------------------------------------------------+
//| Проверка состояния позиций                                        |
//+------------------------------------------------------------------+
void CheckPositionsState()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(!PositionSelectByTicket(PositionGetTicket(i))) continue;
        
        // Проверка стоп-лоссов
        double sl = PositionGetDouble(POSITION_SL);
        if(sl == 0.0)
        {
            // Установка стоп-лосса
            double price = PositionGetDouble(POSITION_PRICE_OPEN);
            double new_sl = PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? 
                          price - StopLoss * _Point : 
                          price + StopLoss * _Point;
            trade.PositionModify(PositionGetTicket(i), new_sl, PositionGetDouble(POSITION_TP));
        }
    }
}

//+------------------------------------------------------------------+
//| Восстановление параметров                                         |
//+------------------------------------------------------------------+
void RestoreParameters()
{
    // Восстановление параметров из файла настроек
    // Временная заглушка
}

//+------------------------------------------------------------------+
//| Проверка индикаторов                                              |
//+------------------------------------------------------------------+
void ValidateIndicators()
{
    // Проверка хендлов индикаторов
    if(macd_handle == INVALID_HANDLE) macd_handle = iMACD(_Symbol, PERIOD_CURRENT, 12, 26, 9, PRICE_CLOSE);
    if(rsi_handle == INVALID_HANDLE) rsi_handle = iRSI(_Symbol, PERIOD_CURRENT, 14, PRICE_CLOSE);
    if(ma_handle_fast == INVALID_HANDLE) ma_handle_fast = iMA(_Symbol, PERIOD_CURRENT, 10, 0, MODE_EMA, PRICE_CLOSE);
    if(ma_handle_slow == INVALID_HANDLE) ma_handle_slow = iMA(_Symbol, PERIOD_CURRENT, 20, 0, MODE_EMA, PRICE_CLOSE);
}

//+------------------------------------------------------------------+
//| Улучшенная версия FiboScalper                                     |
//+------------------------------------------------------------------+
void ProcessFiboScalper(double lots)
{
    // Обновление трейлинг-стопов
    if(UseTrailingStop) TrailingStop_Hilo();
    
    // Проверка таймаута
    static datetime timeout_Hilo = 0;
    if(timeout_Hilo > 0 && TimeCurrent() >= timeout_Hilo)
    {
        CloseThisSymbolAll_Hilo();
        Print("Закрыты все позиции Hilo из-за таймаута");
        timeout_Hilo = 0;
    }
    
    // Проверка убытков и остановка по риску
    double profit_Hilo = CalculateProfit_Hilo();
    if(UseEquityStop && profit_Hilo < 0 && MathAbs(profit_Hilo) > TotalEquityRisk / 100.0 * AccountEquityHigh_Hilo())
    {
        CloseThisSymbolAll_Hilo();
        Print("Закрыты все позиции Hilo из-за достижения максимального риска");
    }
    
    // Подсчет открытых позиций
    int trades_count_Hilo = CountTrades_Hilo();
    
    // Проверка условий для торговли
    bool can_buy_Hilo = false;
    bool can_sell_Hilo = false;
    bool can_trade_Hilo = false;
    double last_buy_price_Hilo = 0;
    double last_sell_price_Hilo = 0;
    
    // Определение существующих позиций
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(!PositionSelectByTicket(PositionGetTicket(i))) continue;
        if(PositionGetSymbol(i) != _Symbol) continue;
        if(PositionGetInteger(POSITION_MAGIC) != MagicNumber_Hilo) continue;
        
        if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
        {
            can_buy_Hilo = true;
            can_sell_Hilo = false;
            break;
        }
        else if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL)
        {
            can_buy_Hilo = false;
            can_sell_Hilo = true;
            break;
        }
    }
    
    // Проверка условий добавления позиций
    if(trades_count_Hilo > 0 && trades_count_Hilo < MaxTrades_Hilo)
    {
        last_buy_price_Hilo = FindLastBuyPrice_Hilo();
        last_sell_price_Hilo = FindLastSellPrice_Hilo();
        
        // Адаптивный PipStep на основе волатильности
        double atr = iATR(_Symbol, PERIOD_CURRENT, 14);
        double adaptivePipStep = PipStep * (1.0 + atr / 100.0);
        
        if(can_buy_Hilo && last_buy_price_Hilo - SymbolInfoDouble(_Symbol, SYMBOL_ASK) >= adaptivePipStep * _Point) 
            can_trade_Hilo = true;
        if(can_sell_Hilo && SymbolInfoDouble(_Symbol, SYMBOL_BID) - last_sell_price_Hilo >= adaptivePipStep * _Point) 
            can_trade_Hilo = true;
    }
    
    // Если нет открытых позиций, разрешаем торговлю
    if(trades_count_Hilo < 1)
    {
        can_buy_Hilo = false;
        can_sell_Hilo = false;
        can_trade_Hilo = true;
    }
    
    // Обработка торговых сигналов
    if(can_trade_Hilo)
    {
        // Получение значений MACD для анализа
        double macd_buffer[];
        double signal_buffer[];
        ArraySetAsSeries(macd_buffer, true);
        ArraySetAsSeries(signal_buffer, true);
        CopyBuffer(macd_handle, MAIN_LINE, 0, 2, macd_buffer);
        CopyBuffer(macd_handle, SIGNAL_LINE, 0, 2, signal_buffer);
        
        // Расчет динамического размера лота
        double lot_size = NormalizeDouble(lots * MathPow(LotExponent, trades_count_Hilo), lotdecimal);
        
        // Проверка сигнала MACD на покупку и учет мультитаймфреймового анализа
        if(macd_buffer[0] > signal_buffer[0] && macd_buffer[1] <= signal_buffer[1] && (macd_buy_signal || !UseMultiTimeframeAnalysis))
        {
            // Расчет динамических уровней стоп-лосса и тейк-профита
            double atr = iATR(_Symbol, PERIOD_CURRENT, 14);
            double sl = SymbolInfoDouble(_Symbol, SYMBOL_ASK) - atr * 2.0;
            double tp = SymbolInfoDouble(_Symbol, SYMBOL_ASK) + atr * 3.0;
            
            int ticket = OpenPendingOrder_Hilo(0, lot_size, SymbolInfoDouble(_Symbol, SYMBOL_ASK), SlipPage, 0, sl, tp, 
                                             "HILO Buy-" + IntegerToString(trades_count_Hilo), MagicNumber_Hilo, 0, clrGreen);
            
            if(ticket > 0)
            {
                timeout_Hilo = TimeCurrent() + 3600 * 48; // Таймаут 48 часов
                can_trade_Hilo = false;
            }
        }
        
        // Проверка сигнала MACD на продажу и учет мультитаймфреймового анализа
        if(macd_buffer[0] < signal_buffer[0] && macd_buffer[1] >= signal_buffer[1] && (macd_sell_signal || !UseMultiTimeframeAnalysis))
        {
            // Расчет динамических уровней стоп-лосса и тейк-профита
            double atr = iATR(_Symbol, PERIOD_CURRENT, 14);
            double sl = SymbolInfoDouble(_Symbol, SYMBOL_BID) + atr * 2.0;
            double tp = SymbolInfoDouble(_Symbol, SYMBOL_BID) - atr * 3.0;
            
            int ticket = OpenPendingOrder_Hilo(1, lot_size, SymbolInfoDouble(_Symbol, SYMBOL_BID), SlipPage, 0, sl, tp, 
                                             "HILO Sell-" + IntegerToString(trades_count_Hilo), MagicNumber_Hilo, 0, clrRed);
            
            if(ticket > 0)
            {
                timeout_Hilo = TimeCurrent() + 3600 * 48; // Таймаут 48 часов
                can_trade_Hilo = false;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Улучшенная версия AF-Scalper                                      |
//+------------------------------------------------------------------+
void ProcessAFScalper(double lots)
{
    // Обновление трейлинг-стопов
    if(UseTrailingStop) TrailingStop_15();
    
    // Проверка таймаута
    static datetime timeout_15 = 0;
    if(timeout_15 > 0 && TimeCurrent() >= timeout_15)
    {
        CloseThisSymbolAll_15();
        Print("Закрыты все позиции RSI из-за таймаута");
        timeout_15 = 0;
    }
    
    // Проверка убытков и остановка по риску
    double profit_15 = CalculateProfit_15();
    if(UseEquityStop && profit_15 < 0 && MathAbs(profit_15) > TotalEquityRisk / 100.0 * AccountEquityHigh_15())
    {
        CloseThisSymbolAll_15();
        Print("Закрыты все позиции RSI из-за достижения максимального риска");
    }
    
    // Подсчет открытых позиций
    int trades_count_15 = CountTrades_15();
    
    // Проверка условий для торговли
    bool can_buy_15 = false;
    bool can_sell_15 = false;
    bool can_trade_15 = false;
    double last_buy_price_15 = 0;
    double last_sell_price_15 = 0;
    
    // Определение существующих позиций
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(!PositionSelectByTicket(PositionGetTicket(i))) continue;
        if(PositionGetSymbol(i) != _Symbol) continue;
        if(PositionGetInteger(POSITION_MAGIC) != g_magic_176_15) continue;
        
        if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
        {
            can_buy_15 = true;
            can_sell_15 = false;
            break;
        }
        else if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL)
        {
            can_buy_15 = false;
            can_sell_15 = true;
            break;
        }
    }
    
    // Проверка условий добавления позиций
    if(trades_count_15 > 0 && trades_count_15 < MaxTrades_15)
    {
        last_buy_price_15 = FindLastBuyPrice_15();
        last_sell_price_15 = FindLastSellPrice_15();
        
        // Адаптивный PipStep на основе волатильности
        double atr = iATR(_Symbol, PERIOD_CURRENT, 14);
        double adaptivePipStep = PipStep * (1.0 + atr / 100.0);
        
        if(can_buy_15 && last_buy_price_15 - SymbolInfoDouble(_Symbol, SYMBOL_ASK) >= adaptivePipStep * _Point) 
            can_trade_15 = true;
        if(can_sell_15 && SymbolInfoDouble(_Symbol, SYMBOL_BID) - last_sell_price_15 >= adaptivePipStep * _Point) 
            can_trade_15 = true;
    }
    
    // Если нет открытых позиций, разрешаем торговлю
    if(trades_count_15 < 1)
    {
        can_buy_15 = false;
        can_sell_15 = false;
        can_trade_15 = true;
    }
    
    // Обработка торговых сигналов
    if(can_trade_15)
    {
        // Получение значений RSI для анализа
        double rsi_values[];
        ArraySetAsSeries(rsi_values, true);
        CopyBuffer(rsi_handle, 0, 0, 2, rsi_values);
        
        // Расчет динамического размера лота
        double lot_size = NormalizeDouble(lots * MathPow(LotExponent, trades_count_15), lotdecimal);
        
        // Проверка сигнала RSI на покупку и учет мультитаймфреймового анализа
        if(rsi_values[0] < 30 && rsi_values[1] >= 30 && (rsi_buy_signal || !UseMultiTimeframeAnalysis))
        {
            // Расчет динамических уровней стоп-лосса и тейк-профита
            double atr = iATR(_Symbol, PERIOD_CURRENT, 14);
            double sl = SymbolInfoDouble(_Symbol, SYMBOL_ASK) - atr * 1.5;
            double tp = SymbolInfoDouble(_Symbol, SYMBOL_ASK) + atr * 2.5;
            
            int ticket = OpenPendingOrder_15(0, lot_size, SymbolInfoDouble(_Symbol, SYMBOL_ASK), SlipPage, 0, sl, tp, 
                                          "RSI Buy-" + IntegerToString(trades_count_15), g_magic_176_15, 0, clrGreen);
            
            if(ticket > 0)
            {
                timeout_15 = TimeCurrent() + 3600 * 48; // Таймаут 48 часов
                can_trade_15 = false;
            }
        }
        
        // Проверка сигнала RSI на продажу и учет мультитаймфреймового анализа
        if(rsi_values[0] > 70 && rsi_values[1] <= 70 && (rsi_sell_signal || !UseMultiTimeframeAnalysis))
        {
            // Расчет динамических уровней стоп-лосса и тейк-профита
            double atr = iATR(_Symbol, PERIOD_CURRENT, 14);
            double sl = SymbolInfoDouble(_Symbol, SYMBOL_BID) + atr * 1.5;
            double tp = SymbolInfoDouble(_Symbol, SYMBOL_BID) - atr * 2.5;
            
            int ticket = OpenPendingOrder_15(1, lot_size, SymbolInfoDouble(_Symbol, SYMBOL_BID), SlipPage, 0, sl, tp, 
                                          "RSI Sell-" + IntegerToString(trades_count_15), g_magic_176_15, 0, clrRed);
            
            if(ticket > 0)
            {
                timeout_15 = TimeCurrent() + 3600 * 48; // Таймаут 48 часов
                can_trade_15 = false;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Улучшенная версия AF-TrendKiller                                  |
//+------------------------------------------------------------------+
void ProcessAFTrendKiller(double lots)
{
    // Обновление трейлинг-стопов
    if(UseTrailingStop) TrailingStop_16();
    
    // Проверка таймаута
    static datetime timeout_16 = 0;
    if(timeout_16 > 0 && TimeCurrent() >= timeout_16)
    {
        CloseThisSymbolAll_16();
        Print("Закрыты все позиции TrendKiller из-за таймаута");
        timeout_16 = 0;
    }
    
    // Проверка убытков и остановка по риску
    double profit_16 = CalculateProfit_16();
    if(UseEquityStop && profit_16 < 0 && MathAbs(profit_16) > TotalEquityRisk / 100.0 * AccountEquityHigh_16())
    {
        CloseThisSymbolAll_16();
        Print("Закрыты все позиции TrendKiller из-за достижения максимального риска");
    }
    
    // Подсчет открытых позиций
    int trades_count_16 = CountTrades_16();
    
    // Проверка условий для торговли
    bool can_buy_16 = false;
    bool can_sell_16 = false;
    bool can_trade_16 = false;
    double last_buy_price_16 = 0;
    double last_sell_price_16 = 0;
    
    // Определение существующих позиций
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(!PositionSelectByTicket(PositionGetTicket(i))) continue;
        if(PositionGetSymbol(i) != _Symbol) continue;
        if(PositionGetInteger(POSITION_MAGIC) != g_magic_176_16) continue;
        
        if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
        {
            can_buy_16 = true;
            can_sell_16 = false;
            break;
        }
        else if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL)
        {
            can_buy_16 = false;
            can_sell_16 = true;
            break;
        }
    }
    
    // Проверка условий добавления позиций
    if(trades_count_16 > 0 && trades_count_16 < MaxTrades_16)
    {
        last_buy_price_16 = FindLastBuyPrice_16();
        last_sell_price_16 = FindLastSellPrice_16();
        
        // Адаптивный PipStep на основе волатильности
        double atr_value = iATR(_Symbol, PERIOD_CURRENT, 14);
        double adaptivePipStep = PipStep * (1.0 + atr_value / 100.0);
        
        if(can_buy_16 && last_buy_price_16 - SymbolInfoDouble(_Symbol, SYMBOL_ASK) >= adaptivePipStep * _Point) 
            can_trade_16 = true;
        if(can_sell_16 && SymbolInfoDouble(_Symbol, SYMBOL_BID) - last_sell_price_16 >= adaptivePipStep * _Point) 
            can_trade_16 = true;
    }
    
    // Если нет открытых позиций, разрешаем торговлю
    if(trades_count_16 < 1)
    {
        can_buy_16 = false;
        can_sell_16 = false;
        can_trade_16 = true;
    }
    
    // Обработка торговых сигналов
    if(can_trade_16)
    {
        // Получение значений MA для анализа
        double ma_fast[];
        double ma_slow[];
        ArraySetAsSeries(ma_fast, true);
        ArraySetAsSeries(ma_slow, true);
        CopyBuffer(ma_handle_fast, 0, 0, 2, ma_fast);
        CopyBuffer(ma_handle_slow, 0, 0, 2, ma_slow);
        
        // Расчет динамического размера лота
        double lot_size = NormalizeDouble(lots * MathPow(LotExponent, trades_count_16), lotdecimal);
        
        // Проверка сигнала MA на покупку и учет мультитаймфреймового анализа
        if(ma_fast[0] > ma_slow[0] && ma_fast[1] <= ma_slow[1] && (ma_buy_signal || !UseMultiTimeframeAnalysis))
        {
            // Расчет динамических уровней стоп-лосса и тейк-профита
            double atr_value = iATR(_Symbol, PERIOD_CURRENT, 14);
            double dynamic_sl = SymbolInfoDouble(_Symbol, SYMBOL_ASK) - atr_value * 2.5;
            double dynamic_tp = SymbolInfoDouble(_Symbol, SYMBOL_ASK) + atr_value * 4.0;
            
            int ticket = OpenPendingOrder_16(0, lot_size, SymbolInfoDouble(_Symbol, SYMBOL_ASK), SlipPage, 0, dynamic_sl, dynamic_tp, 
                                          "TrendKiller Buy-" + IntegerToString(trades_count_16), g_magic_176_16, 0, clrGreen);
            
            if(ticket > 0)
            {
                timeout_16 = TimeCurrent() + 3600 * 48; // Таймаут 48 часов
                can_trade_16 = false;
            }
        }
        
        // Проверка сигнала MA на продажу и учет мультитаймфреймового анализа
        if(ma_fast[0] < ma_slow[0] && ma_fast[1] >= ma_slow[1] && (ma_sell_signal || !UseMultiTimeframeAnalysis))
        {
            // Расчет динамических уровней стоп-лосса и тейк-профита
            double atr_value = iATR(_Symbol, PERIOD_CURRENT, 14);
            double dynamic_sl = SymbolInfoDouble(_Symbol, SYMBOL_BID) + atr_value * 2.5;
            double dynamic_tp = SymbolInfoDouble(_Symbol, SYMBOL_BID) - atr_value * 4.0;
            
            int ticket = OpenPendingOrder_16(1, lot_size, SymbolInfoDouble(_Symbol, SYMBOL_BID), SlipPage, 0, dynamic_sl, dynamic_tp, 
                                          "TrendKiller Sell-" + IntegerToString(trades_count_16), g_magic_176_16, 0, clrRed);
            
            if(ticket > 0)
            {
                timeout_16 = TimeCurrent() + 3600 * 48; // Таймаут 48 часов
                can_trade_16 = false;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Функция для расчета ATR с правильными параметрами                 |
//+------------------------------------------------------------------+
double CalculateATR(int period = 14)
{
    return iATR(_Symbol, PERIOD_CURRENT, period);
}

//+------------------------------------------------------------------+
//| Функция для расчета динамических уровней SL/TP                    |
//+------------------------------------------------------------------+
void CalculateDynamicLevels(double &sl, double &tp, double price, bool is_buy)
{
    double atr_value = CalculateATR();
    if(is_buy)
    {
        sl = price - atr_value * 2.5;
        tp = price + atr_value * 4.0;
    }
    else
    {
        sl = price + atr_value * 2.5;
        tp = price - atr_value * 4.0;
    }
}

//+------------------------------------------------------------------+
//| Функция для обновления MACD сигналов                              |
//+------------------------------------------------------------------+
void UpdateMACDSignals()
{
    double macd_buffer[];
    double signal_buffer[];
    ArraySetAsSeries(macd_buffer, true);
    ArraySetAsSeries(signal_buffer, true);
    
    CopyBuffer(macd_handle, 0, 0, 2, macd_buffer);
    CopyBuffer(macd_handle, 1, 0, 2, signal_buffer);
    
    macd_buy_signal = macd_buffer[0] > signal_buffer[0] && macd_buffer[1] <= signal_buffer[1];
    macd_sell_signal = macd_buffer[0] < signal_buffer[0] && macd_buffer[1] >= signal_buffer[1];
}

//+------------------------------------------------------------------+
//| Вспомогательная функция для расчета ATR                           |
//+------------------------------------------------------------------+
double GetATRValue(int period = 14)
{
    return iATR(_Symbol, PERIOD_CURRENT, period);
}

//+------------------------------------------------------------------+
//| Инициализация мультитаймфреймовых сигналов                        |
//+------------------------------------------------------------------+
void InitializeMultiTimeframeSignals()
{
   // Обновляем сигналы на разных таймфреймах
   UpdateMultiTimeframeSignals();
}

//+------------------------------------------------------------------+
//| Обновление мультитаймфреймовых сигналов                           |
//+------------------------------------------------------------------+
void UpdateMultiTimeframeSignals()
{
   // Обновление MACD сигналов
   UpdateMACDSignals();
   
   // Обновление RSI сигналов
   UpdateRSISignals();
   
   // Обновление MA сигналов
   UpdateMASignals();
}

//+------------------------------------------------------------------+
//| Обновление сигналов RSI                                           |
//+------------------------------------------------------------------+
void UpdateRSISignals()
{
   double rsi_values_m15[], rsi_values_m30[], rsi_values_h1[], rsi_values_h4[], rsi_values_d1[];
   
   // Настраиваем массивы для работы с историческими данными
   ArraySetAsSeries(rsi_values_m15, true);
   ArraySetAsSeries(rsi_values_m30, true);
   ArraySetAsSeries(rsi_values_h1, true);
   ArraySetAsSeries(rsi_values_h4, true);
   ArraySetAsSeries(rsi_values_d1, true);
   
   // Копируем данные в массивы
   CopyBuffer(rsi_handle_m15, 0, 0, 3, rsi_values_m15);
   CopyBuffer(rsi_handle_m30, 0, 0, 3, rsi_values_m30);
   CopyBuffer(rsi_handle_h1, 0, 0, 3, rsi_values_h1);
   CopyBuffer(rsi_handle_h4, 0, 0, 3, rsi_values_h4);
   CopyBuffer(rsi_handle_d1, 0, 0, 3, rsi_values_d1);
   
   // Определяем сигналы на покупку для разных таймфреймов
   bool m15_buy = rsi_values_m15[0] < 30 && rsi_values_m15[1] >= 30;
   bool m30_buy = rsi_values_m30[0] < 30 && rsi_values_m30[1] >= 30;
   bool h1_buy = rsi_values_h1[0] < 30 && rsi_values_h1[1] >= 30;
   bool h4_buy = rsi_values_h4[0] < 30 && rsi_values_h4[1] >= 30;
   bool d1_buy = rsi_values_d1[0] < 30 && rsi_values_d1[1] >= 30;
   
   // Определяем сигналы на продажу для разных таймфреймов
   bool m15_sell = rsi_values_m15[0] > 70 && rsi_values_m15[1] <= 70;
   bool m30_sell = rsi_values_m30[0] > 70 && rsi_values_m30[1] <= 70;
   bool h1_sell = rsi_values_h1[0] > 70 && rsi_values_h1[1] <= 70;
   bool h4_sell = rsi_values_h4[0] > 70 && rsi_values_h4[1] <= 70;
   bool d1_sell = rsi_values_d1[0] > 70 && rsi_values_d1[1] <= 70;
   
   // Итоговый сигнал - большинство таймфреймов должны давать одинаковый сигнал
   int buy_count = (m15_buy ? 1 : 0) + (m30_buy ? 1 : 0) + (h1_buy ? 1 : 0) + (h4_buy ? 1 : 0) + (d1_buy ? 1 : 0);
   int sell_count = (m15_sell ? 1 : 0) + (m30_sell ? 1 : 0) + (h1_sell ? 1 : 0) + (h4_sell ? 1 : 0) + (d1_sell ? 1 : 0);
   
   rsi_buy_signal = buy_count >= 3; // Сигнал на покупку, если 3 или более таймфреймов показывают buy
   rsi_sell_signal = sell_count >= 3; // Сигнал на продажу, если 3 или более таймфреймов показывают sell
}

//+------------------------------------------------------------------+
//| Обновление сигналов MA                                            |
//+------------------------------------------------------------------+
void UpdateMASignals()
{
   double ma_fast_m15[], ma_slow_m15[], ma_fast_m30[], ma_slow_m30[];
   double ma_fast_h1[], ma_slow_h1[], ma_fast_h4[], ma_slow_h4[], ma_fast_d1[], ma_slow_d1[];
   
   // Настраиваем массивы для работы с историческими данными
   ArraySetAsSeries(ma_fast_m15, true);
   ArraySetAsSeries(ma_slow_m15, true);
   ArraySetAsSeries(ma_fast_m30, true);
   ArraySetAsSeries(ma_slow_m30, true);
   ArraySetAsSeries(ma_fast_h1, true);
   ArraySetAsSeries(ma_slow_h1, true);
   ArraySetAsSeries(ma_fast_h4, true);
   ArraySetAsSeries(ma_slow_h4, true);
   ArraySetAsSeries(ma_fast_d1, true);
   ArraySetAsSeries(ma_slow_d1, true);
   
   // Копируем данные в массивы
   CopyBuffer(ma_handle_fast_m15, 0, 0, 3, ma_fast_m15);
   CopyBuffer(ma_handle_slow_m15, 0, 0, 3, ma_slow_m15);
   CopyBuffer(ma_handle_fast_m30, 0, 0, 3, ma_fast_m30);
   CopyBuffer(ma_handle_slow_m30, 0, 0, 3, ma_slow_m30);
   CopyBuffer(ma_handle_fast_h1, 0, 0, 3, ma_fast_h1);
   CopyBuffer(ma_handle_slow_h1, 0, 0, 3, ma_slow_h1);
   CopyBuffer(ma_handle_fast_h4, 0, 0, 3, ma_fast_h4);
   CopyBuffer(ma_handle_slow_h4, 0, 0, 3, ma_slow_h4);
   CopyBuffer(ma_handle_fast_d1, 0, 0, 3, ma_fast_d1);
   CopyBuffer(ma_handle_slow_d1, 0, 0, 3, ma_slow_d1);
   
   // Определяем сигналы на покупку для разных таймфреймов
   bool m15_buy = ma_fast_m15[0] > ma_slow_m15[0] && ma_fast_m15[1] <= ma_slow_m15[1];
   bool m30_buy = ma_fast_m30[0] > ma_slow_m30[0] && ma_fast_m30[1] <= ma_slow_m30[1];
   bool h1_buy = ma_fast_h1[0] > ma_slow_h1[0] && ma_fast_h1[1] <= ma_slow_h1[1];
   bool h4_buy = ma_fast_h4[0] > ma_slow_h4[0] && ma_fast_h4[1] <= ma_slow_h4[1];
   bool d1_buy = ma_fast_d1[0] > ma_slow_d1[0] && ma_fast_d1[1] <= ma_slow_d1[1];
   
   // Определяем сигналы на продажу для разных таймфреймов
   bool m15_sell = ma_fast_m15[0] < ma_slow_m15[0] && ma_fast_m15[1] >= ma_slow_m15[1];
   bool m30_sell = ma_fast_m30[0] < ma_slow_m30[0] && ma_fast_m30[1] >= ma_slow_m30[1];
   bool h1_sell = ma_fast_h1[0] < ma_slow_h1[0] && ma_fast_h1[1] >= ma_slow_h1[1];
   bool h4_sell = ma_fast_h4[0] < ma_slow_h4[0] && ma_fast_h4[1] >= ma_slow_h4[1];
   bool d1_sell = ma_fast_d1[0] < ma_slow_d1[0] && ma_fast_d1[1] >= ma_slow_d1[1];
   
   // Итоговый сигнал - большинство таймфреймов должны давать одинаковый сигнал
   int buy_count = (m15_buy ? 1 : 0) + (m30_buy ? 1 : 0) + (h1_buy ? 1 : 0) + (h4_buy ? 1 : 0) + (d1_buy ? 1 : 0);
   int sell_count = (m15_sell ? 1 : 0) + (m30_sell ? 1 : 0) + (h1_sell ? 1 : 0) + (h4_sell ? 1 : 0) + (d1_sell ? 1 : 0);
   
   ma_buy_signal = buy_count >= 3; // Сигнал на покупку, если 3 или более таймфреймов показывают buy
   ma_sell_signal = sell_count >= 3; // Сигнал на продажу, если 3 или более таймфреймов показывают sell
}