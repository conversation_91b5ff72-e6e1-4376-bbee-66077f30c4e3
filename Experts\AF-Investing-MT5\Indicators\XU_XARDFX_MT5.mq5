//+------------------------------------------------------------------+
//|                                               XU_XARDFX_MT5.mq5 |
//|                         Copyright 2023, AFSID-Group.Cv           |
//|                                      https://afs-id.com          |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, AFSID-Group.Cv"
#property link      "https://afs-id.com"
#property version   "1.20"
#property indicator_chart_window
#property indicator_buffers 10
#property indicator_plots   4

#property indicator_label1  "XARDFX Buy"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrBlue
#property indicator_style1  STYLE_SOLID
#property indicator_width1  2

#property indicator_label2  "XARDFX Sell"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrRed
#property indicator_style2  STYLE_SOLID
#property indicator_width2  2

#property indicator_label3  "XARDFX Exit Buy"
#property indicator_type3   DRAW_ARROW
#property indicator_color3  clrAqua
#property indicator_style3  STYLE_SOLID
#property indicator_width3  2

#property indicator_label4  "XARDFX Exit Sell"
#property indicator_type4   DRAW_ARROW
#property indicator_color4  clrMagenta
#property indicator_style4  STYLE_SOLID
#property indicator_width4  2

// Определение перечисления для углов
enum ENUM_CORNER_POSITION
{
   CORNER_LEFT_UPPER_POS    = 0,  // Левый верхний угол
   CORNER_RIGHT_UPPER_POS   = 1,  // Правый верхний угол
   CORNER_LEFT_LOWER_POS    = 2,  // Левый нижний угол
   CORNER_RIGHT_LOWER_POS   = 3   // Правый нижний угол
};

// Перечисление для типов оповещений
enum ENUM_ALERT_MODE
{
   ALERT_NONE = 0,        // Нет оповещений
   ALERT_ALL = 1,         // Все сигналы
   ALERT_ENTRY_ONLY = 2,  // Только сигналы входа
   ALERT_EXIT_ONLY = 3    // Только сигналы выхода
};

//--- input parameters
input int         inpRSIPeriod = 14;               // RSI Period
input int         inpRSILevel = 50;                // RSI Level
input int         inpMAPeriod = 100;               // Moving Average Period
input ENUM_MA_METHOD inpMAMethod = MODE_EMA;       // Moving Average Method
input ENUM_APPLIED_PRICE inpPrice = PRICE_CLOSE;   // Applied Price
input bool        inpShowBuyExit = true;           // Show Buy Exit signals
input bool        inpShowSellExit = true;          // Show Sell Exit signals
input ENUM_ALERT_MODE inpAlertMode = ALERT_NONE;   // Alert Mode
input int         inpADXPeriod = 14;               // ADX Period
input int         inpADXLevel = 25;                // ADX Minimum Level
input bool        inpUseCCI = true;                // Use CCI Filter
input int         inpCCIPeriod = 14;               // CCI Period
input double      inpCCILevel = 100.0;             // CCI Level
input int         inpArrowShiftBuy = 5;            // Shift for Buy Arrows
input int         inpArrowShiftSell = -5;          // Shift for Sell Arrows
input bool        inpSoundAlert = false;           // Enable Sound Alerts
input string      inpSoundFile = "alert.wav";      // Sound file for alerts
input color       inpBuyColor = clrBlue;           // Buy Signal Color
input color       inpSellColor = clrRed;           // Sell Signal Color
input color       inpExitBuyColor = clrAqua;       // Exit Buy Signal Color
input color       inpExitSellColor = clrMagenta;   // Exit Sell Signal Color
input bool        inpShowInfoPanel = true;         // Show Info Panel
input ENUM_CORNER_POSITION inpInfoCorner = CORNER_RIGHT_UPPER_POS;  // Положение панели
input int         inpInfoX = 20;                   // Отступ по X
input int         inpInfoY = 20;                   // Отступ по Y
input color       inpInfoColor = clrWhite;         // Цвет текста
input color       inpInfoBgColor = C'25,25,25';    // Info Background Color
input bool        inpPushNotifications = false;    // Push Notifications
input bool        inpEmailAlerts = false;          // Email Alerts
input bool        inpExportSignals = false;        // Export Signals to CSV
input string      inpExportFolder = "XU_XARDFX";   // Export Folder Name

//--- indicator buffers
double buyBuffer[];
double sellBuffer[];
double buyExitBuffer[];
double sellExitBuffer[];
double rsiBuffer[];
double maBuffer[];
double adxBuffer[];
double plusDIBuffer[];
double minusDIBuffer[];
double cciBuffer[];

//--- Arrow codes
#define ARROW_BUY        233
#define ARROW_SELL       234
#define ARROW_EXIT_BUY   251
#define ARROW_EXIT_SELL  251

//--- Indicator handles
int rsiHandle;
int maHandle;
int adxHandle;
int cciHandle;

//--- Info panel objects
string infoObjPrefix = "XU_XARDFX_";
string infoBackground = infoObjPrefix + "BG";
string infoRSI = infoObjPrefix + "RSI";
string infoMA = infoObjPrefix + "MA";
string infoADX = infoObjPrefix + "ADX";
string infoCCI = infoObjPrefix + "CCI";
string infoSignal = infoObjPrefix + "Signal";

//--- Signal state variables
bool isUptrend = false;
bool isDowntrend = false;
string currentSignal = "NEUTRAL";

//--- CSV export variables
string csvFileName;
int exportFileHandle = INVALID_HANDLE;
datetime lastSignalTime = 0;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   // Indicator buffers mapping
   SetIndexBuffer(0, buyBuffer, INDICATOR_DATA);
   SetIndexBuffer(1, sellBuffer, INDICATOR_DATA);
   SetIndexBuffer(2, buyExitBuffer, INDICATOR_DATA);
   SetIndexBuffer(3, sellExitBuffer, INDICATOR_DATA);
   SetIndexBuffer(4, rsiBuffer, INDICATOR_CALCULATIONS);
   SetIndexBuffer(5, maBuffer, INDICATOR_CALCULATIONS);
   SetIndexBuffer(6, adxBuffer, INDICATOR_CALCULATIONS);
   SetIndexBuffer(7, plusDIBuffer, INDICATOR_CALCULATIONS);
   SetIndexBuffer(8, minusDIBuffer, INDICATOR_CALCULATIONS);
   SetIndexBuffer(9, cciBuffer, INDICATOR_CALCULATIONS);
   
   // Set arrow codes
   PlotIndexSetInteger(0, PLOT_ARROW, ARROW_BUY);
   PlotIndexSetInteger(1, PLOT_ARROW, ARROW_SELL);
   PlotIndexSetInteger(2, PLOT_ARROW, ARROW_EXIT_BUY);
   PlotIndexSetInteger(3, PLOT_ARROW, ARROW_EXIT_SELL);
   
   // Set indicator colors
   PlotIndexSetInteger(0, PLOT_LINE_COLOR, inpBuyColor);
   PlotIndexSetInteger(1, PLOT_LINE_COLOR, inpSellColor);
   PlotIndexSetInteger(2, PLOT_LINE_COLOR, inpExitBuyColor);
   PlotIndexSetInteger(3, PLOT_LINE_COLOR, inpExitSellColor);
   
   // Set indicator name
   string shortName = "XU XARDFX MT5(RSI: " + string(inpRSIPeriod) + 
                     ", MA: " + string(inpMAPeriod) + 
                     ", ADX: " + string(inpADXPeriod) + ")";
   IndicatorSetString(INDICATOR_SHORTNAME, shortName);
   
   // Set digits
   IndicatorSetInteger(INDICATOR_DIGITS, _Digits);
   
   // Set empty value
   PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, EMPTY_VALUE);
   PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, EMPTY_VALUE);
   PlotIndexSetDouble(2, PLOT_EMPTY_VALUE, EMPTY_VALUE);
   PlotIndexSetDouble(3, PLOT_EMPTY_VALUE, EMPTY_VALUE);
   
   // Set shift for arrows
   PlotIndexSetInteger(0, PLOT_ARROW_SHIFT, inpArrowShiftBuy);
   PlotIndexSetInteger(1, PLOT_ARROW_SHIFT, inpArrowShiftSell);
   PlotIndexSetInteger(2, PLOT_ARROW_SHIFT, inpArrowShiftBuy);
   PlotIndexSetInteger(3, PLOT_ARROW_SHIFT, inpArrowShiftSell);
   
   // Create indicator handles
   rsiHandle = iRSI(_Symbol, PERIOD_CURRENT, inpRSIPeriod, inpPrice);
   maHandle = iMA(_Symbol, PERIOD_CURRENT, inpMAPeriod, 0, (ENUM_MA_METHOD)inpMAMethod, inpPrice);
   adxHandle = iADX(_Symbol, PERIOD_CURRENT, inpADXPeriod);
   cciHandle = iCCI(_Symbol, PERIOD_CURRENT, inpCCIPeriod, inpPrice);
   
   // Check for errors
   if(rsiHandle == INVALID_HANDLE || maHandle == INVALID_HANDLE || 
      adxHandle == INVALID_HANDLE || cciHandle == INVALID_HANDLE)
   {
      Print("Error creating indicator handles: RSI=", rsiHandle, 
            ", MA=", maHandle, ", ADX=", adxHandle, ", CCI=", cciHandle);
      return(INIT_FAILED);
   }
   
   // Create info panel
   if(inpShowInfoPanel)
   {
      CreateInfoPanel();
   }
   
   // Initialize CSV export
   if(inpExportSignals)
   {
      InitializeCSVExport();
   }
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Initialize CSV export file                                       |
//+------------------------------------------------------------------+
void InitializeCSVExport()
{
   // Create folder if it doesn't exist
   string terminal_data_path = TerminalInfoString(TERMINAL_DATA_PATH);
   string export_path = terminal_data_path + "\\MQL5\\Files\\" + inpExportFolder;
   
   if(!FileIsExist(export_path, FILE_COMMON))
   {
      FolderCreate(export_path, FILE_COMMON);
   }
   
   // Create CSV filename based on symbol, timeframe and date
   string timeframe_str = EnumToString((ENUM_TIMEFRAMES)_Period);
   string current_date = TimeToString(TimeCurrent(), TIME_DATE);
   string filename = StringFormat("%s\\XU_XARDFX_%s_%s_%s.csv", 
                                inpExportFolder, 
                                _Symbol, 
                                timeframe_str,
                                current_date);
   
   csvFileName = filename;
   
   // Create or open CSV file
   exportFileHandle = FileOpen(csvFileName, FILE_CSV|FILE_WRITE|FILE_COMMON, ",");
   
   if(exportFileHandle != INVALID_HANDLE)
   {
      // Write CSV header
      FileWrite(exportFileHandle, "DateTime", "Symbol", "Timeframe", "Price", "Signal", 
                "RSI", "MA", "ADX", "+DI", "-DI", "CCI");
      FileFlush(exportFileHandle);
   }
   else
   {
      Print("Error opening CSV file: ", GetLastError());
   }
}

//+------------------------------------------------------------------+
//| Export signal to CSV file                                        |
//+------------------------------------------------------------------+
void ExportSignalToCSV(datetime time, double price, string signal_type)
{
   if(!inpExportSignals || exportFileHandle == INVALID_HANDLE || time == lastSignalTime)
      return;
      
   // Format datetime
   string time_str = TimeToString(time, TIME_DATE|TIME_SECONDS);
   
   // Write CSV record
   FileWrite(exportFileHandle, 
             time_str, 
             _Symbol, 
             EnumToString((ENUM_TIMEFRAMES)_Period),
             DoubleToString(price, _Digits),
             signal_type,
             DoubleToString(rsiBuffer[ArraySize(rsiBuffer)-1], 2),
             DoubleToString(maBuffer[ArraySize(maBuffer)-1], _Digits),
             DoubleToString(adxBuffer[ArraySize(adxBuffer)-1], 2),
             DoubleToString(plusDIBuffer[ArraySize(plusDIBuffer)-1], 2),
             DoubleToString(minusDIBuffer[ArraySize(minusDIBuffer)-1], 2),
             DoubleToString(cciBuffer[ArraySize(cciBuffer)-1], 2));
             
   FileFlush(exportFileHandle);
   lastSignalTime = time;
}

//+------------------------------------------------------------------+
//| Create text label helper function                                |
//+------------------------------------------------------------------+
void CreateLabel(string name, int x, int y, string text, ENUM_BASE_CORNER corner)
{
   if(!ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0))
   {
      Print("Error creating label ", name, ": ", GetLastError());
      return;
   }
   
   if(!ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x))
      Print("Error setting XDISTANCE for ", name, ": ", GetLastError());
      
   if(!ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y))
      Print("Error setting YDISTANCE for ", name, ": ", GetLastError());
      
   if(!ObjectSetInteger(0, name, OBJPROP_COLOR, inpInfoColor))
      Print("Error setting COLOR for ", name, ": ", GetLastError());
      
   if(!ObjectSetInteger(0, name, OBJPROP_CORNER, corner))
      Print("Error setting CORNER for ", name, ": ", GetLastError());
      
   if(!ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false))
      Print("Error setting SELECTABLE for ", name, ": ", GetLastError());
      
   if(!ObjectSetInteger(0, name, OBJPROP_HIDDEN, true))
      Print("Error setting HIDDEN for ", name, ": ", GetLastError());
      
   if(!ObjectSetString(0, name, OBJPROP_TEXT, text))
      Print("Error setting TEXT for ", name, ": ", GetLastError());
      
   if(!ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 9))
      Print("Error setting FONTSIZE for ", name, ": ", GetLastError());
}

//+------------------------------------------------------------------+
//| Create info panel objects                                        |
//+------------------------------------------------------------------+
void CreateInfoPanel()
{
   int width = 240;
   int height = 160;
   
   // Create background
   if(!ObjectCreate(0, infoBackground, OBJ_RECTANGLE_LABEL, 0, 0, 0))
   {
      Print("Error creating background object: ", GetLastError());
      return;
   }
   
   // Set background properties
   if(!ObjectSetInteger(0, infoBackground, OBJPROP_XDISTANCE, inpInfoX))
      Print("Error setting XDISTANCE: ", GetLastError());
      
   if(!ObjectSetInteger(0, infoBackground, OBJPROP_YDISTANCE, inpInfoY))
      Print("Error setting YDISTANCE: ", GetLastError());
      
   if(!ObjectSetInteger(0, infoBackground, OBJPROP_XSIZE, width))
      Print("Error setting XSIZE: ", GetLastError());
      
   if(!ObjectSetInteger(0, infoBackground, OBJPROP_YSIZE, height))
      Print("Error setting YSIZE: ", GetLastError());
      
   if(!ObjectSetInteger(0, infoBackground, OBJPROP_BGCOLOR, inpInfoBgColor))
      Print("Error setting BGCOLOR: ", GetLastError());
      
   if(!ObjectSetInteger(0, infoBackground, OBJPROP_BORDER_TYPE, BORDER_FLAT))
      Print("Error setting BORDER_TYPE: ", GetLastError());
      
   if(!ObjectSetInteger(0, infoBackground, OBJPROP_CORNER, (int)inpInfoCorner))
      Print("Error setting CORNER: ", GetLastError());
      
   if(!ObjectSetInteger(0, infoBackground, OBJPROP_SELECTABLE, false))
      Print("Error setting SELECTABLE: ", GetLastError());
      
   if(!ObjectSetInteger(0, infoBackground, OBJPROP_HIDDEN, true))
      Print("Error setting HIDDEN: ", GetLastError());
   
   // Create text labels
   CreateLabel(infoRSI, inpInfoX + 10, inpInfoY + 30, "RSI(" + string(inpRSIPeriod) + "): ", (ENUM_BASE_CORNER)inpInfoCorner);
   CreateLabel(infoMA, inpInfoX + 10, inpInfoY + 55, "MA(" + string(inpMAPeriod) + "): ", (ENUM_BASE_CORNER)inpInfoCorner);
   CreateLabel(infoADX, inpInfoX + 10, inpInfoY + 80, "ADX(" + string(inpADXPeriod) + "): ", (ENUM_BASE_CORNER)inpInfoCorner);
   CreateLabel(infoCCI, inpInfoX + 10, inpInfoY + 105, "CCI(" + string(inpCCIPeriod) + "): ", (ENUM_BASE_CORNER)inpInfoCorner);
   CreateLabel(infoSignal, inpInfoX + 10, inpInfoY + 130, "Signal: NEUTRAL", (ENUM_BASE_CORNER)inpInfoCorner);
   
   // Create title
   string titleObj = infoObjPrefix + "Title";
   if(!ObjectCreate(0, titleObj, OBJ_LABEL, 0, 0, 0))
   {
      Print("Error creating title object: ", GetLastError());
      return;
   }
   
   if(!ObjectSetInteger(0, titleObj, OBJPROP_XDISTANCE, inpInfoX + 10))
      Print("Error setting title XDISTANCE: ", GetLastError());
      
   if(!ObjectSetInteger(0, titleObj, OBJPROP_YDISTANCE, inpInfoY + 5))
      Print("Error setting title YDISTANCE: ", GetLastError());
      
   if(!ObjectSetInteger(0, titleObj, OBJPROP_COLOR, inpInfoColor))
      Print("Error setting title COLOR: ", GetLastError());
      
   if(!ObjectSetInteger(0, titleObj, OBJPROP_CORNER, (int)inpInfoCorner))
      Print("Error setting title CORNER: ", GetLastError());
      
   if(!ObjectSetInteger(0, titleObj, OBJPROP_SELECTABLE, false))
      Print("Error setting title SELECTABLE: ", GetLastError());
      
   if(!ObjectSetInteger(0, titleObj, OBJPROP_HIDDEN, true))
      Print("Error setting title HIDDEN: ", GetLastError());
      
   if(!ObjectSetString(0, titleObj, OBJPROP_TEXT, "XU XARDFX INDICATOR"))
      Print("Error setting title TEXT: ", GetLastError());
      
   if(!ObjectSetInteger(0, titleObj, OBJPROP_FONTSIZE, 10))
      Print("Error setting title FONTSIZE: ", GetLastError());
      
   if(!ObjectSetString(0, titleObj, OBJPROP_FONT, "Arial Bold"))
      Print("Error setting title FONT: ", GetLastError());
}

//+------------------------------------------------------------------+
//| Update info panel values                                         |
//+------------------------------------------------------------------+
void UpdateInfoPanel(int index)
{
   if(!inpShowInfoPanel) return;
   
   // Format values with proper precision
   string rsiValue = DoubleToString(rsiBuffer[index], 2);
   string maValue = DoubleToString(maBuffer[index], _Digits);
   string adxValue = DoubleToString(adxBuffer[index], 2);
   string cciValue = DoubleToString(cciBuffer[index], 2);
   
   // Set colors based on values
   color rsiColor = rsiBuffer[index] > inpRSILevel ? inpBuyColor : inpSellColor;
   color adxColor = adxBuffer[index] > inpADXLevel ? clrLime : clrGray;
   color cciColor = inpUseCCI ? (cciBuffer[index] > inpCCILevel ? inpBuyColor : 
                  (cciBuffer[index] < -inpCCILevel ? inpSellColor : clrGray)) : clrGray;
   
   // Update labels
   ObjectSetString(0, infoRSI, OBJPROP_TEXT, "RSI(" + string(inpRSIPeriod) + "): " + rsiValue);
   ObjectSetInteger(0, infoRSI, OBJPROP_COLOR, rsiColor);
   
   ObjectSetString(0, infoMA, OBJPROP_TEXT, "MA(" + string(inpMAPeriod) + "): " + maValue);
   
   ObjectSetString(0, infoADX, OBJPROP_TEXT, "ADX(" + string(inpADXPeriod) + "): " + adxValue);
   ObjectSetInteger(0, infoADX, OBJPROP_COLOR, adxColor);
   
   ObjectSetString(0, infoCCI, OBJPROP_TEXT, "CCI(" + string(inpCCIPeriod) + "): " + cciValue);
   ObjectSetInteger(0, infoCCI, OBJPROP_COLOR, cciColor);
   
   // Update signal
   ObjectSetString(0, infoSignal, OBJPROP_TEXT, "Signal: " + currentSignal);
   color signalColor = clrGray;
   if(currentSignal == "BUY") signalColor = inpBuyColor;
   else if(currentSignal == "SELL") signalColor = inpSellColor;
   else if(currentSignal == "EXIT BUY") signalColor = inpExitBuyColor;
   else if(currentSignal == "EXIT SELL") signalColor = inpExitSellColor;
   
   ObjectSetInteger(0, infoSignal, OBJPROP_COLOR, signalColor);
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Release indicator handles
   if(rsiHandle != INVALID_HANDLE) IndicatorRelease(rsiHandle);
   if(maHandle != INVALID_HANDLE) IndicatorRelease(maHandle);
   if(adxHandle != INVALID_HANDLE) IndicatorRelease(adxHandle);
   if(cciHandle != INVALID_HANDLE) IndicatorRelease(cciHandle);
   
   // Delete info panel objects
   if(inpShowInfoPanel)
   {
      ObjectDelete(0, infoBackground);
      ObjectDelete(0, infoRSI);
      ObjectDelete(0, infoMA);
      ObjectDelete(0, infoADX);
      ObjectDelete(0, infoCCI);
      ObjectDelete(0, infoSignal);
      ObjectDelete(0, infoObjPrefix + "Title");
   }
   
   // Close export file
   if(exportFileHandle != INVALID_HANDLE)
   {
      FileClose(exportFileHandle);
      exportFileHandle = INVALID_HANDLE;
   }
}

//+------------------------------------------------------------------+
//| Function to send alerts                                          |
//+------------------------------------------------------------------+
void SendAlert(string signal, string message)
{
   // Check if we should send this type of alert
   bool shouldAlert = false;
   
   if(inpAlertMode == ALERT_ALL)
      shouldAlert = true;
   else if(inpAlertMode == ALERT_ENTRY_ONLY && (signal == "BUY" || signal == "SELL"))
      shouldAlert = true;
   else if(inpAlertMode == ALERT_EXIT_ONLY && (signal == "EXIT BUY" || signal == "EXIT SELL"))
      shouldAlert = true;
   
   if(!shouldAlert)
      return;
   
   // Send alert
   if(inpAlertMode != ALERT_NONE)
      Alert(message);
   
   // Play sound
   if(inpSoundAlert) 
      PlaySound(inpSoundFile);
   
   // Send push notification
   if(inpPushNotifications)
      SendNotification(message);
   
   // Send email
   if(inpEmailAlerts)
      SendMail("XU XARDFX Signal: " + _Symbol, message);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   // Check for minimum bars
   if(rates_total < MathMax(inpRSIPeriod, MathMax(inpMAPeriod, inpADXPeriod)) + 1)
      return(0);
   
   // Calculate starting point
   int start;
   if(prev_calculated == 0)
   {
      start = MathMax(inpRSIPeriod, MathMax(inpMAPeriod, inpADXPeriod)) + 1;
      
      // Initialize buffers
      ArrayInitialize(buyBuffer, EMPTY_VALUE);
      ArrayInitialize(sellBuffer, EMPTY_VALUE);
      ArrayInitialize(buyExitBuffer, EMPTY_VALUE);
      ArrayInitialize(sellExitBuffer, EMPTY_VALUE);
   }
   else
   {
      start = prev_calculated - 1;
   }
   
   // Copy indicator values
   int copied = 0;
   
   // Copy RSI values
   copied = CopyBuffer(rsiHandle, 0, 0, rates_total, rsiBuffer);
   if(copied <= 0) 
   {
      Print("Error copying RSI values: ", GetLastError());
      return(0);
   }
   
   // Copy MA values
   copied = CopyBuffer(maHandle, 0, 0, rates_total, maBuffer);
   if(copied <= 0) 
   {
      Print("Error copying MA values: ", GetLastError());
      return(0);
   }
   
   // Copy ADX values
   copied = CopyBuffer(adxHandle, 0, 0, rates_total, adxBuffer);
   if(copied <= 0) 
   {
      Print("Error copying ADX values: ", GetLastError());
      return(0);
   }
   
   // Copy +DI values
   copied = CopyBuffer(adxHandle, 1, 0, rates_total, plusDIBuffer);
   if(copied <= 0) 
   {
      Print("Error copying +DI values: ", GetLastError());
      return(0);
   }
   
   // Copy -DI values
   copied = CopyBuffer(adxHandle, 2, 0, rates_total, minusDIBuffer);
   if(copied <= 0) 
   {
      Print("Error copying -DI values: ", GetLastError());
      return(0);
   }
   
   // Copy CCI values
   copied = CopyBuffer(cciHandle, 0, 0, rates_total, cciBuffer);
   if(copied <= 0) 
   {
      Print("Error copying CCI values: ", GetLastError());
      return(0);
   }
   
   // Reset current signal
   currentSignal = "NEUTRAL";
   
   // Main calculation loop
   for(int i = start; i < rates_total; i++)
   {
      // Default values
      buyBuffer[i] = EMPTY_VALUE;
      sellBuffer[i] = EMPTY_VALUE;
      buyExitBuffer[i] = EMPTY_VALUE;
      sellExitBuffer[i] = EMPTY_VALUE;
      
      // Check ADX filter
      bool adxUpTrend = adxBuffer[i] > inpADXLevel && plusDIBuffer[i] > minusDIBuffer[i];
      bool adxDownTrend = adxBuffer[i] > inpADXLevel && plusDIBuffer[i] < minusDIBuffer[i];
      
      // Check CCI filter
      bool cciUpOK = inpUseCCI ? cciBuffer[i] > inpCCILevel : true;
      bool cciDownOK = inpUseCCI ? cciBuffer[i] < -inpCCILevel : true;
      
      // Buy signal
      if(rsiBuffer[i] > inpRSILevel && close[i] > maBuffer[i] && adxUpTrend && cciUpOK &&
         (rsiBuffer[i-1] <= inpRSILevel || close[i-1] <= maBuffer[i-1]))
      {
         buyBuffer[i] = low[i] - _Point * 10;
         
         if(i == rates_total - 1)
         {
            currentSignal = "BUY";
            SendAlert("BUY", _Symbol + ": XARDFX Buy Signal");
            if(inpExportSignals)
               ExportSignalToCSV(time[i], close[i], "BUY");
         }
      }
      
      // Sell signal
      if(rsiBuffer[i] < inpRSILevel && close[i] < maBuffer[i] && adxDownTrend && cciDownOK &&
         (rsiBuffer[i-1] >= inpRSILevel || close[i-1] >= maBuffer[i-1]))
      {
         sellBuffer[i] = high[i] + _Point * 10;
         
         if(i == rates_total - 1)
         {
            currentSignal = "SELL";
            SendAlert("SELL", _Symbol + ": XARDFX Sell Signal");
            if(inpExportSignals)
               ExportSignalToCSV(time[i], close[i], "SELL");
         }
      }
      
      // Buy exit
      if(inpShowBuyExit && rsiBuffer[i] < inpRSILevel && rsiBuffer[i-1] >= inpRSILevel)
      {
         buyExitBuffer[i] = high[i] + _Point * 5;
         
         if(i == rates_total - 1)
         {
            currentSignal = "EXIT BUY";
            SendAlert("EXIT BUY", _Symbol + ": XARDFX Exit Buy Signal");
            if(inpExportSignals)
               ExportSignalToCSV(time[i], close[i], "EXIT BUY");
         }
      }
      
      // Sell exit
      if(inpShowSellExit && rsiBuffer[i] > inpRSILevel && rsiBuffer[i-1] <= inpRSILevel)
      {
         sellExitBuffer[i] = low[i] - _Point * 5;
         
         if(i == rates_total - 1)
         {
            currentSignal = "EXIT SELL";
            SendAlert("EXIT SELL", _Symbol + ": XARDFX Exit Sell Signal");
            if(inpExportSignals)
               ExportSignalToCSV(time[i], close[i], "EXIT SELL");
         }
      }
      
      // Update trend state variables
      isUptrend = rsiBuffer[i] > inpRSILevel && close[i] > maBuffer[i] && adxUpTrend;
      isDowntrend = rsiBuffer[i] < inpRSILevel && close[i] < maBuffer[i] && adxDownTrend;
      
      // Update info panel on the last bar
      if(i == rates_total - 1)
      {
         UpdateInfoPanel(i);
      }
   }
   
   // Return value of prev_calculated for next call
   return(rates_total);
}
//+------------------------------------------------------------------+ 