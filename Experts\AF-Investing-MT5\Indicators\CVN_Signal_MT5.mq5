//+------------------------------------------------------------------+
//|                                                  CVN_Signal_MT5.mq5 |
//|                                Copyright 2023, AFSID-Group.Cv       |
//|                                         https://afs-id.com          |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, AFSID-Group.Cv"
#property link      "https://afs-id.com"
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 4
#property indicator_plots   4

// Определение свойств для графиков индикатора
#property indicator_label1  "Buy Signal"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrLime
#property indicator_style1  STYLE_SOLID
#property indicator_width1  1

#property indicator_label2  "Sell Signal"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrRed
#property indicator_style2  STYLE_SOLID
#property indicator_width2  1

#property indicator_label3  "Exit Buy"
#property indicator_type3   DRAW_ARROW
#property indicator_color3  clrYellow
#property indicator_style3  STYLE_SOLID
#property indicator_width3  1

#property indicator_label4  "Exit Sell"
#property indicator_type4   DRAW_ARROW
#property indicator_color4  clrYellow
#property indicator_style4  STYLE_SOLID
#property indicator_width4  1

// Буферы индикатора
double BuyBuffer[];
double SellBuffer[];
double ExitBuyBuffer[];
double ExitSellBuffer[];

// Массивы для хранения значений индикаторов
double RSIValues[];
double StochMainValues[];
double StochSignalValues[];
double BBUpper[];
double BBLower[];

// Параметры индикатора
input int    BandsPeriod = 20;          // Период Bollinger Bands
input double BandsDeviation = 2.0;      // Отклонение Bollinger Bands
input int    RSIPeriod = 14;            // Период RSI
input int    RSIBuyLevel = 30;          // Уровень перепроданности RSI
input int    RSISellLevel = 70;         // Уровень перекупленности RSI
input int    StochKPeriod = 8;          // K период стохастика
input int    StochDPeriod = 3;          // D период стохастика
input int    StochSlowing = 3;          // Замедление стохастика
input int    StochBuyLevel = 20;        // Уровень перепроданности Stochastic
input int    StochSellLevel = 80;       // Уровень перекупленности Stochastic

// Коды для стрелок
input uchar  BuyArrow = 233;            // Символ стрелки покупки
input uchar  SellArrow = 234;           // Символ стрелки продажи
input uchar  ExitArrow = 251;           // Символ стрелки выхода

// Хэндлы индикаторов
int BandsHandle;
int RSIHandle;
int StochHandle;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   // Настройка буферов
   SetIndexBuffer(0, BuyBuffer, INDICATOR_DATA);
   SetIndexBuffer(1, SellBuffer, INDICATOR_DATA);
   SetIndexBuffer(2, ExitBuyBuffer, INDICATOR_DATA);
   SetIndexBuffer(3, ExitSellBuffer, INDICATOR_DATA);
   
   // Установка кодов стрелок
   PlotIndexSetInteger(0, PLOT_ARROW, BuyArrow);
   PlotIndexSetInteger(1, PLOT_ARROW, SellArrow);
   PlotIndexSetInteger(2, PLOT_ARROW, ExitArrow);
   PlotIndexSetInteger(3, PLOT_ARROW, ExitArrow);
   
   // Смещение стрелок от цены
   PlotIndexSetInteger(0, PLOT_ARROW_SHIFT, 5);
   PlotIndexSetInteger(1, PLOT_ARROW_SHIFT, -5);
   PlotIndexSetInteger(2, PLOT_ARROW_SHIFT, 5);
   PlotIndexSetInteger(3, PLOT_ARROW_SHIFT, -5);
   
   // Установка пустого значения
   PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, EMPTY_VALUE);
   PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, EMPTY_VALUE);
   PlotIndexSetDouble(2, PLOT_EMPTY_VALUE, EMPTY_VALUE);
   PlotIndexSetDouble(3, PLOT_EMPTY_VALUE, EMPTY_VALUE);
   
   // Инициализация индикаторов
   BandsHandle = iBands(_Symbol, PERIOD_CURRENT, BandsPeriod, 0, BandsDeviation, PRICE_CLOSE);
   RSIHandle = iRSI(_Symbol, PERIOD_CURRENT, RSIPeriod, PRICE_CLOSE);
   StochHandle = iStochastic(_Symbol, PERIOD_CURRENT, StochKPeriod, StochDPeriod, StochSlowing, MODE_SMA, STO_LOWHIGH);
   
   if(BandsHandle == INVALID_HANDLE || RSIHandle == INVALID_HANDLE || StochHandle == INVALID_HANDLE)
   {
      Print("Ошибка создания хэндлов индикаторов");
      return(INIT_FAILED);
   }
   
   // Установка короткого имени для индикатора
   IndicatorSetString(INDICATOR_SHORTNAME, "CVN Signal");
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Удаление объектов
   ObjectsDeleteAll(0, "CVN_Signal_");
   
   // Освобождение хэндлов индикаторов
   if(BandsHandle != INVALID_HANDLE)
      IndicatorRelease(BandsHandle);
   if(RSIHandle != INVALID_HANDLE)
      IndicatorRelease(RSIHandle);
   if(StochHandle != INVALID_HANDLE)
      IndicatorRelease(StochHandle);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   // Проверяем достаточность данных
   if(rates_total < 2) return(0);
   
   // Проверяем хэндлы индикаторов
   if(BandsHandle == INVALID_HANDLE || RSIHandle == INVALID_HANDLE || StochHandle == INVALID_HANDLE)
   {
      Print("Ошибка: недействительные хэндлы индикаторов");
      return(0);
   }
   
   // Определяем количество баров для пересчета
   int limit = rates_total - prev_calculated;
   if(limit > rates_total - 1) limit = rates_total - 1;
   
   // Инициализация массивов
   ArraySetAsSeries(RSIValues, true);
   ArraySetAsSeries(StochMainValues, true);
   ArraySetAsSeries(StochSignalValues, true);
   ArraySetAsSeries(BBUpper, true);
   ArraySetAsSeries(BBLower, true);
   
   // Устанавливаем размеры массивов
   ArrayResize(RSIValues, rates_total);
   ArrayResize(StochMainValues, rates_total);
   ArrayResize(StochSignalValues, rates_total);
   ArrayResize(BBUpper, rates_total);
   ArrayResize(BBLower, rates_total);
   
   // Проверяем успешность инициализации массивов
   if(ArraySize(RSIValues) < rates_total || 
      ArraySize(StochMainValues) < rates_total || 
      ArraySize(StochSignalValues) < rates_total || 
      ArraySize(BBUpper) < rates_total || 
      ArraySize(BBLower) < rates_total)
   {
      Print("Ошибка инициализации массивов");
      return(0);
   }
   
   // Копируем данные из существующих хэндлов
   int copied = CopyBuffer(RSIHandle, 0, 0, rates_total, RSIValues);
   if(copied <= 0)
   {
      Print("Ошибка копирования данных RSI");
      return(0);
   }
   
   copied = CopyBuffer(StochHandle, 0, 0, rates_total, StochMainValues);
   if(copied <= 0)
   {
      Print("Ошибка копирования данных Stochastic Main");
      return(0);
   }
   
   copied = CopyBuffer(StochHandle, 1, 0, rates_total, StochSignalValues);
   if(copied <= 0)
   {
      Print("Ошибка копирования данных Stochastic Signal");
      return(0);
   }
   
   copied = CopyBuffer(BandsHandle, 1, 0, rates_total, BBUpper);
   if(copied <= 0)
   {
      Print("Ошибка копирования данных BB Upper");
      return(0);
   }
   
   copied = CopyBuffer(BandsHandle, 2, 0, rates_total, BBLower);
   if(copied <= 0)
   {
      Print("Ошибка копирования данных BB Lower");
      return(0);
   }
   
   // Обработка сигналов
   for(int i = limit; i >= 0; i--)
   {
      // Проверяем, что у нас есть все необходимые данные
      if(i >= ArraySize(RSIValues) || i >= ArraySize(StochMainValues) || 
         i >= ArraySize(BBUpper) || i >= ArraySize(BBLower))
         continue;
         
      // Очищаем предыдущие значения
      BuyBuffer[i] = EMPTY_VALUE;
      SellBuffer[i] = EMPTY_VALUE;
      ExitBuyBuffer[i] = EMPTY_VALUE;
      ExitSellBuffer[i] = EMPTY_VALUE;
         
      // Сигналы на покупку
      if(close[i] < BBLower[i] &&                    // Цена ниже нижней полосы Боллинджера
         RSIValues[i] < RSIBuyLevel &&               // RSI показывает перепроданность
         StochMainValues[i] < StochBuyLevel)         // Стохастик показывает перепроданность
      {
         BuyBuffer[i] = low[i] - 5 * _Point;
      }
      
      // Сигналы на продажу
      if(close[i] > BBUpper[i] &&                    // Цена выше верхней полосы Боллинджера
         RSIValues[i] > RSISellLevel &&              // RSI показывает перекупленность
         StochMainValues[i] > StochSellLevel)        // Стохастик показывает перекупленность
      {
         SellBuffer[i] = high[i] + 5 * _Point;
      }
      
      // Сигналы на выход - добавляем проверку на границы массива
      if(i < rates_total - 1 && i + 1 < ArraySize(RSIValues)) // Проверяем, что не последний бар и есть следующий элемент
      {
         if(RSIValues[i] > 50 && RSIValues[i+1] <= 50)  // Пересечение RSI уровня 50 снизу вверх
         {
            ExitSellBuffer[i] = high[i] + 3 * _Point;
         }
         if(RSIValues[i] < 50 && RSIValues[i+1] >= 50)  // Пересечение RSI уровня 50 сверху вниз
         {
            ExitBuyBuffer[i] = low[i] - 3 * _Point;
         }
      }
   }
   
   return(rates_total);
} 