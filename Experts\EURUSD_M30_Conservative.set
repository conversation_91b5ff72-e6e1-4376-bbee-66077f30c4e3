#
# Настройки SniperEA-V3.11_UNI для EURUSD M30
# Консервативная стратегия (минимальный риск)
#

# Общие настройки
g_comment=EURUSD_M30_Conservative
magic=12301
IndicatorName=super-arrow-indicator
BuyBuffer=0
SellBuffer=1

# Настройки управления капиталом
g_lots=0.01
lotsoptimized=true
risk=1.0
minlot=0.01
maxlot=1.0
lotdigits=2

# Настройки управления ордерами
oppositeclose=true
reversesignals=false
maxtrades=3
tradesperbar=1
lstoploss=50
sstoploss=50
ltakeprofit=100
ltakeprofit2=150
ltakeprofit3=200
ltakeprofit4=0
ltakeprofit5=0
stakeprofit=100
stakeprofit2=150
stakeprofit3=200
stakeprofit4=0
stakeprofit5=0
trailingstart=50
trailing_stop_pts=30
trailingstep=5
breakevengain=30
breakeven_level=5
g_expiration=0
slippage=5
maxspread=5

# Расширенные настройки управления ордерами
open5orders=false
closeonbadconditions=true
closeonarrows=true
autostop=true
autostopmargin=5
minimumstop=300
maximumstop=500
notradeovermaxstop=true
trailonsniperstop=true

# Логика входа
onlycross=true
usesniper=true
SniperPeriod=35
SniperType=3
arrow_shift=30
Alerts=0
usestop=true
usetrenda=true
usetrendb=true
Sniper=0.01
SniperSS=0.1
SniperP=7
AlertsEnabled=false
g_shift=1

# Настройки фильтра
usefilter=true
timeframe=16384
onlycrosstf=true
usesnipertf=true
SniperPeriodtf=35
SniperTypetf=3
arrow_shifttf=30
Alertstf=0
usestoptf=true
usetrendatf=true
usetrendbtf=true
Snipertf=0.01
SniperSStf=0.1
SniperPtf=7
AlertsEnabledtf=false

# Дополнительные функции
samedirection=true
minclosetime=true
mincloseminutes=120

# Фильтр времени
gmtshift=2
filter=true
trade_start_hour=8
trade_end_hour=22
tradesunday=false
fridayfilter=true
fridayend=12

# Настройки трейлинга
DetailedLogging=false

# ... existing code ... 