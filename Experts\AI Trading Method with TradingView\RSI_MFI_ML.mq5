//+------------------------------------------------------------------+
//|                                              RSI_MFI_ML.mq5 |
//|                                         Copyright ChartPrime |
//|                                                              |
//+------------------------------------------------------------------+
#property copyright "ChartPrime"
#property link      ""
#property version   "1.00"
#property indicator_separate_window
#property indicator_buffers 5
#property indicator_plots   3

//--- plot ML
#property indicator_label1  "ML"
#property indicator_type1   DRAW_LINE
#property indicator_color1  clrBlue
#property indicator_style1  STYLE_SOLID
#property indicator_width1  2

//--- plot ML_Color
#property indicator_label2  "ML_Color"
#property indicator_type2   DRAW_COLOR
#property indicator_color2  clrGreen,clrRed
#property indicator_style2  STYLE_SOLID
#property indicator_width2  2

//--- plot ML_Signal
#property indicator_label3  "ML_Signal"
#property indicator_type3   DRAW_LINE
#property indicator_color3  clrMagenta
#property indicator_style3  STYLE_SOLID
#property indicator_width3  2

//--- input parameters
input int      RSI_Period = 14;            // Период RSI
input int      MFI_Period = 14;            // Период MFI
input int      ML_LookbackPeriod = 50;     // Период для ML
input int      LorentzianNeighbors = 8;    // Количество соседей
input double   BandWidth = 0.5;            // Ширина полосы

//--- indicator buffers
double         MLBuffer[];
double         MLColorBuffer[];
double         MLSignalBuffer[];
double         RSIBuffer[];
double         MFIBuffer[];

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- indicator buffers mapping
   SetIndexBuffer(0, MLBuffer, INDICATOR_DATA);
   SetIndexBuffer(1, MLColorBuffer, INDICATOR_COLOR_INDEX);
   SetIndexBuffer(2, MLSignalBuffer, INDICATOR_DATA);
   SetIndexBuffer(3, RSIBuffer, INDICATOR_CALCULATIONS);
   SetIndexBuffer(4, MFIBuffer, INDICATOR_CALCULATIONS);
   
   //--- установка точности отображения
   IndicatorSetInteger(INDICATOR_DIGITS, 2);
   
   //--- установка пустого значения
   PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, 0.0);
   PlotIndexSetDouble(2, PLOT_EMPTY_VALUE, 0.0);
   
   //--- имена для отображения в окне данных и всплывающей подсказке
   IndicatorSetString(INDICATOR_SHORTNAME, "RSI MFI ML Indicator");
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Расчет RSI                                                       |
//+------------------------------------------------------------------+
void CalculateRSI(const double &price[], double &rsi_buffer[])
{
   double pos = 0, neg = 0;
   
   for(int i = 1; i < ArraySize(price); i++)
   {
      double diff = price[i] - price[i-1];
      
      if(diff > 0)
         pos = (pos * (RSI_Period - 1) + diff) / RSI_Period;
      else
         neg = (neg * (RSI_Period - 1) - diff) / RSI_Period;
         
      if(i >= RSI_Period)
      {
         if(neg == 0)
            rsi_buffer[i] = 100;
         else
            rsi_buffer[i] = 100 - (100 / (1 + pos/neg));
      }
   }
}

//+------------------------------------------------------------------+
//| Расчет MFI                                                       |
//+------------------------------------------------------------------+
void CalculateMFI(const double &price[], const long &volume[], double &mfi_buffer[])
{
   double pos_mf = 0, neg_mf = 0;
   double typical_price[];
   ArrayResize(typical_price, ArraySize(price));
   
   for(int i = 1; i < ArraySize(price); i++)
   {
      typical_price[i] = price[i];
      double money_flow = typical_price[i] * (double)volume[i];
      
      if(typical_price[i] > typical_price[i-1])
         pos_mf = (pos_mf * (MFI_Period - 1) + money_flow) / MFI_Period;
      else
         neg_mf = (neg_mf * (MFI_Period - 1) + money_flow) / MFI_Period;
         
      if(i >= MFI_Period)
      {
         if(neg_mf == 0)
            mfi_buffer[i] = 100;
         else
            mfi_buffer[i] = 100 - (100 / (1 + pos_mf/neg_mf));
      }
   }
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   //--- проверка количества баров на достаточность
   if(rates_total < ML_LookbackPeriod) return(0);
   
   //--- расчет начинаем с prev_calculated
   int start = prev_calculated > 0 ? prev_calculated - 1 : 0;
   
   //--- расчет RSI и MFI
   CalculateRSI(close, RSIBuffer);
   CalculateMFI(close, volume, MFIBuffer);
   
   //--- основной цикл расчета
   for(int i = start; i < rates_total; i++)
   {
      if(i < ML_LookbackPeriod) continue;
      
      // Простая логика на основе RSI и MFI
      double signal = 0;
      
      if(RSIBuffer[i] < 30 && MFIBuffer[i] < 20)
         signal = 1;  // Сигнал на покупку
      else if(RSIBuffer[i] > 70 && MFIBuffer[i] > 80)
         signal = -1; // Сигнал на продажу
         
      MLBuffer[i] = (RSIBuffer[i] + MFIBuffer[i]) / 2;
      MLColorBuffer[i] = signal != 0 ? 1 : 0;
      MLSignalBuffer[i] = signal;
   }
   
   //--- return value of prev_calculated for next call
   return(rates_total);
} 