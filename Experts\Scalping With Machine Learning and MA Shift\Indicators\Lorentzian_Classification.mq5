//+------------------------------------------------------------------+
//|                                   Lorentzian_Classification.mq5 |
//|                                                       jdehorty  |
//|                                        Перенесено на MT5        |
//+------------------------------------------------------------------+
#property copyright "jdehorty"
#property link      ""
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 7
#property indicator_plots   4

#property indicator_label1  "Buy Signal"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrLime
#property indicator_style1  STYLE_SOLID
#property indicator_width1  2

#property indicator_label2  "Sell Signal"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrRed
#property indicator_style2  STYLE_SOLID
#property indicator_width2  2

#property indicator_label3  "Exit Buy Signal"
#property indicator_type3   DRAW_ARROW
#property indicator_color3  clrGreen
#property indicator_style3  STYLE_SOLID
#property indicator_width3  1

#property indicator_label4  "Exit Sell Signal"
#property indicator_type4   DRAW_ARROW
#property indicator_color4  clrMaroon
#property indicator_style4  STYLE_SOLID
#property indicator_width4  1

// Включение пользовательских классов
#include <MLExtensions.mqh>
#include <KernelFunctions.mqh>

// Буферы индикатора
double BuyBuffer[];
double SellBuffer[];
double ExitBuyBuffer[];
double ExitSellBuffer[];
double PredictionBuffer[];
double SignalBuffer[];
double KernelEstimateBuffer[];

// Константы для направления
#define DIRECTION_LONG 1
#define DIRECTION_SHORT -1
#define DIRECTION_NEUTRAL 0

// Входные параметры
input group "Общие настройки";
input ENUM_APPLIED_PRICE SourcePrice = PRICE_CLOSE;  // Источник цены
input int NeighborsCount = 8;                      // Количество соседей
input int MaxBarsBack = 2000;                      // Максимальное количество баров
input int FeatureCount = 5;                        // Количество признаков
input int ColorCompression = 1;                    // Сжатие цвета
input bool ShowDefaultExits = false;               // Показывать стандартные выходы
input bool UseDynamicExits = false;                // Использовать динамические выходы

input group "Фильтры";
input bool UseVolatilityFilter = true;             // Использовать фильтр волатильности
input bool UseRegimeFilter = true;                 // Использовать фильтр режима
input bool UseAdxFilter = false;                   // Использовать фильтр ADX
input double RegimeThreshold = -0.1;               // Порог для режима
input int AdxThreshold = 20;                       // Порог для ADX

input bool UseEmaFilter = false;                   // Использовать фильтр EMA
input int EmaPeriod = 200;                         // Период EMA

input bool UseSmaFilter = false;                   // Использовать фильтр SMA
input int SmaPeriod = 200;                         // Период SMA

input group "Настройки ядра";
input bool UseKernelFilter = true;                 // Торговать с ядром
input bool ShowKernelEstimate = true;              // Показывать оценку ядра
input bool UseKernelSmoothing = false;             // Улучшить сглаживание ядра
input int LookbackWindow = 8;                      // Окно обзора
input double RelativeWeighting = 8.0;              // Относительное взвешивание
input int RegressionLevel = 25;                    // Уровень регрессии
input int LagValue = 2;                            // Задержка

input group "Признаки";
input string Feature1 = "RSI";                     // Признак 1
input int Feature1ParamA = 14;                     // Параметр A признака 1
input int Feature1ParamB = 1;                      // Параметр B признака 1

input string Feature2 = "WT";                      // Признак 2
input int Feature2ParamA = 10;                     // Параметр A признака 2
input int Feature2ParamB = 11;                     // Параметр B признака 2

input string Feature3 = "CCI";                     // Признак 3
input int Feature3ParamA = 20;                     // Параметр A признака 3
input int Feature3ParamB = 1;                      // Параметр B признака 3

input string Feature4 = "ADX";                     // Признак 4
input int Feature4ParamA = 20;                     // Параметр A признака 4
input int Feature4ParamB = 2;                      // Параметр B признака 4

input string Feature5 = "RSI";                     // Признак 5
input int Feature5ParamA = 9;                      // Параметр A признака 5
input int Feature5ParamB = 1;                      // Параметр B признака 5

input group "Отображение";
input bool ShowBarColors = true;                   // Показывать цвета баров
input bool ShowBarPredictions = true;              // Показывать значения прогнозов
input bool UseAtrOffset = false;                   // Использовать смещение ATR
input double BarPredictionsOffset = 0;             // Смещение прогнозов баров

// Глобальные переменные и массивы
int first_bar_index = 0;
double lastDistance = -1.0;
datetime last_signal_time = 0;

// Массивы для хранения данных признаков
double f1_array[];
double f2_array[];
double f3_array[];
double f4_array[];
double f5_array[];

// Массивы для алгоритма KNN
double distances[];
double predictions[];
int y_train_array[];

// Переменные для хранения состояний
int last_signal = 0;
int bars_held = 0;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   // Инициализация буферов
   SetIndexBuffer(0, BuyBuffer, INDICATOR_DATA);
   SetIndexBuffer(1, SellBuffer, INDICATOR_DATA);
   SetIndexBuffer(2, ExitBuyBuffer, INDICATOR_DATA);
   SetIndexBuffer(3, ExitSellBuffer, INDICATOR_DATA);
   SetIndexBuffer(4, PredictionBuffer, INDICATOR_CALCULATIONS);
   SetIndexBuffer(5, SignalBuffer, INDICATOR_CALCULATIONS);
   SetIndexBuffer(6, KernelEstimateBuffer, INDICATOR_CALCULATIONS);
   
   // Установка стрелок для сигналов
   PlotIndexSetInteger(0, PLOT_ARROW, 233);
   PlotIndexSetInteger(1, PLOT_ARROW, 234);
   PlotIndexSetInteger(2, PLOT_ARROW, 251);
   PlotIndexSetInteger(3, PLOT_ARROW, 251);
   
   // Инициализация массивов
   ArrayInitialize(f1_array, 0);
   ArrayInitialize(f2_array, 0);
   ArrayInitialize(f3_array, 0);
   ArrayInitialize(f4_array, 0);
   ArrayInitialize(f5_array, 0);
   
   ArrayInitialize(distances, 0);
   ArrayInitialize(predictions, 0);
   ArrayInitialize(y_train_array, 0);
   
   // Установка названия индикатора
   IndicatorSetString(INDICATOR_SHORTNAME, "Lorentzian Classification");
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Функция для выбора и расчета признака                            |
//+------------------------------------------------------------------+
double calculate_feature(string feature_name, double &close[], double &high[], double &low[], double &hlc3[], int param_a, int param_b)
{
   if(feature_name == "RSI")
      return n_rsi(close, param_a, param_b);
   else if(feature_name == "WT")
      return n_wt(hlc3, param_a, param_b);
   else if(feature_name == "CCI")
      return n_cci(close, param_a, param_b);
   else if(feature_name == "ADX")
      return n_adx(high, low, close, param_a);
   
   return 0; // По умолчанию
}

//+------------------------------------------------------------------+
//| Функция для расчета Лоренцова расстояния                         |
//+------------------------------------------------------------------+
double get_lorentzian_distance(int i, int feature_count, 
                              double f1_now, double f2_now, double f3_now, double f4_now, double f5_now)
{
   double distance = 0;
   
   if(feature_count >= 1)
      distance += MathLog(1 + MathAbs(f1_now - f1_array[i]));
   
   if(feature_count >= 2)
      distance += MathLog(1 + MathAbs(f2_now - f2_array[i]));
   
   if(feature_count >= 3)
      distance += MathLog(1 + MathAbs(f3_now - f3_array[i]));
   
   if(feature_count >= 4)
      distance += MathLog(1 + MathAbs(f4_now - f4_array[i]));
   
   if(feature_count >= 5)
      distance += MathLog(1 + MathAbs(f5_now - f5_array[i]));
   
   return distance;
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   if(rates_total < 30) return(0); // Недостаточно данных
   
   // Расчет HLC3
   double hlc3[];
   ArrayResize(hlc3, rates_total);
   for(int i = 0; i < rates_total; i++)
      hlc3[i] = (high[i] + low[i] + close[i]) / 3;
   
   int start;
   if(prev_calculated == 0)
   {
      // Инициализация буферов
      ArrayInitialize(BuyBuffer, EMPTY_VALUE);
      ArrayInitialize(SellBuffer, EMPTY_VALUE);
      ArrayInitialize(ExitBuyBuffer, EMPTY_VALUE);
      ArrayInitialize(ExitSellBuffer, EMPTY_VALUE);
      ArrayInitialize(PredictionBuffer, 0);
      ArrayInitialize(SignalBuffer, 0);
      ArrayInitialize(KernelEstimateBuffer, 0);
      
      // Инициализация массивов признаков
      ArrayResize(f1_array, rates_total);
      ArrayResize(f2_array, rates_total);
      ArrayResize(f3_array, rates_total);
      ArrayResize(f4_array, rates_total);
      ArrayResize(f5_array, rates_total);
      
      // Инициализация массивов для KNN
      ArrayResize(distances, 0);
      ArrayResize(predictions, 0);
      ArrayResize(y_train_array, rates_total);
      
      start = 30; // Начинаем с достаточного количества данных для расчета признаков
   }
   else
   {
      start = prev_calculated - 1;
   }
   
   // Расчет признаков для всех баров
   for(int i = MathMax(30, start); i < rates_total; i++)
   {
      // Создание временных массивов для расчета признаков
      double temp_close[], temp_high[], temp_low[], temp_hlc3[];
      
      // Копирование данных для расчета признаков
      ArrayCopy(temp_close, close, 0, 0, i+1);
      ArrayCopy(temp_high, high, 0, 0, i+1);
      ArrayCopy(temp_low, low, 0, 0, i+1);
      ArrayCopy(temp_hlc3, hlc3, 0, 0, i+1);
      
      // Расчет признаков
      f1_array[i] = calculate_feature(Feature1, temp_close, temp_high, temp_low, temp_hlc3, Feature1ParamA, Feature1ParamB);
      f2_array[i] = calculate_feature(Feature2, temp_close, temp_high, temp_low, temp_hlc3, Feature2ParamA, Feature2ParamB);
      f3_array[i] = calculate_feature(Feature3, temp_close, temp_high, temp_low, temp_hlc3, Feature3ParamA, Feature3ParamB);
      f4_array[i] = calculate_feature(Feature4, temp_close, temp_high, temp_low, temp_hlc3, Feature4ParamA, Feature4ParamB);
      f5_array[i] = calculate_feature(Feature5, temp_close, temp_high, temp_low, temp_hlc3, Feature5ParamA, Feature5ParamB);
      
      // Расчет целевой переменной для обучения
      // Прогноз на 4 бара вперед
      if(i >= 4 && i < rates_total - 4)
      {
         if(close[i+4] < close[i])
            y_train_array[i] = DIRECTION_SHORT;
         else if(close[i+4] > close[i])
            y_train_array[i] = DIRECTION_LONG;
         else
            y_train_array[i] = DIRECTION_NEUTRAL;
      }
   }
   
   // Индекс максимального бара для анализа
   int max_bars_back_index = rates_total >= MaxBarsBack ? rates_total - MaxBarsBack : 0;
   
   // Расчет прогнозов для последнего бара
   if(rates_total > max_bars_back_index + 30) // Проверяем, есть ли достаточно данных
   {
      // Очистка массивов расстояний и прогнозов
      ArrayResize(distances, 0);
      ArrayResize(predictions, 0);
      
      // Получение текущих значений признаков
      double f1_current = f1_array[rates_total-1];
      double f2_current = f2_array[rates_total-1];
      double f3_current = f3_array[rates_total-1];
      double f4_current = f4_array[rates_total-1];
      double f5_current = f5_array[rates_total-1];
      
      // Алгоритм KNN с Лоренцовым расстоянием
      lastDistance = -1.0;
      int size = MathMin(rates_total - max_bars_back_index - 1, rates_total - 1);
      
      for(int i = max_bars_back_index; i < rates_total - 5; i++) // Исключаем последние 5 баров, так как у них нет целевой метки
      {
         // Расчет Лоренцова расстояния
         double d = get_lorentzian_distance(i, FeatureCount, f1_current, f2_current, f3_current, f4_current, f5_current);
         
         // Выбор каждого 4-го бара для уменьшения вычислительной нагрузки
         if(d >= lastDistance && i % 4 == 0)
         {
            lastDistance = d;
            ArrayResize(distances, ArraySize(distances) + 1);
            ArrayResize(predictions, ArraySize(predictions) + 1);
            
            distances[ArraySize(distances) - 1] = d;
            predictions[ArraySize(predictions) - 1] = y_train_array[i];
            
            // Если количество соседей превышает заданное, удаляем самый дальний
            if(ArraySize(predictions) > NeighborsCount)
            {
               // Устанавливаем новое пороговое значение distance на уровне 75% квартиля
               lastDistance = distances[(int)(NeighborsCount * 3 / 4)];
               
               // Удаляем первый элемент (самый старый сосед)
               ArrayRemove(distances, 0, 1);
               ArrayRemove(predictions, 0, 1);
            }
         }
      }
      
      // Суммируем прогнозы для получения итогового прогноза
      double prediction_sum = 0;
      for(int i = 0; i < ArraySize(predictions); i++)
         prediction_sum += predictions[i];
      
      PredictionBuffer[rates_total-1] = prediction_sum;
      
      // Применение фильтров
      bool filter_volatility = filter_volatility(1, 10, UseVolatilityFilter);
      bool filter_regime = regime_filter(close[rates_total-1], RegimeThreshold, UseRegimeFilter);
      bool filter_adx = filter_adx(close[rates_total-1], 14, AdxThreshold, UseAdxFilter);
      bool filter_all = filter_volatility && filter_regime && filter_adx;
      
      // Проверка EMA/SMA фильтров
      int ema_handle = iMA(NULL, 0, EmaPeriod, 0, MODE_EMA, PRICE_CLOSE);
      int sma_handle = iMA(NULL, 0, SmaPeriod, 0, MODE_SMA, PRICE_CLOSE);
      double ema_buffer[1], sma_buffer[1];

      bool is_ema_uptrend = true, is_ema_downtrend = true;
      bool is_sma_uptrend = true, is_sma_downtrend = true;

      if(UseEmaFilter && ema_handle != INVALID_HANDLE)
      {
         CopyBuffer(ema_handle, 0, 0, 1, ema_buffer);
         is_ema_uptrend = close[rates_total-1] > ema_buffer[0];
         is_ema_downtrend = close[rates_total-1] < ema_buffer[0];
         IndicatorRelease(ema_handle);
      }

      if(UseSmaFilter && sma_handle != INVALID_HANDLE)
      {
         CopyBuffer(sma_handle, 0, 0, 1, sma_buffer);
         is_sma_uptrend = close[rates_total-1] > sma_buffer[0];
         is_sma_downtrend = close[rates_total-1] < sma_buffer[0];
         IndicatorRelease(sma_handle);
      }
      
      // Расчет сигнала с фильтрами
      int current_signal;
      if(prediction_sum > 0 && filter_all)
         current_signal = DIRECTION_LONG;
      else if(prediction_sum < 0 && filter_all)
         current_signal = DIRECTION_SHORT;
      else
         current_signal = last_signal;
      
      // Сохранение текущего сигнала
      SignalBuffer[rates_total-1] = current_signal;
      
      // Обновление счетчика удерживаемых баров
      if(current_signal != last_signal)
         bars_held = 0;
      else
         bars_held++;
      
      // Проверка условий для входа и выхода
      bool is_held_four_bars = bars_held == 4;
      bool is_held_less_than_four_bars = bars_held > 0 && bars_held < 4;
      
      // Проверка смены типа сигнала
      bool is_different_signal_type = current_signal != last_signal;
      
      // Проверка ранней смены сигнала
      bool is_early_signal_flip = is_different_signal_type && 
                                (SignalBuffer[rates_total-2] != SignalBuffer[rates_total-3] || 
                                SignalBuffer[rates_total-3] != SignalBuffer[rates_total-4] || 
                                SignalBuffer[rates_total-4] != SignalBuffer[rates_total-5]);
      
      // Сигналы покупки/продажи
      bool is_buy_signal = current_signal == DIRECTION_LONG && is_ema_uptrend && is_sma_uptrend;
      bool is_sell_signal = current_signal == DIRECTION_SHORT && is_ema_downtrend && is_sma_downtrend;
      
      // Проверка последнего сигнала
      bool is_last_signal_buy = SignalBuffer[rates_total-5] == DIRECTION_LONG && 
                               (rates_total >= 5 ? is_ema_uptrend && is_sma_uptrend : true);
      bool is_last_signal_sell = SignalBuffer[rates_total-5] == DIRECTION_SHORT && 
                                (rates_total >= 5 ? is_ema_downtrend && is_sma_downtrend : true);
      
      // Новые сигналы покупки/продажи
      bool is_new_buy_signal = is_buy_signal && is_different_signal_type;
      bool is_new_sell_signal = is_sell_signal && is_different_signal_type;
      
      // Расчет ядра регрессии
      double price_copy[];
      ArrayCopy(price_copy, close);
      double y_hat1 = rationalQuadratic(price_copy, LookbackWindow, RelativeWeighting, RegressionLevel);
      double y_hat2 = gaussian(price_copy, LookbackWindow-LagValue, RegressionLevel);
      double kernel_estimate = y_hat1;
      
      // Сохранение оценки ядра
      KernelEstimateBuffer[rates_total-1] = kernel_estimate;
      
      // Расчет скорости изменения ядра
      bool was_bearish_rate = rates_total >= 3 ? KernelEstimateBuffer[rates_total-3] > KernelEstimateBuffer[rates_total-2] : false;
      bool was_bullish_rate = rates_total >= 3 ? KernelEstimateBuffer[rates_total-3] < KernelEstimateBuffer[rates_total-2] : false;
      bool is_bearish_rate = KernelEstimateBuffer[rates_total-2] > KernelEstimateBuffer[rates_total-1];
      bool is_bullish_rate = KernelEstimateBuffer[rates_total-2] < KernelEstimateBuffer[rates_total-1];
      
      bool is_bearish_change = is_bearish_rate && was_bullish_rate;
      bool is_bullish_change = is_bullish_rate && was_bearish_rate;
      
      // Пересечения ядра
      int ma_handle = iMA(NULL, 0, 1, 0, MODE_SMA, PRICE_CLOSE);
      double ma_buffer[2];
      bool is_bullish_cross = false, is_bearish_cross = false;

      if(ma_handle != INVALID_HANDLE)
      {
         CopyBuffer(ma_handle, 0, 0, 2, ma_buffer);
         is_bullish_cross = ma_buffer[0] > y_hat1 && ma_buffer[1] <= y_hat1;
         is_bearish_cross = ma_buffer[0] < y_hat1 && ma_buffer[1] >= y_hat1;
         IndicatorRelease(ma_handle);
      }
      
      bool is_bullish_smooth = y_hat2 >= y_hat1;
      bool is_bearish_smooth = y_hat2 <= y_hat1;
      
      // Настройка оповещений
      bool alert_bullish = UseKernelSmoothing ? is_bullish_cross : is_bullish_change;
      bool alert_bearish = UseKernelSmoothing ? is_bearish_cross : is_bearish_change;
      
      // Фильтры ядра
      bool is_bullish = UseKernelFilter ? (UseKernelSmoothing ? is_bullish_smooth : is_bullish_rate) : true;
      bool is_bearish = UseKernelFilter ? (UseKernelSmoothing ? is_bearish_smooth : is_bearish_rate) : true;
      
      // Условия входа
      bool start_long_trade = is_new_buy_signal && is_bullish && is_ema_uptrend && is_sma_uptrend;
      bool start_short_trade = is_new_sell_signal && is_bearish && is_ema_downtrend && is_sma_downtrend;
      
      // Динамические условия выхода
      int bars_since_long = CountBarsSince(rates_total, time, BuyBuffer);
      int bars_since_short = CountBarsSince(rates_total, time, SellBuffer);
      int bars_since_bullish = 0, bars_since_bearish = 0;
      
      // Подсчет баров с момента изменения тренда
      for(int i = rates_total-1; i >= 0; i--)
      {
         if(i+2 < rates_total && i >= 2 && KernelEstimateBuffer[i] > KernelEstimateBuffer[i+1] && KernelEstimateBuffer[i+1] <= KernelEstimateBuffer[i+2])
         {
            bars_since_bullish = rates_total-1-i;
            break;
         }
      }

      for(int i = rates_total-1; i >= 0; i--)
      {
         if(i+2 < rates_total && i >= 2 && KernelEstimateBuffer[i] < KernelEstimateBuffer[i+1] && KernelEstimateBuffer[i+1] >= KernelEstimateBuffer[i+2])
         {
            bars_since_bearish = rates_total-1-i;
            break;
         }
      }
      
      bool is_valid_short_exit = bars_since_bullish > bars_since_short;
      bool is_valid_long_exit = bars_since_bearish > bars_since_long;
      
      bool end_long_trade_dynamic = is_bearish_change && (rates_total >= 2 ? is_valid_long_exit : false);
      bool end_short_trade_dynamic = is_bullish_change && (rates_total >= 2 ? is_valid_short_exit : false);
      
      // Фиксированные условия выхода
      bool end_long_trade_strict = ((is_held_four_bars && is_last_signal_buy) || 
                                  (is_held_less_than_four_bars && is_new_sell_signal && is_last_signal_buy)) && 
                                  (rates_total >= 5 ? SignalBuffer[rates_total-5] == DIRECTION_LONG : false);
      
      bool end_short_trade_strict = ((is_held_four_bars && is_last_signal_sell) || 
                                  (is_held_less_than_four_bars && is_new_buy_signal && is_last_signal_sell)) && 
                                  (rates_total >= 5 ? SignalBuffer[rates_total-5] == DIRECTION_SHORT : false);
      
      // Проверка достоверности динамического выхода
      bool is_dynamic_exit_valid = !UseEmaFilter && !UseSmaFilter && !UseKernelSmoothing;
      
      bool end_long_trade = UseDynamicExits && is_dynamic_exit_valid ? end_long_trade_dynamic : end_long_trade_strict;
      bool end_short_trade = UseDynamicExits && is_dynamic_exit_valid ? end_short_trade_dynamic : end_short_trade_strict;
      
      // Установка сигналов
      if(start_long_trade)
         BuyBuffer[rates_total-1] = low[rates_total-1];
      else
         BuyBuffer[rates_total-1] = EMPTY_VALUE;
      
      if(start_short_trade)
         SellBuffer[rates_total-1] = high[rates_total-1];
      else
         SellBuffer[rates_total-1] = EMPTY_VALUE;
      
      if(end_long_trade && ShowDefaultExits)
         ExitBuyBuffer[rates_total-1] = high[rates_total-1];
      else
         ExitBuyBuffer[rates_total-1] = EMPTY_VALUE;
      
      if(end_short_trade && ShowDefaultExits)
         ExitSellBuffer[rates_total-1] = low[rates_total-1];
      else
         ExitSellBuffer[rates_total-1] = EMPTY_VALUE;
      
      // Обновление последнего сигнала
      last_signal = current_signal;
      
      // Окрашивание баров
      if(ShowBarColors)
      {
         // Сжатие для цвета
         double compression_factor = NeighborsCount / (double)ColorCompression;
         
         // Расчет цвета
         color bar_color;
         if(PredictionBuffer[rates_total-1] > 0)
            bar_color = color_green(PredictionBuffer[rates_total-1] / compression_factor);
         else
            bar_color = color_red(PredictionBuffer[rates_total-1] / compression_factor);
         
         // Установка цвета
         PlotIndexSetInteger(0, PLOT_COLOR_INDEXES, 1);
         PlotIndexSetInteger(0, PLOT_LINE_COLOR, 0, bar_color);
      }
   }
   
   return(rates_total);
}

//+------------------------------------------------------------------+
//| Счетчик баров с момента последнего события                       |
//+------------------------------------------------------------------+
int CountBarsSince(int rates_total, const datetime &time[], double &buffer[])
{
   int count = rates_total;
   
   for(int i = rates_total-1; i >= 0; i--)
   {
      if(i < ArraySize(buffer) && buffer[i] != EMPTY_VALUE)
      {
         count = rates_total-1-i;
         break;
      }
   }
   
   return count;
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Очистка всех динамических массивов
   ArrayFree(f1_array);
   ArrayFree(f2_array);
   ArrayFree(f3_array);
   ArrayFree(f4_array);
   ArrayFree(f5_array);
   ArrayFree(distances);
   ArrayFree(predictions);
   ArrayFree(y_train_array);
}
//+------------------------------------------------------------------+ 