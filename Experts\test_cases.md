# Тестовые сценарии для проверки советника Turbo-profit v.3.1 после переноса в MT5

## 1. Базовая функциональность

### 1.1. Инициализация и параметры
- **Цель**: Проверить корректную инициализацию советника и загрузку параметров
- **Шаги**:
  1. Запустить советник на графике с параметрами по умолчанию
  2. Изменить параметры и перезапустить советник
- **Ожидаемый результат**: 
  - Советник инициализируется без ошибок
  - Все параметры корректно загружаются и используются в логике работы

### 1.2. Многослойный режим работы
- **Цель**: Проверить корректную работу с несколькими торговыми слоями
- **Шаги**:
  1. Настроить советник для работы с одним слоем (TorgSloy = 1)
  2. Настроить советник для работы с двумя слоями (TorgSloy = 2)
  3. Настроить советник для работы с тремя слоями (TorgSloy = 3)
- **Ожидаемый результат**: 
  - Советник корректно определяет активные слои
  - Логика работы соответствует выбранному режиму

### 1.3. Расчет размера лота
- **Цель**: Проверить корректный расчет размера лота
- **Шаги**:
  1. Установить LotConst_or_not = true и проверить работу с фиксированным лотом
  2. Установить LotConst_or_not = false и проверить расчет лота на основе баланса
  3. Проверить работу с множителем лота для следующих ордеров
- **Ожидаемый результат**: 
  - При фиксированном лоте используется значение Lot
  - При динамическом лоте корректно рассчитывается на основе баланса и RiskPercent
  - Множитель LotMultiplicator корректно применяется

## 2. Торговые операции

### 2.1. Открытие рыночных позиций
- **Цель**: Проверить корректное открытие позиций Buy и Sell
- **Шаги**:
  1. Настроить советник для генерации сигнала на покупку
  2. Настроить советник для генерации сигнала на продажу
- **Ожидаемый результат**: 
  - Позиции открываются с корректными параметрами (объем, Stop Loss, Take Profit)
  - Magic Number соответствует настройкам для каждого слоя

### 2.2. Закрытие позиций
- **Цель**: Проверить корректное закрытие позиций
- **Шаги**:
  1. Создать условия для закрытия позиции Buy
  2. Создать условия для закрытия позиции Sell
  3. Включить Exit_mode и проверить поведение
- **Ожидаемый результат**: 
  - Позиции закрываются корректно при соответствующих сигналах
  - В режиме Exit_mode советник закрывает позиции и не открывает новые

### 2.3. Модификация позиций
- **Цель**: Проверить корректную модификацию Stop Loss и Take Profit
- **Шаги**:
  1. Открыть позицию и дождаться изменения рыночных условий
  2. Проверить модификацию уровней SL и TP
- **Ожидаемый результат**: 
  - Уровни SL и TP корректно модифицируются
  - Уровни соответствуют заданным параметрам (ProtectionTP, TrallTP)

### 2.4. Трейлинг-стоп
- **Цель**: Проверить корректную работу трейлинг-стопа
- **Шаги**:
  1. Открыть позицию Buy и дождаться движения цены вверх
  2. Открыть позицию Sell и дождаться движения цены вниз
- **Ожидаемый результат**: 
  - Трейлинг-стоп корректно передвигает уровень Stop Loss
  - Шаг трейлинга соответствует параметру TrallingStop

## 3. Разные рыночные условия

### 3.1. Тестирование на разных валютных парах
- **Цель**: Убедиться в корректной работе на разных символах
- **Шаги**:
  1. Запустить советник на основных валютных парах (EURUSD, GBPUSD, USDJPY)
  2. Проверить работу на кросс-парах (EURGBP, EURJPY)
- **Ожидаемый результат**: 
  - Советник корректно работает на всех символах
  - Правильно учитываются особенности каждого инструмента (размер пункта, минимальный лот)

### 3.2. Тестирование на разных таймфреймах
- **Цель**: Убедиться в корректной работе на разных таймфреймах
- **Шаги**:
  1. Запустить советник на M5, M15, M30, H1, H4, D1
- **Ожидаемый результат**: 
  - Советник корректно работает на всех таймфреймах
  - Правильно учитываются особенности каждого таймфрейма

### 3.3. Тестирование при высокой волатильности
- **Цель**: Проверить работу при высокой волатильности рынка
- **Шаги**:
  1. Запустить тестирование на периоде с высокой волатильностью
  2. Проверить открытие и закрытие позиций в таких условиях
- **Ожидаемый результат**: 
  - Советник корректно обрабатывает резкие движения цены
  - Защитные механизмы работают правильно

## 4. Визуализация и информация

### 4.1. Отображение графических объектов
- **Цель**: Проверить корректное отображение графических объектов
- **Шаги**:
  1. Запустить советник с ShowTableOnTesting = true
  2. Проверить отображение линий уровней, торговых зон и информационной панели
- **Ожидаемый результат**: 
  - Графические объекты отображаются корректно
  - Обновление объектов происходит своевременно

### 4.2. Отображение информации о торговле
- **Цель**: Проверить корректное отображение торговой информации
- **Шаги**:
  1. Запустить советник и открыть несколько позиций
  2. Проверить отображение информации о позициях, прибыли, балансе
- **Ожидаемый результат**: 
  - Информация отображается корректно
  - Обновление информации происходит своевременно

## 5. Обработка ошибок

### 5.1. Недостаточно средств
- **Цель**: Проверить обработку ситуации с недостаточной маржой
- **Шаги**:
  1. Установить высокое значение Min_Proc_Sv_Sr (например, 95%)
  2. Открыть несколько позиций для снижения свободной маржи
- **Ожидаемый результат**: 
  - Советник корректно определяет недостаточную маржу
  - Новые позиции не открываются, если свободная маржа ниже порога

### 5.2. Ошибки выполнения торговых операций
- **Цель**: Проверить обработку ошибок торговых операций
- **Шаги**:
  1. Смоделировать ситуацию с ошибкой выполнения ордера
  2. Проверить поведение советника
- **Ожидаемый результат**: 
  - Ошибки корректно обрабатываются
  - Советник продолжает работу после ошибки

## 6. Сравнительное тестирование

### 6.1. Сравнение результатов MT4 и MT5
- **Цель**: Сравнить результаты работы оригинальной версии и версии после переноса
- **Шаги**:
  1. Запустить тестирование оригинальной версии в MT4
  2. Запустить тестирование версии после переноса в MT5 на тех же данных
  3. Сравнить результаты (количество сделок, прибыль, drawdown)
- **Ожидаемый результат**: 
  - Результаты тестирования близки
  - Основные показатели (количество сделок, прибыль) совпадают или имеют минимальные отличия

### 6.2. Сравнение сигналов на открытие/закрытие
- **Цель**: Убедиться, что сигналы на открытие и закрытие позиций совпадают
- **Шаги**:
  1. Записать сигналы оригинальной версии
  2. Записать сигналы версии после переноса
  3. Сравнить сигналы по времени и параметрам
- **Ожидаемый результат**: 
  - Сигналы на открытие и закрытие позиций совпадают
  - Параметры сигналов (цена, объем, SL, TP) близки или идентичны

## 7. Производительность

### 7.1. Тестирование производительности
- **Цель**: Убедиться, что версия после переноса не уступает по производительности
- **Шаги**:
  1. Измерить время выполнения тестирования оригинальной версии
  2. Измерить время выполнения тестирования версии после переноса
- **Ожидаемый результат**: 
  - Время выполнения не увеличивается значительно
  - Нет утечек памяти или других проблем с ресурсами

### 7.2. Тестирование на длительном периоде
- **Цель**: Проверить стабильность работы на длительном периоде
- **Шаги**:
  1. Запустить тестирование на периоде в несколько лет
- **Ожидаемый результат**: 
  - Советник стабильно работает на длительном периоде
  - Нет аномалий в поведении или ухудшения производительности со временем 