//+------------------------------------------------------------------+
//|                                            THV_Cobra_MT5.mq5     |
//|                            Copyright 2023, THV Cobra Developers  |
//|                                        http://www.metaquotes.net |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, THV Cobra Developers"
#property link      "http://www.metaquotes.net"
#property version   "1.00"

#property indicator_chart_window
#property indicator_buffers 8
#property indicator_plots   3

#property indicator_label1  "THV Trix"
#property indicator_type1   DRAW_COLOR_HISTOGRAM
#property indicator_color1  clrLime, clrRed
#property indicator_style1  STYLE_SOLID
#property indicator_width1  2

#property indicator_label2  "THV Signal"
#property indicator_type2   DRAW_LINE
#property indicator_color2  clrYellow
#property indicator_style2  STYLE_SOLID
#property indicator_width2  1

#property indicator_label3  "THV Zero"
#property indicator_type3   DRAW_LINE
#property indicator_color3  clrWhite
#property indicator_style3  STYLE_DOT
#property indicator_width3  1

// Параметры индикатора
input int    T3Period = 8;           // T3 Period
input double T3Hot = 0.7;            // T3 Hot Factor
input int    TrixPeriod = 14;        // Trix Period
input int    SignalPeriod = 9;       // Signal Period

// Буферы индикатора
double trixBuffer[];
double signalBuffer[];
double zeroBuffer[];
double colorBuffer[];

// Вспомогательные буферы
double e1Buffer[];
double e2Buffer[];
double e3Buffer[];
double e4Buffer[];

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    // Инициализация буферов
    SetIndexBuffer(0, trixBuffer, INDICATOR_DATA);
    SetIndexBuffer(1, colorBuffer, INDICATOR_COLOR_INDEX);
    SetIndexBuffer(2, signalBuffer, INDICATOR_DATA);
    SetIndexBuffer(3, zeroBuffer, INDICATOR_DATA);
    SetIndexBuffer(4, e1Buffer, INDICATOR_CALCULATIONS);
    SetIndexBuffer(5, e2Buffer, INDICATOR_CALCULATIONS);
    SetIndexBuffer(6, e3Buffer, INDICATOR_CALCULATIONS);
    SetIndexBuffer(7, e4Buffer, INDICATOR_CALCULATIONS);
    
    // Установка стилей для индикаторов
    PlotIndexSetInteger(0, PLOT_DRAW_BEGIN, TrixPeriod + SignalPeriod);
    PlotIndexSetInteger(1, PLOT_DRAW_BEGIN, TrixPeriod + SignalPeriod);
    PlotIndexSetInteger(2, PLOT_DRAW_BEGIN, TrixPeriod + SignalPeriod);
    
    // Установка меток
    IndicatorSetString(INDICATOR_SHORTNAME, "THV Cobra (T3="+IntegerToString(T3Period)+", Trix="+IntegerToString(TrixPeriod)+")");
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    // Проверка доступных данных
    if(rates_total < TrixPeriod + SignalPeriod)
        return(0);
        
    // Определение расчетного диапазона
    int start;
    if(prev_calculated == 0)
    {
        start = TrixPeriod + SignalPeriod;
        // Инициализация буферов
        ArrayInitialize(trixBuffer, 0.0);
        ArrayInitialize(signalBuffer, 0.0);
        ArrayInitialize(zeroBuffer, 0.0);
        ArrayInitialize(colorBuffer, 0.0);
        ArrayInitialize(e1Buffer, 0.0);
        ArrayInitialize(e2Buffer, 0.0);
        ArrayInitialize(e3Buffer, 0.0);
        ArrayInitialize(e4Buffer, 0.0);
    }
    else
    {
        start = prev_calculated - 1;
    }
    
    // Основной цикл расчета
    for(int i = start; i < rates_total; i++)
    {
        // Расчет T3 (Triple EMA)
        CalculateT3(i, close, rates_total);
        
        // Расчет Trix
        CalculateTrix(i, rates_total);
        
        // Расчет сигнальной линии (EMA от Trix)
        signalBuffer[i] = CalculateEMA(i, trixBuffer, SignalPeriod, rates_total);
        
        // Установка нулевой линии
        zeroBuffer[i] = 0.0;
        
        // Установка цвета гистограммы
        colorBuffer[i] = (trixBuffer[i] > signalBuffer[i]) ? 0 : 1;
    }
    
    return(rates_total);
}

//+------------------------------------------------------------------+
//| Расчет индикатора T3 (Triple Exponential Moving Average)        |
//+------------------------------------------------------------------+
void CalculateT3(int index, const double &price[], int total)
{
    double b = T3Hot;
    double c1 = -b*b*b;
    double c2 = 3*b*b + 3*b*b*b;
    double c3 = -6*b*b - 3*b - 3*b*b*b;
    double c4 = 1 + 3*b + b*b*b + 3*b*b;
    
    // Первый этап EMA
    if(index == 0)
        e1Buffer[index] = price[index];
    else
        e1Buffer[index] = e1Buffer[index-1] + (2.0/(T3Period+1)) * (price[index] - e1Buffer[index-1]);
    
    // Второй этап EMA
    if(index == 0)
        e2Buffer[index] = e1Buffer[index];
    else
        e2Buffer[index] = e2Buffer[index-1] + (2.0/(T3Period+1)) * (e1Buffer[index] - e2Buffer[index-1]);
    
    // Третий этап EMA
    if(index == 0)
        e3Buffer[index] = e2Buffer[index];
    else
        e3Buffer[index] = e3Buffer[index-1] + (2.0/(T3Period+1)) * (e2Buffer[index] - e3Buffer[index-1]);
    
    // Четвертый этап EMA (T3)
    if(index == 0)
        e4Buffer[index] = e3Buffer[index];
    else
        e4Buffer[index] = c1*e1Buffer[index] + c2*e2Buffer[index] + c3*e3Buffer[index] + c4*e4Buffer[index-1];
}

//+------------------------------------------------------------------+
//| Расчет индикатора Trix                                           |
//+------------------------------------------------------------------+
void CalculateTrix(int index, int total)
{
    if(index < TrixPeriod)
        trixBuffer[index] = 0.0;
    else
    {
        double prev = (index > 0) ? e4Buffer[index-1] : 0.0;
        if(prev != 0.0)
            trixBuffer[index] = (e4Buffer[index] - prev) / prev * 100.0;
        else
            trixBuffer[index] = 0.0;
    }
}

//+------------------------------------------------------------------+
//| Расчет экспоненциальной скользящей средней (EMA)                |
//+------------------------------------------------------------------+
double CalculateEMA(int index, const double &source[], int period, int total)
{
    if(index < period)
        return 0.0;
        
    double alpha = 2.0 / (period + 1.0);
    
    if(index == period)
    {
        double sum = 0.0;
        for(int i = 0; i < period; i++)
            sum += source[index - i];
        
        return sum / period;
    }
    
    return source[index] * alpha + (1.0 - alpha) * CalculateEMA(index - 1, source, period, total);
} 