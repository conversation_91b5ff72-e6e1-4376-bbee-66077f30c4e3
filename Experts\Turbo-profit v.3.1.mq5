//+------------------------------------------------------------------+
//|                                           Turbo-profit v.3.1.mq5 |
//|                              Copyright © 2023, Forex-Investor.net|
//|                                       Forex-Investor.net         |
//+------------------------------------------------------------------+
//|                                                                  |
//| СИСТЕМА АВТОКАЛИБРОВКИ (AutoCalibration)                         |
//|                                                                  |
//| Система автоматически адаптирует параметры советника в зависимости от:
//| 1. Текущей просадки (4 уровня риска: низкий, средний, высокий, экстремальный)
//| 2. Рыночных условий (тренд/флэт)
//| 3. Волатильности рынка
//|
//| Основные функции:
//| - Автоматическое определение просадки и корректировка параметров
//| - Определение тренда с использованием нескольких индикаторов (MA, MACD, ADX)
//| - Адаптация размера лота, шага сетки и трейлинга
//| - Отслеживание восстановления из просадки
//| - Визуализация всех параметров на графике
//|
//| Настройки:
//| - UseAutoCalibration - включение/выключение автокалибровки
//| - CalibrationInterval - интервал проверки и калибровки в секундах
//| - LowRiskDrawdown, MediumRiskDrawdown, HighRiskDrawdown, ExtremeRiskDrawdown - 
//|   пороговые значения просадки для разных уровней риска
//| - UseAutoTrendDetection - автоматическое определение тренда
//| - AdjustGridStepOnCalibration - корректировать шаг сетки при калибровке
//| - AdjustLotSizeOnCalibration - корректировать размер лота при калибровке
//| - AdjustTrailingOnCalibration - корректировать трейлинг при калибровке
//|
//+------------------------------------------------------------------+
#property copyright "Copyright © 2023, Forex-Investor.net"
#property link      "Forex-Investor.net"
#property version   "3.1"
#property description "Turbo-profit v.3.1 для MetaTrader 5"
#property description "Адаптированная версия советника из MetaTrader 4"

// Определения перечислений для адаптивного размера сетки
enum ENUM_MARKET_SESSION {
   SESSION_ASIAN = 0,     // Азиатская сессия (00:00-08:00)
   SESSION_EUROPEAN = 1,  // Европейская сессия (08:00-16:00)
   SESSION_AMERICAN = 2   // Американская сессия (16:00-24:00)
};

enum ENUM_SYMBOL_TYPE {
   TYPE_FOREX_MAJOR = 0,  // Основные валютные пары (EURUSD, GBPUSD и т.д.)
   TYPE_FOREX_CROSS = 1,  // Кросс-валютные пары (EURGBP, AUDJPY и т.д.)
   TYPE_COMMODITY = 2,    // Сырьевые инструменты (XAUUSD, XAGUSD и т.д.)
   TYPE_INDEX = 3         // Индексы (DE30, US30 и т.д.)
};

// Методы восстановления из просадки
enum ENUM_RECOVERY_METHOD {
   RECOVERY_AVERAGING = 0,       // Интеллектуальное усреднение
   RECOVERY_PARTIAL_CLOSE = 1,   // Частичное закрытие
   RECOVERY_HEDGING = 2,         // Хеджирование
   RECOVERY_COMBINED = 3         // Комбинированный метод
};

// Причины просадки
enum ENUM_DRAWDOWN_CAUSE {
   DD_CAUSE_UNKNOWN,        // Неизвестная причина
   DD_CAUSE_TREND_CHANGE,   // Изменение тренда
   DD_CAUSE_NEWS_IMPACT,    // Влияние новостей
   DD_CAUSE_VOLATILITY,     // Всплеск волатильности
   DD_CAUSE_TECHNICAL       // Технический фактор
};

// Уровни восстановления из просадки
enum ENUM_RECOVERY_LEVEL {
   RECOVERY_LEVEL_1 = 1, // Начальный уровень (5-10% просадки)
   RECOVERY_LEVEL_2 = 2, // Средний уровень (10-20% просадки)
   RECOVERY_LEVEL_3 = 3, // Повышенный уровень (20-30% просадки)
   RECOVERY_LEVEL_4 = 4, // Высокий уровень (30-40% просадки)
   RECOVERY_LEVEL_5 = 5  // Критический уровень (свыше 40% просадки)
};

// Подключение необходимых библиотек
#include <Trade\\Trade.mqh>
#include <Trade\\PositionInfo.mqh>
#include <Arrays\\ArrayDouble.mqh>
#include <Arrays\\ArrayInt.mqh>
#include <Indicators\\Trend.mqh>

// Входные параметры советника
// Режим работы
input string __r__ = "Режим работы";
input bool   Exit_mode = false;  // Режим выхода из позиций

// Настройки умного выхода из просадки с прибылью
input string __smart_exit__ = "Настройки умного выхода из просадки с прибылью";
input bool   UseSmartExit = false;              // Использовать умный выход из просадки
input ENUM_RECOVERY_METHOD RecoveryMethod = RECOVERY_COMBINED; // Метод восстановления
input double DrawdownMax_Percent = 15.0;       // Максимальная просадка (% от депозита)
input int    DrawdownMax_Time = 48;           // Макс. время в просадке (часы)
input double TargetProfit_Percent = 1.0;       // Целевая прибыль (% от убытка)
input int    MaxRecoveryPositions = 5;         // Максимум дополнительных позиций
input double SmartLotMultiplier = 1.3;         // Множитель лота при усреднении
input int    PartialClosePercent = 30;         // Процент частичного закрытия на откатах
input bool   UseMarketStructure = true;        // Учитывать структуру рынка
input int    MinProfitToClose = 15;            // Минимальная прибыль для закрытия (пункты)
input double TrailingActivationLevel = 0.5;    // Уровень активации трейлинга (0.0-1.0)
input int    DrawdownPause_After_Exit = 4;     // Пауза после выхода (часы)

// Настройки интеллектуального восстановления
input string __smart_recovery__ = "Настройки интеллектуального восстановления";
input bool   UseSmartPrediction = true;       // Использовать прогнозирование глубины просадки
input int    PredictionSamples = 10;          // Количество точек для прогноза
input double AcceleratedRecoveryLevel = 50.0;  // Уровень ускоренного восстановления (%)
input bool   UseAdaptiveTrailing = true;      // Использовать адаптивный трейлинг
input double TrailingAcceleration = 2.0;      // Коэффициент ускорения трейлинга
input bool   UseMarketBasedRecovery = true;   // Адаптировать восстановление к типу рынка

// Фильтр тренда
input string __trend__ = "Настройки фильтра тренда";
input bool   UseTrendFilter = true;      // Использовать фильтр тренда
input bool   TradeOnlyWithTrend = false; // Торговать только по тренду
input int    TrendDetectionMethod = 0;   // Метод определения тренда (0-ADX, 1-MA, 2-оба)

// Настройки ADX
input int    ADX_Period = 14;            // Период ADX
input int    ADX_TrendLevel = 25;        // Уровень силы тренда ADX
input bool   ADX_UseDIFilter = true;     // Использовать фильтр по DI+ и DI-
input double ADX_DI_Threshold = 1.0;     // Минимальная разница между DI+ и DI- (новый параметр)

// Настройки Moving Average
input int    MA_Fast_Period = 20;        // Период быстрой MA
input int    MA_Slow_Period = 50;        // Период медленной MA
input int    MA_Method = MODE_SMA;       // Метод расчета MA 
input ENUM_APPLIED_PRICE MA_Applied_Price = PRICE_CLOSE; // Цена для MA
input int    MA_SignalBar = 1;           // Бар для сигнала (0-текущий, 1-предыдущий)

// Настройки Heiken Ashi Smoothed
input string __heiken_ashi__ = "Настройки Heiken Ashi Smoothed";
input bool   UseHeikenAshiFilter = true;   // Использовать фильтр Heiken Ashi
input int    HeikenAshiMaMethod1 = 1;      // Метод сглаживания 1 (1-SMA, 2-EMA, 3-SMMA, 4-LWMA)
input int    HeikenAshiMaPeriod1 = 21;     // Период сглаживания 1
input int    HeikenAshiMaMethod2 = 3;      // Метод сглаживания 2 (1-SMA, 2-EMA, 3-SMMA, 4-LWMA)
input int    HeikenAshiMaPeriod2 = 1;      // Период сглаживания 2
input bool   HeikenAshiConfirmTrend = true; // Требовать подтверждения тренда Heiken Ashi

// Настройки балансировки позиций (новый блок)
input string __position_balance__ = "Настройки балансировки позиций";
input bool   UsePositionBalancing = true;  // Использовать балансировку позиций Buy/Sell
input double MaxPositionImbalance = 1.5;   // Максимальный дисбаланс (соотношение Buy/Sell)
input bool   ForceBalanceOnImbalance = true; // Принудительно балансировать при дисбалансе
input double BuyTrendLotMultiplier = 1.0;  // Множитель лота для Buy в трендовом рынке
input double SellTrendLotMultiplier = 1.0; // Множитель лота для Sell в трендовом рынке
input double BuyFlatLotMultiplier = 1.0;   // Множитель лота для Buy во флэте
input double SellFlatLotMultiplier = 1.0;  // Множитель лота для Sell во флэте

// Настройки адаптивного размера сетки
input string __adaptive_grid__ = "Настройки адаптивного размера сетки";
input bool   UseAdaptiveGrid = true;     // Использовать адаптивный размер сетки
input bool   UseATRForGrid = true;       // Привязать шаг сетки к волатильности (ATR)
input int    ATR_Period = 14;            // Период ATR
input double ATR_Multiplier = 1.0;       // Множитель ATR для шага сетки
input bool   SessionBasedSettings = true; // Изменять параметры в зависимости от сессии
double AsianSessionMultiplier = 0.8; // Множитель для Азиатской сессии
double EuropeanSessionMultiplier = 1.0; // Множитель для Европейской сессии
double AmericanSessionMultiplier = 1.2; // Множитель для Американской сессии
input bool   AutoAdjustForSymbol = true; // Автоподстройка параметров для разных инструментов
double ForexMajorMultiplier = 1.0; // Множитель для основных валютных пар
double ForexCrossMultiplier = 1.2; // Множитель для кросс-валютных пар
double CommodityMultiplier = 1.5;  // Множитель для сырьевых инструментов
double IndexMultiplier = 0.8;      // Множитель для индексов

// Улучшенная адаптация к волатильности
input string __enhanced_volatility__ = "Улучшенная адаптация к волатильности";
input bool   UseEnhancedVolatility = true;     // Использовать улучшенную адаптацию к волатильности
input bool   UseAdaptiveATRPeriod = true;      // Динамически адаптировать период ATR
input int    ATR_Period_Min = 7;              // Минимальный период ATR
input int    ATR_Period_Max = 21;             // Максимальный период ATR
input double ATR_Volatility_Threshold = 1.5;   // Порог изменения волатильности
input bool   UseMultiTimeframeVolatility = true; // Использовать мультитаймфреймовый анализ
input ENUM_TIMEFRAMES VolatilityTimeframe1 = PERIOD_M15; // Таймфрейм 1 для анализа
input ENUM_TIMEFRAMES VolatilityTimeframe2 = PERIOD_H1;  // Таймфрейм 2 для анализа
input ENUM_TIMEFRAMES VolatilityTimeframe3 = PERIOD_H4;  // Таймфрейм 3 для анализа
input double VolatilityWeight1 = 0.5;         // Вес таймфрейма 1
input double VolatilityWeight2 = 0.3;         // Вес таймфрейма 2
input double VolatilityWeight3 = 0.2;         // Вес таймфрейма 3

// Оптимизированная система динамического ATR
input string __dynamic_atr__ = "Оптимизированная система динамического ATR";
input bool   UseOptimalATRSelection = true;    // Автоматический подбор оптимального периода ATR
input int    ATR_OptimizationPeriod = 100;     // Период для оптимизации ATR (в барах)
input double ATR_EfficiencyThreshold = 0.75;   // Порог эффективности для смены периода ATR
input bool   UseATRVolatilityRegime = true;    // Использовать режимы волатильности для ATR
input double LowVolatilityThreshold = 0.5;     // Порог низкой волатильности (множитель среднего ATR)
input double HighVolatilityThreshold = 1.8;    // Порог высокой волатильности (множитель среднего ATR)

// Сезонный анализ волатильности
input string __seasonal_volatility__ = "Сезонный анализ волатильности";
input bool   UseSeasonalVolatilityAnalysis = true; // Использовать сезонный анализ волатильности
input bool   UseHourlyVolatilityPattern = true;    // Учитывать паттерны волатильности по часам
input bool   UseDailyVolatilityPattern = true;     // Учитывать паттерны волатильности по дням недели
input bool   UseSessionVolatilityPattern = true;   // Учитывать паттерны волатильности по торговым сессиям
input int    VolatilityHistoryDays = 30;           // Количество дней для анализа исторической волатильности
input double SeasonalVolatilityWeight = 0.3;       // Вес сезонного анализа в общем расчете

// Улучшенная фильтрация всплесков волатильности
input string __spike_filtering__ = "Улучшенная фильтрация всплесков волатильности";
input bool   UseStatisticalSpikeDetection = true; // Использовать статистические методы обнаружения всплесков
input double SpikeDetectionSigma = 2.0;           // Количество стандартных отклонений для обнаружения всплеска
input int    SpikeAnalysisPeriod = 50;            // Период для анализа всплесков (в барах)
input bool   UseAdaptiveSpikeThreshold = true;    // Адаптивный порог всплесков
input double MinSpikeThreshold = 1.5;             // Минимальный порог всплеска
input double MaxSpikeThreshold = 4.0;             // Максимальный порог всплеска

// Защита от резких движений и фильтр рыночных событий
input string __market_protection__ = "Защита от резких движений и фильтр рыночных событий";
input bool   DetectVolatilitySpikes = true;    // Обнаруживать всплески волатильности
input double VolatilitySpikeThreshold = 2.5;   // Порог всплеска волатильности (ATR множитель)
input bool   UseNewsFilter = false;            // Использовать фильтр новостей (требует внешнего календаря)
input int    MinutesBeforeNews = 60;          // Минут до новости (пауза)
input int    MinutesAfterNews = 30;           // Минут после новости (пауза)
input bool   AnalyzeLiquidity = true;         // Анализировать ликвидность рынка
input double LiquidityThreshold = 0.7;        // Порог ликвидности (0.0-1.0)

// Параметры для разных типов рынка
input string __market_type__ = "Настройки для разных типов рынка";
input int    TrendMode_Setka = 15;       // Шаг сетки для трендового рынка
input int    FlatMode_Setka = 10;        // Шаг сетки для флэтового рынка
input double TrendMode_LotMult = 1.15;   // Множитель лота для трендового рынка
input double FlatMode_LotMult = 1.3;     // Множитель лота для флэтового рынка

// Настройки размера лота
input string __l__ = "Настройка размера лота (риск)";
input bool   LotConst_or_not = false;  // Использовать постоянный лот
input double Lot = 0.01;               // Размер лота
input double RiskPercent = 1.0;        // Процент риска
input double LotMultiplicator = 1.25;  // Множитель лота
input double MaxLotMultiplier = 2.0;  // Максимальный итоговый множитель лота для сетки (ограничение агрессии)

// Настройки торговых слоев
input string __s__ = "Настройка торговых слоев";
input int    Work_Sloy_mode = 1;     // Режим работы слоев (0-1)
input int    TorgSloy = 3;           // Количество торговых слоев (1-3)
input bool   D_D_LOT_auto_calc = true; // Автоматический расчет лота для ДД
input double D_D_Lot = 0.5;          // Размер лота для ДД
input int    N_enable_Sloy = 7;      // Начальное количество слоев

// Настройки торговой сетки
input string _____ = "Настройка торговой сетки (пунктов)";
input int    hSETKY = 10;            // Шаг сетки
input int    Uvel_hSETKY = 1;        // Режим увеличения шага сетки (0-2)
input int    ShagUvel_hSETKY = 10;   // Шаг увеличения сетки

// Настройки 3-го слоя
input bool   mode_enable_3_sloy = false;  // Включение 3-го слоя
input double h_D_T = 500.0;              // Расстояние между экстремумами для 3-го слоя
input double pr_h_3_sl = 25.0;           // Процент высоты 3-го слоя

// Настройки защиты и трейлинга
input int    slippage = 10;           // Проскальзывание
input int    ProtectionTP = 7;        // Защитный тейк-профит
input int    TrallingStop = 7;        // Трейлинг-стоп
input int    TrallTP = 15;            // Трейлинг тейк-профита
input bool   DetailedLogging = true; // Подробное логирование трейлинга

// Настройки маржи
input string ______________ = "Насто.св.марж.от.начал";
input double Min_Proc_Sv_Sr = 80.0;     // Минимальный процент свободной маржи (%)

// Идентификация и визуализация
input int    Magic = 1230;            // Магический номер советника
input bool   ShowTableOnTesting = true;  // Показывать таблицу при тестировании
input int    Text_Syze = 14;          // Размер текста
input color  ColorTableOnTesting = Yellow;  // Цвет таблицы при тестировании
input color  ColorLogotipName = Maroon;     // Цвет названия логотипа
input color  ColorLogotipSite = Gray;       // Цвет сайта логотипа
input color  color_Trade_area_of_3rd_layer = DarkSlateGray; // Цвет торговой зоны 3-го слоя

// Настройки автокалибровки
input string __auto_calibration__ = "Настройки автокалибровки";
input bool   UseAutoCalibration = true;      // Использовать автокалибровку
input int    CalibrationInterval = 300;      // Интервал калибровки (секунды)
input double LowRiskDrawdown = 5.0;          // Просадка для низкого риска (%)
input double MediumRiskDrawdown = 10.0;      // Просадка для среднего риска (%)
input double HighRiskDrawdown = 15.0;        // Просадка для высокого риска (%)
input double ExtremeRiskDrawdown = 20.0;     // Просадка для экстремального риска (%)
input bool   UseAutoTrendDetection = true;   // Автоопределение тренда
input bool   AdjustGridStepOnCalibration = true; // Корректировать шаг сетки при калибровке
input bool   AdjustLotSizeOnCalibration = true;  // Корректировать размер лота при калибровке
input bool   AdjustTrailingOnCalibration = true; // Корректировать трейлинг при калибровке
input bool   DebugMode = false;                  // Режим отладки (вывод дополнительной информации)

// Параметры многоступенчатой системы восстановления из просадки
input bool   UseAdvancedRecovery = true;        // Использовать улучшенную систему восстановления
input bool   UseDrawdownPrediction = true;      // Использовать прогнозирование просадки
input int    RecoveryLevelsCount = 5;           // Количество уровней восстановления
input double RecoveryLevelStep = 3.0;           // Шаг между уровнями просадки (%)
input bool   UseAdaptiveRecovery = true;        // Адаптивное восстановление
// UseMarketBasedRecovery уже определен выше - дубликат удален
input int    DrawdownHistorySize = 10;          // Размер истории просадок для анализа
input int    RecoveryPauseHours = 6;            // Пауза после восстановления (часы)

// Параметры умной адаптивной системы управления просадкой
input string __smart_drawdown__ = "Умная адаптивная система управления просадкой";
input bool   UseSmartDrawdownManagement = true;    // Использовать умную систему управления просадкой
input double VolatilityDrawdownMultiplier = 1.5;   // Множитель просадки для высокой волатильности
input double TrendDrawdownMultiplier = 1.3;        // Множитель просадки для трендового рынка
input double FlatDrawdownMultiplier = 0.8;         // Множитель просадки для флэтового рынка
input double SessionDrawdownMultiplier = 1.2;      // Множитель просадки для активных сессий
input bool   UseMarketConditionFiltering = true;   // Фильтрация по рыночным условиям
input double MaxVolatilityThreshold = 2.5;         // Максимальный порог волатильности для торговли
input bool   UseDynamicRiskAdjustment = true;      // Динамическая корректировка риска
input double RiskReductionFactor = 0.7;            // Коэффициент снижения риска при неблагоприятных условиях
input bool   UseHistoricalDrawdownAnalysis = true; // Анализ исторических просадок
input int    HistoricalAnalysisPeriod = 30;        // Период анализа истории (дни)
input double DrawdownRecoverySpeedFactor = 1.5;    // Коэффициент скорости восстановления

// Глобальные переменные
string   m_symbol;                       // Символ (валютная пара)
string   m_prefix;                       // Префикс для объектов на графике
int      m_magic;                        // Магический номер советника
bool     m_exit_mode;                    // Режим выхода из позиций
double   m_point;                        // Размер пункта
int      m_point_digits;                 // Множитель для пунктов (10 для 3-х знаков, 1 для 4-х и 5-ти знаков)
int      m_digits;                       // Количество знаков после запятой
int      m_server_time_offset = 0; // Смещение времени сервера относительно UTC (в часах)

// Переменные для автокалибровки
bool     m_use_auto_calibration;         // Использовать автокалибровку
int      m_calibration_interval;         // Интервал калибровки в секундах
double   m_low_risk_dd;                  // Просадка для низкого риска (%)
double   m_medium_risk_dd;               // Просадка для среднего риска (%)
double   m_high_risk_dd;                 // Просадка для высокого риска (%)
double   m_extreme_risk_dd;              // Просадка для экстремального риска (%)
bool     m_use_auto_trend_detection;     // Автоопределение тренда
bool     m_adjust_grid_step;             // Корректировать шаг сетки при калибровке
bool     m_adjust_lot_size;              // Корректировать размер лота при калибровке
bool     m_adjust_trailing;              // Корректировать трейлинг при калибровке
datetime m_last_calibration_time;        // Время последней калибровки
int      m_current_risk_level;           // Текущий уровень риска (0-низкий, 1-средний, 2-высокий, 3-экстремальный)
double   m_current_drawdown_pct;         // Текущая просадка в процентах
double   m_max_balance;                  // Максимальный баланс для расчета просадки
double   m_original_lot;                 // Исходный размер лота
int      m_original_hsetky;              // Исходный шаг сетки
double   m_original_lot_mult;            // Исходный множитель лота
int      m_original_trailing;            // Исходный трейлинг-стоп
bool     m_drawdown_detected;            // Флаг обнаружения просадки
datetime m_drawdown_start_time;          // Время начала просадки
double   m_drawdown_max_value;           // Максимальное значение просадки
bool     m_recovery_active;              // Флаг активного восстановления
double   m_buy_trend_lot_mult;           // Множитель лота для покупок в тренде
double   m_sell_trend_lot_mult;          // Множитель лота для продаж в тренде
double   m_buy_flat_lot_mult;            // Множитель лота для покупок во флэте
double   m_sell_flat_lot_mult;           // Множитель лота для продаж во флэте
int      m_trend_mode_setka;             // Шаг сетки для трендового рынка
int      m_flat_mode_setka;              // Шаг сетки для флэтового рынка

// Переменные для улучшенной системы восстановления из просадки
bool     m_use_advanced_recovery;        // Использовать улучшенную систему восстановления
bool     m_use_drawdown_prediction;      // Использовать прогнозирование просадки
int      m_recovery_levels_count;        // Количество уровней восстановления
double   m_recovery_level_step;          // Шаг между уровнями просадки (%)
bool     m_use_adaptive_recovery;        // Адаптивное восстановление
bool     m_use_market_based_recovery;    // Выбор метода восстановления на основе рынка
int      m_drawdown_history_size;        // Размер истории просадок для анализа
int      m_recovery_pause_hours;         // Пауза после восстановления (часы)
double   m_predicted_max_drawdown;       // Прогнозируемая максимальная просадка
double   m_drawdown_trend;               // Тренд изменения просадки
double   m_drawdown_history[];           // История значений просадки
datetime m_drawdown_time_history[];      // История времени просадок
int      m_drawdown_history_count;       // Количество записей в истории просадок
int      m_current_recovery_level;       // Текущий уровень восстановления
ENUM_RECOVERY_METHOD m_recovery_method;  // Метод восстановления
double   m_recovery_percent;             // Процент восстановления
int      m_recovery_positions_added;     // Количество добавленных позиций для восстановления
datetime m_drawdown_pause_until;         // Время до которого действует пауза после восстановления
double   m_market_condition_weights[];   // Веса рыночных условий для выбора метода

// Переменные для умной адаптивной системы управления просадкой
bool     m_use_smart_drawdown_management;    // Использовать умную систему управления просадкой
double   m_volatility_dd_multiplier;         // Множитель просадки для высокой волатильности
double   m_trend_dd_multiplier;              // Множитель просадки для трендового рынка
double   m_flat_dd_multiplier;               // Множитель просадки для флэтового рынка
double   m_session_dd_multiplier;            // Множитель просадки для активных сессий
bool     m_use_market_condition_filtering;   // Фильтрация по рыночным условиям
double   m_max_volatility_threshold;         // Максимальный порог волатильности для торговли
bool     m_use_dynamic_risk_adjustment;      // Динамическая корректировка риска
double   m_risk_reduction_factor;            // Коэффициент снижения риска при неблагоприятных условиях
bool     m_use_historical_dd_analysis;       // Анализ исторических просадок
int      m_historical_analysis_period;       // Период анализа истории (дни)
double   m_dd_recovery_speed_factor;         // Коэффициент скорости восстановления
double   m_adaptive_dd_threshold[];          // Адаптивные пороги просадки для разных условий
double   m_current_market_risk_factor;       // Текущий фактор рыночного риска
double   m_historical_max_dd;                // Историческая максимальная просадка
double   m_average_recovery_time;            // Среднее время восстановления
bool     m_market_conditions_favorable;      // Флаг благоприятных рыночных условий

// Дополнительные переменные для умного выхода из просадки
bool     m_use_smart_exit;               // Использовать умный выход из просадки
double   m_drawdown_max_percent;         // Максимальная просадка в процентах
int      m_drawdown_max_time;            // Максимальное время в просадке (часы)
double   m_target_profit_percent;        // Целевая прибыль (% от убытка)
int      m_max_recovery_positions;       // Максимальное количество позиций для восстановления
double   m_smart_lot_multiplier;         // Множитель лота при умном усреднении
int      m_partial_close_percent;        // Процент частичного закрытия на откатах
bool     m_use_market_structure;         // Учитывать структуру рынка
int      m_min_profit_to_close;          // Минимальная прибыль для закрытия (пункты)
double   m_trailing_activation_level;    // Уровень активации трейлинга
int      m_drawdown_pause_after_exit;    // Пауза после выхода (часы)

// Переменные для интеллектуального восстановления
bool     m_use_smart_prediction;         // Использовать прогнозирование глубины просадки
int      m_prediction_samples;           // Количество точек для прогноза
double   m_accelerated_recovery_level;   // Уровень ускоренного восстановления (%)
bool     m_use_adaptive_trailing;        // Использовать адаптивный трейлинг
double   m_trailing_acceleration;        // Коэффициент ускорения трейлинга

// Состояние просадки и восстановления
int      m_positions_closed;             // Количество закрытых позиций при восстановлении
double   m_current_drawdown_percent;     // Текущий процент просадки
int      m_recovery_position_tickets[100]; // Тикеты позиций восстановления

// Параметры для работы с лотом
bool     m_lot_const;                    // Использовать постоянный лот
double   m_lot;                          // Размер лота
double   m_risk_percent;                 // Процент риска
double   m_lot_multiplicator;            // Множитель лота
double   m_lot_multiplier;               // Общий множитель лота для умной системы
double   m_min_lot;                      // Минимальный размер лота
double   m_max_lot;                      // Максимальный размер лота

// Параметры слоев
int      m_work_sloy_mode;               // Режим работы слоев (0-1)
int      m_torg_sloy;                    // Количество торговых слоев (1-3)
bool     m_dd_lot_auto_calc;             // Автоматический расчет лота для ДД
double   m_dd_lot;                       // Размер лота для ДД
int      m_n_enable_sloy;                // Начальное количество слоев

// Параметры торговой сетки
int      m_hsetky;                       // Шаг сетки
int      m_uvel_hsetky;                  // Режим увеличения шага сетки (0-2)
int      m_shag_uvel_hsetky;             // Шаг увеличения сетки

// Параметры 3-го слоя
bool     m_mode_enable_3_sloy;           // Включение 3-го слоя
double   m_h_d_t;                        // Расстояние между экстремумами для 3-го слоя
double   m_pr_h_3_sl;                    // Процент высоты 3-го слоя

// Параметры защиты и торговли
int      m_slippage;                     // Проскальзывание
int      m_protection_tp;                // Защитный тейк-профит
int      m_tralling_stop;                // Трейлинг-стоп
int      m_trall_tp;                     // Трейлинг тейк-профита
bool     m_detailed_logging;             // Подробное логирование трейлинга
double   m_min_proc_sv_sr;               // Минимальный процент свободной маржи (%)
double   m_free_margin_limit;            // Предел свободной маржи

// Визуальные параметры
bool     m_show_table_on_testing;        // Показывать таблицу при тестировании
int      m_text_size;                    // Размер текста
color    m_color_table_on_testing;       // Цвет таблицы при тестировании
color    m_color_logotip_name;           // Цвет названия логотипа
color    m_color_logotip_site;           // Цвет сайта логотипа
color    m_color_trade_area_of_3rd_layer; // Цвет торговой зоны 3-го слоя

// Переменные для хранения позиций и уровней
int      m_active_layers[3];             // Активные слои (0 - отключен, 1 - обычный, 2 - только для открытия, 3 - только для закрытия)

// Массивы для Buy позиций
int      m_buy_positions_count[3];       // Количество Buy позиций для каждого слоя
ulong    m_buy_tickets[3][100];          // Тикеты Buy позиций
double   m_buy_levels[3][100];           // Уровни Buy позиций
double   m_buy_lots[3][100];             // Размеры лотов Buy позиций
double   m_buy_sl[3][100];               // Стоп-лоссы Buy позиций
double   m_buy_tp[3][100];               // Тейк-профиты Buy позиций
double   m_buy_lots_total[3];            // Общие объемы Buy позиций

// Массивы для Sell позиций
int      m_sell_positions_count[3];      // Количество Sell позиций для каждого слоя
ulong    m_sell_tickets[3][100];         // Тикеты Sell позиций
double   m_sell_levels[3][100];          // Уровни Sell позиций
double   m_sell_lots[3][100];            // Размеры лотов Sell позиций
double   m_sell_sl[3][100];              // Стоп-лоссы Sell позиций
double   m_sell_tp[3][100];              // Тейк-профиты Sell позиций
double   m_sell_lots_total[3];           // Общие объемы Sell позиций

// Переменные для работы с 3-м слоем
double   m_max_price;                    // Максимальная цена для расчета 3-го слоя
double   m_min_price;                    // Минимальная цена для расчета 3-го слоя
int      m_max_price_index;              // Индекс бара с максимальной ценой
int      m_min_price_index;              // Индекс бара с минимальной ценой
double   m_trade_high;                   // Верхняя граница торговой зоны 3-го слоя
double   m_trade_low;                    // Нижняя граница торговой зоны 3-го слоя
string   m_trade_area_name;              // Имя объекта торговой зоны 3-го слоя

// Прочие переменные
double   m_mid_price;                    // Средняя цена
double   m_high_price;                   // Максимальная цена
double   m_low_price;                    // Минимальная цена
double   m_upper_bound;                  // Верхняя граница
double   m_lower_bound;                  // Нижняя граница

// Переменные для фильтра тренда
bool     m_use_trend_filter;             // Использовать фильтр тренда
bool     m_trade_only_with_trend;        // Торговать только по тренду
int      m_trend_detection_method;       // Метод определения тренда
int      m_adx_period;                   // Период ADX
int      m_adx_trend_level;              // Уровень силы тренда ADX
bool     m_adx_use_di_filter;            // Использовать фильтр по DI+ и DI-
int      m_ma_fast_period;               // Период быстрой MA
int      m_ma_slow_period;               // Период медленной MA
int      m_ma_method;                    // Метод расчета MA
ENUM_APPLIED_PRICE m_ma_applied_price;   // Цена для MA
int      m_ma_signal_bar;                // Бар для сигнала
double   m_trend_mode_lot_mult;          // Множитель лота для трендового рынка
double   m_flat_mode_lot_mult;           // Множитель лота для флэтового рынка
int      m_current_market_type;          // Текущий тип рынка (0-флэт, 1-восходящий тренд, -1-нисходящий тренд)

// Параметры балансировки позиций
bool     m_use_position_balancing;       // Использовать балансировку позиций
double   m_max_position_imbalance;       // Максимальный дисбаланс позиций
bool     m_force_balance_on_imbalance;   // Принудительно балансировать при дисбалансе
double   m_adx_di_threshold;             // Минимальная разница между DI+ и DI-
double   m_current_position_balance;     // Текущий баланс позиций (Buy/Sell)

// Переменные для адаптивного размера сетки
bool     m_use_adaptive_grid;            // Использовать адаптивный размер сетки
bool     m_use_atr_for_grid;             // Привязать шаг сетки к волатильности (ATR)
int      m_atr_period;                   // Период ATR
double   m_atr_multiplier;               // Множитель ATR для шага сетки
bool     m_session_based_settings;       // Изменять параметры в зависимости от сессии
double   m_asian_session_multiplier;     // Множитель для Азиатской сессии
double   m_european_session_multiplier;  // Множитель для Европейской сессии
double   m_american_session_multiplier;  // Множитель для Американской сессии
bool     m_auto_adjust_for_symbol;       // Автоподстройка параметров для разных инструментов
double   m_forex_major_multiplier;       // Множитель для основных валютных пар
double   m_forex_cross_multiplier;       // Множитель для кросс-валютных пар
double   m_commodity_multiplier;         // Множитель для сырьевых инструментов
double   m_index_multiplier;             // Множитель для индексов
double   m_current_grid_step;            // Текущий адаптивный шаг сетки
int      m_atr_handle;                   // Хендл индикатора ATR
ENUM_MARKET_SESSION m_current_session;   // Текущая торговая сессия
ENUM_SYMBOL_TYPE m_symbol_type;          // Тип торгового инструмента

// Переменные для улучшенной адаптации к волатильности
bool     m_use_enhanced_volatility;      // Использовать улучшенную адаптацию к волатильности
bool     m_use_adaptive_atr_period;      // Динамически адаптировать период ATR
int      m_atr_period_min;               // Минимальный период ATR
int      m_atr_period_max;               // Максимальный период ATR
double   m_atr_volatility_threshold;     // Порог изменения волатильности
int      m_current_atr_period;           // Текущий адаптивный период ATR
double   m_prev_atr_value;               // Предыдущее значение ATR
bool     m_use_mtf_volatility;           // Использовать мультитаймфреймовый анализ
ENUM_TIMEFRAMES m_volatility_tf1;        // Таймфрейм 1 для анализа
ENUM_TIMEFRAMES m_volatility_tf2;        // Таймфрейм 2 для анализа
ENUM_TIMEFRAMES m_volatility_tf3;        // Таймфрейм 3 для анализа
double   m_volatility_weight1;           // Вес таймфрейма 1
double   m_volatility_weight2;           // Вес таймфрейма 2
double   m_volatility_weight3;           // Вес таймфрейма 3
int      m_atr_handle_tf1;               // Хендл ATR для таймфрейма 1
int      m_atr_handle_tf2;               // Хендл ATR для таймфрейма 2
int      m_atr_handle_tf3;               // Хендл ATR для таймфрейма 3

// Переменные для оптимизированной системы динамического ATR
bool     m_use_optimal_atr_selection;    // Автоматический подбор оптимального периода ATR
int      m_atr_optimization_period;      // Период для оптимизации ATR
double   m_atr_efficiency_threshold;     // Порог эффективности для смены периода ATR
bool     m_use_atr_volatility_regime;    // Использовать режимы волатильности для ATR
double   m_low_volatility_threshold;     // Порог низкой волатильности
double   m_high_volatility_threshold;    // Порог высокой волатильности
int      m_optimal_atr_period;           // Оптимальный период ATR
double   m_atr_efficiency_history[];     // История эффективности разных периодов ATR
datetime m_last_atr_optimization;        // Время последней оптимизации ATR
int      m_volatility_regime;            // Текущий режим волатильности (0-низкая, 1-средняя, 2-высокая)

// Переменные для сезонного анализа волатильности
bool     m_use_seasonal_volatility;      // Использовать сезонный анализ волатильности
bool     m_use_hourly_pattern;           // Учитывать паттерны по часам
bool     m_use_daily_pattern;            // Учитывать паттерны по дням недели
bool     m_use_session_pattern;          // Учитывать паттерны по торговым сессиям
int      m_volatility_history_days;      // Количество дней для анализа истории
double   m_seasonal_volatility_weight;   // Вес сезонного анализа
double   m_hourly_volatility_pattern[24]; // Паттерн волатильности по часам
double   m_daily_volatility_pattern[7];   // Паттерн волатильности по дням недели
double   m_session_volatility_pattern[4]; // Паттерн волатильности по сессиям
datetime m_last_seasonal_update;         // Время последнего обновления сезонных данных

// Переменные для улучшенной фильтрации всплесков
bool     m_use_statistical_spike_detection; // Использовать статистические методы
double   m_spike_detection_sigma;        // Количество стандартных отклонений
int      m_spike_analysis_period;        // Период для анализа всплесков
bool     m_use_adaptive_spike_threshold; // Адаптивный порог всплесков
double   m_min_spike_threshold;          // Минимальный порог всплеска
double   m_max_spike_threshold;          // Максимальный порог всплеска
double   m_atr_history_for_spikes[];     // История ATR для анализа всплесков
double   m_current_spike_threshold;      // Текущий адаптивный порог всплесков
double   m_atr_mean;                     // Среднее значение ATR
double   m_atr_std_dev;                  // Стандартное отклонение ATR

// Переменная для отладки
bool     m_debug_mode;                   // Режим отладки

// Переменные для защиты от резких движений и фильтра рыночных событий
bool     m_detect_volatility_spikes;     // Обнаруживать всплески волатильности
double   m_volatility_spike_threshold;   // Порог всплеска волатильности
bool     m_use_news_filter;              // Использовать фильтр новостей
int      m_minutes_before_news;          // Минут до новости (пауза)
int      m_minutes_after_news;           // Минут после новости (пауза)
bool     m_analyze_liquidity;            // Анализировать ликвидность рынка
double   m_liquidity_threshold;          // Порог ликвидности
bool     m_is_spike_detected;            // Флаг обнаружения всплеска волатильности
bool     m_is_news_time;                 // Флаг времени важных новостей
double   m_current_liquidity;            // Текущая оценка ликвидности
datetime m_last_spike_time;              // Время последнего всплеска волатильности
datetime m_last_liquidity_check;         // Время последней проверки ликвидности
datetime m_last_atr_adjustment;          // Время последней корректировки периода ATR
datetime m_last_grid_step_calc;          // Время последнего расчета адаптивного шага сетки

// Переменные для умного выхода из просадки (удалены дубликаты - используются переменные из основного блока выше)
// Дублированные переменные удалены для устранения ошибок компиляции

// Прототипы функций для системы восстановления из просадки
void UpdateDrawdownHistory(double drawdown);
double PredictMaximumDrawdown();
ENUM_DRAWDOWN_CAUSE DetectDrawdownCause();
ENUM_RECOVERY_METHOD SelectOptimalRecoveryMethod(ENUM_DRAWDOWN_CAUSE cause);
void ApplyAdaptiveTrailing(double drawdown);
bool PartialClosePositions(double percent = 0);
bool ApplyHedging();
double CalculateMinLot();

// Прототипы функций для умной адаптивной системы управления просадкой
void UpdateSmartDrawdownThresholds();
double CalculateMarketRiskFactor();
bool IsMarketConditionsFavorable();
double GetAdaptiveDrawdownThreshold(int risk_level);
void AnalyzeHistoricalDrawdowns();
bool ShouldReduceRisk();
void ApplyDynamicRiskAdjustment();
double CalculateVolatilityAdjustedThreshold(double base_threshold);
bool IsHighVolatilityPeriod();
void UpdateMarketConditionsAssessment();

// Массивы для хранения значений индикаторов
double   m_adx_main[], m_plus_di[], m_minus_di[];
double   m_ma_fast[], m_ma_slow[];

// Объект для выполнения торговых операций
CTrade   m_trade;

// Хендлы индикаторов
int      m_adx_handle = INVALID_HANDLE;  // Хендл индикатора ADX
int      m_ma_fast_handle = INVALID_HANDLE; // Хендл быстрой MA
int      m_ma_slow_handle = INVALID_HANDLE; // Хендл медленной MA
int      m_heiken_ashi_handle = INVALID_HANDLE; // Хендл индикатора Heiken Ashi Smoothed

// Переменные для Heiken Ashi Smoothed
bool     m_use_heiken_ashi_filter;        // Использовать фильтр Heiken Ashi
int      m_heiken_ashi_ma_method1;        // Метод сглаживания 1
int      m_heiken_ashi_ma_period1;        // Период сглаживания 1
int      m_heiken_ashi_ma_method2;        // Метод сглаживания 2
int      m_heiken_ashi_ma_period2;        // Период сглаживания 2
bool     m_heiken_ashi_confirm_trend;     // Требовать подтверждения тренда Heiken Ashi

// Буферы для встроенного индикатора Heiken Ashi Smoothed
double   m_ha_open_buffer[];              // Буфер для Open
double   m_ha_close_buffer[];             // Буфер для Close
double   m_ha_high_buffer[];              // Буфер для High
double   m_ha_low_buffer[];               // Буфер для Low
int      m_ha_ma_method1;                 // Метод MA 1 для расчетов
int      m_ha_ma_period1;                 // Период MA 1 для расчетов
int      m_ha_ma_method2;                 // Метод MA 2 для расчетов
int      m_ha_ma_period2;                 // Период MA 2 для расчетов

//+------------------------------------------------------------------+
//| Функция инициализации эксперта                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // Инициализация по умолчанию
   m_symbol = Symbol();
   m_prefix = "TP_";
   m_magic = Magic;
   m_exit_mode = Exit_mode;
   m_trade_area_name = m_prefix + "Trade_Area";
   
   // Инициализация активных слоев
   ArrayInitialize(m_active_layers, 0);
   
   // Инициализация массивов для позиций
   ArrayInitialize(m_buy_positions_count, 0);
   ArrayInitialize(m_sell_positions_count, 0);
   ArrayInitialize(m_buy_lots_total, 0.0);
   ArrayInitialize(m_sell_lots_total, 0.0);
   
   // Получение информации о текущем символе
   m_digits = (int)SymbolInfoInteger(m_symbol, SYMBOL_DIGITS);
   m_point = SymbolInfoDouble(m_symbol, SYMBOL_POINT);
   m_point_digits = (m_digits == 3 || m_digits == 5) ? 10 : 1;
   
   // Получение информации о минимальном и максимальном лоте
   m_min_lot = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MIN);
   m_max_lot = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MAX);
   
   // Установка параметров лота
   m_lot_const = LotConst_or_not;
   m_lot = Lot;
   m_risk_percent = RiskPercent;
   m_lot_multiplicator = LotMultiplicator;
   m_lot_multiplier = 1.0; // Инициализация множителя для умной системы
   
   // Установка параметров слоев
   m_work_sloy_mode = Work_Sloy_mode;
   m_torg_sloy = TorgSloy;
   m_dd_lot_auto_calc = D_D_LOT_auto_calc;
   m_dd_lot = D_D_Lot;
   m_n_enable_sloy = N_enable_Sloy;
   
   // Установка параметров сетки
   m_hsetky = hSETKY;
   m_uvel_hsetky = Uvel_hSETKY;
   m_shag_uvel_hsetky = ShagUvel_hSETKY;
   
   // Установка параметров третьего слоя
   m_mode_enable_3_sloy = mode_enable_3_sloy;
   m_h_d_t = h_D_T * m_point_digits;
   m_pr_h_3_sl = pr_h_3_sl;
   
   // Установка параметров торговли
   m_slippage = slippage;
   m_protection_tp = ProtectionTP;
   m_tralling_stop = TrallingStop;
   m_trall_tp = TrallTP;
   m_detailed_logging = DetailedLogging;
   m_min_proc_sv_sr = Min_Proc_Sv_Sr;
   
   // Установка визуальных параметров
   m_show_table_on_testing = ShowTableOnTesting;
   m_text_size = Text_Syze;
   m_color_table_on_testing = ColorTableOnTesting;
   m_color_logotip_name = ColorLogotipName;
   m_color_logotip_site = ColorLogotipSite;
   m_color_trade_area_of_3rd_layer = color_Trade_area_of_3rd_layer;
   
   // Инициализация параметров фильтра тренда
   m_use_trend_filter = UseTrendFilter;
   m_trade_only_with_trend = TradeOnlyWithTrend;
   m_trend_detection_method = TrendDetectionMethod;
   m_adx_period = ADX_Period;
   m_adx_trend_level = ADX_TrendLevel;
   m_adx_use_di_filter = ADX_UseDIFilter;
   m_ma_fast_period = MA_Fast_Period;
   m_ma_slow_period = MA_Slow_Period;
   m_ma_method = MA_Method;
   m_ma_applied_price = MA_Applied_Price;
   m_ma_signal_bar = MA_SignalBar;
   m_trend_mode_setka = TrendMode_Setka;
   m_flat_mode_setka = FlatMode_Setka;
   m_trend_mode_lot_mult = TrendMode_LotMult;
   m_flat_mode_lot_mult = FlatMode_LotMult;
   
   // Инициализация параметров балансировки позиций
   m_use_position_balancing = UsePositionBalancing;
   m_max_position_imbalance = MaxPositionImbalance;
   m_force_balance_on_imbalance = ForceBalanceOnImbalance;
   // Инициализация множителей лотов для разных рыночных условий
   m_buy_trend_lot_mult = 1.0;
   m_sell_trend_lot_mult = 1.0;
   m_buy_flat_lot_mult = 1.0;
   m_sell_flat_lot_mult = 1.0;
   m_adx_di_threshold = ADX_DI_Threshold;
   m_current_position_balance = 1.0; // Инициализация с нейтрального значения
   m_current_market_type = 0; // По умолчанию считаем рынок флэтовым
   
   // Инициализация параметров Heiken Ashi Smoothed
   m_use_heiken_ashi_filter = UseHeikenAshiFilter;
   m_heiken_ashi_ma_method1 = HeikenAshiMaMethod1;
   m_heiken_ashi_ma_period1 = HeikenAshiMaPeriod1;
   m_heiken_ashi_ma_method2 = HeikenAshiMaMethod2;
   m_heiken_ashi_ma_period2 = HeikenAshiMaPeriod2;
   m_heiken_ashi_confirm_trend = HeikenAshiConfirmTrend;
   
   // Инициализация буферов для индикатора Heiken Ashi
   ArrayResize(m_ha_open_buffer, 1000);
   ArrayResize(m_ha_close_buffer, 1000);
   ArrayResize(m_ha_high_buffer, 1000);
   ArrayResize(m_ha_low_buffer, 1000);
   ArraySetAsSeries(m_ha_open_buffer, true);
   ArraySetAsSeries(m_ha_close_buffer, true);
   ArraySetAsSeries(m_ha_high_buffer, true);
   ArraySetAsSeries(m_ha_low_buffer, true);
   
   // Инициализация параметров адаптивного размера сетки
   m_use_adaptive_grid = UseAdaptiveGrid;
   m_use_atr_for_grid = UseATRForGrid;
   m_atr_period = ATR_Period;
   m_atr_multiplier = ATR_Multiplier;
   m_session_based_settings = SessionBasedSettings;
   m_asian_session_multiplier = AsianSessionMultiplier;
   m_european_session_multiplier = EuropeanSessionMultiplier;
   m_american_session_multiplier = AmericanSessionMultiplier;
   m_auto_adjust_for_symbol = AutoAdjustForSymbol;
   m_forex_major_multiplier = ForexMajorMultiplier;
   m_forex_cross_multiplier = ForexCrossMultiplier;
   m_commodity_multiplier = CommodityMultiplier;
   m_index_multiplier = IndexMultiplier;
   m_current_grid_step = 0.0; // Будет рассчитан позже
   
   // Инициализация параметров улучшенной адаптации к волатильности
   m_use_enhanced_volatility = UseEnhancedVolatility;
   m_use_adaptive_atr_period = UseAdaptiveATRPeriod;
   m_atr_period_min = ATR_Period_Min;
   m_atr_period_max = ATR_Period_Max;
   m_atr_volatility_threshold = ATR_Volatility_Threshold;
   m_current_atr_period = m_atr_period; // Начальное значение - стандартный период
   m_prev_atr_value = 0.0;
   m_use_mtf_volatility = UseMultiTimeframeVolatility;
   m_volatility_tf1 = VolatilityTimeframe1;
   m_volatility_tf2 = VolatilityTimeframe2;
   m_volatility_tf3 = VolatilityTimeframe3;
   m_volatility_weight1 = VolatilityWeight1;
   m_volatility_weight2 = VolatilityWeight2;
   m_volatility_weight3 = VolatilityWeight3;
   m_atr_handle_tf1 = INVALID_HANDLE;
   m_atr_handle_tf2 = INVALID_HANDLE;
   m_atr_handle_tf3 = INVALID_HANDLE;

   // Инициализация параметров оптимизированной системы динамического ATR
   m_use_optimal_atr_selection = UseOptimalATRSelection;
   m_atr_optimization_period = ATR_OptimizationPeriod;
   m_atr_efficiency_threshold = ATR_EfficiencyThreshold;
   m_use_atr_volatility_regime = UseATRVolatilityRegime;
   m_low_volatility_threshold = LowVolatilityThreshold;
   m_high_volatility_threshold = HighVolatilityThreshold;
   m_optimal_atr_period = m_atr_period;
   m_last_atr_optimization = 0;
   m_volatility_regime = 1; // Начинаем со средней волатильности
   ArrayResize(m_atr_efficiency_history, m_atr_period_max - m_atr_period_min + 1);
   ArrayInitialize(m_atr_efficiency_history, 0.0);

   // Инициализация параметров сезонного анализа волатильности
   m_use_seasonal_volatility = UseSeasonalVolatilityAnalysis;
   m_use_hourly_pattern = UseHourlyVolatilityPattern;
   m_use_daily_pattern = UseDailyVolatilityPattern;
   m_use_session_pattern = UseSessionVolatilityPattern;
   m_volatility_history_days = VolatilityHistoryDays;
   m_seasonal_volatility_weight = SeasonalVolatilityWeight;
   m_last_seasonal_update = 0;
   ArrayInitialize(m_hourly_volatility_pattern, 1.0);
   ArrayInitialize(m_daily_volatility_pattern, 1.0);
   ArrayInitialize(m_session_volatility_pattern, 1.0);

   // Инициализация параметров улучшенной фильтрации всплесков
   m_use_statistical_spike_detection = UseStatisticalSpikeDetection;
   m_spike_detection_sigma = SpikeDetectionSigma;
   m_spike_analysis_period = SpikeAnalysisPeriod;
   m_use_adaptive_spike_threshold = UseAdaptiveSpikeThreshold;
   m_min_spike_threshold = MinSpikeThreshold;
   m_max_spike_threshold = MaxSpikeThreshold;
   m_current_spike_threshold = VolatilitySpikeThreshold;
   m_atr_mean = 0.0;
   m_atr_std_dev = 0.0;
   ArrayResize(m_atr_history_for_spikes, m_spike_analysis_period);
   ArrayInitialize(m_atr_history_for_spikes, 0.0);

   // Инициализация режима отладки
   m_debug_mode = DebugMode;

   // Инициализация параметров защиты от резких движений и фильтра рыночных событий
   m_detect_volatility_spikes = DetectVolatilitySpikes;
   m_volatility_spike_threshold = VolatilitySpikeThreshold;
   m_use_news_filter = UseNewsFilter;
   m_minutes_before_news = MinutesBeforeNews;
   m_minutes_after_news = MinutesAfterNews;
   m_analyze_liquidity = AnalyzeLiquidity;
   m_liquidity_threshold = LiquidityThreshold;
   m_is_spike_detected = false;
   m_is_news_time = false;
   m_current_liquidity = 1.0;
   m_last_spike_time = 0;
   m_last_liquidity_check = 0;
   m_last_atr_adjustment = 0;
   m_last_grid_step_calc = 0;
   
   // Инициализация параметров умного выхода из просадки
   m_use_smart_exit = UseSmartExit;
   m_recovery_method = RecoveryMethod;
   m_drawdown_max_percent = DrawdownMax_Percent;
   m_drawdown_max_time = DrawdownMax_Time;
   m_target_profit_percent = TargetProfit_Percent;
   m_max_recovery_positions = MaxRecoveryPositions;
   m_smart_lot_multiplier = SmartLotMultiplier;
   m_partial_close_percent = PartialClosePercent;
   m_use_market_structure = UseMarketStructure;
   m_min_profit_to_close = MinProfitToClose;
   m_trailing_activation_level = TrailingActivationLevel;
   m_drawdown_pause_after_exit = DrawdownPause_After_Exit;
   
   // Инициализация параметров интеллектуального восстановления
   m_use_smart_prediction = UseSmartPrediction;
   m_prediction_samples = PredictionSamples;
   m_accelerated_recovery_level = AcceleratedRecoveryLevel;
   m_use_adaptive_trailing = UseAdaptiveTrailing;
   m_trailing_acceleration = TrailingAcceleration;
   m_use_market_based_recovery = UseMarketBasedRecovery;
   m_drawdown_history_count = 0;
   m_predicted_max_drawdown = 0.0;
   m_drawdown_trend = 0.0;
   ArrayInitialize(m_drawdown_history, 0.0);
   
   // Инициализация состояния просадки и восстановления
   m_recovery_positions_added = 0;
   m_positions_closed = 0;
   m_drawdown_pause_until = 0;
   m_current_drawdown_percent = 0.0;
   m_recovery_percent = 0.0;
   ArrayInitialize(m_recovery_position_tickets, 0);
   
   // Определение текущей сессии и типа инструмента
   m_current_session = (ENUM_MARKET_SESSION)DetectCurrentSession();
   DetermineSymbolType();
   
   // Создание индикаторов, если используется фильтр тренда
   if(m_use_trend_filter)
   {
      if(m_trend_detection_method == 0 || m_trend_detection_method == 2) // ADX или оба метода
      {
         m_adx_handle = iADX(m_symbol, PERIOD_CURRENT, m_adx_period);
         if(m_adx_handle == INVALID_HANDLE)
         {
            Print("Ошибка создания индикатора ADX: ", GetLastError());
            
            // Пробуем создать индикатор еще раз с небольшой задержкой
            Sleep(100);
            m_adx_handle = iADX(m_symbol, PERIOD_CURRENT, m_adx_period);
            
            if(m_adx_handle == INVALID_HANDLE)
            {
               Print("Повторная ошибка создания индикатора ADX: ", GetLastError());
            return(INIT_FAILED);
            }
            else
            {
               Print("Индикатор ADX успешно создан со второй попытки");
            }
         }
      }
      
      if(m_trend_detection_method == 1 || m_trend_detection_method == 2) // MA или оба метода
      {
         m_ma_fast_handle = iMA(m_symbol, PERIOD_CURRENT, m_ma_fast_period, 0, (ENUM_MA_METHOD)m_ma_method, m_ma_applied_price);
         if(m_ma_fast_handle == INVALID_HANDLE)
         {
            Print("Ошибка создания быстрой MA");
            return(INIT_FAILED);
         }
         
         m_ma_slow_handle = iMA(m_symbol, PERIOD_CURRENT, m_ma_slow_period, 0, (ENUM_MA_METHOD)m_ma_method, m_ma_applied_price);
         if(m_ma_slow_handle == INVALID_HANDLE)
         {
            Print("Ошибка создания медленной MA");
            return(INIT_FAILED);
         }
      }
   }
   
   // Создание индикатора ATR, если используется адаптивный размер сетки
   if(m_use_adaptive_grid && m_use_atr_for_grid)
   {
      m_atr_handle = iATR(m_symbol, PERIOD_CURRENT, m_atr_period);
      if(m_atr_handle == INVALID_HANDLE)
      {
         Print("Ошибка создания индикатора ATR");
         return(INIT_FAILED);
      }
   }
   
   // Создание индикаторов для улучшенной адаптации к волатильности
   if(m_use_enhanced_volatility)
   {
      if(m_use_mtf_volatility)
      {
         // Создание ATR индикаторов для разных таймфреймов
         m_atr_handle_tf1 = iATR(m_symbol, m_volatility_tf1, m_atr_period);
         if(m_atr_handle_tf1 == INVALID_HANDLE)
         {
            Print("Ошибка создания индикатора ATR для таймфрейма 1");
            return(INIT_FAILED);
         }
         
         m_atr_handle_tf2 = iATR(m_symbol, m_volatility_tf2, m_atr_period);
         if(m_atr_handle_tf2 == INVALID_HANDLE)
         {
            Print("Ошибка создания индикатора ATR для таймфрейма 2");
            return(INIT_FAILED);
         }
         
         m_atr_handle_tf3 = iATR(m_symbol, m_volatility_tf3, m_atr_period);
         if(m_atr_handle_tf3 == INVALID_HANDLE)
         {
            Print("Ошибка создания индикатора ATR для таймфрейма 3");
            return(INIT_FAILED);
         }
      }
   }
   
   // Создание встроенного индикатора Heiken Ashi Smoothed, если используется
   if(m_use_heiken_ashi_filter)
   {
      // Используем встроенную реализацию индикатора
      m_heiken_ashi_handle = CreateHeikenAshiSmoothedIndicator(
                                    m_symbol, PERIOD_CURRENT,
                                    m_heiken_ashi_ma_method1, m_heiken_ashi_ma_period1,
                                    m_heiken_ashi_ma_method2, m_heiken_ashi_ma_period2);
      
      if(m_heiken_ashi_handle == INVALID_HANDLE)
      {
         Print("Ошибка создания встроенного индикатора Heiken Ashi Smoothed: ", GetLastError());
         m_use_heiken_ashi_filter = false; // Отключаем фильтр, если индикатор не создан
      }
      else
      {
         Print("Встроенный индикатор Heiken Ashi Smoothed успешно инициализирован");
      }
   }
   
   // Установка таймера на 1 секунду для обновления информации
   EventSetTimer(1);
   
   // --- Автоматическое определение смещения времени сервера относительно UTC ---
   int offset_seconds = (int)(TimeCurrent() - TimeGMT());
   m_server_time_offset = (int)MathRound((double)offset_seconds / 3600.0);
   Print("[Turbo-profit] Автоматически определено смещение времени сервера относительно UTC: ", m_server_time_offset, " ч.");
   
   // Инициализация параметров автокалибровки
   m_use_auto_calibration = UseAutoCalibration;
   m_calibration_interval = CalibrationInterval;
   m_low_risk_dd = LowRiskDrawdown;
   m_medium_risk_dd = MediumRiskDrawdown;
   m_high_risk_dd = HighRiskDrawdown;
   m_extreme_risk_dd = ExtremeRiskDrawdown;
   m_use_auto_trend_detection = UseAutoTrendDetection;
   m_adjust_grid_step = AdjustGridStepOnCalibration;
   m_adjust_lot_size = AdjustLotSizeOnCalibration;
   m_adjust_trailing = AdjustTrailingOnCalibration;
   m_last_calibration_time = 0;
   m_current_risk_level = 0;
   m_current_drawdown_pct = 0.0;
   m_max_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   m_original_lot = m_lot;
       m_original_hsetky = m_hsetky;
    m_original_lot_mult = m_lot_multiplicator;
    m_original_trailing = m_tralling_stop;
    
       // Инициализация переменных для работы с просадкой
   m_drawdown_detected = false;
   m_drawdown_start_time = 0;
   m_drawdown_max_value = 0.0;
   m_recovery_active = false;
   
   // Инициализация параметров многоступенчатой системы восстановления из просадки
   m_use_advanced_recovery = UseAdvancedRecovery;
   m_use_drawdown_prediction = UseDrawdownPrediction;
   m_recovery_levels_count = RecoveryLevelsCount;
   m_recovery_level_step = RecoveryLevelStep;
   m_use_adaptive_recovery = UseAdaptiveRecovery;
   m_use_market_based_recovery = UseMarketBasedRecovery;
   m_drawdown_history_size = DrawdownHistorySize;
   m_recovery_pause_hours = RecoveryPauseHours;
   m_current_drawdown_pct = 0.0;
   m_drawdown_max_value = 0.0;
   m_drawdown_detected = false;
   m_drawdown_start_time = 0;
   m_current_risk_level = 0;
   m_recovery_active = false;
   m_recovery_percent = 0.0;
   m_recovery_positions_added = 0;
   m_drawdown_pause_until = 0;
   m_last_calibration_time = 0;
   m_predicted_max_drawdown = 0.0;
   m_drawdown_trend = 0.0;

   // Инициализация массивов истории просадок
   ArrayResize(m_drawdown_history, m_drawdown_history_size);
   ArrayResize(m_drawdown_time_history, m_drawdown_history_size);
   ArrayInitialize(m_drawdown_history, 0.0);
   ArrayInitialize(m_drawdown_time_history, 0);
   m_drawdown_history_count = 0;
   m_current_recovery_level = 0;
   m_recovery_method = RECOVERY_AVERAGING;

   // Инициализация массива весов рыночных условий (4 типа рынка)
   ArrayResize(m_market_condition_weights, 4);
   ArrayInitialize(m_market_condition_weights, 0.0);

   // Инициализация умной адаптивной системы управления просадкой
   m_use_smart_drawdown_management = UseSmartDrawdownManagement;
   m_volatility_dd_multiplier = VolatilityDrawdownMultiplier;
   m_trend_dd_multiplier = TrendDrawdownMultiplier;
   m_flat_dd_multiplier = FlatDrawdownMultiplier;
   m_session_dd_multiplier = SessionDrawdownMultiplier;
   m_use_market_condition_filtering = UseMarketConditionFiltering;
   m_max_volatility_threshold = MaxVolatilityThreshold;
   m_use_dynamic_risk_adjustment = UseDynamicRiskAdjustment;
   m_risk_reduction_factor = RiskReductionFactor;
   m_use_historical_dd_analysis = UseHistoricalDrawdownAnalysis;
   m_historical_analysis_period = HistoricalAnalysisPeriod;
   m_dd_recovery_speed_factor = DrawdownRecoverySpeedFactor;

   // Инициализация адаптивных порогов просадки
   ArrayResize(m_adaptive_dd_threshold, 4); // 4 уровня риска
   m_adaptive_dd_threshold[0] = m_low_risk_dd;
   m_adaptive_dd_threshold[1] = m_medium_risk_dd;
   m_adaptive_dd_threshold[2] = m_high_risk_dd;
   m_adaptive_dd_threshold[3] = m_extreme_risk_dd;

   m_current_market_risk_factor = 1.0;
   m_historical_max_dd = 0.0;
   m_average_recovery_time = 0.0;
   m_market_conditions_favorable = true;

   // Первоначальная инициализация сезонных паттернов волатильности
   if(m_use_enhanced_volatility && m_use_seasonal_volatility)
   {
      // Принудительно обновляем сезонные паттерны при первом запуске
      m_last_seasonal_update = 0;
      UpdateSeasonalVolatilityPatterns();
   }

   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Анализ исторических просадок                                     |
//+------------------------------------------------------------------+
void AnalyzeHistoricalDrawdowns()
{
   if(m_drawdown_history_count < 3) return; // Недостаточно данных

   // Находим максимальную историческую просадку
   m_historical_max_dd = 0.0;
   double total_recovery_time = 0.0;
   int recovery_count = 0;

   for(int i = 0; i < m_drawdown_history_count; i++)
   {
      if(m_drawdown_history[i] > m_historical_max_dd)
      {
         m_historical_max_dd = m_drawdown_history[i];
      }

      // Рассчитываем время восстановления
      if(i > 0 && m_drawdown_history[i-1] > m_low_risk_dd && m_drawdown_history[i] <= m_low_risk_dd)
      {
         total_recovery_time += (m_drawdown_time_history[i] - m_drawdown_time_history[i-1]) / 3600.0; // в часах
         recovery_count++;
      }
   }

   if(recovery_count > 0)
   {
      m_average_recovery_time = total_recovery_time / recovery_count;
   }

   // Корректируем пороги на основе исторических данных
   if(m_historical_max_dd > 0)
   {
      double historical_factor = MathMin(1.5, m_historical_max_dd / m_low_risk_dd);
      for(int i = 0; i < 4; i++)
      {
         m_adaptive_dd_threshold[i] *= historical_factor;
      }
   }
}

//+------------------------------------------------------------------+
//| Проверка необходимости снижения риска                           |
//+------------------------------------------------------------------+
bool ShouldReduceRisk()
{
   // Проверяем текущую просадку
   if(m_current_drawdown_pct > m_low_risk_dd * 0.8) return true;

   // Проверяем волатильность
   if(IsHighVolatilityPeriod()) return true;

   // Проверяем неблагоприятные рыночные условия
   if(!IsMarketConditionsFavorable()) return true;

   // Проверяем историческую максимальную просадку
   if(m_historical_max_dd > 0 && m_current_drawdown_pct > m_historical_max_dd * 0.7) return true;

   return false;
}

//+------------------------------------------------------------------+
//| Применение динамической корректировки риска                     |
//+------------------------------------------------------------------+
void ApplyDynamicRiskAdjustment()
{
   // Снижаем все пороги просадки
   for(int i = 0; i < 4; i++)
   {
      m_adaptive_dd_threshold[i] *= m_risk_reduction_factor;
   }

   // Снижаем размер лота
   m_lot_multiplier *= m_risk_reduction_factor;

   if(m_debug_mode)
   {
      Print("Применена динамическая корректировка риска. Коэффициент: ", m_risk_reduction_factor);
   }
}

//+------------------------------------------------------------------+
//| Расчет порога просадки с учетом волатильности                   |
//+------------------------------------------------------------------+
double CalculateVolatilityAdjustedThreshold(double base_threshold)
{
   double adjusted_threshold = base_threshold;

   // Корректировка на основе волатильности
   double current_volatility = GetCurrentVolatility();
   double avg_volatility = 1.0; // Базовое значение для нормализации

   // Простая оценка средней волатильности
   static double stored_avg_volatility = 0.0;
   if(stored_avg_volatility == 0.0)
   {
      stored_avg_volatility = current_volatility;
   }
   else
   {
      stored_avg_volatility = 0.95 * stored_avg_volatility + 0.05 * current_volatility;
   }
   avg_volatility = stored_avg_volatility;

   if(avg_volatility > 0)
   {
      double volatility_ratio = current_volatility / avg_volatility;
      if(volatility_ratio > 1.5) // Высокая волатильность
      {
         adjusted_threshold *= m_volatility_dd_multiplier;
      }
      else if(volatility_ratio < 0.7) // Низкая волатильность
      {
         adjusted_threshold *= 0.8; // Более строгие пороги при низкой волатильности
      }
   }

   // Корректировка на основе рыночного фактора риска
   adjusted_threshold *= m_current_market_risk_factor;

   return adjusted_threshold;
}

//+------------------------------------------------------------------+
//| Проверка периода высокой волатильности                          |
//+------------------------------------------------------------------+
bool IsHighVolatilityPeriod()
{
   double current_volatility = GetCurrentVolatility();

   // Простая оценка средней волатильности
   static double stored_avg_volatility = 0.0;
   if(stored_avg_volatility == 0.0)
   {
      stored_avg_volatility = current_volatility;
      return false; // При первом вызове считаем волатильность нормальной
   }
   else
   {
      stored_avg_volatility = 0.95 * stored_avg_volatility + 0.05 * current_volatility;
   }

   if(stored_avg_volatility > 0)
   {
      return (current_volatility / stored_avg_volatility) > m_max_volatility_threshold;
   }

   return false;
}

//+------------------------------------------------------------------+
//| Обновление оценки рыночных условий                              |
//+------------------------------------------------------------------+
void UpdateMarketConditionsAssessment()
{
   m_market_conditions_favorable = IsMarketConditionsFavorable();

   // Обновляем умные пороги просадки каждые 4 часа
   static datetime last_update = 0;
   if(TimeCurrent() - last_update > 4 * 3600)
   {
      UpdateSmartDrawdownThresholds();
      last_update = TimeCurrent();
   }
}

//+------------------------------------------------------------------+
//| Определение текущей торговой сессии                               |
//+------------------------------------------------------------------+
int DetectCurrentSession()
{
   // Получение текущего времени сервера с учетом смещения
   datetime server_time = TimeCurrent() - m_server_time_offset * 3600;
   // Преобразование времени сервера в структуру с компонентами времени
   MqlDateTime dt;
   TimeToStruct(server_time, dt);
   int hour = dt.hour;
   // Определение сессии по времени (UTC)
   // Азиатская: 00:00-08:00
   // Европейская: 08:00-16:00
   // Американская: 16:00-24:00
   if(hour >= 0 && hour < 8)
      return SESSION_ASIAN;
   else if(hour >= 8 && hour < 16)
      return SESSION_EUROPEAN;
   else // hour >= 16 && hour < 24
      return SESSION_AMERICAN;
}

//+------------------------------------------------------------------+
//| Определение типа инструмента                                      |
//+------------------------------------------------------------------+
void DetermineSymbolType()
{
   string symbol = m_symbol;
   
   // Проверка на основные валютные пары
   if(StringFind(symbol, "USD") != -1 && 
      (StringFind(symbol, "EUR") != -1 || StringFind(symbol, "GBP") != -1 || 
       StringFind(symbol, "JPY") != -1 || StringFind(symbol, "AUD") != -1 || 
       StringFind(symbol, "NZD") != -1 || StringFind(symbol, "CAD") != -1 || 
       StringFind(symbol, "CHF") != -1))
   {
      m_symbol_type = TYPE_FOREX_MAJOR;
      return;
   }
   
   // Проверка на кросс-валютные пары
   if((StringFind(symbol, "EUR") != -1 || StringFind(symbol, "GBP") != -1 || 
       StringFind(symbol, "JPY") != -1 || StringFind(symbol, "AUD") != -1 || 
       StringFind(symbol, "NZD") != -1 || StringFind(symbol, "CAD") != -1 || 
       StringFind(symbol, "CHF") != -1) && StringFind(symbol, "USD") == -1)
   {
      m_symbol_type = TYPE_FOREX_CROSS;
      return;
   }
   
   // Проверка на сырьевые инструменты
   if(StringFind(symbol, "XAU") != -1 || StringFind(symbol, "XAG") != -1 || 
      StringFind(symbol, "GOLD") != -1 || StringFind(symbol, "SILVER") != -1 || 
      StringFind(symbol, "OIL") != -1 || StringFind(symbol, "BRENT") != -1)
   {
      m_symbol_type = TYPE_COMMODITY;
      return;
   }
   
   // Проверка на индексы
   if(StringFind(symbol, "JP") != -1 || StringFind(symbol, "DE") != -1 || 
      StringFind(symbol, "UK") != -1 || StringFind(symbol, "US") != -1 || 
      StringFind(symbol, "NQ") != -1 || StringFind(symbol, "SP") != -1)
   {
      m_symbol_type = TYPE_INDEX;
      return;
   }
   
   // По умолчанию - основная валютная пара
   m_symbol_type = TYPE_FOREX_MAJOR;
}

//+------------------------------------------------------------------+
//| Функция деинициализации эксперта                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Удаление таймера
   EventKillTimer();
   
   // Удаление объектов
   ObjectsDeleteAll(0, m_prefix);
   
   // Очистка комментария
   Comment("");
   
   // Освобождение индикаторов
   if(m_adx_handle != INVALID_HANDLE)
      IndicatorRelease(m_adx_handle);
      
   if(m_ma_fast_handle != INVALID_HANDLE)
      IndicatorRelease(m_ma_fast_handle);
      
   if(m_ma_slow_handle != INVALID_HANDLE)
      IndicatorRelease(m_ma_slow_handle);
      
   // Освобождение индикатора ATR
   if(m_atr_handle != INVALID_HANDLE)
      IndicatorRelease(m_atr_handle);
      
   // Освобождение индикаторов для улучшенной адаптации к волатильности
   if(m_atr_handle_tf1 != INVALID_HANDLE)
      IndicatorRelease(m_atr_handle_tf1);
      
   if(m_atr_handle_tf2 != INVALID_HANDLE)
      IndicatorRelease(m_atr_handle_tf2);
      
   if(m_atr_handle_tf3 != INVALID_HANDLE)
      IndicatorRelease(m_atr_handle_tf3);
      
   // Освобождение индикатора Heiken Ashi Smoothed
   if(m_heiken_ashi_handle != INVALID_HANDLE)
      IndicatorRelease(m_heiken_ashi_handle);
}

//+------------------------------------------------------------------+
//| Основная функция эксперта                                        |
//+------------------------------------------------------------------+
void OnTick()
{
   // Обновление максимального баланса для расчета просадки
   double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   if(current_balance > m_max_balance)
      m_max_balance = current_balance;

   // Обновление умной системы управления просадкой
   if(m_use_smart_drawdown_management)
   {
      UpdateMarketConditionsAssessment();
   }

   // Обновление индикаторов
   UpdateIndicators();
   
   // Получение информации о позициях
   GetPositionsInfo();
   
   // Определение текущего тренда рынка
   int prev_market_type = m_current_market_type;
   m_current_market_type = DetectMarketTrend();
   
   // Логирование изменения типа рынка
   if(prev_market_type != m_current_market_type)
   {
      string market_type_str = "";
      switch(m_current_market_type)
      {
         case 1: market_type_str = "восходящий тренд"; break;
         case -1: market_type_str = "нисходящий тренд"; break;
         case 0: market_type_str = "флэт"; break;
      }
      Print("Изменение типа рынка: ", market_type_str);
   }
   
   // Обновление и вывод информации о балансе позиций
   if(m_use_position_balancing)
   {
      double prev_balance = m_current_position_balance;
      m_current_position_balance = CalculatePositionBalance();
      
      // Вывод информации при значительном изменении баланса
      if(MathAbs(prev_balance - m_current_position_balance) > 0.5 || 
         (m_current_position_balance > m_max_position_imbalance && prev_balance <= m_max_position_imbalance) ||
         (m_current_position_balance < 1.0/m_max_position_imbalance && prev_balance >= 1.0/m_max_position_imbalance))
      {
         Print("Баланс позиций Buy/Sell: ", DoubleToString(m_current_position_balance, 2), 
               " (максимальный допустимый дисбаланс: ", DoubleToString(m_max_position_imbalance, 2), ")");
      }
   }
   
   // Определение активных слоев
   DetermineActiveLayers();
   
   // Определение экстремальных цен для 3-го слоя
   DetectExtremePrices();
   
   // Расчет уровней открытия и закрытия позиций
   CalculateLevels();
   
   // Обработка торговых операций с учетом рыночных условий
   ProcessTrade();
   
   // Управление умным выходом из просадки
   ManageSmartExit();
   
   // Обработка трейлинга
   ProcessTrailing();
   
   // Отображение информации о торговле
   ShowTradeInfo();
   
   // Отображение таблицы при тестировании
   if(m_show_table_on_testing && ((bool)MQLInfoInteger(MQL_TESTER) || (bool)MQLInfoInteger(MQL_VISUAL_MODE)))
   {
      ShowTable();
   }
   
   // Автокалибровка
   if(m_use_auto_calibration)
   {
      AutoCalibration();
   }
}

//+------------------------------------------------------------------+
//| Обновление данных индикаторов                                     |
//+------------------------------------------------------------------+
void UpdateIndicators()
{
   // Обновляем данные встроенного индикатора Heiken Ashi Smoothed, если он используется
   if(m_use_heiken_ashi_filter && m_heiken_ashi_handle != INVALID_HANDLE)
   {
      CalculateHeikenAshiSmoothed(10); // Обновляем последние 10 баров
   }
   
   // Обновляем данные индикаторов, если используется фильтр тренда
   if(m_use_trend_filter)
   {
      if(m_trend_detection_method == 0 || m_trend_detection_method == 2) // ADX или оба метода
      {
         // Копирование данных ADX
         static datetime last_adx_error = 0;
         bool adx_data_ok = true;
         
         if(CopyBuffer(m_adx_handle, 0, 0, 3, m_adx_main) <= 0)
         {
            adx_data_ok = false;
            if(TimeCurrent() - last_adx_error > 300) // Не чаще раза в 5 минут
            {
               Print("Ошибка копирования данных ADX (основная линия): ", GetLastError());
               last_adx_error = TimeCurrent();
            }
         }
         
         if(CopyBuffer(m_adx_handle, 1, 0, 3, m_plus_di) <= 0)
         {
            adx_data_ok = false;
            if(TimeCurrent() - last_adx_error > 300) // Не чаще раза в 5 минут
            {
               Print("Ошибка копирования данных ADX (+DI): ", GetLastError());
               last_adx_error = TimeCurrent();
            }
         }
         
         if(CopyBuffer(m_adx_handle, 2, 0, 3, m_minus_di) <= 0)
         {
            adx_data_ok = false;
            if(TimeCurrent() - last_adx_error > 300) // Не чаще раза в 5 минут
            {
               Print("Ошибка копирования данных ADX (-DI): ", GetLastError());
               last_adx_error = TimeCurrent();
            }
         }
         
         if(!adx_data_ok)
         {
            return;
         }
      }
      
      if(m_trend_detection_method == 1 || m_trend_detection_method == 2) // MA или оба метода
      {
         // Копирование данных MA
         static datetime last_ma_error = 0;
         bool ma_data_ok = true;
         
         if(CopyBuffer(m_ma_fast_handle, 0, 0, 3, m_ma_fast) <= 0)
         {
            ma_data_ok = false;
            if(TimeCurrent() - last_ma_error > 300) // Не чаще раза в 5 минут
            {
               Print("Ошибка копирования данных быстрой MA: ", GetLastError());
               last_ma_error = TimeCurrent();
            }
         }
         
         if(CopyBuffer(m_ma_slow_handle, 0, 0, 3, m_ma_slow) <= 0)
         {
            ma_data_ok = false;
            if(TimeCurrent() - last_ma_error > 300) // Не чаще раза в 5 минут
            {
               Print("Ошибка копирования данных медленной MA: ", GetLastError());
               last_ma_error = TimeCurrent();
            }
         }
         
         if(!ma_data_ok)
         {
            return;
         }
      }
   }
   
   // Обновляем данные ATR, если используется адаптивный размер сетки
   if(m_use_adaptive_grid && m_use_atr_for_grid)
   {
      double atr_buffer[];
      static datetime last_atr_error = 0;

      // Проверяем готовность данных индикатора перед копированием
      if(BarsCalculated(m_atr_handle) <= 0)
      {
         if(m_debug_mode && TimeCurrent() - last_atr_error > 300)
         {
            Print("ATR индикатор еще не готов");
            last_atr_error = TimeCurrent();
         }
         return;
      }

      if(CopyBuffer(m_atr_handle, 0, 0, 1, atr_buffer) <= 0)
      {
         // Ограничиваем частоту сообщений об ошибках
         if(m_debug_mode && TimeCurrent() - last_atr_error > 300) // Не чаще раза в 5 минут
         {
            Print("Ошибка копирования данных ATR: ", GetLastError());
            last_atr_error = TimeCurrent();
         }
         return;
      }
   }
   
   // Обновляем данные для мультитаймфреймового анализа волатильности
   if(m_use_enhanced_volatility && m_use_mtf_volatility)
   {
      double atr_buffer[];
      static datetime last_mtf_error = 0;
      bool mtf_data_ok = true;
      
      // Проверяем каждый таймфрейм с дополнительными проверками
      if(m_atr_handle_tf1 != INVALID_HANDLE)
      {
         // Проверяем готовность данных индикатора
         if(BarsCalculated(m_atr_handle_tf1) > 0)
         {
            if(CopyBuffer(m_atr_handle_tf1, 0, 0, 1, atr_buffer) <= 0)
            {
               mtf_data_ok = false;
               if(m_debug_mode && TimeCurrent() - last_mtf_error > 300) // Не чаще раза в 5 минут
               {
                  Print("Ошибка копирования данных ATR для таймфрейма 1: ", GetLastError());
                  last_mtf_error = TimeCurrent();
               }
            }
         }
         else
         {
            mtf_data_ok = false; // Данные еще не готовы
         }
      }

      if(m_atr_handle_tf2 != INVALID_HANDLE)
      {
         if(BarsCalculated(m_atr_handle_tf2) > 0)
         {
            if(CopyBuffer(m_atr_handle_tf2, 0, 0, 1, atr_buffer) <= 0)
            {
               mtf_data_ok = false;
               if(m_debug_mode && TimeCurrent() - last_mtf_error > 300) // Не чаще раза в 5 минут
               {
                  Print("Ошибка копирования данных ATR для таймфрейма 2: ", GetLastError());
                  last_mtf_error = TimeCurrent();
               }
            }
         }
         else
         {
            mtf_data_ok = false;
         }
      }

      if(m_atr_handle_tf3 != INVALID_HANDLE)
      {
         if(BarsCalculated(m_atr_handle_tf3) > 0)
         {
            if(CopyBuffer(m_atr_handle_tf3, 0, 0, 1, atr_buffer) <= 0)
            {
               mtf_data_ok = false;
               if(m_debug_mode && TimeCurrent() - last_mtf_error > 300) // Не чаще раза в 5 минут
               {
                  Print("Ошибка копирования данных ATR для таймфрейма 3: ", GetLastError());
                  last_mtf_error = TimeCurrent();
               }
            }
         }
         else
         {
            mtf_data_ok = false;
         }
      }
      
      if(!mtf_data_ok)
      {
         return;
      }
   }
}

//+------------------------------------------------------------------+
//| Получение информации о позициях                                  |
//+------------------------------------------------------------------+
void GetPositionsInfo()
{
   // Сброс массивов перед заполнением
   for(int layer_index = 0; layer_index < 3; layer_index++)
   {
      m_buy_positions_count[layer_index] = 0;
      m_sell_positions_count[layer_index] = 0;
      m_buy_lots_total[layer_index] = 0.0;
      m_sell_lots_total[layer_index] = 0.0;
   }
   
   // Проверка всех открытых позиций
   int positions_total = PositionsTotal();
   
   for(int i = 0; i < positions_total; i++)
   {
      ulong ticket = PositionGetTicket(i);
      
      if(ticket > 0)
         {
            string position_symbol = PositionGetString(POSITION_SYMBOL);
            long position_magic = PositionGetInteger(POSITION_MAGIC);
            long position_type = PositionGetInteger(POSITION_TYPE);
            double position_volume = PositionGetDouble(POSITION_VOLUME);
            double position_price = PositionGetDouble(POSITION_PRICE_OPEN);
            double position_sl = PositionGetDouble(POSITION_SL);
            double position_tp = PositionGetDouble(POSITION_TP);
            
            // Проверка, принадлежит ли позиция этому советнику
            if(position_symbol == m_symbol)
            {
               for(int layer_index = 0; layer_index < 3; layer_index++)
               {
                  // Проверка magic-номера слоя
                  if(position_magic == m_magic + layer_index)
                  {
                     if(position_type == POSITION_TYPE_BUY)
                     {
                        // Увеличение счетчика Buy позиций для слоя
                        m_buy_positions_count[layer_index]++;
                        
                        // Сохранение информации о позиции
                        m_buy_tickets[layer_index][m_buy_positions_count[layer_index]] = ticket;
                        m_buy_levels[layer_index][m_buy_positions_count[layer_index]] = position_price;
                        m_buy_lots[layer_index][m_buy_positions_count[layer_index]] = position_volume;
                        m_buy_sl[layer_index][m_buy_positions_count[layer_index]] = position_sl;
                        m_buy_tp[layer_index][m_buy_positions_count[layer_index]] = position_tp;
                        
                        // Учет общего объема Buy позиций для слоя
                        m_buy_lots_total[layer_index] += position_volume;
                     }
                     else if(position_type == POSITION_TYPE_SELL)
                     {
                        // Увеличение счетчика Sell позиций для слоя
                        m_sell_positions_count[layer_index]++;
                        
                        // Сохранение информации о позиции
                        m_sell_tickets[layer_index][m_sell_positions_count[layer_index]] = ticket;
                        m_sell_levels[layer_index][m_sell_positions_count[layer_index]] = position_price;
                        m_sell_lots[layer_index][m_sell_positions_count[layer_index]] = position_volume;
                        m_sell_sl[layer_index][m_sell_positions_count[layer_index]] = position_sl;
                        m_sell_tp[layer_index][m_sell_positions_count[layer_index]] = position_tp;
                        
                        // Учет общего объема Sell позиций для слоя
                        m_sell_lots_total[layer_index] += position_volume;
                  }
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Определение экстремальных цен для 3-го слоя                       |
//+------------------------------------------------------------------+
void DetectExtremePrices()
{
   // Анализ только если включен 3-й слой
   if(!m_mode_enable_3_sloy)
      return;
      
   m_max_price = 0.0;
   m_min_price = 999999.0;
   m_max_price_index = 0;
   m_min_price_index = 0;
   
   // Поиск максимальной и минимальной цены за последние N баров
   int history_bars = 100; // Количество анализируемых баров
   
   for(int i = 0; i < history_bars; i++)
   {
      double high = iHigh(m_symbol, PERIOD_H1, i);
      double low = iLow(m_symbol, PERIOD_H1, i);
      
      if(high > m_max_price)
      {
         m_max_price = high;
         m_max_price_index = i;
      }
      
      if(low < m_min_price)
      {
         m_min_price = low;
         m_min_price_index = i;
      }
   }
   
   // Расчет торговых уровней для 3-го слоя
   double price_range = m_max_price - m_min_price;
   double trade_zone_height = price_range * m_pr_h_3_sl / 100.0;
   
   m_trade_high = m_min_price + trade_zone_height;
   m_trade_low = m_max_price - trade_zone_height;
   
   // Создание прямоугольника торговой зоны 3-го слоя
   if(m_symbol == Symbol())
   {
      // Удаление существующего объекта
      ObjectDelete(0, m_trade_area_name);
      
      // Создание нового прямоугольника
      ObjectCreate(0, m_trade_area_name, OBJ_RECTANGLE, 0, 
                  iTime(m_symbol, PERIOD_H1, history_bars - 1), m_trade_high, 
                  iTime(m_symbol, PERIOD_H1, 0), m_trade_low);
                  
      // Установка свойств объекта
      ObjectSetInteger(0, m_trade_area_name, OBJPROP_COLOR, m_color_trade_area_of_3rd_layer);
      ObjectSetInteger(0, m_trade_area_name, OBJPROP_FILL, true);
      ObjectSetInteger(0, m_trade_area_name, OBJPROP_BACK, true);
      ObjectSetInteger(0, m_trade_area_name, OBJPROP_WIDTH, 1);
      ObjectSetInteger(0, m_trade_area_name, OBJPROP_STYLE, STYLE_SOLID);
      ObjectSetString(0, m_trade_area_name, OBJPROP_TEXT, "Trade Area 3rd Layer");
   }
}

//+------------------------------------------------------------------+
//| Поиск индекса максимальной цены                                  |
//+------------------------------------------------------------------+
int FindMaxPriceIndex(int layer_index, int dir)
{
   int result_index = 0;
   double max_price = 0.0;
   
   // Поиск максимальной цены в массиве уровней
   for(int i = 1; i <= (dir == 0 ? m_buy_positions_count[layer_index] : m_sell_positions_count[layer_index]); i++)
   {
      if(max_price <= (dir == 0 ? m_buy_levels[layer_index][i] : m_sell_levels[layer_index][i]))
      {
         max_price = (dir == 0 ? m_buy_levels[layer_index][i] : m_sell_levels[layer_index][i]);
         result_index = i;
      }
   }
   
   return result_index;
}

//+------------------------------------------------------------------+
//| Поиск индекса минимальной цены                                   |
//+------------------------------------------------------------------+
int FindMinPriceIndex(int layer_index, int dir)
{
   int result_index = 0;
   double min_price = 1000000.0;
   
   // Поиск минимальной цены в массиве уровней
   for(int i = 1; i <= (dir == 0 ? m_buy_positions_count[layer_index] : m_sell_positions_count[layer_index]); i++)
   {
      if(min_price >= (dir == 0 ? m_buy_levels[layer_index][i] : m_sell_levels[layer_index][i]))
      {
         min_price = (dir == 0 ? m_buy_levels[layer_index][i] : m_sell_levels[layer_index][i]);
         result_index = i;
      }
   }
   
   return result_index;
}

//+------------------------------------------------------------------+
//| Получение средней цены для заданного количества позиций          |
//+------------------------------------------------------------------+
double GetAveragePrice(int layer_index, int dir)
{
   double summ_price = 0.0;
   double summ_lots = 0.0;
   
   // Расчет средневзвешенной цены по объему лотов
   for(int i = 1; i <= (dir == 0 ? m_buy_positions_count[layer_index] : m_sell_positions_count[layer_index]); i++)
   {
      summ_price += (dir == 0 ? m_buy_levels[layer_index][i] * m_buy_lots[layer_index][i] : 
                               m_sell_levels[layer_index][i] * m_sell_lots[layer_index][i]);
      summ_lots += (dir == 0 ? m_buy_lots[layer_index][i] : m_sell_lots[layer_index][i]);
   }
   
   // Предотвращение деления на ноль
   if(summ_lots == 0.0)
      return 0.0;
      
   return summ_price / summ_lots;
}

//+------------------------------------------------------------------+
//| Получение сбалансированной цены между Buy и Sell позициями       |
//+------------------------------------------------------------------+
double GetBalancedPrice(int layer_index)
{
   double buy_price_sum = 0.0;
   double buy_lots_sum = 0.0;
   double sell_price_sum = 0.0;
   double sell_lots_sum = 0.0;
   
   // Расчет сумм для Buy позиций
   for(int i = 1; i <= m_buy_positions_count[layer_index]; i++)
   {
      buy_price_sum += m_buy_levels[layer_index][i] * m_buy_lots[layer_index][i];
      buy_lots_sum += m_buy_lots[layer_index][i];
   }
   
   // Расчет сумм для Sell позиций
   for(int i = 1; i <= m_sell_positions_count[layer_index]; i++)
   {
      sell_price_sum += m_sell_levels[layer_index][i] * m_sell_lots[layer_index][i];
      sell_lots_sum += m_sell_lots[layer_index][i];
   }
   
   // Предотвращение деления на ноль
   if(buy_lots_sum - sell_lots_sum == 0.0)
      return 0.0;
      
   return (buy_price_sum - sell_price_sum) / (buy_lots_sum - sell_lots_sum);
}

//+------------------------------------------------------------------+
//| Создание линии уровня                                            |
//+------------------------------------------------------------------+
void CreateLayerLine(string name, double price, color clr, int width = 1, int style = STYLE_SOLID)
{
   // Удаление объекта, если он существует
   ObjectDelete(0, name);
   
   // Создание новой горизонтальной линии
   ObjectCreate(0, name, OBJ_HLINE, 0, 0, price);
   ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
   ObjectSetInteger(0, name, OBJPROP_WIDTH, width);
   ObjectSetInteger(0, name, OBJPROP_STYLE, style);
}

//+------------------------------------------------------------------+
//| Удаление линии уровня                                            |
//+------------------------------------------------------------------+
void DeleteLayerLine(string name)
{
   ObjectDelete(0, name);
}

//+------------------------------------------------------------------+
//| Определение активных слоев                                        |
//+------------------------------------------------------------------+
void DetermineActiveLayers()
{
   // Расчет предела свободной маржи
   m_free_margin_limit = AccountInfoDouble(ACCOUNT_BALANCE) * m_min_proc_sv_sr / 100.0;
   bool margin_limit_reached = (m_min_proc_sv_sr < 100.0 && AccountInfoDouble(ACCOUNT_MARGIN_FREE) < m_free_margin_limit);
   
   // Получение текущих цен
   double ask = SymbolInfoDouble(m_symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(m_symbol, SYMBOL_BID);
   m_mid_price = (ask + bid) / 2.0;
   
   // Сброс активных слоев
   ArrayInitialize(m_active_layers, 0);
   
   // Если нет открытых позиций и не в режиме выхода, активируем слои согласно m_torg_sloy
   if(m_buy_positions_count[0] == 0 && m_sell_positions_count[0] == 0 && 
      m_buy_positions_count[1] == 0 && m_sell_positions_count[1] == 0 && 
      m_buy_positions_count[2] == 0 && m_sell_positions_count[2] == 0 && 
      !m_exit_mode)
   {
      // Активируем слои согласно настройке количества торговых слоев
      m_active_layers[0] = 2; // Всегда активируем первый слой
      
      if(m_torg_sloy >= 2) // Если используется 2 или более слоев
         m_active_layers[1] = 2;
         
      if(m_torg_sloy >= 3) // Если используется 3 слоя
         m_active_layers[2] = 2;
      
      // Если используем фильтр тренда и торгуем только по тренду
      if(m_use_trend_filter && m_trade_only_with_trend && m_current_market_type != 0)
      {
         if(m_current_market_type == 1) // Восходящий тренд
         {
            // Разрешаем только Buy позиции на всех активных слоях
            for(int i=0; i<3; i++)
               if(m_active_layers[i] > 0)
                  m_active_layers[i] = 2;
         }
         else if(m_current_market_type == -1) // Нисходящий тренд
         {
            // Разрешаем только Sell позиции на всех активных слоях
            for(int i=0; i<3; i++)
               if(m_active_layers[i] > 0)
                  m_active_layers[i] = 3;
         }
      }
   }
   else
   {
      // Активация слоев на основе наличия открытых позиций
      if(m_buy_positions_count[0] > 0 || m_sell_positions_count[0] > 0) m_active_layers[0] = 1;
      if(m_buy_positions_count[1] > 0 || m_sell_positions_count[1] > 0) m_active_layers[1] = 1;
      if(m_buy_positions_count[2] > 0 || m_sell_positions_count[2] > 0) m_active_layers[2] = 1;
      
      // Активируем слои, которые разрешены настройкой, даже если у них нет позиций
      if(m_active_layers[0] == 0 && m_torg_sloy >= 1) m_active_layers[0] = 2;
      if(m_active_layers[1] == 0 && m_torg_sloy >= 2) m_active_layers[1] = 2;
      if(m_active_layers[2] == 0 && m_torg_sloy >= 3) m_active_layers[2] = 2;
      
      // Ограничение открытия новых позиций против тренда
      if(m_use_trend_filter && m_trade_only_with_trend)
      {
         for(int layer_index = 0; layer_index < 3; layer_index++)
         {
            if(m_active_layers[layer_index] > 0)
            {
               if(m_current_market_type == 1) // Восходящий тренд
               {
                  // Разрешаем только Buy позиции, запрещаем Sell
                  if(m_buy_positions_count[layer_index] == 0 && m_sell_positions_count[layer_index] > 0)
                     m_active_layers[layer_index] = 3; // Только закрытие Sell
                  else
                     m_active_layers[layer_index] = 2; // Разрешено открытие Buy
               }
               else if(m_current_market_type == -1) // Нисходящий тренд
               {
                  // Разрешаем только Sell позиции, запрещаем Buy
                  if(m_buy_positions_count[layer_index] > 0 && m_sell_positions_count[layer_index] == 0)
                     m_active_layers[layer_index] = 2; // Только закрытие Buy
                  else
                     m_active_layers[layer_index] = 3; // Разрешено открытие Sell
               }
            }
         }
      }
   }
   
   // Проверка достаточности маржи
   if(margin_limit_reached)
   {
      // Отключение слоев при нехватке маржи
      if(m_torg_sloy == 1)
      {
         if(MathAbs(m_buy_lots_total[0] - m_sell_lots_total[0]) > 0.0) m_active_layers[0] = 0;
         if(MathAbs(m_buy_lots_total[1] - m_sell_lots_total[1]) > 0.0) m_active_layers[1] = 0;
         if(MathAbs(m_buy_lots_total[2] - m_sell_lots_total[2]) > 0.0) m_active_layers[2] = 0;
      }
   }
   
   // Удаление неактивных линий уровней
   if((m_buy_positions_count[0] == 0 && m_sell_positions_count[0] == 0) || m_active_layers[0] == 0)
   {
      DeleteLayerLine("NextBUY_0");
      DeleteLayerLine("NextSELL_0");
   }
   
   if((m_buy_positions_count[1] == 0 && m_sell_positions_count[1] == 0) || m_active_layers[1] == 0)
   {
      DeleteLayerLine("NextBUY_1");
      DeleteLayerLine("NextSELL_1");
   }
   
   if((m_buy_positions_count[2] == 0 && m_sell_positions_count[2] == 0) || m_active_layers[2] == 0)
   {
      DeleteLayerLine("NextBUY_2");
      DeleteLayerLine("NextSELL_2");
   }
}

//+------------------------------------------------------------------+
//| Расчет уровней открытия и закрытия позиций                        |
//+------------------------------------------------------------------+
void CalculateLevels()
{
   // Проверка всех слоев
   for(int layer_index = 0; layer_index < 3; layer_index++)
   {
      if(m_active_layers[layer_index] > 0)
      {
         // Расчет адаптивного размера сетки, если включена соответствующая опция
         double buy_grid_step, sell_grid_step;
         
         if(m_use_adaptive_grid)
         {
            // Получение адаптивного шага сетки
            CalcAdaptiveGridStep();
            
            // Использование рассчитанного шага сетки
            buy_grid_step = m_current_grid_step;
            sell_grid_step = m_current_grid_step;
         }
         else
         {
            // Использование стандартных настроек шага сетки
            // Базовый шаг сетки в зависимости от типа рынка
            if(m_current_market_type == 0) // Флэт
            {
               buy_grid_step = m_flat_mode_setka * m_point * m_point_digits;
               sell_grid_step = m_flat_mode_setka * m_point * m_point_digits;
            }
            else // Тренд
            {
               buy_grid_step = m_trend_mode_setka * m_point * m_point_digits;
               sell_grid_step = m_trend_mode_setka * m_point * m_point_digits;
            }
            
            // Расчет шага сетки с учетом режима увеличения
            if(m_uvel_hsetky == 1) // Режим увеличения шага 1
            {
               if(m_buy_positions_count[layer_index] > 1)
                  buy_grid_step = (m_hsetky + m_shag_uvel_hsetky * (m_buy_positions_count[layer_index] - 1)) * m_point * m_point_digits;
                  
               if(m_sell_positions_count[layer_index] > 1)
                  sell_grid_step = (m_hsetky + m_shag_uvel_hsetky * (m_sell_positions_count[layer_index] - 1)) * m_point * m_point_digits;
            }
            else if(m_uvel_hsetky == 2) // Режим увеличения шага 2
            {
               if(m_buy_positions_count[layer_index] > 1)
                  buy_grid_step = (m_hsetky - m_shag_uvel_hsetky * (m_buy_positions_count[layer_index] - 1)) * m_point * m_point_digits;
                  
               if(m_sell_positions_count[layer_index] > 1)
                  sell_grid_step = (m_hsetky - m_shag_uvel_hsetky * (m_sell_positions_count[layer_index] - 1)) * m_point * m_point_digits;
            }
         }
         
         // Установка минимального шага сетки
         if(buy_grid_step < 10 * m_point_digits * m_point) buy_grid_step = 10 * m_point_digits * m_point;
         if(sell_grid_step < 10 * m_point_digits * m_point) sell_grid_step = 10 * m_point_digits * m_point;
         
         // Создание линий уровней для открытия новых позиций
         color line_colors[3][2] = {
            {clrRed, clrBlue},
            {clrMagenta, clrTeal},
            {clrMaroon, clrNavy}
         };
         
         string line_names[3][2] = {
            {"NextBUY_0", "NextSELL_0"},
            {"NextBUY_1", "NextSELL_1"},
            {"NextBUY_2", "NextSELL_2"}
         };
         
         // Отображение линий уровней в зависимости от количества позиций
         if(m_buy_positions_count[layer_index] > 0 || m_sell_positions_count[layer_index] > 0)
         {
            int buy_min_index = FindMinPriceIndex(layer_index, 0);
            int buy_max_index = FindMaxPriceIndex(layer_index, 0);
            int sell_min_index = FindMinPriceIndex(layer_index, 1);
            int sell_max_index = FindMaxPriceIndex(layer_index, 1);
            
            // Создаем линии уровней открытия в зависимости от соотношения количества позиций
            if(m_buy_positions_count[layer_index] > m_sell_positions_count[layer_index])
            {
               if(m_active_layers[layer_index] != 3) // Не запрещено открытие Buy
               {
                  CreateLayerLine(line_names[layer_index][0], m_buy_levels[layer_index][buy_min_index] - buy_grid_step, 
                                  line_colors[layer_index][0], 1, 7);
               }
               
               if(m_active_layers[layer_index] != 2) // Не запрещено открытие Sell
               {
                  CreateLayerLine(line_names[layer_index][1], m_buy_levels[layer_index][buy_max_index] + sell_grid_step, 
                                  line_colors[layer_index][1], 1, 7);
               }
            }
            else if(m_buy_positions_count[layer_index] < m_sell_positions_count[layer_index])
            {
               if(m_active_layers[layer_index] != 3) // Не запрещено открытие Buy
               {
                  CreateLayerLine(line_names[layer_index][0], m_sell_levels[layer_index][sell_min_index] - buy_grid_step, 
                                  line_colors[layer_index][0], 1, 7);
               }
               
               if(m_active_layers[layer_index] != 2) // Не запрещено открытие Sell
               {
                  CreateLayerLine(line_names[layer_index][1], m_sell_levels[layer_index][sell_max_index] + sell_grid_step, 
                                  line_colors[layer_index][1], 1, 7);
               }
            }
            else if(m_buy_positions_count[layer_index] == m_sell_positions_count[layer_index] && 
                    m_buy_positions_count[layer_index] > 0)
            {
               if(m_active_layers[layer_index] != 3) // Не запрещено открытие Buy
               {
                  CreateLayerLine(line_names[layer_index][0], m_buy_levels[layer_index][buy_min_index] - buy_grid_step, 
                                  line_colors[layer_index][0], 1, 7);
               }
               
               if(m_active_layers[layer_index] != 2) // Не запрещено открытие Sell
               {
                  CreateLayerLine(line_names[layer_index][1], m_sell_levels[layer_index][sell_max_index] + sell_grid_step, 
                                  line_colors[layer_index][1], 1, 7);
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Расчет адаптивного размера сетки с учетом всех факторов           |
//+------------------------------------------------------------------+
void CalcAdaptiveGridStep()
{
   // Проверяем, не слишком ли часто вызываем функцию (не чаще раза в минуту)
   if(TimeCurrent() - m_last_grid_step_calc < 60)
      return;
      
   m_last_grid_step_calc = TimeCurrent();
      
   // Базовый размер сетки
   double base_grid_step = m_hsetky * m_point * m_point_digits;
   
   // Коэффициент для адаптивного размера сетки
   double adaptive_multiplier = 1.0;
   
   // 1. УЛУЧШЕННЫЙ АНАЛИЗ ВОЛАТИЛЬНОСТИ
   // 1.1. Многоуровневый анализ ATR
   if(m_use_atr_for_grid)
   {
      double atr_buffer[];
      double atr_h4_buffer[];
      double atr_d1_buffer[];
      
      // Получение значения ATR на текущем таймфрейме
      if(CopyBuffer(m_atr_handle, 0, 0, 3, atr_buffer) > 0)
      {
         double atr_value = atr_buffer[0];
         
         // Если ATR успешно получен, используем его для корректировки шага сетки
         if(atr_value > 0)
         {
            adaptive_multiplier *= (atr_value * m_atr_multiplier) / (m_point * m_point_digits);
         }
      }
      
         // Анализ старших таймфреймов для определения глобальной волатильности
   static bool atr_warning_shown = false;
   
   // Используем try-catch подход с флагами для обработки ошибок
   bool h4_data_ok = false;
   bool d1_data_ok = false;
   
   // Инициализируем буферы
   if(ArraySize(atr_h4_buffer) == 0)
      ArrayResize(atr_h4_buffer, 1);
   if(ArraySize(atr_d1_buffer) == 0)
      ArrayResize(atr_d1_buffer, 1);
   
   // Получаем данные H4
   int h4_handle = iATR(m_symbol, PERIOD_H4, 14);
   if(h4_handle != INVALID_HANDLE)
   {
      // Проверяем готовность данных
      if(BarsCalculated(h4_handle) > 0)
      {
         if(CopyBuffer(h4_handle, 0, 0, 1, atr_h4_buffer) > 0)
         {
            h4_data_ok = true;
         }
         else if(m_debug_mode && !atr_warning_shown)
         {
            Print("Ошибка копирования данных ATR H4: ", GetLastError());
         }
      }
      IndicatorRelease(h4_handle);
   }
   else if(m_debug_mode && !atr_warning_shown)
   {
      Print("Ошибка создания индикатора ATR H4: ", GetLastError());
   }

   // Получаем данные D1
   int d1_handle = iATR(m_symbol, PERIOD_D1, 14);
   if(d1_handle != INVALID_HANDLE)
   {
      if(BarsCalculated(d1_handle) > 0)
      {
         if(CopyBuffer(d1_handle, 0, 0, 1, atr_d1_buffer) > 0)
         {
            d1_data_ok = true;
         }
         else if(m_debug_mode && !atr_warning_shown)
         {
            Print("Ошибка копирования данных ATR D1: ", GetLastError());
         }
      }
      IndicatorRelease(d1_handle);
   }
   else if(m_debug_mode && !atr_warning_shown)
   {
      Print("Ошибка создания индикатора ATR D1: ", GetLastError());
   }
   
   // Устанавливаем флаг, чтобы не спамить логи
   atr_warning_shown = true;
   
   // Если все данные получены успешно, корректируем множитель
   if(h4_data_ok && d1_data_ok && atr_buffer[0] > 0 && atr_d1_buffer[0] > 0)
   {
      // Соотношение текущей волатильности к дневной
      double volatility_ratio = atr_buffer[0] / atr_d1_buffer[0];
      
      // Если текущая волатильность выше дневной - увеличиваем шаг
      if(volatility_ratio > 0.3) // Более 30% от дневной волатильности
      {
         adaptive_multiplier *= (1.0 + (volatility_ratio - 0.3) * 2.0);
      }
      // Если волатильность низкая - уменьшаем шаг для более частых сделок
      else if(volatility_ratio < 0.1) // Менее 10% от дневной волатильности
      {
         adaptive_multiplier *= MathMax(0.7, 1.0 - (0.1 - volatility_ratio) * 3.0);
      }
   }
   }
   
   // 2. УЛУЧШЕННЫЙ АНАЛИЗ ТРЕНДА И ФЛЭТА
   // 2.1. Более точное определение типа рынка с плавным переходом
   double trend_strength = CalculateTrendStrength();
   
   // Плавный переход между режимами тренда и флэта
   double trend_mode_step = m_trend_mode_setka * m_point * m_point_digits;
   double flat_mode_step = m_flat_mode_setka * m_point * m_point_digits;
   
   // Базовый шаг с плавным переходом между трендом и флэтом
   base_grid_step = flat_mode_step * (1.0 - trend_strength) + trend_mode_step * trend_strength;
   
   // 2.2. Анализ силы импульса для определения потенциала движения
   double momentum_factor = 1.0;
   
   // Встраиваем код функции CalculateMomentumFactor() для избежания ошибок компиляции
   int momentum_handle = iMomentum(m_symbol, PERIOD_CURRENT, 14, PRICE_CLOSE);
   
   if(momentum_handle != INVALID_HANDLE)
   {
      double momentum_buffer[];
      if(CopyBuffer(momentum_handle, 0, 0, 1, momentum_buffer) > 0)
      {
         // Нормализация значения Momentum
         double normalized_momentum = MathAbs(momentum_buffer[0] - 100.0) / 10.0;
         
         // Корректировка множителя в зависимости от силы импульса
         if(normalized_momentum > 3.0) // Сильный импульс
            momentum_factor = 1.3;
         else if(normalized_momentum > 1.5) // Средний импульс
            momentum_factor = 1.15;
         else if(normalized_momentum < 0.5) // Слабый импульс
            momentum_factor = 0.9;
      }
      else
      {
         // Ошибка копирования данных, используем значение по умолчанию
         static datetime last_momentum_error = 0;
         if(TimeCurrent() - last_momentum_error > 300) // Выводим сообщение не чаще раза в 5 минут
         {
            Print("Ошибка копирования данных индикатора Momentum: ", GetLastError());
            last_momentum_error = TimeCurrent();
         }
      }
      
      IndicatorRelease(momentum_handle);
   }
   else
   {
      static datetime last_momentum_create_error = 0;
      if(TimeCurrent() - last_momentum_create_error > 300) // Выводим сообщение не чаще раза в 5 минут
      {
         Print("Ошибка создания индикатора Momentum: ", GetLastError());
         last_momentum_create_error = TimeCurrent();
      }
   }
   
   adaptive_multiplier *= momentum_factor;
   
   // 3. АНАЛИЗ РЫНОЧНЫХ УРОВНЕЙ
   // 3.1. Проверка близости к ключевым уровням поддержки/сопротивления
   double level_distance_factor = AnalyzeKeyLevelsProximity();
   adaptive_multiplier *= level_distance_factor;
   
   // 3.2. Учет ценовых паттернов
   // Упрощенная реализация для избежания ошибок компиляции
   double pattern_factor = 1.0;
   
   // Получаем данные для анализа паттернов
   MqlRates pattern_rates[];
   if(CopyRates(m_symbol, PERIOD_CURRENT, 0, 10, pattern_rates) > 0)
   {
      // Проверка на флаг или вымпел (консолидация после импульса)
      if(ArraySize(pattern_rates) >= 7)
      {
         // Проверяем наличие импульса
         double impulse = MathAbs(pattern_rates[6].close - pattern_rates[3].close);
         
         // Проверяем консолидацию после импульса
         double consolidation_range = 0;
         for(int i = 0; i < 3; i++)
         {
            consolidation_range = MathMax(consolidation_range, pattern_rates[i].high - pattern_rates[i].low);
         }
         
         if(impulse > 3 * consolidation_range)
         {
            pattern_factor = 0.9; // Уменьшаем шаг сетки для более частых сделок во время консолидации
         }
      }
   }
   
   adaptive_multiplier *= pattern_factor;
   
   // 4. УЧЕТ ТЕКУЩЕГО СОСТОЯНИЯ ПОРТФЕЛЯ
   // 4.1. Корректировка в зависимости от текущего P/L
   double current_profit = CalculateTotalProfit();
   double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   
   // Если убыток превышает определенный процент баланса, увеличиваем шаг сетки
   if(current_profit < -0.02 * account_balance)
   {
      double loss_percent = MathAbs(current_profit) / account_balance;
      double risk_adjustment = 1.0 + MathMin(loss_percent * 5.0, 2.0); // Максимум удвоение шага
      adaptive_multiplier *= risk_adjustment;
   }
   
   // 4.2. Учет количества открытых позиций
   int total_positions = m_buy_positions_count[0] + m_buy_positions_count[1] + m_buy_positions_count[2] +
                         m_sell_positions_count[0] + m_sell_positions_count[1] + m_sell_positions_count[2];
                         
   if(total_positions > 10)
   {
      // Увеличиваем шаг сетки при большом количестве позиций
      adaptive_multiplier *= (1.0 + (total_positions - 10) * 0.05);
   }
   
   // 5. УЧЕТ РЫНОЧНЫХ УСЛОВИЙ
   // 5.1. Учет текущей торговой сессии с более точной настройкой
   if(m_session_based_settings)
   {
      switch(m_current_session)
      {
         case SESSION_ASIAN:
            // Азиатская сессия обычно менее волатильна
            adaptive_multiplier *= m_asian_session_multiplier * (1.0 - trend_strength * 0.3);
            break;
         case SESSION_EUROPEAN:
            // Европейская сессия - средняя волатильность
            adaptive_multiplier *= m_european_session_multiplier;
            break;
         case SESSION_AMERICAN:
            // Американская сессия обычно более волатильна
            adaptive_multiplier *= m_american_session_multiplier * (1.0 + trend_strength * 0.2);
            break;
      }
   }
   
   // 5.2. Учет дня недели
   MqlDateTime dt;
   TimeToStruct(TimeCurrent(), dt);
   int day_of_week = dt.day_of_week;
   switch(day_of_week)
   {
      case 1: // Понедельник
         adaptive_multiplier *= 0.95; // Часто низкая волатильность
         break;
      case 5: // Пятница
         adaptive_multiplier *= 1.1; // Повышенная волатильность перед выходными
         break;
   }
   
   // 5.3. Учет типа инструмента с более точной настройкой
   if(m_auto_adjust_for_symbol)
   {
      switch((ENUM_SYMBOL_TYPE)m_symbol_type)
      {
         case TYPE_FOREX_MAJOR:
            adaptive_multiplier *= m_forex_major_multiplier;
            break;
         case TYPE_FOREX_CROSS:
            adaptive_multiplier *= m_forex_cross_multiplier * (1.0 + trend_strength * 0.1);
            break;
         case TYPE_COMMODITY:
            adaptive_multiplier *= m_commodity_multiplier * (1.0 + trend_strength * 0.2);
            break;
         case TYPE_INDEX:
            adaptive_multiplier *= m_index_multiplier * (1.0 + trend_strength * 0.15);
            break;
      }
   }
   
   // 6. УЛУЧШЕННАЯ ОБРАБОТКА ЭКСТРЕМАЛЬНЫХ УСЛОВИЙ
   // 6.1. Учет всплесков волатильности
   if(m_detect_volatility_spikes && DetectVolatilitySpike())
   {
      // Значительно увеличиваем шаг сетки при всплеске волатильности
      adaptive_multiplier *= 1.8;
      
      static datetime last_spike_message = 0;
      if(TimeCurrent() - last_spike_message > 600)
      {
         Print("Значительное увеличение шага сетки из-за всплеска волатильности");
         last_spike_message = TimeCurrent();
      }
   }
   
   // 6.2. Учет ликвидности рынка с более детальным анализом
   if(m_analyze_liquidity)
   {
      double liquidity = AssessMarketLiquidity();
      double spread_factor = SymbolInfoInteger(m_symbol, SYMBOL_SPREAD) / 10.0; // Нормализация спреда
      
      // Комплексная оценка ликвидности с учетом спреда
      double liquidity_factor = (m_liquidity_threshold / MathMax(liquidity, 0.1)) * (1.0 + spread_factor * 0.2);
      
      adaptive_multiplier *= liquidity_factor;
      
      static datetime last_liquidity_message = 0;
      if(liquidity < m_liquidity_threshold && TimeCurrent() - last_liquidity_message > 600)
      {
         Print("Корректировка шага сетки из-за изменения ликвидности, множитель: ", DoubleToString(liquidity_factor, 2));
         last_liquidity_message = TimeCurrent();
      }
   }
   
   // Расчет итогового адаптивного шага сетки
   m_current_grid_step = base_grid_step * adaptive_multiplier;
   
   // Ограничение минимального и максимального шага сетки
   double min_grid_step = 10 * m_point_digits * m_point;
   double max_grid_step = 200 * m_point_digits * m_point; // Добавляем верхний предел
   
   if(m_current_grid_step < min_grid_step)
      m_current_grid_step = min_grid_step;
   if(m_current_grid_step > max_grid_step)
      m_current_grid_step = max_grid_step;
      
   // Логирование для анализа - с ограничением частоты и только при существенных изменениях
   static double last_logged_grid_step = 0;
   static double last_logged_multiplier = 0;
   static datetime last_log_time = 0;
   static int log_counter = 0;
   
   // Выводим информацию в следующих случаях:
   // 1. Не чаще раза в 5 минут (300 секунд)
   // 2. При существенном изменении шага сетки (более 15%)
   // 3. При существенном изменении множителя (более 0.5)
   // 4. Не более одного сообщения каждые 10 тиков (для предотвращения спама)
   if((TimeCurrent() - last_log_time > 300 || 
       (last_logged_grid_step > 0 && MathAbs(m_current_grid_step - last_logged_grid_step)/last_logged_grid_step > 0.15) || 
       MathAbs(adaptive_multiplier - last_logged_multiplier) > 0.5) &&
       (++log_counter % 10 == 0))
   {
      string market_type = m_current_market_type == 1 ? "тренд вверх" : (m_current_market_type == -1 ? "тренд вниз" : "флэт");
      
      Print("Адаптивный шаг сетки: ", DoubleToString(m_current_grid_step/(m_point*m_point_digits), 1), 
            " пунктов, множитель: ", DoubleToString(adaptive_multiplier, 2),
            ", базовый шаг: ", DoubleToString(base_grid_step/(m_point*m_point_digits), 1),
            ", тип рынка: ", market_type);
            
      last_logged_grid_step = m_current_grid_step;
      last_logged_multiplier = adaptive_multiplier;
      last_log_time = TimeCurrent();
   }
}

//+------------------------------------------------------------------+
//| Расчет силы тренда (от 0.0 до 1.0)                               |
//+------------------------------------------------------------------+
double CalculateTrendStrength()
{
   // Используем несколько индикаторов для более точного определения силы тренда
   double trend_strength = 0.0;
   
   // 1. Анализ на основе ADX
   static datetime last_adx_error = 0;
   int adx_handle = iADX(m_symbol, PERIOD_CURRENT, 14);
   if(adx_handle != INVALID_HANDLE)
   {
      double adx_buffer[];
      if(CopyBuffer(adx_handle, 0, 0, 1, adx_buffer) > 0)
      {
         // ADX > 25 указывает на наличие тренда, ADX > 50 - на сильный тренд
         if(adx_buffer[0] > 50)
            trend_strength += 0.5;
         else if(adx_buffer[0] > 25)
            trend_strength += 0.3;
         else if(adx_buffer[0] > 15)
            trend_strength += 0.1;
      }
      else
      {
         // Ограничиваем частоту сообщений об ошибках
         if(TimeCurrent() - last_adx_error > 300) // Не чаще раза в 5 минут
         {
            Print("Ошибка копирования данных индикатора ADX: ", GetLastError());
            last_adx_error = TimeCurrent();
         }
      }
      IndicatorRelease(adx_handle);
   }
   else
   {
      // Ограничиваем частоту сообщений об ошибках
      if(TimeCurrent() - last_adx_error > 300) // Не чаще раза в 5 минут
      {
         Print("Ошибка создания индикатора ADX: ", GetLastError());
         last_adx_error = TimeCurrent();
      }
   }
   
   // 2. Анализ на основе MA
   static bool ma_warning_shown = false;
   int ma_fast_handle = iMA(m_symbol, PERIOD_CURRENT, 20, 0, MODE_EMA, PRICE_CLOSE);
   int ma_slow_handle = iMA(m_symbol, PERIOD_CURRENT, 50, 0, MODE_EMA, PRICE_CLOSE);
   
   bool ma_fast_ok = false;
   bool ma_slow_ok = false;
   
   double ma_fast_buffer[];
   double ma_slow_buffer[];
   
   // Проверяем быструю MA
   if(ma_fast_handle != INVALID_HANDLE)
   {
      if(CopyBuffer(ma_fast_handle, 0, 0, 3, ma_fast_buffer) > 0)
      {
         ma_fast_ok = true;
      }
      else if(!ma_warning_shown)
      {
         Print("Ошибка копирования данных быстрой MA: ", GetLastError());
      }
      IndicatorRelease(ma_fast_handle);
   }
   else if(!ma_warning_shown)
   {
      Print("Ошибка создания индикатора быстрой MA: ", GetLastError());
   }
   
   // Проверяем медленную MA
   if(ma_slow_handle != INVALID_HANDLE)
   {
      if(CopyBuffer(ma_slow_handle, 0, 0, 3, ma_slow_buffer) > 0)
      {
         ma_slow_ok = true;
      }
      else if(!ma_warning_shown)
      {
         Print("Ошибка копирования данных медленной MA: ", GetLastError());
      }
      IndicatorRelease(ma_slow_handle);
   }
   else if(!ma_warning_shown)
   {
      Print("Ошибка создания индикатора медленной MA: ", GetLastError());
   }
   
   // Устанавливаем флаг, чтобы не спамить логи
   ma_warning_shown = true;
   
   // Если все данные получены успешно, анализируем тренд
   if(ma_fast_ok && ma_slow_ok)
   {
      // Расчет угла наклона MA
      double fast_slope = (ma_fast_buffer[0] - ma_fast_buffer[2]) / 2.0;
      double slow_slope = (ma_slow_buffer[0] - ma_slow_buffer[2]) / 2.0;
      
      // Нормализация наклона
      double normalized_fast_slope = MathAbs(fast_slope) / (0.0001 * SymbolInfoDouble(m_symbol, SYMBOL_BID));
      double normalized_slow_slope = MathAbs(slow_slope) / (0.0001 * SymbolInfoDouble(m_symbol, SYMBOL_BID));
      
      // Учет наклона MA
      if(normalized_fast_slope > 0.5)
         trend_strength += 0.2;
      if(normalized_slow_slope > 0.3)
         trend_strength += 0.2;
         
      // Учет пересечения MA
      if((ma_fast_buffer[0] > ma_slow_buffer[0] && ma_fast_buffer[2] < ma_slow_buffer[2]) || 
         (ma_fast_buffer[0] < ma_slow_buffer[0] && ma_fast_buffer[2] > ma_slow_buffer[2]))
      {
         trend_strength += 0.1; // Недавнее пересечение может указывать на начало нового тренда
      }
   }
   
   // 3. Анализ на основе свечных паттернов
   // Используем простой алгоритм вместо вызова отдельной функции для избежания ошибок компиляции
   MqlRates rates[];
   if(CopyRates(m_symbol, PERIOD_CURRENT, 0, 5, rates) > 0)
   {
      // Проверка на наличие длинных свечей (признак сильного движения)
      double avg_body_size = 0.0;
      for(int i = 0; i < 5; i++)
      {
         avg_body_size += MathAbs(rates[i].close - rates[i].open);
      }
      avg_body_size /= 5.0;
      
      // Проверка размера тела текущей свечи
      double current_body = MathAbs(rates[0].close - rates[0].open);
      if(current_body > avg_body_size * 1.5)
      {
         trend_strength += 0.1; // Длинная свеча - сильное движение
      }
      
      // Проверка на последовательность свечей в одном направлении
      int bullish_count = 0;
      int bearish_count = 0;
      
      for(int i = 0; i < 3; i++)
      {
         if(rates[i].close > rates[i].open)
            bullish_count++;
         else if(rates[i].close < rates[i].open)
            bearish_count++;
      }
      
      if(bullish_count >= 3 || bearish_count >= 3)
      {
         trend_strength += 0.15; // Последовательность одинаковых свечей - признак тренда
      }
   }
   
   // Ограничиваем результат в диапазоне [0.0, 1.0]
   return MathMin(1.0, trend_strength);
}

//+------------------------------------------------------------------+
//| Анализ свечных паттернов для определения силы тренда              |
//+------------------------------------------------------------------+
// Эта функция больше не используется, так как её код перенесен в CalculateTrendStrength
// для избежания ошибок компиляции
double AnalyzeCandlePatterns()
{
   return 0.0;
}

//+------------------------------------------------------------------+
//| Расчет фактора импульса для определения потенциала движения       |
//+------------------------------------------------------------------+
double CalculateMomentumFactor()
{
   double momentum_factor = 1.0;
   
   // Используем индикатор Momentum для оценки
   int momentum_handle = iMomentum(m_symbol, PERIOD_CURRENT, 14, PRICE_CLOSE);
   
   if(momentum_handle != INVALID_HANDLE)
   {
      double momentum_buffer[];
      if(CopyBuffer(momentum_handle, 0, 0, 1, momentum_buffer) > 0)
      {
         // Нормализация значения Momentum
         double normalized_momentum = MathAbs(momentum_buffer[0] - 100.0) / 10.0;
         
         // Корректировка множителя в зависимости от силы импульса
         if(normalized_momentum > 3.0) // Сильный импульс
            momentum_factor = 1.3;
         else if(normalized_momentum > 1.5) // Средний импульс
            momentum_factor = 1.15;
         else if(normalized_momentum < 0.5) // Слабый импульс
            momentum_factor = 0.9;
      }
      
      IndicatorRelease(momentum_handle);
   }
   
   return momentum_factor;
}

//+------------------------------------------------------------------+
//| Анализ близости к ключевым уровням поддержки/сопротивления        |
//+------------------------------------------------------------------+
double AnalyzeKeyLevelsProximity()
{
   double level_factor = 1.0;
   double current_price = SymbolInfoDouble(m_symbol, SYMBOL_BID);
   
   // Получаем данные для анализа уровней
   MqlRates rates[];
   if(CopyRates(m_symbol, PERIOD_D1, 0, 20, rates) <= 0)
      return level_factor;
   
   // Находим локальные максимумы и минимумы (упрощенный алгоритм)
   double levels[];
   int levels_count = 0;
   
   for(int i = 1; i < ArraySize(rates) - 1; i++)
   {
      // Локальный максимум
      if(rates[i].high > rates[i-1].high && rates[i].high > rates[i+1].high)
      {
         ArrayResize(levels, levels_count + 1);
         levels[levels_count] = rates[i].high;
         levels_count++;
      }
      
      // Локальный минимум
      if(rates[i].low < rates[i-1].low && rates[i].low < rates[i+1].low)
      {
         ArrayResize(levels, levels_count + 1);
         levels[levels_count] = rates[i].low;
         levels_count++;
      }
   }
   
   // Добавляем психологические уровни (круглые числа)
   double price_magnitude = MathPow(10, MathFloor(MathLog10(current_price)));
   for(int i = 1; i <= 10; i++)
   {
      double psych_level = i * price_magnitude;
      if(psych_level > rates[ArraySize(rates)-1].low && psych_level < rates[ArraySize(rates)-1].high)
      {
         ArrayResize(levels, levels_count + 1);
         levels[levels_count] = psych_level;
         levels_count++;
      }
   }
   
   // Проверяем близость к уровням
   double min_distance = DBL_MAX;
   for(int i = 0; i < levels_count; i++)
   {
      double distance = MathAbs(current_price - levels[i]) / current_price;
      if(distance < min_distance)
         min_distance = distance;
   }
   
   // Корректируем фактор в зависимости от близости к уровням
   if(min_distance < 0.0005) // Очень близко к уровню (0.05%)
      level_factor = 1.5; // Увеличиваем шаг сетки
   else if(min_distance < 0.001) // Близко к уровню (0.1%)
      level_factor = 1.3;
   else if(min_distance < 0.002) // Относительно близко (0.2%)
      level_factor = 1.1;
   
   return level_factor;
}

//+------------------------------------------------------------------+
//| Анализ ценовых паттернов для определения потенциала движения      |
//+------------------------------------------------------------------+
double AnalyzePricePatterns()
{
   double pattern_factor = 1.0;
   
   // Получаем данные для анализа паттернов
   MqlRates rates[];
   if(CopyRates(m_symbol, PERIOD_CURRENT, 0, 10, rates) <= 0)
      return pattern_factor;
   
   // 1. Проверка на паттерн "Двойная вершина" или "Двойное дно"
   bool double_top = false;
   bool double_bottom = false;
   
   // Упрощенный алгоритм проверки
   if(ArraySize(rates) >= 5)
   {
      // Проверка на двойную вершину
      if(MathAbs(rates[1].high - rates[3].high) / rates[1].high < 0.001 && 
         rates[2].high < rates[1].high && rates[2].high < rates[3].high)
      {
         double_top = true;
      }
      
      // Проверка на двойное дно
      if(MathAbs(rates[1].low - rates[3].low) / rates[1].low < 0.001 && 
         rates[2].low > rates[1].low && rates[2].low > rates[3].low)
      {
         double_bottom = true;
      }
   }
   
   if(double_top || double_bottom)
   {
      pattern_factor = 1.2; // Потенциальный разворот
   }
   
   // 2. Проверка на флаг или вымпел (консолидация после импульса)
   bool flag_pattern = false;
   
   if(ArraySize(rates) >= 7)
   {
      // Проверяем наличие импульса
      double impulse = MathAbs(rates[6].close - rates[3].close);
      
      // Проверяем консолидацию после импульса
      double consolidation_range = 0;
      for(int i = 0; i < 3; i++)
      {
         consolidation_range = MathMax(consolidation_range, rates[i].high - rates[i].low);
      }
      
      if(impulse > 3 * consolidation_range)
      {
         flag_pattern = true;
      }
   }
   
   if(flag_pattern)
   {
      pattern_factor = 0.9; // Уменьшаем шаг сетки для более частых сделок во время консолидации
   }
   
   return pattern_factor;
}

//+------------------------------------------------------------------+
//| Расчет общего профита по всем открытым позициям                   |
//+------------------------------------------------------------------+
double CalculateTotalProfit()
{
   double total_profit = 0.0;
   
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      ulong ticket = PositionGetTicket(i);
      if(ticket > 0)
      {
         string symbol = PositionGetString(POSITION_SYMBOL);
         ulong magic = PositionGetInteger(POSITION_MAGIC);
         ulong magic_max = (ulong)m_magic + 2; // Явное приведение типа для избежания ошибок
         
            if(symbol == m_symbol && magic >= (ulong)m_magic && magic <= magic_max)
         {
            total_profit += PositionGetDouble(POSITION_PROFIT) + 
                           PositionGetDouble(POSITION_SWAP);
         }
      }
   }
   
   return total_profit;
}

//+------------------------------------------------------------------+
//| Открытие позиции                                                 |
//+------------------------------------------------------------------+
bool OpenPosition(string symbol, int type, double lot, double price, double sl, double tp, int magic, color clr)
{
   // Настройка объекта для торговых операций
   m_trade.SetExpertMagicNumber(magic);
   m_trade.SetDeviationInPoints(m_slippage);
   m_trade.SetTypeFillingBySymbol(m_symbol);
   
   bool result = false;
   
   if(type == 0) // Buy
   {
      result = m_trade.Buy(lot, symbol, price, sl, tp, symbol + " MGK " + IntegerToString(magic) + 
               " - слой. № " + IntegerToString(magic - m_magic));
   }
   else if(type == 1) // Sell
   {
      result = m_trade.Sell(lot, symbol, price, sl, tp, symbol + " MGK " + IntegerToString(magic) + 
                " - слой. № " + IntegerToString(magic - m_magic));
   }
   
   // Ожидание обработки ордера
   Sleep(1000);
   
   return result;
}

//+------------------------------------------------------------------+
//| Закрытие позиций определенного слоя и направления                |
//+------------------------------------------------------------------+
void ClosePositions(int layer_index, int direction)
{
   if(direction == 0) // Buy позиции
   {
      for(int i = 1; i <= m_buy_positions_count[layer_index]; i++)
      {
         if(m_buy_tickets[layer_index][i] > 0)
         {
            if(PositionSelectByTicket(m_buy_tickets[layer_index][i]))
            {
               m_trade.PositionClose(m_buy_tickets[layer_index][i], m_slippage);
            }
         }
      }
   }
   else if(direction == 1) // Sell позиции
   {
      for(int i = 1; i <= m_sell_positions_count[layer_index]; i++)
      {
         if(m_sell_tickets[layer_index][i] > 0)
         {
            if(PositionSelectByTicket(m_sell_tickets[layer_index][i]))
            {
               m_trade.PositionClose(m_sell_tickets[layer_index][i], m_slippage);
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Обработка торговли                                               |
//+------------------------------------------------------------------+
void ProcessTrade()
{
   // Обновление баланса позиций
   if(m_use_position_balancing)
      m_current_position_balance = CalculatePositionBalance();

   // Проверка умной системы управления просадкой
   if(m_use_smart_drawdown_management)
   {
      // Проверка благоприятности рыночных условий
      if(m_use_market_condition_filtering && !IsMarketConditionsFavorable())
      {
         if(m_debug_mode)
         {
            Print("Торговля временно приостановлена из-за неблагоприятных рыночных условий");
         }
         return;
      }

      // Проверка превышения максимального порога волатильности
      if(IsHighVolatilityPeriod())
      {
         if(m_debug_mode)
         {
            Print("Торговля временно приостановлена из-за высокой волатильности");
         }
         return;
      }
   }

   // Проверка улучшенной адаптации к волатильности
   if(m_use_enhanced_volatility)
   {
      // Проверка на всплески волатильности
      if(m_detect_volatility_spikes && DetectVolatilitySpike())
      {
         Print("Торговля временно приостановлена из-за всплеска волатильности");
         return;
      }
      
      // Проверка на время важных новостей
      if(m_use_news_filter && IsImportantNewsTime())
      {
         Print("Торговля временно приостановлена из-за близости важных новостей");
         return;
      }
      
      // Проверка ликвидности рынка
      if(m_analyze_liquidity)
      {
         double liquidity = AssessMarketLiquidity();
         if(liquidity < m_liquidity_threshold * 0.5) // Критически низкая ликвидность
         {
            Print("Торговля временно приостановлена из-за критически низкой ликвидности: ", 
                  DoubleToString(liquidity, 2));
            return;
         }
      }
      
      // Динамическая корректировка ATR
      if(m_use_adaptive_atr_period)
      {
         // Вызываем функцию без дополнительного логирования
         AdjustATRPeriod();
      }
   }
   
   // Обновление адаптивного шага сетки
   if(m_use_adaptive_grid)
   {
      // Просто вызываем функцию расчета адаптивного шага сетки
      // Логирование уже реализовано внутри функции
      CalcAdaptiveGridStep();
   }
   
   // Проверка всех слоев
   for(int layer_index = 0; layer_index < 3; layer_index++)
   {
      if(m_active_layers[layer_index] > 0)
      {
         // Получение текущих цен
         double ask = SymbolInfoDouble(m_symbol, SYMBOL_ASK);
         double bid = SymbolInfoDouble(m_symbol, SYMBOL_BID);
         
         // Определение индексов максимальных и минимальных цен
         int buy_max_index = 0;
         int buy_min_index = 0;
         int sell_max_index = 0;
         int sell_min_index = 0;
         
         if(m_buy_positions_count[layer_index] > 0)
         {
            buy_max_index = FindMaxPriceIndex(layer_index, 0);
            buy_min_index = FindMinPriceIndex(layer_index, 0);
         }
         
         if(m_sell_positions_count[layer_index] > 0)
         {
            sell_max_index = FindMaxPriceIndex(layer_index, 1);
            sell_min_index = FindMinPriceIndex(layer_index, 1);
         }
         
         // Флаги для закрытия и открытия позиций
         bool close_buy = false;
         bool close_sell = false;
         bool open_buy = false;
         bool open_sell = false;
         
         // Расчет шага сетки
         double buy_grid_step = m_hsetky * m_point * m_point_digits;
         double sell_grid_step = m_hsetky * m_point * m_point_digits;
         
         // Если используется адаптивный размер сетки, применяем его
         if(m_use_adaptive_grid)
         {
            buy_grid_step = m_current_grid_step;
            sell_grid_step = m_current_grid_step;
         }
         else
         {
         // Расчет шага сетки с учетом режима увеличения
         if(m_uvel_hsetky == 1) // Режим увеличения шага 1
         {
            if(m_buy_positions_count[layer_index] < 2) buy_grid_step = m_hsetky * m_point * m_point_digits;
            if(m_sell_positions_count[layer_index] < 2) sell_grid_step = m_hsetky * m_point * m_point_digits;
            
            if(m_buy_positions_count[layer_index] > 1)
               buy_grid_step = (m_hsetky + m_shag_uvel_hsetky * (m_buy_positions_count[layer_index] - 1)) * m_point * m_point_digits;
               
            if(m_sell_positions_count[layer_index] > 1)
               sell_grid_step = (m_hsetky + m_shag_uvel_hsetky * (m_sell_positions_count[layer_index] - 1)) * m_point * m_point_digits;
         }
         else if(m_uvel_hsetky == 2) // Режим увеличения шага 2
         {
            if(m_buy_positions_count[layer_index] < 2) buy_grid_step = m_hsetky * m_point * m_point_digits;
            if(m_sell_positions_count[layer_index] < 2) sell_grid_step = m_hsetky * m_point * m_point_digits;
            
            if(m_buy_positions_count[layer_index] > 1)
               buy_grid_step = (m_hsetky - m_shag_uvel_hsetky * (m_buy_positions_count[layer_index] - 1)) * m_point * m_point_digits;
               
            if(m_sell_positions_count[layer_index] > 1)
               sell_grid_step = (m_hsetky - m_shag_uvel_hsetky * (m_sell_positions_count[layer_index] - 1)) * m_point * m_point_digits;
            }
         }
         
         // Минимальный шаг сетки не менее 10 пунктов
         if(buy_grid_step < 10 * m_point_digits * m_point) buy_grid_step = 10 * m_point_digits * m_point;
         if(sell_grid_step < 10 * m_point_digits * m_point) sell_grid_step = 10 * m_point_digits * m_point;
         
         // Проверка условий для закрытия Buy позиций
         if(m_buy_positions_count[layer_index] > 1 && m_sell_positions_count[layer_index] > 1 && 
            ask > m_buy_levels[layer_index][buy_max_index] + buy_grid_step)
         {
            close_buy = true;
         }
         
         // Проверка условий для закрытия Sell позиций
         if(m_sell_positions_count[layer_index] > 1 && m_buy_positions_count[layer_index] > 1 && 
            bid < m_sell_levels[layer_index][sell_min_index] - sell_grid_step)
         {
            close_sell = true;
         }
         
         // Закрытие Buy позиций
         if(close_buy || m_exit_mode)
         {
            ClosePositions(layer_index, 0); // 0 - Buy позиции
         }
         
         // Закрытие Sell позиций
         if(close_sell || m_exit_mode)
         {
            ClosePositions(layer_index, 1); // 1 - Sell позиции
         }
         
         // Проверка условий для открытия позиций
         if(m_buy_positions_count[layer_index] == 0 && m_sell_positions_count[layer_index] == 0)
         {
            open_buy = true;
            open_sell = true;
            
            // Проверка тренда для открытия позиций
            if(m_use_trend_filter && m_trade_only_with_trend)
            {
               if(m_current_market_type == 1) // Восходящий тренд
               {
                  open_sell = false; // Запрещаем открытие Sell позиций против тренда
               }
               else if(m_current_market_type == -1) // Нисходящий тренд
               {
                  open_buy = false; // Запрещаем открытие Buy позиций против тренда
               }
            }
         }
         
         if(m_buy_positions_count[layer_index] == 1 && m_sell_positions_count[layer_index] == 1)
         {
            if(ask < m_buy_levels[layer_index][buy_min_index] - buy_grid_step)
               open_buy = true;
               
            if(bid > m_sell_levels[layer_index][sell_max_index] + sell_grid_step)
               open_sell = true;
               
            // Проверка тренда для открытия позиций
            if(m_use_trend_filter && m_trade_only_with_trend)
            {
               if(m_current_market_type == 1) // Восходящий тренд
               {
                  open_sell = false; // Запрещаем открытие Sell позиций против тренда
               }
               else if(m_current_market_type == -1) // Нисходящий тренд
               {
                  open_buy = false; // Запрещаем открытие Buy позиций против тренда
               }
            }
         }
         
         if(m_buy_positions_count[layer_index] > 1 && m_sell_positions_count[layer_index] > 0)
         {
            if(bid < m_buy_levels[layer_index][buy_min_index] - buy_grid_step)
               open_buy = true;
               
            // Проверка тренда
            if(m_use_trend_filter && m_trade_only_with_trend && m_current_market_type == -1)
               open_buy = false; // Запрещаем Buy в нисходящем тренде
         }
         
         if(m_sell_positions_count[layer_index] > 1 && m_buy_positions_count[layer_index] > 0)
         {
            if(bid > m_sell_levels[layer_index][sell_max_index] + sell_grid_step)
               open_sell = true;
               
            // Проверка тренда
            if(m_use_trend_filter && m_trade_only_with_trend && m_current_market_type == 1)
               open_sell = false; // Запрещаем Sell в восходящем тренде
         }
         
         if(m_buy_positions_count[layer_index] > 0 && m_sell_positions_count[layer_index] == 0)
         {
            if(bid < m_buy_levels[layer_index][buy_min_index] - buy_grid_step)
               open_buy = true;
               
            // Проверка тренда
            if(m_use_trend_filter && m_trade_only_with_trend && m_current_market_type == -1)
               open_buy = false; // Запрещаем Buy в нисходящем тренде
         }
         
         if(m_buy_positions_count[layer_index] == 0 && m_sell_positions_count[layer_index] > 0)
         {
            if(bid > m_sell_levels[layer_index][sell_max_index] + sell_grid_step)
               open_sell = true;
               
            // Проверка тренда
            if(m_use_trend_filter && m_trade_only_with_trend && m_current_market_type == 1)
               open_sell = false; // Запрещаем Sell в восходящем тренде
         }
         
         // Проверка сигналов Heiken Ashi
         if(m_use_heiken_ashi_filter)
         {
            int heiken_ashi_signal = GetHeikenAshiSignal();
            static datetime last_heiken_log = 0;

            if(heiken_ashi_signal == 1) // Сигнал на покупку
            {
               open_sell = false; // Запрещаем открытие Sell позиций против сигнала
               if(m_debug_mode && TimeCurrent() - last_heiken_log > 3600) // Не чаще раза в час
               {
                  Print("Ограничение Sell из-за сигнала Heiken Ashi на покупку");
                  last_heiken_log = TimeCurrent();
               }
            }
            else if(heiken_ashi_signal == -1) // Сигнал на продажу
            {
               open_buy = false; // Запрещаем открытие Buy позиций против сигнала
               if(m_debug_mode && TimeCurrent() - last_heiken_log > 3600) // Не чаще раза в час
               {
                  Print("Ограничение Buy из-за сигнала Heiken Ashi на продажу");
                  last_heiken_log = TimeCurrent();
               }
            }
         }
         
         // Проверка баланса позиций перед открытием новых
         if(m_use_position_balancing)
         {
            // Если дисбаланс в сторону Buy, ограничиваем открытие новых Buy позиций
            if(m_current_position_balance > m_max_position_imbalance && m_force_balance_on_imbalance)
            {
               open_buy = false;
               Print("Ограничение Buy из-за дисбаланса позиций: ", DoubleToString(m_current_position_balance, 2));
            }
            
            // Если дисбаланс в сторону Sell, ограничиваем открытие новых Sell позиций
            if(m_current_position_balance < 1.0/m_max_position_imbalance && m_force_balance_on_imbalance)
            {
               open_sell = false;
               Print("Ограничение Sell из-за дисбаланса позиций: ", DoubleToString(m_current_position_balance, 2));
            }
         }
         
         // Открытие Buy позиции, если условия выполнены
         if(open_buy && m_active_layers[layer_index] != 3 && !m_exit_mode)
         {
            double new_lot = AdaptiveLotCalculation(0, layer_index);
            // Открытие Buy позиции
            OpenPosition(m_symbol, 0, new_lot, ask, 0, 0, m_magic + layer_index, clrRed);
         }
         
         // Открытие Sell позиции, если условия выполнены
         if(open_sell && m_active_layers[layer_index] != 2 && !m_exit_mode)
         {
            double new_lot = AdaptiveLotCalculation(1, layer_index);
            // Открытие Sell позиции
            OpenPosition(m_symbol, 1, new_lot, bid, 0, 0, m_magic + layer_index, clrBlue);
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Обработка трейлинга                                              |
//+------------------------------------------------------------------+
void ProcessTrailing()
{
   // Если включен умный выход из просадки, используем его собственные функции трейлинга
   if(m_use_smart_exit)
   {
      // Для каждого слоя применяем соответствующий тип трейлинга
      for(int layer_index = 0; layer_index < 3; layer_index++)
      {
         if(m_active_layers[layer_index] > 0)
         {
            if(m_use_adaptive_trailing)
               ApplyAdaptiveTrailing(layer_index);
            else
               ApplyProgressiveTrailing(layer_index);
         }
      }
      return;
   }
   
   // Улучшенный трейлинг-стоп для стандартного режима (без умного выхода из просадки)
   for(int layer_index = 0; layer_index < 3; layer_index++)
   {
      if(m_active_layers[layer_index] > 0)
      {
         double ask = SymbolInfoDouble(m_symbol, SYMBOL_ASK);
         double bid = SymbolInfoDouble(m_symbol, SYMBOL_BID);
         int spread = (int)SymbolInfoInteger(m_symbol, SYMBOL_SPREAD);
         
         // Расчет оптимального размера трейлинга с учетом всех факторов
         double trailing_size = CalculateOptimalTrailingSize();
         
         // Трейлинг для Buy позиций
         if(m_buy_positions_count[layer_index] > 0)
         {
            double buy_trailing_level = NormalizeDouble(GetAveragePrice(layer_index, 0) + m_trall_tp * m_point * m_point_digits, m_digits);
            
            // Создание линий трейлинга для Buy
            if(m_symbol == Symbol())
            {
               CreateLayerLine("UBB_" + IntegerToString(layer_index), buy_trailing_level, clrRed, 2);
               CreateLayerLine("SLB_" + IntegerToString(layer_index), bid - trailing_size * m_point * m_point_digits, clrRed, 1, STYLE_DASHDOT);
            }
            
            // Установка стоп-лосса для Buy
            if(buy_trailing_level < bid - trailing_size * m_point * m_point_digits)
            {
               // Логирование информации о трейлинге
               LogTrailingInfo(trailing_size, layer_index, 0); // 0 - Buy
               
               for(int i = 1; i <= m_buy_positions_count[layer_index]; i++)
               {
                  if(m_buy_tickets[layer_index][i] > 0)
                  {
                     if(PositionSelectByTicket(m_buy_tickets[layer_index][i]))
                     {
                        double current_sl = PositionGetDouble(POSITION_SL);
                        double new_sl = NormalizeDouble(bid - trailing_size * m_point * m_point_digits, m_digits);
                        
                        if(current_sl < new_sl || current_sl == 0)
                        {
                           m_trade.PositionModify(m_buy_tickets[layer_index][i], new_sl, 0);
                        }
                     }
                  }
               }
            }
         }
         else
         {
            // Удаление линий
            DeleteLayerLine("UBB_" + IntegerToString(layer_index));
            DeleteLayerLine("SLB_" + IntegerToString(layer_index));
         }
         
         // Трейлинг для Sell позиций
         if(m_sell_positions_count[layer_index] > 0)
         {
            double sell_trailing_level = NormalizeDouble(GetAveragePrice(layer_index, 1) - m_trall_tp * m_point * m_point_digits, m_digits);
            
            // Создание линий трейлинга для Sell
            if(m_symbol == Symbol())
            {
               CreateLayerLine("UBS_" + IntegerToString(layer_index), sell_trailing_level, clrBlue, 2);
               CreateLayerLine("SLS_" + IntegerToString(layer_index), ask + trailing_size * m_point * m_point_digits, clrBlue, 1, STYLE_DASHDOT);
            }
            
            // Установка стоп-лосса для Sell
            if(sell_trailing_level > ask + trailing_size * m_point * m_point_digits)
            {
               // Логирование информации о трейлинге
               LogTrailingInfo(trailing_size, layer_index, 1); // 1 - Sell
               
               for(int i = 1; i <= m_sell_positions_count[layer_index]; i++)
               {
                  if(m_sell_tickets[layer_index][i] > 0)
                  {
                     if(PositionSelectByTicket(m_sell_tickets[layer_index][i]))
                     {
                        double current_sl = PositionGetDouble(POSITION_SL);
                        double new_sl = NormalizeDouble(ask + trailing_size * m_point * m_point_digits, m_digits);
                        
                        if(current_sl > new_sl || current_sl == 0)
                        {
                           m_trade.PositionModify(m_sell_tickets[layer_index][i], new_sl, 0);
                        }
                     }
                  }
               }
            }
         }
         else
         {
            // Удаление линий
            DeleteLayerLine("UBS_" + IntegerToString(layer_index));
            DeleteLayerLine("SLS_" + IntegerToString(layer_index));
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Отображение информации о торговле                                |
//+------------------------------------------------------------------+
void ShowTradeInfo()
{
   // Отображаем информацию об умном выходе из просадки
   if(m_use_smart_exit)
   {
      ShowSmartExitInfo();
      return; // Если используется умный выход, он сам формирует комментарий
   }
   
   // Формирование стандартного комментария
   string comment = "";
   
   if(m_symbol != Symbol())
      return;
      
   string market_type_str = "Флэт";
   color market_type_color = clrGray;
   
   if(m_use_trend_filter)
   {
      if(m_current_market_type == 1)
      {
         market_type_str = "Восходящий тренд";
         market_type_color = clrGreen;
      }
      else if(m_current_market_type == -1)
      {
         market_type_str = "Нисходящий тренд";
         market_type_color = clrRed;
      }
   }
   
   string session_str = "Не определена";
   switch(m_current_session)
   {
      case SESSION_ASIAN:
         session_str = "Азиатская";
         break;
      case SESSION_EUROPEAN:
         session_str = "Европейская";
         break;
      case SESSION_AMERICAN:
         session_str = "Американская";
         break;
   }
   
   string mode_str = "Работа";
   if(m_exit_mode) mode_str = "ВЫХОД";
   
   comment = "                         +-----------------------------------------------------------------------+\n" +
                   "                          |   Режим - " + mode_str + "   Прибыль: " + 
                   DoubleToString(m_buy_lots_total[0] - m_sell_lots_total[0] + 
                   (m_buy_lots_total[1] - m_sell_lots_total[1]) + 
                   (m_buy_lots_total[2] - m_sell_lots_total[2]), 2) + "\n";
   
   // Добавляем информацию о типе рынка
   if(m_use_trend_filter)
   {
      comment += "                          |   Тип рынка - " + market_type_str + "\n";
   }
   
   // Добавляем информацию об адаптивной сетке
   if(m_use_adaptive_grid)
   {
      comment += "                          |   Сессия - " + session_str + 
               "   Адаптивный шаг: " + DoubleToString(m_current_grid_step / (m_point * m_point_digits), 1) + "\n";
   }
   
   // Добавляем разделитель
   comment += "                         +-----------------------------------------------------------------------+\n";
   
   comment += "                          |   Торг. слой - " + IntegerToString(m_torg_sloy) + 
              "   Текущий мул. 3-го слоя: " + DoubleToString(m_dd_lot, 2) + "\n" +
              "                         +-----------------------------------------------------------------------+\n";
              
   // Информация о слое 0
   comment += "                          |   Актив. позиций 0 слоя = " + 
              IntegerToString(m_buy_positions_count[0] + m_sell_positions_count[0]) + 
              " (" + IntegerToString(m_buy_positions_count[0]) + "+" + 
              IntegerToString(m_sell_positions_count[0]) + ")\n" +
              "                          |   Проц. прибыли - " + GetLayerStatus(0) + "\n" +
              "                          |   Изменение лотов: " + 
              DoubleToString(m_buy_lots_total[0] - m_sell_lots_total[0], 2) + "\n" +
              "                          |   \n";
              
   // Информация о слое 1
   comment += "                          |   Актив. позиций 1 слоя = " + 
              IntegerToString(m_buy_positions_count[1] + m_sell_positions_count[1]) + 
              " (" + IntegerToString(m_buy_positions_count[1]) + "+" + 
              IntegerToString(m_sell_positions_count[1]) + ")\n" +
              "                          |   Проц. прибыли - " + GetLayerStatus(1) + "\n" +
              "                          |   Изменение лотов: " + 
              DoubleToString(m_buy_lots_total[1] - m_sell_lots_total[1], 2) + "\n" +
              "                          |   \n";
              
   // Информация о слое 2
   comment += "                          |   Актив. позиций 2 слоя = " + 
              IntegerToString(m_buy_positions_count[2] + m_sell_positions_count[2]) + 
              " (" + IntegerToString(m_buy_positions_count[2]) + "+" + 
              IntegerToString(m_sell_positions_count[2]) + ")\n" +
              "                          |   Проц. прибыли - " + GetLayerStatus(2) + "\n" +
              "                          |   Изменение лотов: " + 
              DoubleToString(m_buy_lots_total[2] - m_sell_lots_total[2], 2) + "\n" +
              "                         +-----------------------------------------------------------------------+";
              
   Comment(comment);
}

//+------------------------------------------------------------------+
//| Отображение таблицы при тестировании                              |
//+------------------------------------------------------------------+
void ShowTable()
{
   if(!m_show_table_on_testing || (!(bool)MQLInfoInteger(MQL_TESTER) && (bool)MQLInfoInteger(MQL_OPTIMIZATION)))
      return;
      
   // Загрузить полную историю сделок
   HistorySelect(0, TimeCurrent());
   
   // Отображение таблицы с информацией о прибыли
   double profit_today = CalculateProfit(0);
   string name = m_prefix + "1";
   
   if(ObjectFind(0, name) == -1)
   {
      ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, name, OBJPROP_CORNER, 1);
      ObjectSetInteger(0, name, OBJPROP_XDISTANCE, 10);
      ObjectSetInteger(0, name, OBJPROP_YDISTANCE, 15);
   }
   
   ObjectSetString(0, name, OBJPROP_TEXT, "Прибыль сегодня: " + DoubleToString(profit_today, 2));
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, m_text_size);
   ObjectSetString(0, name, OBJPROP_FONT, "Courier New");
   ObjectSetInteger(0, name, OBJPROP_COLOR, m_color_table_on_testing);
   
   // Прибыль вчера
   double profit_yesterday = CalculateProfit(1);
   name = m_prefix + "2";
   
   if(ObjectFind(0, name) == -1)
   {
      ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, name, OBJPROP_CORNER, 1);
      ObjectSetInteger(0, name, OBJPROP_XDISTANCE, 10);
      ObjectSetInteger(0, name, OBJPROP_YDISTANCE, 33);
   }
   
   ObjectSetString(0, name, OBJPROP_TEXT, "Прибыль вчера: " + DoubleToString(profit_yesterday, 2));
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, m_text_size);
   ObjectSetString(0, name, OBJPROP_FONT, "Courier New");
   ObjectSetInteger(0, name, OBJPROP_COLOR, m_color_table_on_testing);
   
   // Прибыль позавчера
   double profit_before_yesterday = CalculateProfit(2);
   name = m_prefix + "3";
   
   if(ObjectFind(0, name) == -1)
   {
      ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, name, OBJPROP_CORNER, 1);
      ObjectSetInteger(0, name, OBJPROP_XDISTANCE, 10);
      ObjectSetInteger(0, name, OBJPROP_YDISTANCE, 51);
   }
   
   ObjectSetString(0, name, OBJPROP_TEXT, "Прибыль позавчера: " + DoubleToString(profit_before_yesterday, 2));
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, m_text_size);
   ObjectSetString(0, name, OBJPROP_FONT, "Courier New");
   ObjectSetInteger(0, name, OBJPROP_COLOR, m_color_table_on_testing);
   
   // Баланс
   name = m_prefix + "4";
   
   if(ObjectFind(0, name) == -1)
   {
      ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, name, OBJPROP_CORNER, 1);
      ObjectSetInteger(0, name, OBJPROP_XDISTANCE, 10);
      ObjectSetInteger(0, name, OBJPROP_YDISTANCE, 76);
   }
   
   ObjectSetString(0, name, OBJPROP_TEXT, "Баланс : " + DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2));
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, m_text_size);
   ObjectSetString(0, name, OBJPROP_FONT, "Courier New");
   ObjectSetInteger(0, name, OBJPROP_COLOR, m_color_table_on_testing);
   
   // Свободная маржа
   name = m_prefix + "5";
   
   if(ObjectFind(0, name) == -1)
   {
      ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, name, OBJPROP_CORNER, 1);
      ObjectSetInteger(0, name, OBJPROP_XDISTANCE, 10);
      ObjectSetInteger(0, name, OBJPROP_YDISTANCE, 96);
   }
   
   ObjectSetString(0, name, OBJPROP_TEXT, "Своб. средства: " + DoubleToString(AccountInfoDouble(ACCOUNT_MARGIN_FREE), 2));
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, m_text_size);
   ObjectSetString(0, name, OBJPROP_FONT, "Courier New");
   ObjectSetInteger(0, name, OBJPROP_COLOR, m_color_table_on_testing);
   
   // Отображение информации об автокалибровке
   if(m_use_auto_calibration)
   {
      // Просадка
      name = m_prefix + "AC_1";
      
      if(ObjectFind(0, name) == -1)
      {
         ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
         ObjectSetInteger(0, name, OBJPROP_CORNER, 1);
         ObjectSetInteger(0, name, OBJPROP_XDISTANCE, 10);
         ObjectSetInteger(0, name, OBJPROP_YDISTANCE, 116);
      }
      
      color drawdown_color = m_color_table_on_testing;
      if(m_current_drawdown_pct > m_extreme_risk_dd)
         drawdown_color = clrRed;
      else if(m_current_drawdown_pct > m_high_risk_dd)
         drawdown_color = clrOrangeRed;
      else if(m_current_drawdown_pct > m_medium_risk_dd)
         drawdown_color = clrOrange;
      else if(m_current_drawdown_pct > m_low_risk_dd)
         drawdown_color = clrYellow;
      
      ObjectSetString(0, name, OBJPROP_TEXT, "Просадка: " + DoubleToString(m_current_drawdown_pct, 2) + "%");
      ObjectSetInteger(0, name, OBJPROP_FONTSIZE, m_text_size);
      ObjectSetString(0, name, OBJPROP_FONT, "Courier New");
      ObjectSetInteger(0, name, OBJPROP_COLOR, drawdown_color);
      
      // Уровень риска
      name = m_prefix + "AC_2";
      
      if(ObjectFind(0, name) == -1)
      {
         ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
         ObjectSetInteger(0, name, OBJPROP_CORNER, 1);
         ObjectSetInteger(0, name, OBJPROP_XDISTANCE, 10);
         ObjectSetInteger(0, name, OBJPROP_YDISTANCE, 136);
      }
      
      string risk_levels[] = {"Низкий", "Средний", "Высокий", "Экстремальный"};
      color risk_color = m_color_table_on_testing;
      
      if(m_current_risk_level == 3)
         risk_color = clrRed;
      else if(m_current_risk_level == 2)
         risk_color = clrOrangeRed;
      else if(m_current_risk_level == 1)
         risk_color = clrOrange;
      
      ObjectSetString(0, name, OBJPROP_TEXT, "Риск: " + risk_levels[m_current_risk_level]);
      ObjectSetInteger(0, name, OBJPROP_FONTSIZE, m_text_size);
      ObjectSetString(0, name, OBJPROP_FONT, "Courier New");
      ObjectSetInteger(0, name, OBJPROP_COLOR, risk_color);
      
      // Восстановление
      if(m_drawdown_detected)
      {
         name = m_prefix + "AC_3";
         
         if(ObjectFind(0, name) == -1)
         {
            ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
            ObjectSetInteger(0, name, OBJPROP_CORNER, 1);
            ObjectSetInteger(0, name, OBJPROP_XDISTANCE, 10);
            ObjectSetInteger(0, name, OBJPROP_YDISTANCE, 156);
         }
         
         double recovery_pct = CalculateRecoveryPercent();
         
         ObjectSetString(0, name, OBJPROP_TEXT, "Восстановление: " + DoubleToString(recovery_pct, 2) + "%");
         ObjectSetInteger(0, name, OBJPROP_FONTSIZE, m_text_size);
         ObjectSetString(0, name, OBJPROP_FONT, "Courier New");
         ObjectSetInteger(0, name, OBJPROP_COLOR, clrLimeGreen);
      }
      else
      {
         // Удаляем объект, если нет просадки
         if(ObjectFind(0, m_prefix + "AC_3") != -1)
            ObjectDelete(0, m_prefix + "AC_3");
      }
      
      // Тренд
      name = m_prefix + "AC_4";
      
      if(ObjectFind(0, name) == -1)
      {
         ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
         ObjectSetInteger(0, name, OBJPROP_CORNER, 1);
         ObjectSetInteger(0, name, OBJPROP_XDISTANCE, 10);
         ObjectSetInteger(0, name, OBJPROP_YDISTANCE, 176);
      }
      
      int market_trend = DetectMarketTrend();
      string trend_text = "Тренд: ";
      color trend_color = m_color_table_on_testing;
      
      if(market_trend == 1)
      {
         trend_text += "Восходящий";
         trend_color = clrLimeGreen;
      }
      else if(market_trend == -1)
      {
         trend_text += "Нисходящий";
         trend_color = clrRed;
      }
      else
      {
         trend_text += "Флэт";
      }
      
      ObjectSetString(0, name, OBJPROP_TEXT, trend_text);
      ObjectSetInteger(0, name, OBJPROP_FONTSIZE, m_text_size);
      ObjectSetString(0, name, OBJPROP_FONT, "Courier New");
      ObjectSetInteger(0, name, OBJPROP_COLOR, trend_color);
   }
   else
   {
      // Удаляем объекты, если автокалибровка отключена
      for(int i = 1; i <= 4; i++)
      {
         string obj_name = m_prefix + "AC_" + IntegerToString(i);
         if(ObjectFind(0, obj_name) != -1)
            ObjectDelete(0, obj_name);
      }
   }
   
   // Создание логотипа
   CreateLogoObjects();
}

//+------------------------------------------------------------------+
//| Создание объектов логотипа                                       |
//+------------------------------------------------------------------+
void CreateLogoObjects()
{
   string name = m_prefix + "L_1";
   
   if(ObjectFind(0, name) == -1)
   {
      ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, name, OBJPROP_CORNER, 0);
      ObjectSetInteger(0, name, OBJPROP_XDISTANCE, 390);
      ObjectSetInteger(0, name, OBJPROP_YDISTANCE, 10);
   }
   
   ObjectSetString(0, name, OBJPROP_TEXT, "F O R E X");
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 28);
   ObjectSetString(0, name, OBJPROP_FONT, "Arial");
   ObjectSetInteger(0, name, OBJPROP_COLOR, m_color_logotip_name);
   
   name = m_prefix + "L_2";
   
   if(ObjectFind(0, name) == -1)
   {
      ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, name, OBJPROP_CORNER, 0);
      ObjectSetInteger(0, name, OBJPROP_XDISTANCE, 382);
      ObjectSetInteger(0, name, OBJPROP_YDISTANCE, 50);
   }
   
   ObjectSetString(0, name, OBJPROP_TEXT, "I  N  V  E  S  T  O  R");
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 16);
   ObjectSetString(0, name, OBJPROP_FONT, "Arial");
   ObjectSetInteger(0, name, OBJPROP_COLOR, m_color_logotip_name);
   
   name = m_prefix + "L_3";
   
   if(ObjectFind(0, name) == -1)
   {
      ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, name, OBJPROP_CORNER, 0);
      ObjectSetInteger(0, name, OBJPROP_XDISTANCE, 397);
      ObjectSetInteger(0, name, OBJPROP_YDISTANCE, 75);
   }
   
   ObjectSetString(0, name, OBJPROP_TEXT, "www.forex-investor.net");
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 12);
   ObjectSetString(0, name, OBJPROP_FONT, "Arial");
   ObjectSetInteger(0, name, OBJPROP_COLOR, m_color_logotip_site);
}

//+------------------------------------------------------------------+
//| Расчет прибыли за определенный день                              |
//+------------------------------------------------------------------+
double CalculateProfit(int days_ago)
{
   double profit = 0.0;
   
   // Определение начала и конца дня
   datetime day_start = iTime(m_symbol, PERIOD_D1, days_ago);
   datetime day_end = day_start + 86399; // +23:59:59
   
   // Проверка всех сделок в истории
   int history_deals = HistoryDealsTotal();
   
   for(int i = 0; i < history_deals; i++)
   {
      ulong deal_ticket = HistoryDealGetTicket(i);
      
      if(deal_ticket > 0 && HistoryDealSelect(deal_ticket))
      {
         // Проверка принадлежности сделки к советнику и указанному периоду
         datetime deal_time = (datetime)HistoryDealGetInteger(deal_ticket, DEAL_TIME);
         
         if(deal_time >= day_start && deal_time <= day_end)
         {
            string deal_symbol = HistoryDealGetString(deal_ticket, DEAL_SYMBOL);
            ulong deal_magic = HistoryDealGetInteger(deal_ticket, DEAL_MAGIC);
            
            if(deal_symbol == m_symbol && 
               (deal_magic == m_magic || deal_magic == m_magic + 1 || deal_magic == m_magic + 2))
            {
               // Добавление прибыли и комиссии
               profit += HistoryDealGetDouble(deal_ticket, DEAL_PROFIT);
               profit += HistoryDealGetDouble(deal_ticket, DEAL_COMMISSION);
               profit += HistoryDealGetDouble(deal_ticket, DEAL_SWAP);
            }
         }
      }
   }
   
   return profit;
} 

//+------------------------------------------------------------------+
//| Создание встроенного индикатора Heiken Ashi Smoothed                |
//+------------------------------------------------------------------+
int CreateHeikenAshiSmoothedIndicator(string symbol, ENUM_TIMEFRAMES timeframe, 
                                      int ma_method1, int ma_period1, 
                                      int ma_method2, int ma_period2)
{
   // Инициализация буферов
   ArrayResize(m_ha_open_buffer, 1000);
   ArrayResize(m_ha_close_buffer, 1000);
   ArrayResize(m_ha_high_buffer, 1000);
   ArrayResize(m_ha_low_buffer, 1000);
   ArraySetAsSeries(m_ha_open_buffer, true);
   ArraySetAsSeries(m_ha_close_buffer, true);
   ArraySetAsSeries(m_ha_high_buffer, true);
   ArraySetAsSeries(m_ha_low_buffer, true);
   
   // Устанавливаем параметры для расчета
   m_ha_ma_method1 = ma_method1;
   m_ha_ma_period1 = ma_period1;
   m_ha_ma_method2 = ma_method2;
   m_ha_ma_period2 = ma_period2;
   
   // Инициализируем буферы
   ArrayInitialize(m_ha_open_buffer, 0);
   ArrayInitialize(m_ha_close_buffer, 0);
   ArrayInitialize(m_ha_high_buffer, 0);
   ArrayInitialize(m_ha_low_buffer, 0);
   
   // Рассчитываем начальные значения индикатора
   CalculateHeikenAshiSmoothed(100);
   
   // Возвращаем условный хэндл (не INVALID_HANDLE)
   return 1;
}

//+------------------------------------------------------------------+
//| Расчет значений индикатора Heiken Ashi Smoothed                   |
//+------------------------------------------------------------------+
void CalculateHeikenAshiSmoothed(int rates_total)
{
   // Получаем данные цен
   MqlRates rates[];
   if(CopyRates(m_symbol, PERIOD_CURRENT, 0, rates_total, rates) <= 0)
   {
      Print("Ошибка получения исторических данных: ", GetLastError());
      return;
   }
   
   // Временные буферы для расчетов
   double haOpen[], haClose[], haHigh[], haLow[];
   ArrayResize(haOpen, rates_total);
   ArrayResize(haClose, rates_total);
   ArrayResize(haHigh, rates_total);
   ArrayResize(haLow, rates_total);
   
   // Расчет обычного Heiken Ashi
   for(int i = 0; i < rates_total; i++)
   {
      if(i == 0)
      {
         haOpen[i] = rates[i].open;
         haClose[i] = rates[i].close;
         haHigh[i] = rates[i].high;
         haLow[i] = rates[i].low;
      }
      else
      {
         haOpen[i] = (haOpen[i-1] + haClose[i-1]) / 2.0;
         haClose[i] = (rates[i].open + rates[i].high + rates[i].low + rates[i].close) / 4.0;
         haHigh[i] = MathMax(rates[i].high, MathMax(haOpen[i], haClose[i]));
         haLow[i] = MathMin(rates[i].low, MathMin(haOpen[i], haClose[i]));
      }
   }
   
   // Применяем первое сглаживание
   double haOpenSmoothed1[], haCloseSmoothed1[];
   ArrayResize(haOpenSmoothed1, rates_total);
   ArrayResize(haCloseSmoothed1, rates_total);
   
   ApplyMA(haOpen, haOpenSmoothed1, rates_total, m_ha_ma_period1, m_ha_ma_method1);
   ApplyMA(haClose, haCloseSmoothed1, rates_total, m_ha_ma_period1, m_ha_ma_method1);
   
   // Применяем второе сглаживание
   ApplyMA(haOpenSmoothed1, m_ha_open_buffer, rates_total, m_ha_ma_period2, m_ha_ma_method2);
   ApplyMA(haCloseSmoothed1, m_ha_close_buffer, rates_total, m_ha_ma_period2, m_ha_ma_method2);
   
   // Рассчитываем High и Low для сглаженного Heiken Ashi
   for(int i = 0; i < rates_total; i++)
   {
      m_ha_high_buffer[i] = MathMax(haHigh[i], MathMax(m_ha_open_buffer[i], m_ha_close_buffer[i]));
      m_ha_low_buffer[i] = MathMin(haLow[i], MathMin(m_ha_open_buffer[i], m_ha_close_buffer[i]));
   }
}

//+------------------------------------------------------------------+
//| Применение скользящей средней к массиву данных                    |
//+------------------------------------------------------------------+
void ApplyMA(const double &source[], double &target[], int rates_total, int period, int ma_method)
{
   for(int i = 0; i < rates_total; i++)
   {
      if(i < period)
      {
         // Недостаточно данных для полного расчета
         target[i] = source[i];
         continue;
      }
      
      double sum = 0.0;
      double weightSum = 0.0;
      
      switch(ma_method)
      {
         case 1: // SMA
            for(int j = 0; j < period; j++)
               sum += source[i-j];
            target[i] = sum / period;
            break;
            
         case 2: // EMA
            {
               double alpha = 2.0 / (period + 1.0);
               target[i] = source[i] * alpha;
               for(int j = 1; j < period; j++)
                  target[i] += source[i-j] * alpha * MathPow(1.0 - alpha, j);
            }
            break;
            
         case 3: // SMMA
            if(i == period)
            {
               sum = 0;
               for(int j = 0; j < period; j++)
                  sum += source[i-j];
               target[i] = sum / period;
            }
            else if(i > period)
            {
               target[i] = (target[i-1] * (period - 1) + source[i]) / period;
            }
            break;
            
         case 4: // LWMA
            for(int j = 0; j < period; j++)
            {
               double weight = period - j;
               sum += source[i-j] * weight;
               weightSum += weight;
            }
            target[i] = sum / weightSum;
            break;
            
         default: // По умолчанию SMA
            for(int j = 0; j < period; j++)
               sum += source[i-j];
            target[i] = sum / period;
      }
   }
}

//+------------------------------------------------------------------+
//| Частичное закрытие позиций                                        |
//+------------------------------------------------------------------+
bool ExecutePartialClose(int layer)
{
   // Проверка наличия позиций
   if(m_buy_positions_count[layer] == 0 && m_sell_positions_count[layer] == 0)
      return false;
   
   // Определение направления и наибольшего убытка
   int direction = -1; // -1 - не определено, 0 - Buy, 1 - Sell
   int worst_pos_index = -1;
   double worst_profit = 0.0;
   ulong worst_ticket = 0;
   
   // Проверка Buy позиций
   for(int i = 0; i < m_buy_positions_count[layer]; i++)
   {
      if(PositionSelectByTicket(m_buy_tickets[layer][i]))
      {
         double profit = PositionGetDouble(POSITION_PROFIT);
         if(profit < worst_profit)
         {
            worst_profit = profit;
            worst_pos_index = i;
            worst_ticket = m_buy_tickets[layer][i];
            direction = 0;
         }
      }
   }
   
   // Проверка Sell позиций
   for(int i = 0; i < m_sell_positions_count[layer]; i++)
   {
      if(PositionSelectByTicket(m_sell_tickets[layer][i]))
      {
         double profit = PositionGetDouble(POSITION_PROFIT);
         if(profit < worst_profit)
         {
            worst_profit = profit;
            worst_pos_index = i;
            worst_ticket = m_sell_tickets[layer][i];
            direction = 1;
         }
      }
   }
   
   // Если не найдено убыточных позиций
   if(worst_pos_index == -1 || worst_ticket == 0)
      return false;
   
   // Выбор позиции с наибольшим убытком
   if(!PositionSelectByTicket(worst_ticket))
      return false;
   
   // Расчет объема для частичного закрытия
   double pos_volume = PositionGetDouble(POSITION_VOLUME);
   double close_volume = pos_volume * m_partial_close_percent / 100.0;
   
   // Получаем информацию о минимальном и максимальном лоте для данного инструмента
   double min_volume = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MIN);
   double max_volume = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MAX);
   
   // Обновляем m_min_lot для текущего инструмента
   m_min_lot = min_volume;
   
   Print("Позиция #", worst_ticket, ": объем=", pos_volume, 
         ", мин. лот=", min_volume, 
         ", макс. лот=", max_volume, 
         ", расчетный объем закрытия=", close_volume);
   
   // Получение шага объема и округление до ближайшего корректного значения
   double volume_step = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_STEP);
   
   // Для EURUSD у некоторых брокеров минимальный шаг может быть 0.01, а не 0.001
   if(m_symbol == "EURUSD" && volume_step < 0.01)
      volume_step = 0.01;
      
   // Округление вниз до ближайшего кратному шагу объема
   close_volume = MathFloor(close_volume / volume_step) * volume_step;
   
   // Минимальный объем для закрытия
   if(close_volume < m_min_lot)
   {
      // Если объем для закрытия меньше минимального, проверяем возможность закрытия всей позиции
      if(pos_volume <= m_min_lot * 2)
      {
         // Закрываем всю позицию, если она небольшая
         close_volume = pos_volume;
      }
      else
      {
         // Иначе закрываем минимально допустимый объем
      close_volume = m_min_lot;
      }
   }
   
   // Если объем для закрытия больше текущего объема, закрываем всю позицию
   if(close_volume >= pos_volume)
      close_volume = pos_volume;
   
   // Проверка корректности объема перед закрытием
   if(close_volume < m_min_lot || close_volume > pos_volume)
   {
      Print("Некорректный объем для частичного закрытия: ", close_volume, 
            ", мин. лот: ", m_min_lot, ", объем позиции: ", pos_volume);
      
      // Если объем позиции меньше 2*минимальный лот, пробуем закрыть всю позицию
      if(pos_volume < 2 * m_min_lot)
      {
         close_volume = pos_volume;
         Print("Пробуем закрыть всю позицию с объемом: ", close_volume);
      }
      else
      {
         // Для основных валютных пар используем стандартные объемы
         if(m_symbol == "EURUSD" || m_symbol == "GBPUSD" || m_symbol == "USDJPY")
         {
            if(pos_volume >= 0.1) close_volume = 0.1;
            else if(pos_volume >= 0.05) close_volume = 0.05;
            else if(pos_volume >= 0.03) close_volume = 0.03;
            else if(pos_volume >= 0.02) close_volume = 0.02;
            else close_volume = 0.01;
            
            Print("Используем стандартный объем для закрытия: ", close_volume);
         }
         else
         {
            return false;
         }
      }
   }
   
   // Частичное закрытие позиции
   m_trade.SetExpertMagicNumber(m_magic + layer);
   
   // Проверка, не пытаемся ли мы закрыть позицию слишком малым объемом
   if(pos_volume > 0 && close_volume / pos_volume < 0.05)
   {
      // Если пытаемся закрыть менее 5% позиции, увеличиваем объем до минимум 5%
      close_volume = MathMax(close_volume, pos_volume * 0.05);
      close_volume = MathFloor(close_volume / volume_step) * volume_step;
      Print("Увеличен объем закрытия до минимум 5% от позиции: ", close_volume);
   }
   
   // Добавляем повторные попытки в случае ошибки
   int max_attempts = 3;
   bool success = false;
   
   for(int attempt = 0; attempt < max_attempts; attempt++)
   {
   if(m_trade.PositionClosePartial(worst_ticket, close_volume, 0))
   {
      m_positions_closed++;
      Print("Частичное закрытие позиции: Ticket ", worst_ticket, ", Volume: ", DoubleToString(close_volume, 2));
         success = true;
         break;
      }
      else
      {
         uint error_code = m_trade.ResultRetcode();
         Print("Ошибка при частичном закрытии позиции: ", error_code);
         
         // Если ошибка связана с неверным объемом, пробуем уменьшить объем
         if(error_code == 10014) // ERR_INVALID_VOLUME
         {
            // Для EURUSD попробуем использовать только стандартные объемы: 0.01, 0.02, 0.03, 0.05, 0.1, 0.2, 0.3, 0.5, 1.0
            if(m_symbol == "EURUSD" || m_symbol == "GBPUSD" || m_symbol == "USDJPY")
            {
               // Определяем ближайший подходящий объем
               if(close_volume > 0.5) close_volume = 0.5;
               else if(close_volume > 0.3) close_volume = 0.3;
               else if(close_volume > 0.2) close_volume = 0.2;
               else if(close_volume > 0.1) close_volume = 0.1;
               else if(close_volume > 0.05) close_volume = 0.05;
               else if(close_volume > 0.03) close_volume = 0.03;
               else if(close_volume > 0.02) close_volume = 0.02;
               else close_volume = 0.01;
            }
            else
            {
               // Для других инструментов используем стандартное округление
               close_volume = MathFloor(close_volume / volume_step) * volume_step;
               
               // Удаляем все связанные с преобразованием типов операции
               // Вместо этого просто логируем объем для отладки
               if(m_detailed_logging)
               {
                  Print("Скорректированный объем для закрытия: ", DoubleToString(close_volume, 2));
               }
            }
            
            if(close_volume < m_min_lot)
            {
               Print("Невозможно выполнить частичное закрытие - объем меньше минимального: ", close_volume);
   return false;
            }
            Print("Пробуем закрыть с меньшим объемом: ", close_volume);
         }
         else
         {
            // Для других ошибок делаем паузу перед следующей попыткой
            Sleep(500);
         }
      }
   }
   
   return success;
}

//+------------------------------------------------------------------+
//| Получение длительности просадки в часах                           |
//+------------------------------------------------------------------+
int GetDrawdownDuration()
{
   if(!m_drawdown_detected || m_drawdown_start_time == 0)
      return 0;
      
   // Расчет длительности просадки в часах
   return (int)((TimeCurrent() - m_drawdown_start_time) / 3600);
}

//+------------------------------------------------------------------+
//| Выбор оптимального метода восстановления (базовый)                |
//+------------------------------------------------------------------+
ENUM_RECOVERY_METHOD SelectOptimalRecoveryMethodBasic()
{
   // По умолчанию используем метод, указанный в настройках
   ENUM_RECOVERY_METHOD optimal_method = m_recovery_method;
   
   // Если включена адаптация к типу рынка
   if(m_use_market_based_recovery)
   {
      // В трендовом рынке лучше работает частичное закрытие
      if(m_current_market_type != 0)
      {
         optimal_method = RECOVERY_PARTIAL_CLOSE;
      }
      // Во флэте лучше работает усреднение
      else
      {
         optimal_method = RECOVERY_AVERAGING;
      }
      
      // При очень глубокой просадке всегда используем комбинированный подход
      if(m_current_drawdown_pct > m_extreme_risk_dd)
      {
         optimal_method = RECOVERY_COMBINED;
      }
   }
   
   return optimal_method;
}

//+------------------------------------------------------------------+
//| Получение статуса слоя (строка с информацией о прибыли)           |
//+------------------------------------------------------------------+
string GetLayerStatus(int layer_index)
{
   if(layer_index < 0 || layer_index > 2)
      return "Ошибка: неверный индекс слоя";
      
   // Расчет прибыли по слою
   double profit = 0.0;
   int positions_total = PositionsTotal();
   
   for(int i = 0; i < positions_total; i++)
   {
      ulong ticket = PositionGetTicket(i);
      if(ticket != 0 && PositionGetInteger(POSITION_MAGIC) == m_magic)
      {
         // Получаем слой позиции из комментария
         string comment = PositionGetString(POSITION_COMMENT);
         int pos_layer = -1;
         
         if(StringFind(comment, "Слой 0") >= 0)
            pos_layer = 0;
         else if(StringFind(comment, "Слой 1") >= 0)
            pos_layer = 1;
         else if(StringFind(comment, "Слой 2") >= 0)
            pos_layer = 2;
            
         if(pos_layer == layer_index)
         {
            profit += PositionGetDouble(POSITION_PROFIT);
         }
      }
   }
   
   // Форматируем строку с информацией о прибыли
   string status = DoubleToString(profit, 2) + " USD";
   
   // Добавляем индикатор прибыльности
   if(profit > 0)
      status += " (↑)";
   else if(profit < 0)
      status += " (↓)";
   else
      status += " (-)";
      
   return status;
}

//+------------------------------------------------------------------+
//| Применение адаптивного трейлинга                                 |
//+------------------------------------------------------------------+
void ApplyAdaptiveTrailing(int layer_index)
{
   if(layer_index < 0 || layer_index > 2)
      return;
   
   // Получаем текущий процент восстановления
   double recovery_pct = CalculateRecoveryPercent();
   
   // Коэффициент ускорения трейлинга в зависимости от процента восстановления
   double acceleration = 1.0;
   
   if(recovery_pct > 50.0)
      acceleration = 1.0 + (recovery_pct - 50.0) * m_trailing_acceleration / 50.0;
   
   // Применяем трейлинг к позициям слоя
   int positions_total = PositionsTotal();
   
   for(int i = 0; i < positions_total; i++)
   {
      ulong ticket = PositionGetTicket(i);
      if(ticket != 0 && PositionGetInteger(POSITION_MAGIC) == m_magic)
      {
         // Получаем слой позиции из комментария
         string comment = PositionGetString(POSITION_COMMENT);
         int pos_layer = -1;
         
         if(StringFind(comment, "Слой 0") >= 0)
            pos_layer = 0;
         else if(StringFind(comment, "Слой 1") >= 0)
            pos_layer = 1;
         else if(StringFind(comment, "Слой 2") >= 0)
            pos_layer = 2;
            
         if(pos_layer == layer_index)
         {
            // Применяем адаптивный трейлинг
            double current_price = PositionGetDouble(POSITION_PRICE_CURRENT);
            double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
            double sl = PositionGetDouble(POSITION_SL);
            ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            
            // Рассчитываем новый стоп-лосс с учетом ускорения
            double new_sl = sl;
            int adjusted_trailing = (int)(m_tralling_stop * acceleration);
            
            if(type == POSITION_TYPE_BUY && current_price > open_price)
            {
               new_sl = current_price - adjusted_trailing * m_point * m_point_digits;
               if(new_sl > sl)
               {
                  m_trade.PositionModify(ticket, new_sl, PositionGetDouble(POSITION_TP));
               }
            }
            else if(type == POSITION_TYPE_SELL && current_price < open_price)
            {
               new_sl = current_price + adjusted_trailing * m_point * m_point_digits;
               if(new_sl < sl || sl == 0)
               {
                  m_trade.PositionModify(ticket, new_sl, PositionGetDouble(POSITION_TP));
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Применение прогрессивного трейлинга                              |
//+------------------------------------------------------------------+
void ApplyProgressiveTrailing(int layer_index)
{
   if(layer_index < 0 || layer_index > 2)
      return;
      
   // Применяем прогрессивный трейлинг к позициям слоя
   int positions_total = PositionsTotal();
   
   for(int i = 0; i < positions_total; i++)
   {
      ulong ticket = PositionGetTicket(i);
      if(ticket != 0 && PositionGetInteger(POSITION_MAGIC) == m_magic)
      {
         // Получаем слой позиции из комментария
         string comment = PositionGetString(POSITION_COMMENT);
         int pos_layer = -1;
         
         if(StringFind(comment, "Слой 0") >= 0)
            pos_layer = 0;
         else if(StringFind(comment, "Слой 1") >= 0)
            pos_layer = 1;
         else if(StringFind(comment, "Слой 2") >= 0)
            pos_layer = 2;
            
         if(pos_layer == layer_index)
         {
            // Применяем прогрессивный трейлинг
            double current_price = PositionGetDouble(POSITION_PRICE_CURRENT);
            double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
            double sl = PositionGetDouble(POSITION_SL);
            ENUM_POSITION_TYPE type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            
            // Рассчитываем прибыль в пунктах
            double profit_points = 0;
            if(type == POSITION_TYPE_BUY)
               profit_points = (current_price - open_price) / (m_point * m_point_digits);
            else
               profit_points = (open_price - current_price) / (m_point * m_point_digits);
               
            // Прогрессивный трейлинг - увеличиваем трейлинг с ростом прибыли
            int progressive_trailing = m_tralling_stop;
            
            if(profit_points > 50)
               progressive_trailing = (int)(m_tralling_stop * 1.5);
            if(profit_points > 100)
               progressive_trailing = (int)(m_tralling_stop * 2.0);
            if(profit_points > 200)
               progressive_trailing = (int)(m_tralling_stop * 2.5);
               
            // Рассчитываем новый стоп-лосс
            double new_sl = sl;
            
            if(type == POSITION_TYPE_BUY && current_price > open_price)
            {
               new_sl = current_price - progressive_trailing * m_point * m_point_digits;
               if(new_sl > sl)
               {
                  m_trade.PositionModify(ticket, new_sl, PositionGetDouble(POSITION_TP));
               }
            }
            else if(type == POSITION_TYPE_SELL && current_price < open_price)
            {
               new_sl = current_price + progressive_trailing * m_point * m_point_digits;
               if(new_sl < sl || sl == 0)
               {
                  m_trade.PositionModify(ticket, new_sl, PositionGetDouble(POSITION_TP));
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Управление умным выходом из просадки                              |
//+------------------------------------------------------------------+
void ManageSmartExit()
{
   // Если умный выход отключен, выходим
   if(!m_use_smart_exit)
      return;
      
   // Обновляем информацию о просадке
   bool is_drawdown = DetectDrawdown();
   
   // Обновляем историю просадок для анализа
   if(m_use_advanced_recovery && m_use_drawdown_prediction)
   {
      UpdateDrawdownHistory(m_current_drawdown_pct);
      
      // Прогнозируем максимальную просадку
      if(m_drawdown_detected && m_drawdown_history_count >= 3)
      {
         m_predicted_max_drawdown = PredictMaximumDrawdown();
         
         // Если прогнозируемая просадка значительно выше текущей, повышаем уровень риска
         if(m_predicted_max_drawdown > m_current_drawdown_pct * 1.5 && m_current_risk_level < 3)
         {
            m_current_risk_level++;
            Print("[SmartExit] Повышение уровня риска на основе прогноза просадки: ", 
                  DoubleToString(m_predicted_max_drawdown, 2), "%");
            
            // Применяем соответствующий профиль риска
            ApplyRiskProfile(m_current_risk_level);
         }
      }
   }
   
   // Обновляем процент восстановления
   m_recovery_percent = CalculateRecoveryPercent();
   
   // Если используется многоступенчатая система восстановления
   if(m_use_advanced_recovery && m_drawdown_detected)
   {
      // Определяем текущий уровень восстановления на основе просадки
      int recovery_level = (int)(m_current_drawdown_pct / m_recovery_level_step);
      if(recovery_level >= m_recovery_levels_count)
         recovery_level = m_recovery_levels_count - 1;
         
      // Если уровень восстановления изменился, применяем соответствующие меры
      if(recovery_level != m_current_recovery_level)
      {
         m_current_recovery_level = recovery_level;
         
         Print("[SmartExit] Изменение уровня восстановления: ", m_current_recovery_level, 
               " (просадка: ", DoubleToString(m_current_drawdown_pct, 2), "%)");
         
         // Применяем стратегию восстановления в зависимости от уровня
         switch(m_current_recovery_level)
         {
            case 0: // Минимальный уровень - стандартное восстановление
               // Ничего дополнительно не делаем
               break;
               
            case 1: // Первый уровень - умеренное восстановление
               // Увеличиваем размер трейлинга для более быстрого закрытия
               m_tralling_stop = (int)MathRound(m_original_trailing * 0.8);
               break;
               
            case 2: // Второй уровень - активное восстановление
               // Уменьшаем размер новых позиций и увеличиваем трейлинг
               m_lot = m_original_lot * 0.7;
               m_tralling_stop = (int)MathRound(m_original_trailing * 0.6);
               break;
               
            case 3: // Третий уровень - агрессивное восстановление
               // Значительно уменьшаем размер новых позиций и увеличиваем трейлинг
               m_lot = m_original_lot * 0.5;
               m_tralling_stop = (int)MathRound(m_original_trailing * 0.4);
               break;
               
            case 4: // Четвертый уровень - экстремальное восстановление
               // Минимальный размер новых позиций и максимальный трейлинг
               m_lot = m_original_lot * 0.3;
               m_tralling_stop = (int)MathRound(m_original_trailing * 0.3);
               
               // Частичное закрытие убыточных позиций
               if(m_use_adaptive_recovery)
               {
                  ENUM_RECOVERY_METHOD method = SelectOptimalRecoveryMethodBasic();
                  
                  if(method == RECOVERY_PARTIAL_CLOSE || method == RECOVERY_COMBINED)
                  {
                     // Частично закрываем убыточные позиции
                     PartialClosePositions();
                  }
               }
               break;
         }
      }
      
      // Если используется адаптивное восстановление
      if(m_use_adaptive_recovery)
      {
         // Выбираем оптимальный метод восстановления
         ENUM_RECOVERY_METHOD method = SelectOptimalRecoveryMethod(DetectDrawdownCause());
         
         // Применяем выбранный метод восстановления
         switch(method)
            {
               case RECOVERY_AVERAGING:
               // Усреднение позиций - уже реализовано в основной логике
                  break;
                  
               case RECOVERY_PARTIAL_CLOSE:
               // Частичное закрытие убыточных позиций
               if(m_current_recovery_level >= 3) // Применяем только на высоких уровнях риска
                  PartialClosePositions();
                  break;
                  
               case RECOVERY_HEDGING:
               // Хеджирование - открытие противоположных позиций
               if(m_current_recovery_level >= 2) // Применяем только на средних и высоких уровнях риска
                  ApplyHedging();
                  break;
                  
               case RECOVERY_COMBINED:
               // Комбинированный метод - сочетание усреднения и частичного закрытия
               if(m_current_recovery_level >= 3) // Применяем только на высоких уровнях риска
               {
                  PartialClosePositions();
                  ApplyHedging();
               }
               break;
         }
      }
   }
   
   // Отображаем информацию о состоянии умного выхода
   ShowSmartExitInfo();
}

//+------------------------------------------------------------------+
//| Получение сигнала от индикатора Heiken Ashi                       |
//+------------------------------------------------------------------+
int GetHeikenAshiSignal()
{
   if(!m_use_heiken_ashi_filter || m_heiken_ashi_handle == INVALID_HANDLE)
      return 0; // Нет сигнала
      
   // Проверяем данные индикатора
   if(ArraySize(m_ha_open_buffer) < 3 || ArraySize(m_ha_close_buffer) < 3)
      return 0;
      
   // Анализируем последние 2 бара
   // Восходящий тренд: Close > Open для двух последних баров
   if(m_ha_close_buffer[0] > m_ha_open_buffer[0] && m_ha_close_buffer[1] > m_ha_open_buffer[1])
      return 1; // Бычий сигнал
      
   // Нисходящий тренд: Close < Open для двух последних баров
   if(m_ha_close_buffer[0] < m_ha_open_buffer[0] && m_ha_close_buffer[1] < m_ha_open_buffer[1])
      return -1; // Медвежий сигнал
      
   return 0; // Нет четкого сигнала
}

//+------------------------------------------------------------------+
//| Отображение информации о состоянии умного выхода                   |
//+------------------------------------------------------------------+
void ShowSmartExitInfo()
{
   if(!m_use_smart_exit)
      return;
      
   string comment = "";
   string mode_str = "Работа";
   if(m_exit_mode) mode_str = "ВЫХОД";
   
   // Основная информация о советнике
   comment = "                         +-----------------------------------------------------------------------+\n" +
             "                          |   Режим - " + mode_str + "   Прибыль: " + 
             DoubleToString(m_buy_lots_total[0] - m_sell_lots_total[0] + 
             (m_buy_lots_total[1] - m_sell_lots_total[1]) + 
             (m_buy_lots_total[2] - m_sell_lots_total[2]), 2) + "\n";
   
   // Информация о текущем состоянии рынка
   if(m_use_trend_filter)
   {
      string market_type_str = "Флэт";
      
      if(m_current_market_type == 1)
         market_type_str = "Восходящий тренд";
      else if(m_current_market_type == -1)
         market_type_str = "Нисходящий тренд";
         
      comment += "                          |   Тип рынка - " + market_type_str + "\n";
   }
   
   // Информация об адаптивной сетке
   if(m_use_adaptive_grid)
   {
      string session_str = "Не определена";
      switch(m_current_session)
      {
         case SESSION_ASIAN: session_str = "Азиатская"; break;
         case SESSION_EUROPEAN: session_str = "Европейская"; break;
         case SESSION_AMERICAN: session_str = "Американская"; break;
      }
      
      comment += "                          |   Сессия - " + session_str + 
                "   Адаптивный шаг: " + DoubleToString(m_current_grid_step / (m_point * m_point_digits), 1) + "\n";
   }
   
   // Информация об умном выходе из просадки
   comment += "                         +-----------------------------------------------------------------------+\n";
   
   if(m_drawdown_detected)
   {
      comment += "                          |   УМНЫЙ ВЫХОД - АКТИВЕН                                              |\n";
      comment += "                          |   Просадка: " + DoubleToString(m_current_drawdown_pct, 2) + 
                "% | Длительность: " + IntegerToString(GetDrawdownDuration()) + " ч | Макс: " + 
                DoubleToString(m_drawdown_max_value, 2) + "%                 |\n";
      
      // Отображение информации о прогнозе просадки
      if(m_use_advanced_recovery && m_use_drawdown_prediction && m_drawdown_history_count >= 3)
      {
         comment += "                          |   Прогноз макс. просадки: " + DoubleToString(m_predicted_max_drawdown, 2) + 
                   "% | Тренд: " + (m_drawdown_trend > 0 ? "↑" : "↓") + 
                   " " + DoubleToString(MathAbs(m_drawdown_trend), 3) + "                 |\n";
      }
      
      // Отображение информации о многоступенчатой системе восстановления
      if(m_use_advanced_recovery)
      {
         string level_names[] = {"Минимальный", "Умеренный", "Активный", "Агрессивный", "Экстремальный"};
         string level_name = m_current_recovery_level < ArraySize(level_names) ? 
                             level_names[m_current_recovery_level] : "Неизвестный";
                             
         comment += "                          |   Уровень восстановления: " + level_name + 
                   " (" + IntegerToString(m_current_recovery_level) + "/" + 
                   IntegerToString(m_recovery_levels_count - 1) + ")                              |\n";
      }
      
      string recovery_method = "";
      ENUM_RECOVERY_METHOD current_method = m_use_market_based_recovery ? SelectOptimalRecoveryMethodBasic() : m_recovery_method;
      
      switch(current_method)
      {
         case RECOVERY_AVERAGING: recovery_method = "Усреднение"; break;
         case RECOVERY_PARTIAL_CLOSE: recovery_method = "Частичное закрытие"; break;
         case RECOVERY_HEDGING: recovery_method = "Хеджирование"; break;
         case RECOVERY_COMBINED: recovery_method = "Комбинированный"; break;
      }
      
      if(m_recovery_active)
      {
         comment += "                          |   Восстановление: " + DoubleToString(m_recovery_percent, 2) + 
                   "% | Метод: " + recovery_method + " | Доп. позиций: " + IntegerToString(m_recovery_positions_added) + 
                   "                |\n";
                   
         if(m_use_adaptive_trailing)
         {
            comment += "                          |   Адаптивный трейлинг активен                                      |\n";
         }
      }
      
      // Отображение информации о текущих настройках
      comment += "                          |   Текущие настройки: Лот=" + DoubleToString(m_lot, 2) + 
                " | Шаг=" + IntegerToString(m_hsetky) + 
                " | Трейлинг=" + IntegerToString(m_tralling_stop) + 
                "                           |\n";
   }
   else if(m_drawdown_pause_until > 0 && TimeCurrent() < m_drawdown_pause_until)
   {
      int remaining_hours = (int)(m_drawdown_pause_until - TimeCurrent()) / 3600;
      int remaining_minutes = ((int)(m_drawdown_pause_until - TimeCurrent()) % 3600) / 60;
      
      comment += "                          |   УМНЫЙ ВЫХОД - ПАУЗА                                                |\n";
      comment += "                          |   Пауза после восстановления: " + 
                IntegerToString(remaining_hours) + " ч " + 
                IntegerToString(remaining_minutes) + " мин                           |\n";
                
      // Отображение информации о последней просадке
      if(m_drawdown_max_value > 0)
      {
         comment += "                          |   Последняя макс. просадка: " + DoubleToString(m_drawdown_max_value, 2) + 
                   "% | Восстановлено: 100%                          |\n";
      }
   }
   else
   {
      comment += "                          |   УМНЫЙ ВЫХОД - ГОТОВ                                                |\n";
      
      if(m_use_advanced_recovery)
      {
         comment += "                          |   Многоступенчатая система восстановления активна                    |\n";
         
         if(m_use_drawdown_prediction)
         {
            comment += "                          |   Прогнозирование просадки включено                                  |\n";
         }
         
         if(m_use_adaptive_recovery)
         {
            comment += "                          |   Адаптивное восстановление включено                                 |\n";
         }
      }
      else
      {
      comment += "                          |   Ожидание условий для активации                                     |\n";
      }
   }
   
   comment += "                         +-----------------------------------------------------------------------+\n";
   
   // Информация о торговых слоях
   comment += "                          |   Торг. слой - " + IntegerToString(m_torg_sloy) + 
             "   Текущий мул. 3-го слоя: " + DoubleToString(m_dd_lot, 2) + "\n" +
             "                         +-----------------------------------------------------------------------+\n";
             
   // Информация о слое 0
   comment += "                          |   Актив. позиций 0 слоя = " + 
             IntegerToString(m_buy_positions_count[0] + m_sell_positions_count[0]) + 
             " (" + IntegerToString(m_buy_positions_count[0]) + "+" + 
             IntegerToString(m_sell_positions_count[0]) + ")\n" +
             "                          |   Проц. прибыли - " + GetLayerStatus(0) + "\n" +
             "                          |   Изменение лотов: " + 
             DoubleToString(m_buy_lots_total[0] - m_sell_lots_total[0], 2) + "\n" +
             "                          |   \n";
             
   // Информация о слое 1
   comment += "                          |   Актив. позиций 1 слоя = " + 
             IntegerToString(m_buy_positions_count[1] + m_sell_positions_count[1]) + 
             " (" + IntegerToString(m_buy_positions_count[1]) + "+" + 
             IntegerToString(m_sell_positions_count[1]) + ")\n" +
             "                          |   Проц. прибыли - " + GetLayerStatus(1) + "\n" +
             "                          |   Изменение лотов: " + 
             DoubleToString(m_buy_lots_total[1] - m_sell_lots_total[1], 2) + "\n" +
             "                          |   \n";
             
   // Информация о слое 2
   comment += "                          |   Актив. позиций 2 слоя = " + 
             IntegerToString(m_buy_positions_count[2] + m_sell_positions_count[2]) + 
             " (" + IntegerToString(m_buy_positions_count[2]) + "+" + 
             IntegerToString(m_sell_positions_count[2]) + ")\n" +
             "                          |   Проц. прибыли - " + GetLayerStatus(2) + "\n" +
             "                          |   Изменение лотов: " + 
             DoubleToString(m_buy_lots_total[2] - m_sell_lots_total[2], 2) + "\n" +
             "                         +-----------------------------------------------------------------------+";
             
   Comment(comment);
}

//+------------------------------------------------------------------+
//| Обновление текущей сессии (устаревшая функция)                    |
//+------------------------------------------------------------------+
void UpdateAdaptiveGridSize()
{
   // Эта функция устарела и заменена на CalcAdaptiveGridStep
   // Вызываем CalcAdaptiveGridStep для обратной совместимости
   CalcAdaptiveGridStep();
}

//+------------------------------------------------------------------+
//| Timer function                                                   |
//+------------------------------------------------------------------+
void OnTimer()
{
   // Обновляем отображение таблицы информации
   ShowTable();
   
   // Определение типа рынка, если включен фильтр тренда
   if(m_use_trend_filter)
   {
      int previous_market_type = m_current_market_type;
      m_current_market_type = DetectMarketTrend();
      
   }
      
   // Определение текущей сессии, если используется адаптивная сетка
   if(m_use_adaptive_grid)
   {
      ENUM_MARKET_SESSION previous_session = m_current_session;
      m_current_session = (ENUM_MARKET_SESSION)DetectCurrentSession();
      
      // Логируем изменение сессии
      if(previous_session != m_current_session)
      {
         string session_str = "";
      switch(m_current_session)
      {
            case SESSION_ASIAN: session_str = "азиатская"; break;
            case SESSION_EUROPEAN: session_str = "европейская"; break;
            case SESSION_AMERICAN: session_str = "американская"; break;
         }
         Print("Изменение торговой сессии: ", session_str);
      }
      
      // Используем CalcAdaptiveGridStep вместо UpdateAdaptiveGridSize для унификации
      CalcAdaptiveGridStep();
   }
   
   // Обновление информации о волатильности и рыночных условиях
   if(m_use_enhanced_volatility)
   {
      // Оптимизированная система динамического ATR
      if(m_use_optimal_atr_selection)
         OptimizeATRPeriod();

      // Обновление сезонных паттернов волатильности
      if(m_use_seasonal_volatility)
         UpdateSeasonalVolatilityPatterns();

      // Проверка всплесков волатильности (улучшенная)
      if(m_detect_volatility_spikes)
      {
         if(m_use_statistical_spike_detection)
            DetectVolatilitySpikeAdvanced();
         else
            DetectVolatilitySpike();
      }

      // Проверка важных новостей
      if(m_use_news_filter)
         IsImportantNewsTime();

      // Оценка ликвидности рынка
      if(m_analyze_liquidity)
         AssessMarketLiquidity();

      // Динамическая корректировка ATR (старая система, если не используется оптимизированная)
      if(m_use_adaptive_atr_period && !m_use_optimal_atr_selection)
         AdjustATRPeriod();
   }
}

//+------------------------------------------------------------------+
//| Расчет баланса позиций Buy/Sell                                   |
//+------------------------------------------------------------------+
double CalculatePositionBalance()
{
   int total_buy = 0;
   int total_sell = 0;
   
   // Подсчет общего количества Buy и Sell позиций по всем слоям
   for(int layer = 0; layer < 3; layer++)
   {
      total_buy += m_buy_positions_count[layer];
      total_sell += m_sell_positions_count[layer];
   }
   
   // Если нет позиций, возвращаем нейтральное значение
   if(total_buy == 0 && total_sell == 0)
      return 1.0;
      
   // Если есть только Buy позиции
   if(total_buy > 0 && total_sell == 0)
      return 999.0; // Очень высокое значение, указывающее на полный дисбаланс
      
   // Если есть только Sell позиции
   if(total_sell > 0 && total_buy == 0)
      return 0.001; // Очень низкое значение, указывающее на полный дисбаланс
      
   // Вычисляем и возвращаем соотношение Buy/Sell
   return (double)total_buy / total_sell;
}

//+------------------------------------------------------------------+
//| Адаптивный расчет размера лота                                   |
//+------------------------------------------------------------------+
double AdaptiveLotCalculation(int direction, int layer_index)
{
   // direction: 0 - Buy, 1 - Sell
   // layer_index: слой для которого рассчитывается лот

   // 1. Если выбран фиксированный лот, возвращаем его
   if(m_lot_const)
      return m_lot;

   // 2. Базовый расчет по депозиту и риску
   double balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double risk = m_risk_percent / 100.0;
   double base_lot = balance * risk / 1000.0; // Базовая формула (можно скорректировать под специфику брокера)

   // 3. Модификаторы по типу рынка и балансу позиций
   double market_mult = 1.0;
   double balance_mult = 1.0;
   
   // Обновляем текущий баланс позиций
   if(m_use_position_balancing)
      m_current_position_balance = CalculatePositionBalance();
   
   // Применяем множитель в зависимости от типа рынка и направления
   if(m_current_market_type == 1) // Восходящий тренд
   {
      // Базовый множитель для восходящего тренда
      market_mult = m_trend_mode_lot_mult;
      
      // Дополнительная корректировка в зависимости от направления
      if(direction == 0) // Buy
         market_mult *= m_buy_trend_lot_mult;
      else // Sell
         market_mult *= m_sell_trend_lot_mult;
   }
   else if(m_current_market_type == -1) // Нисходящий тренд
   {
      // Базовый множитель для нисходящего тренда
      market_mult = m_trend_mode_lot_mult;
      
      // Дополнительная корректировка в зависимости от направления
      if(direction == 0) // Buy
         market_mult *= m_buy_trend_lot_mult;
      else // Sell
         market_mult *= m_sell_trend_lot_mult;
   }
   else // Флэт
   {
      // Базовый множитель для флэтового рынка
      market_mult = m_flat_mode_lot_mult;
      
      // Дополнительная корректировка в зависимости от направления
      if(direction == 0) // Buy
         market_mult *= m_buy_flat_lot_mult;
      else // Sell
         market_mult *= m_sell_flat_lot_mult;
   }
   
   // Корректировка лота на основе баланса позиций
   if(m_use_position_balancing)
   {
      // Если дисбаланс в сторону Buy и мы открываем Buy
      if(m_current_position_balance > m_max_position_imbalance && direction == 0)
         balance_mult = 1.0 / m_current_position_balance; // Уменьшаем лот для Buy
      
      // Если дисбаланс в сторону Sell и мы открываем Sell
      if(m_current_position_balance < 1.0/m_max_position_imbalance && direction == 1)
         balance_mult = m_current_position_balance; // Уменьшаем лот для Sell
         
      // Применяем корректировку только если включено принудительное балансирование
      if(m_force_balance_on_imbalance)
         market_mult *= balance_mult;
   }

   // 4. Модификаторы по сессии
   double session_mult = 1.0;
   switch(m_current_session)
   {
      case SESSION_ASIAN: session_mult = m_asian_session_multiplier; break;
      case SESSION_EUROPEAN: session_mult = m_european_session_multiplier; break;
      case SESSION_AMERICAN: session_mult = m_american_session_multiplier; break;
   }

   // 5. Модификаторы по типу инструмента
   double symbol_mult = 1.0;
   switch(m_symbol_type)
   {
      case TYPE_FOREX_MAJOR: symbol_mult = m_forex_major_multiplier; break;
      case TYPE_FOREX_CROSS: symbol_mult = m_forex_cross_multiplier; break;
      case TYPE_COMMODITY: symbol_mult = m_commodity_multiplier; break;
      case TYPE_INDEX: symbol_mult = m_index_multiplier; break;
   }

   // 6. Умная корректировка лота на основе рыночных условий
   double smart_mult = 1.0;
   if(m_use_smart_drawdown_management)
   {
      // Снижаем агрессивность при неблагоприятных условиях
      if(!m_market_conditions_favorable)
      {
         smart_mult *= m_risk_reduction_factor;
      }

      // Корректировка на основе текущей просадки
      if(m_current_drawdown_pct > 0)
      {
         double dd_factor = 1.0 - (m_current_drawdown_pct / (m_low_risk_dd * 2.0));
         dd_factor = MathMax(dd_factor, 0.5); // Минимум 50% от базового лота
         smart_mult *= dd_factor;
      }

      // Корректировка на основе волатильности
      if(IsHighVolatilityPeriod())
      {
         smart_mult *= 0.8; // Снижаем лот на 20% при высокой волатильности
      }

      // Корректировка на основе фактора рыночного риска
      if(m_current_market_risk_factor > 1.5)
      {
         smart_mult *= 0.7; // Снижаем лот при высоком рыночном риске
      }
   }

   // 7. Итоговый расчет с умной корректировкой
   double adaptive_lot = base_lot * market_mult * session_mult * symbol_mult * smart_mult;

   // 8. Плавный рост лота: линейная прогрессия по количеству позиций в слое
   int n_positions = 0;
   if(direction == 0) n_positions = m_buy_positions_count[layer_index];
   if(direction == 1) n_positions = m_sell_positions_count[layer_index];
   // Вместо экспоненты: линейно + ограничение максимального множителя
   double progressive_mult = 1.0 + 0.25 * n_positions; // 0.25 - шаг приращения, можно оптимизировать
   if(progressive_mult > MaxLotMultiplier) progressive_mult = MaxLotMultiplier;
   adaptive_lot *= progressive_mult;

   // 9. Ограничения брокера
   double volume_step = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_STEP);
   double min_volume = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MIN);
   double max_volume = SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MAX);
   adaptive_lot = NormalizeDouble(MathRound(adaptive_lot / volume_step) * volume_step, 8);
   if(adaptive_lot < min_volume) adaptive_lot = m_min_lot;
   if(adaptive_lot > max_volume) adaptive_lot = max_volume;

   // 10. Проверка достаточности свободной маржи
   double free_margin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
   double margin_required = 0.0;
   double price = (direction == 0) ? SymbolInfoDouble(m_symbol, SYMBOL_ASK) : SymbolInfoDouble(m_symbol, SYMBOL_BID);
   // Исправлено: используем строгий тип ENUM_ORDER_TYPE для совместимости с OrderCalcMargin
   ENUM_ORDER_TYPE order_type = (direction == 0) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
   double test_lot = adaptive_lot;
   bool margin_ok = false;
   int attempts = 0;
   // Пытаемся уменьшать лот до тех пор, пока хватит маржи или не достигнем минимума
   while(test_lot >= min_volume)
   {
      if(OrderCalcMargin(order_type, m_symbol, test_lot, price, margin_required))
      {
         if(free_margin >= margin_required)
         {
            margin_ok = true;
            break;
         }
      }
      // Уменьшаем лот на шаг
      test_lot -= volume_step;
      test_lot = NormalizeDouble(MathMax(test_lot, min_volume), 8);
      attempts++;
      if(attempts > 100) break; // защита от бесконечного цикла
   }
   if(!margin_ok)
   {
      Print("[Turbo-profit] Недостаточно маржи для открытия даже минимального лота! FreeMargin=", free_margin, " Требуется=", margin_required);
      return 0.0; // Не открывать ордер
   }
   return test_lot;
}

//+------------------------------------------------------------------+
//| Динамическая корректировка периода ATR                           |
//+------------------------------------------------------------------+
void AdjustATRPeriod()
{
   if(!m_use_enhanced_volatility || !m_use_adaptive_atr_period)
      return;
      
   // Проверяем, не слишком ли часто вызываем функцию (не чаще раза в минуту)
   if(TimeCurrent() - m_last_atr_adjustment < 60)
      return;
      
   m_last_atr_adjustment = TimeCurrent();
      
   double atr_buffer[];
   
   // Получение текущего значения ATR
   if(CopyBuffer(m_atr_handle, 0, 0, 2, atr_buffer) <= 0)
      return;
      
   // Если это первый вызов, инициализируем предыдущее значение
   if(m_prev_atr_value == 0)
   {
      m_prev_atr_value = atr_buffer[0];
      m_current_atr_period = m_atr_period;
      return;
   }
   
   // Рассчитываем изменение волатильности
   double volatility_change = MathAbs(atr_buffer[0] - m_prev_atr_value) / m_prev_atr_value;
   
   // Корректируем период ATR в зависимости от изменения волатильности
   int old_period = m_current_atr_period;
   
   // Добавляем переменные для отслеживания последних сообщений
   static datetime last_high_vol_message = 0;
   static datetime last_stable_vol_message = 0;
   static datetime last_normal_vol_message = 0;
   static datetime last_atr_log_time = 0;
   
   // Флаг изменения периода ATR
   bool atr_period_changed = false;
   
   if(volatility_change > m_atr_volatility_threshold)
   {
      // Если волатильность резко выросла, уменьшаем период ATR для более быстрой реакции
      int new_period = MathMax(m_atr_period_min, m_current_atr_period - 2);
      if(new_period != m_current_atr_period)
      {
         m_current_atr_period = new_period;
         atr_period_changed = true;
         
         // Выводим сообщение не чаще раза в 5 минут
         if(TimeCurrent() - last_high_vol_message > 300)
         {
            Print("Высокая волатильность (", DoubleToString(volatility_change, 4), ")! Уменьшаем период ATR до ", m_current_atr_period);
            last_high_vol_message = TimeCurrent();
         }
      }
   }
   else if(volatility_change < 0.1) // Очень низкое изменение волатильности
   {
      // Если волатильность очень стабильна, увеличиваем период ATR для более сглаженных значений
      // Но только если текущий период не достиг максимального значения
      if(m_current_atr_period < m_atr_period_max)
      {
         int new_period = MathMin(m_atr_period_max, m_current_atr_period + 1);
         if(new_period != m_current_atr_period)
         {
            m_current_atr_period = new_period;
            atr_period_changed = true;
            
            // Выводим сообщение не чаще раза в 10 минут и только при фактическом изменении
            if(TimeCurrent() - last_stable_vol_message > 600)
            {
               Print("Стабильная волатильность (", DoubleToString(volatility_change, 4), "). Увеличиваем период ATR до ", m_current_atr_period);
               last_stable_vol_message = TimeCurrent();
            }
         }
      }
   }
   
   // Если период изменился, пересоздаем индикатор
   if(atr_period_changed)
   {
      if(m_atr_handle != INVALID_HANDLE)
         IndicatorRelease(m_atr_handle);
         
      m_atr_handle = iATR(m_symbol, PERIOD_CURRENT, m_current_atr_period);
      
      if(m_atr_handle == INVALID_HANDLE)
      {
         Print("Ошибка создания индикатора ATR с новым периодом: ", m_current_atr_period);
         m_current_atr_period = m_atr_period;
         m_atr_handle = iATR(m_symbol, PERIOD_CURRENT, m_atr_period);
      }
   }
   
   // Обновляем предыдущее значение ATR
   m_prev_atr_value = atr_buffer[0];
}

//+------------------------------------------------------------------+
//| Обнаружение всплесков волатильности                              |
//+------------------------------------------------------------------+
bool DetectVolatilitySpike()
{
   if(!m_use_enhanced_volatility || !m_detect_volatility_spikes)
      return false;
      
   // Проверяем, не обнаружен ли уже всплеск волатильности
   if(m_is_spike_detected && TimeCurrent() - m_last_spike_time < 3600) // Всплеск действует 1 час
      return true;
      
   double atr_buffer[];
   
   // Получение текущего значения ATR
   if(CopyBuffer(m_atr_handle, 0, 0, 2, atr_buffer) <= 0)
      return false;
      
   // Получение средней волатильности за последние N периодов
   double atr_avg_buffer[];
   if(CopyBuffer(m_atr_handle, 0, 0, 10, atr_avg_buffer) <= 0)
      return false;
      
   double avg_atr = 0;
   for(uint i = 0; i < 10; i++)
      avg_atr += atr_avg_buffer[i];
      
   avg_atr /= 10;
   
   // Проверка на всплеск волатильности
   if(atr_buffer[0] > avg_atr * m_volatility_spike_threshold)
   {
      // Выводим сообщение только если это новый всплеск или прошло достаточно времени с последнего сообщения
      static datetime last_spike_message_time = 0;
      if(!m_is_spike_detected || TimeCurrent() - last_spike_message_time > 1800) // Не чаще раза в 30 минут
      {
         Print("Обнаружен всплеск волатильности! Текущий ATR: ", 
               DoubleToString(atr_buffer[0], 5), 
               " Средний ATR: ", DoubleToString(avg_atr, 5),
               " Отношение: ", DoubleToString(atr_buffer[0]/avg_atr, 2));
         last_spike_message_time = TimeCurrent();
      }
            
      m_is_spike_detected = true;
      m_last_spike_time = TimeCurrent();
      return true;
   }
   
   m_is_spike_detected = false;
   return false;
}

//+------------------------------------------------------------------+
//| Мультитаймфреймовый анализ волатильности                          |
//+------------------------------------------------------------------+
double GetMultiTimeframeVolatility()
{
   if(!m_use_enhanced_volatility || !m_use_mtf_volatility)
      return 1.0;
      
   double atr1 = 0, atr2 = 0, atr3 = 0;
   double atr_buffer[];
   
   // Получение значений ATR для каждого таймфрейма
   if(CopyBuffer(m_atr_handle_tf1, 0, 0, 1, atr_buffer) > 0)
      atr1 = atr_buffer[0] / (m_point * m_point_digits);
      
   if(CopyBuffer(m_atr_handle_tf2, 0, 0, 1, atr_buffer) > 0)
      atr2 = atr_buffer[0] / (m_point * m_point_digits);
      
   if(CopyBuffer(m_atr_handle_tf3, 0, 0, 1, atr_buffer) > 0)
      atr3 = atr_buffer[0] / (m_point * m_point_digits);
   
   // Расчет взвешенной волатильности
   double weighted_volatility = atr1 * m_volatility_weight1 + 
                               atr2 * m_volatility_weight2 + 
                               atr3 * m_volatility_weight3;
                               
   return weighted_volatility / m_hsetky; // Нормализация относительно базового шага сетки
}

//+------------------------------------------------------------------+
//| Проверка наличия важных новостей                                  |
//+------------------------------------------------------------------+
bool IsImportantNewsTime()
{
   if(!m_use_enhanced_volatility || !m_use_news_filter)
      return false;
      
   // В реальной реализации здесь должен быть код для получения новостей
   // из экономического календаря или внешнего API
   
   // Пример заглушки для демонстрации логики
   datetime current_time = TimeCurrent();
   
   // Здесь должен быть массив с временем важных новостей
   // В реальной реализации эти данные должны загружаться из внешнего источника
   
   // Для демонстрации возвращаем false
   m_is_news_time = false;
   return false;
}

//+------------------------------------------------------------------+
//| Оценка текущей ликвидности рынка                                 |
//+------------------------------------------------------------------+
double AssessMarketLiquidity()
{
   if(!m_use_enhanced_volatility || !m_analyze_liquidity)
      return 1.0;
      
   // Проверяем, не слишком ли часто вызываем функцию
   if(TimeCurrent() - m_last_liquidity_check < 300) // Проверка раз в 5 минут
      return m_current_liquidity;
      
   m_last_liquidity_check = TimeCurrent();
   
   // Получение данных о спреде
   int current_spread = (int)SymbolInfoInteger(m_symbol, SYMBOL_SPREAD);
   
   // Получение данных о тиках за последнюю минуту (если доступно)
   MqlTick ticks[];
   int ticks_count = CopyTicks(m_symbol, ticks, COPY_TICKS_ALL, (long)(TimeCurrent() - 60) * 1000, (long)60 * 1000);
   
   // Расчет среднего объема тиков
   // Используем тип double с самого начала, чтобы избежать преобразования типов
   double avg_volume = 0.0;
   if(ticks_count > 0)
   {
      double total_volume = 0.0; // Промежуточная переменная для суммирования объемов
      for(int i = 0; i < ticks_count; i++)
         total_volume += (double)ticks[i].volume; // Явное приведение к double
         
      avg_volume = total_volume / ticks_count;
   }
   
   // Оценка ликвидности (нормализованное значение от 0 до 1)
   double liquidity_score = 1.0;
   
   // Учет спреда (чем выше спред, тем ниже ликвидность)
   int avg_spread = (int)SymbolInfoInteger(m_symbol, SYMBOL_SPREAD);
   if(avg_spread > 0 && current_spread > 0)
      liquidity_score *= MathMin((double)avg_spread / (double)current_spread, 1.0);
   
   // Учет объема (чем выше объем, тем выше ликвидность)
   // В реальной реализации нужно настроить базовое значение для сравнения
   double avg_daily_volume = 1000.0; // Примерное среднее значение
   if(avg_volume > 0)
   {
      // Работаем напрямую с avg_volume, избегая явного приведения типов
      // Деление на double автоматически преобразует avg_volume в double
      double volume_ratio = avg_volume / avg_daily_volume;
      liquidity_score *= MathMin(volume_ratio, 1.0);
   }
   
   // Ограничение минимального значения
   m_current_liquidity = MathMax(liquidity_score, 0.1);
   
   return m_current_liquidity;
}

//+------------------------------------------------------------------+
//| Расчет оптимального размера трейлинг-стопа                        |
//+------------------------------------------------------------------+
double CalculateOptimalTrailingSize()
{
   // Базовый размер трейлинга из настроек
   double trailing_size = m_tralling_stop;
   
   // 1. Адаптация к волатильности
   if(m_use_enhanced_volatility)
   {
      double atr_value = 0;
      double atr_buffer[];
      
      if(m_atr_handle != INVALID_HANDLE && CopyBuffer(m_atr_handle, 0, 0, 1, atr_buffer) > 0)
         atr_value = atr_buffer[0] / (m_point * m_point_digits);
         
      // Минимальный размер трейлинга на основе ATR
      trailing_size = MathMax(trailing_size, atr_value * 0.3);
      
      // Анализ динамики волатильности
      if(atr_value > 0)
      {
         double avg_atr = 0;
         double atr_history[];
         
         if(CopyBuffer(m_atr_handle, 0, 0, 10, atr_history) > 0)
         {
            for(int i = 0; i < 10; i++)
               avg_atr += atr_history[i];
            avg_atr /= 10;
            
            // Корректировка на основе сравнения текущей и средней волатильности
            if(atr_value > avg_atr * 1.5) // Значительно выше средней
               trailing_size *= 1.3;
            else if(atr_value > avg_atr * 1.2) // Выше средней
               trailing_size *= 1.2;
            else if(atr_value < avg_atr * 0.7) // Значительно ниже средней
               trailing_size *= 0.7;
            else if(atr_value < avg_atr * 0.8) // Ниже средней
               trailing_size *= 0.8;
         }
      }
   }
   
   // 2. Адаптация к типу рынка (тренд/флэт)
   if(m_use_trend_filter)
   {
      // Получаем силу тренда через ADX
      double trend_strength = 0;
      if(m_adx_handle != INVALID_HANDLE)
      {
         double adx_buffer[];
         if(CopyBuffer(m_adx_handle, 0, 0, 1, adx_buffer) > 0)
            trend_strength = adx_buffer[0];
      }
      
      // В тренде делаем трейлинг более агрессивным (меньше расстояние)
      if(m_current_market_type != 0) // Не флэт
      {
         // Корректировка в зависимости от силы тренда
         if(trend_strength > 40) // Очень сильный тренд
            trailing_size *= 0.6;
         else if(trend_strength > 30) // Сильный тренд
            trailing_size *= 0.7;
         else if(trend_strength > 20) // Умеренный тренд
            trailing_size *= 0.8;
         else // Слабый тренд
            trailing_size *= 0.9;
      }
      else // Флэт
      {
         // Во флэте увеличиваем расстояние трейлинга
         trailing_size *= 1.2;
      }
   }
   
   // 3. Адаптация к импульсу цены
   double momentum_value = 1.0;
   double momentum_buffer[];
   int momentum_handle = iMomentum(m_symbol, PERIOD_CURRENT, 14, PRICE_CLOSE);
   
   if(momentum_handle != INVALID_HANDLE)
   {
      if(CopyBuffer(momentum_handle, 0, 0, 1, momentum_buffer) > 0)
      {
         // Нормализация значения Momentum
         double normalized_momentum = MathAbs(momentum_buffer[0] - 100.0) / 10.0;
         
         // Корректировка множителя в зависимости от силы импульса
         if(normalized_momentum > 5.0) // Очень сильный импульс
            momentum_value = 1.5;
         else if(normalized_momentum > 3.0) // Сильный импульс
            momentum_value = 1.3;
         else if(normalized_momentum > 1.5) // Средний импульс
            momentum_value = 1.15;
         else if(normalized_momentum < 0.5) // Слабый импульс
            momentum_value = 0.9;
      }
      
      IndicatorRelease(momentum_handle);
   }
   
   trailing_size *= momentum_value;
   
   // 4. Адаптация к структуре рынка (свингам)
   MqlRates rates[];
   if(CopyRates(m_symbol, PERIOD_CURRENT, 0, 20, rates) > 0)
   {
      // Определяем, находимся ли мы около локального максимума/минимума
      bool near_swing_high = false;
      bool near_swing_low = false;
      
      for(int i = 1; i < ArraySize(rates) - 1; i++)
      {
         // Локальный максимум
         if(rates[i].high > rates[i-1].high && rates[i].high > rates[i+1].high)
         {
            if(MathAbs(rates[0].close - rates[i].high) / rates[0].close < 0.001)
               near_swing_high = true;
         }
         
         // Локальный минимум
         if(rates[i].low < rates[i-1].low && rates[i].low < rates[i+1].low)
         {
            if(MathAbs(rates[0].close - rates[i].low) / rates[0].close < 0.001)
               near_swing_low = true;
         }
      }
      
      // Если близко к свингу, корректируем трейлинг
      if(near_swing_high || near_swing_low)
         trailing_size *= 0.7; // Уменьшаем расстояние трейлинга около свингов
   }
   
   // 5. Адаптация к спреду
   int current_spread = (int)SymbolInfoInteger(m_symbol, SYMBOL_SPREAD);
   
   // Если спред выше среднего, увеличиваем трейлинг
   if(current_spread > 10) // Порог можно настроить
      trailing_size *= 1.0 + (current_spread - 10) * 0.01; // +1% за каждый пункт спреда выше порога
   
   // 6. Адаптация к времени удержания позиции
   // Чем дольше держим позицию, тем ближе трейлинг
   // Это требуется отслеживания времени открытия позиций, что можно реализовать при необходимости
   
   // Ограничение минимального и максимального значения
   double min_trailing = 5; // Минимум 5 пунктов
   double max_trailing = m_tralling_stop * 2; // Максимум - двойной базовый размер
   
   trailing_size = MathMax(min_trailing, MathMin(max_trailing, trailing_size));
   
   return trailing_size;
}

//+------------------------------------------------------------------+
//| Логирование информации о трейлинг-стопе                            |
//+------------------------------------------------------------------+
void LogTrailingInfo(double trailing_size, int layer_index, int direction)
{
   // Логируем информацию только если включено подробное логирование
   if(!m_detailed_logging)
      return;
      
   string direction_str = (direction == 0) ? "Buy" : "Sell";
   
   // Базовая информация
   string log_message = "Трейлинг-стоп для " + direction_str + " позиций слоя " + IntegerToString(layer_index) + 
                       ": " + DoubleToString(trailing_size, 1) + " пунктов";
   
   // Информация о волатильности
   if(m_use_enhanced_volatility && m_atr_handle != INVALID_HANDLE)
   {
      double atr_buffer[];
      if(CopyBuffer(m_atr_handle, 0, 0, 1, atr_buffer) > 0)
      {
         double atr_value = atr_buffer[0] / (m_point * m_point_digits);
         log_message += " | ATR: " + DoubleToString(atr_value, 1);
      }
   }
   
   // Информация о тренде
   if(m_use_trend_filter)
   {
      string trend_str = "Флэт";
      if(m_current_market_type == 1)
         trend_str = "Восходящий";
      else if(m_current_market_type == -1)
         trend_str = "Нисходящий";
         
      log_message += " | Тренд: " + trend_str;
      
      if(m_adx_handle != INVALID_HANDLE)
      {
         double adx_buffer[];
         if(CopyBuffer(m_adx_handle, 0, 0, 1, adx_buffer) > 0)
         {
            log_message += " | ADX: " + DoubleToString(adx_buffer[0], 1);
         }
      }
   }
   
   // Информация о спреду
   int current_spread = (int)SymbolInfoInteger(m_symbol, SYMBOL_SPREAD);
   log_message += " | Спред: " + IntegerToString(current_spread);
   
   // Информация о моменте
   double momentum_buffer[];
   int momentum_handle = iMomentum(m_symbol, PERIOD_CURRENT, 14, PRICE_CLOSE);
   if(momentum_handle != INVALID_HANDLE)
   {
      if(CopyBuffer(momentum_handle, 0, 0, 1, momentum_buffer) > 0)
      {
         log_message += " | Momentum: " + DoubleToString(momentum_buffer[0], 1);
      }
      IndicatorRelease(momentum_handle);
   }
   
   // Вывод собранной информации в лог
   Print(log_message);
}

//+------------------------------------------------------------------+
//| Расчет просадки                                                  |
//+------------------------------------------------------------------+
double CalculateDrawdown()
{
   double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double drawdown_pct = (m_max_balance - current_balance) / m_max_balance * 100.0;
   return drawdown_pct;
}

//+------------------------------------------------------------------+
//| Автокалибровка параметров                                         |
//+------------------------------------------------------------------+
void AutoCalibration()
{
   // Проверяем, не слишком ли часто вызываем функцию
   if(TimeCurrent() - m_last_calibration_time < m_calibration_interval)
      return;
      
   m_last_calibration_time = TimeCurrent();
   
   // Расчет текущей просадки
   double prev_drawdown = m_current_drawdown_pct;
   m_current_drawdown_pct = CalculateDrawdown();
   
   // Определение уровня риска
   int prev_risk_level = m_current_risk_level;
   
   if(m_current_drawdown_pct <= m_low_risk_dd)
      m_current_risk_level = 0;
   else if(m_current_drawdown_pct <= m_medium_risk_dd)
      m_current_risk_level = 1;
   else if(m_current_drawdown_pct <= m_high_risk_dd)
      m_current_risk_level = 2;
   else
      m_current_risk_level = 3;
   
   // Применение профиля риска только если уровень риска изменился
   // или произошло значительное изменение просадки
   if(prev_risk_level != m_current_risk_level || 
      MathAbs(prev_drawdown - m_current_drawdown_pct) > 2.0)
   {
      // Логирование изменения
      if(prev_risk_level != m_current_risk_level)
      {
         string risk_levels[] = {"низкий", "средний", "высокий", "экстремальный"};
         Print("[AutoCalibration] Изменение уровня риска: ", 
               risk_levels[prev_risk_level], " -> ", risk_levels[m_current_risk_level], 
               " (просадка: ", DoubleToString(m_current_drawdown_pct, 2), "%)");
      }
      
      // Применение профиля риска
      ApplyRiskProfile(m_current_risk_level);
   }
   
   // Дополнительные проверки состояния рынка
   if(m_use_auto_trend_detection)
   {
      // Определение текущего тренда и корректировка параметров
      DetectAndAdjustForMarketConditions();
   }
}

//+------------------------------------------------------------------+
//| Применение профиля риска                                         |
//+------------------------------------------------------------------+
void ApplyRiskProfile(int risk_level)
{
   // Устанавливаем параметры в зависимости от уровня риска
   switch(risk_level)
   {
      case 0: // Низкий риск
         if(m_adjust_lot_size) m_lot = m_original_lot;
         if(m_adjust_grid_step) m_hsetky = m_original_hsetky;
         if(m_adjust_lot_size) m_lot_multiplicator = m_original_lot_mult;
         if(m_adjust_trailing) m_tralling_stop = m_original_trailing;
         break;
         
      case 1: // Средний риск
         if(m_adjust_lot_size) m_lot = m_original_lot * 0.8;
         if(m_adjust_grid_step) m_hsetky = (int)MathRound(m_original_hsetky * 1.5);
         if(m_adjust_lot_size) m_lot_multiplicator = m_original_lot_mult * 1.2;
         if(m_adjust_trailing) m_tralling_stop = (int)MathRound(m_original_trailing * 1.3);
         break;
         
      case 2: // Высокий риск
         if(m_adjust_lot_size) m_lot = m_original_lot * 0.6;
         if(m_adjust_grid_step) m_hsetky = (int)MathRound(m_original_hsetky * 2.0);
         if(m_adjust_lot_size) m_lot_multiplicator = m_original_lot_mult * 1.4;
         if(m_adjust_trailing) m_tralling_stop = (int)MathRound(m_original_trailing * 1.7);
         break;
         
      case 3: // Экстремальный риск
         if(m_adjust_lot_size) m_lot = m_original_lot * 0.4;
         if(m_adjust_grid_step) m_hsetky = (int)MathRound(m_original_hsetky * 3.0);
         if(m_adjust_lot_size) m_lot_multiplicator = m_original_lot_mult * 1.6;
         if(m_adjust_trailing) m_tralling_stop = (int)MathRound(m_original_trailing * 2.0);
         break;
   }
   
   // Логирование текущих настроек
   Print("[AutoCalibration] Применены настройки для уровня риска ", risk_level, 
         ": лот=", DoubleToString(m_lot, 2),
         ", шаг сетки=", m_hsetky,
         ", множитель лота=", DoubleToString(m_lot_multiplicator, 2),
         ", трейлинг=", m_tralling_stop);
}

//+------------------------------------------------------------------+
//| Определение и адаптация к рыночным условиям                      |
//+------------------------------------------------------------------+
void DetectAndAdjustForMarketConditions()
{
   // Получение текущей волатильности
   double current_volatility = GetCurrentVolatility();
   
   // Определение текущего типа рынка
   int market_type = DetectMarketTrend();
   
   // Корректировка параметров в зависимости от волатильности и типа рынка
   if(market_type == 1) // Восходящий тренд
   {
      // Увеличиваем множитель лота для Buy и уменьшаем для Sell
      if(m_adjust_lot_size)
      {
         m_buy_trend_lot_mult = 1.2;
         m_sell_trend_lot_mult = 0.8;
      }
      
      // Корректируем шаг сетки
      if(m_adjust_grid_step)
      {
         // Увеличиваем шаг сетки для трендового рынка
         m_hsetky = (int)MathRound(m_original_hsetky * 1.5);
      }
      
      Print("[AutoCalibration] Обнаружен восходящий тренд, параметры скорректированы");
   }
   else if(market_type == -1) // Нисходящий тренд
   {
      // Увеличиваем множитель лота для Sell и уменьшаем для Buy
      if(m_adjust_lot_size)
      {
         m_buy_trend_lot_mult = 0.8;
         m_sell_trend_lot_mult = 1.2;
      }
      
      // Корректируем шаг сетки
      if(m_adjust_grid_step)
      {
         // Увеличиваем шаг сетки для трендового рынка
         m_hsetky = (int)MathRound(m_original_hsetky * 1.5);
      }
      
      Print("[AutoCalibration] Обнаружен нисходящий тренд, параметры скорректированы");
   }
   else // Флэт
   {
      // Восстанавливаем стандартные множители для флэта
      if(m_adjust_lot_size)
      {
         m_buy_flat_lot_mult = 1.0;
         m_sell_flat_lot_mult = 1.0;
      }
      
      // Корректируем шаг сетки
      if(m_adjust_grid_step)
      {
         // Уменьшаем шаг сетки для флэтового рынка
         m_hsetky = m_original_hsetky;
      }
      
      Print("[AutoCalibration] Обнаружен флэт, параметры скорректированы");
   }
   
   // Корректировка параметров в зависимости от волатильности
   if(current_volatility > 1.5) // Высокая волатильность
   {
      // Увеличиваем шаг сетки и трейлинг
      if(m_adjust_grid_step)
      {
         m_hsetky = (int)MathRound(m_hsetky * 1.3);
      }
      
      if(m_adjust_trailing)
      {
         m_tralling_stop = (int)MathRound(m_tralling_stop * 1.3);
      }
      
      Print("[AutoCalibration] Обнаружена высокая волатильность, параметры скорректированы");
   }
   else if(current_volatility < 0.7) // Низкая волатильность
   {
      // Уменьшаем шаг сетки и трейлинг
      if(m_adjust_grid_step)
      {
         m_hsetky = (int)MathRound(m_hsetky * 0.8);
         if(m_hsetky < 5) m_hsetky = 5; // Минимальный шаг сетки
      }
      
      if(m_adjust_trailing)
      {
         m_tralling_stop = (int)MathRound(m_tralling_stop * 0.8);
         if(m_tralling_stop < 3) m_tralling_stop = 3; // Минимальный трейлинг
      }
      
      Print("[AutoCalibration] Обнаружена низкая волатильность, параметры скорректированы");
   }
}

//+------------------------------------------------------------------+
//| Получение текущей волатильности                                  |
//+------------------------------------------------------------------+
double GetCurrentVolatility()
{
   // Если используется улучшенная система волатильности, используем её
   if(m_use_enhanced_volatility)
   {
      return GetEnhancedVolatilityFactor();
   }

   // Иначе используем старую простую систему
   double atr_value = 0.0;
   double atr_buffer[];

   // Получаем значение ATR
   int atr_handle = iATR(m_symbol, PERIOD_CURRENT, 14);

   if(atr_handle != INVALID_HANDLE)
   {
      if(CopyBuffer(atr_handle, 0, 0, 1, atr_buffer) > 0)
      {
         atr_value = atr_buffer[0];
      }

      IndicatorRelease(atr_handle);
   }

   // Нормализация волатильности относительно средней
   static double avg_atr = 0.0;

   if(avg_atr == 0.0)
   {
      // Первичная инициализация
      avg_atr = atr_value;
   }
   else
   {
      // Плавное обновление среднего значения
      avg_atr = 0.9 * avg_atr + 0.1 * atr_value;
   }

   // Возвращаем отношение текущего ATR к среднему
   if(avg_atr > 0.0)
      return atr_value / avg_atr;
   else
      return 1.0;
}

//+------------------------------------------------------------------+
//| Умная адаптивная система управления просадкой                    |
//+------------------------------------------------------------------+
void UpdateSmartDrawdownThresholds()
{
   if(!m_use_smart_drawdown_management) return;

   // Обновляем оценку рыночных условий
   UpdateMarketConditionsAssessment();

   // Рассчитываем текущий фактор рыночного риска
   m_current_market_risk_factor = CalculateMarketRiskFactor();

   // Обновляем адаптивные пороги просадки для каждого уровня риска
   for(int i = 0; i < 4; i++)
   {
      double base_threshold = 0.0;
      switch(i)
      {
         case 0: base_threshold = m_low_risk_dd; break;
         case 1: base_threshold = m_medium_risk_dd; break;
         case 2: base_threshold = m_high_risk_dd; break;
         case 3: base_threshold = m_extreme_risk_dd; break;
      }

      // Применяем адаптацию на основе рыночных условий
      m_adaptive_dd_threshold[i] = CalculateVolatilityAdjustedThreshold(base_threshold);
   }

   // Анализируем исторические просадки если включен анализ
   if(m_use_historical_dd_analysis)
   {
      AnalyzeHistoricalDrawdowns();
   }

   // Применяем динамическую корректировку риска
   if(m_use_dynamic_risk_adjustment && ShouldReduceRisk())
   {
      ApplyDynamicRiskAdjustment();
   }
}

//+------------------------------------------------------------------+
//| Расчет фактора рыночного риска                                   |
//+------------------------------------------------------------------+
double CalculateMarketRiskFactor()
{
   double risk_factor = 1.0;

   // Фактор волатильности
   double current_volatility = GetCurrentVolatility();
   if(current_volatility > m_max_volatility_threshold)
   {
      risk_factor *= m_volatility_dd_multiplier;
   }

   // Фактор типа рынка
   int market_type = m_current_market_type;
   if(market_type == 1 || market_type == -1) // Тренд
   {
      risk_factor *= m_trend_dd_multiplier;
   }
   else // Флэт
   {
      risk_factor *= m_flat_dd_multiplier;
   }

   // Фактор торговой сессии
   int session = (int)m_current_session;
   if(session == (int)SESSION_EUROPEAN || session == (int)SESSION_AMERICAN)
   {
      risk_factor *= m_session_dd_multiplier;
   }

   return risk_factor;
}

//+------------------------------------------------------------------+
//| Проверка благоприятности рыночных условий                       |
//+------------------------------------------------------------------+
bool IsMarketConditionsFavorable()
{
   if(!m_use_market_condition_filtering) return true;

   // Проверяем волатильность
   if(IsHighVolatilityPeriod()) return false;

   // Проверяем стабильность тренда (используем силу тренда из ADX)
   double trend_strength = 0.5; // Базовое значение
   if(m_use_trend_filter && ArraySize(m_adx_main) > 0)
   {
      trend_strength = m_adx_main[0] / 100.0; // Нормализуем ADX
   }
   if(trend_strength < 0.3) return false; // Слабый тренд

   // Проверяем спреды
   double current_spread = SymbolInfoInteger(_Symbol, SYMBOL_SPREAD) * _Point;
   double avg_spread = current_spread * 1.2; // Простая оценка среднего спреда
   if(current_spread > avg_spread * 2.0) return false; // Высокие спреды

   return true;
}

//+------------------------------------------------------------------+
//| Получение адаптивного порога просадки                           |
//+------------------------------------------------------------------+
double GetAdaptiveDrawdownThreshold(int risk_level)
{
   if(!m_use_smart_drawdown_management || risk_level < 0 || risk_level >= 4)
   {
      // Возвращаем стандартные пороги
      switch(risk_level)
      {
         case 0: return m_low_risk_dd;
         case 1: return m_medium_risk_dd;
         case 2: return m_high_risk_dd;
         case 3: return m_extreme_risk_dd;
         default: return m_low_risk_dd;
      }
   }

   return m_adaptive_dd_threshold[risk_level];
}

//+------------------------------------------------------------------+
//| Обнаружение просадки                                             |
//+------------------------------------------------------------------+
bool DetectDrawdown()
{
   // Обновляем умную систему управления просадкой
   if(m_use_smart_drawdown_management)
   {
      UpdateMarketConditionsAssessment();
   }

   // Расчет текущей просадки
   m_current_drawdown_pct = CalculateDrawdown();

   // Получаем адаптивный порог просадки
   double adaptive_threshold = m_use_smart_drawdown_management ?
                              GetAdaptiveDrawdownThreshold(0) : m_low_risk_dd;

   // Если просадка превышает адаптивный порог, считаем что просадка обнаружена
   if(m_current_drawdown_pct > adaptive_threshold)
   {
      // Если просадка только что обнаружена, запоминаем время
      if(!m_drawdown_detected)
      {
         m_drawdown_detected = true;
         m_drawdown_start_time = TimeCurrent();
         m_drawdown_max_value = m_current_drawdown_pct;
         m_recovery_active = true;
         m_current_recovery_level = 0;
         m_recovery_positions_added = 0;
         
         // Определяем причину просадки
         ENUM_DRAWDOWN_CAUSE cause = DetectDrawdownCause();
         string cause_str = "";
         
         switch(cause)
         {
            case DD_CAUSE_TREND_CHANGE: cause_str = "изменение тренда"; break;
            case DD_CAUSE_NEWS_IMPACT: cause_str = "влияние новостей"; break;
            case DD_CAUSE_VOLATILITY: cause_str = "всплеск волатильности"; break;
            case DD_CAUSE_TECHNICAL: cause_str = "технический фактор"; break;
            default: cause_str = "неизвестная причина"; break;
         }
         
         Print("[SmartExit] Обнаружена просадка: ", DoubleToString(m_current_drawdown_pct, 2), 
               "%, причина: ", cause_str, ", начато восстановление");
               
         // Если используется прогнозирование просадки
         if(m_use_advanced_recovery && m_use_drawdown_prediction)
         {
            // Инициализируем историю просадок
            if(m_drawdown_history_count == 0)
            {
               ArrayResize(m_drawdown_history, m_drawdown_history_size);
               ArrayResize(m_drawdown_time_history, m_drawdown_history_size);
            }
            
            // Добавляем первую запись в историю
            m_drawdown_history[0] = m_current_drawdown_pct;
            m_drawdown_time_history[0] = TimeCurrent();
            m_drawdown_history_count = 1;
         }
      }
      else
      {
         // Обновляем максимальное значение просадки
         if(m_current_drawdown_pct > m_drawdown_max_value)
         {
            m_drawdown_max_value = m_current_drawdown_pct;
            
            // Логируем новый максимум просадки
            Print("[SmartExit] Новый максимум просадки: ", DoubleToString(m_drawdown_max_value, 2), "%");
            
            // Если просадка значительно выросла, повышаем уровень риска
            if(m_current_risk_level < 3 && m_current_drawdown_pct > m_extreme_risk_dd)
            {
               m_current_risk_level = 3; // Экстремальный риск
               Print("[SmartExit] Повышение уровня риска до экстремального из-за резкого роста просадки");
               
               // Применяем соответствующий профиль риска
               ApplyRiskProfile(m_current_risk_level);
            }
         }
      }
      
      return true;
   }
   else
   {
      // Если просадка меньше порогового значения, сбрасываем флаг
      if(m_drawdown_detected && m_current_drawdown_pct <= m_low_risk_dd * 0.5)
      {
         m_drawdown_detected = false;
         
         if(m_recovery_active)
         {
            Print("[SmartExit] Просадка устранена, восстановление завершено");
            m_recovery_active = false;
            
            // Устанавливаем паузу после восстановления
            if(m_recovery_pause_hours > 0)
            {
               m_drawdown_pause_until = TimeCurrent() + m_recovery_pause_hours * 3600;
               Print("[SmartExit] Установлена пауза после восстановления на ", 
                     m_recovery_pause_hours, " часов");
            }
            
            // Возвращаем параметры к исходным значениям
            m_lot = m_original_lot;
            m_hsetky = m_original_hsetky;
            m_lot_multiplicator = m_original_lot_mult;
            m_tralling_stop = m_original_trailing;
            m_current_risk_level = 0;
            m_current_recovery_level = 0;
         }
      }
      
      return false;
   }
}

//+------------------------------------------------------------------+
//| Расчет процента восстановления из просадки                       |
//+------------------------------------------------------------------+
double CalculateRecoveryPercent()
{
   // Если просадка не обнаружена или максимальная просадка равна 0, возвращаем 100%
   if(!m_drawdown_detected || m_drawdown_max_value <= 0.0)
      return 100.0;
      
   // Если текущая просадка больше или равна максимальной, возвращаем 0%
   if(m_current_drawdown_pct >= m_drawdown_max_value)
      return 0.0;
      
   // Расчет процента восстановления
   double recovery_pct = 100.0 * (m_drawdown_max_value - m_current_drawdown_pct) / m_drawdown_max_value;
   
   return recovery_pct;
}

//+------------------------------------------------------------------+
//| Определение текущего тренда                                      |
//+------------------------------------------------------------------+
int DetectMarketTrend()
{
   // Используем несколько индикаторов для определения тренда
   // 1. Направление MA
   // 2. MACD
   // 3. ADX (для силы тренда)
   
   int trend_signals = 0;
   int trend_direction = 0; // 0 - флэт, 1 - восходящий, -1 - нисходящий
   
   // Получаем данные MA
   double ma_fast[], ma_slow[];
   int ma_fast_handle = iMA(m_symbol, PERIOD_CURRENT, 14, 0, MODE_EMA, PRICE_CLOSE);
   int ma_slow_handle = iMA(m_symbol, PERIOD_CURRENT, 50, 0, MODE_EMA, PRICE_CLOSE);
   
   if(ma_fast_handle != INVALID_HANDLE && ma_slow_handle != INVALID_HANDLE)
   {
      if(CopyBuffer(ma_fast_handle, 0, 0, 3, ma_fast) > 0 && 
         CopyBuffer(ma_slow_handle, 0, 0, 3, ma_slow) > 0)
      {
         // Проверка направления MA
         if(ma_fast[0] > ma_slow[0] && ma_fast[1] > ma_slow[1])
         {
            trend_signals++;
            trend_direction += 1;
         }
         else if(ma_fast[0] < ma_slow[0] && ma_fast[1] < ma_slow[1])
         {
            trend_signals++;
            trend_direction -= 1;
         }
         
         // Проверка наклона MA
         if(ma_fast[0] > ma_fast[1] && ma_fast[1] > ma_fast[2])
         {
            trend_signals++;
            trend_direction += 1;
         }
         else if(ma_fast[0] < ma_fast[1] && ma_fast[1] < ma_fast[2])
         {
            trend_signals++;
            trend_direction -= 1;
         }
      }
      
      IndicatorRelease(ma_fast_handle);
      IndicatorRelease(ma_slow_handle);
   }
   
   // Получаем данные MACD
   double macd_main[], macd_signal[];
   int macd_handle = iMACD(m_symbol, PERIOD_CURRENT, 12, 26, 9, PRICE_CLOSE);
   
   if(macd_handle != INVALID_HANDLE)
   {
      if(CopyBuffer(macd_handle, 0, 0, 3, macd_main) > 0 && 
         CopyBuffer(macd_handle, 1, 0, 3, macd_signal) > 0)
      {
         // Проверка значения MACD
         if(macd_main[0] > 0 && macd_main[1] > 0)
         {
            trend_signals++;
            trend_direction += 1;
         }
         else if(macd_main[0] < 0 && macd_main[1] < 0)
         {
            trend_signals++;
            trend_direction -= 1;
         }
         
         // Проверка пересечения MACD
         if(macd_main[0] > macd_signal[0] && macd_main[1] <= macd_signal[1])
         {
            trend_signals++;
            trend_direction += 1;
         }
         else if(macd_main[0] < macd_signal[0] && macd_main[1] >= macd_signal[1])
         {
            trend_signals++;
            trend_direction -= 1;
         }
      }
      
      IndicatorRelease(macd_handle);
   }
   
   // Получаем данные ADX
   double adx_main[], adx_plus[], adx_minus[];
   int adx_handle = iADX(m_symbol, PERIOD_CURRENT, 14);
   
   if(adx_handle != INVALID_HANDLE)
   {
      if(CopyBuffer(adx_handle, 0, 0, 1, adx_main) > 0 && 
         CopyBuffer(adx_handle, 1, 0, 1, adx_plus) > 0 &&
         CopyBuffer(adx_handle, 2, 0, 1, adx_minus) > 0)
      {
         // Проверка силы тренда
         if(adx_main[0] > 25.0)
         {
            trend_signals++;
            
            // Проверка направления тренда
            if(adx_plus[0] > adx_minus[0])
               trend_direction += 1;
            else if(adx_plus[0] < adx_minus[0])
               trend_direction -= 1;
         }
      }
      
      IndicatorRelease(adx_handle);
   }
   
   // Определение итогового направления тренда
   if(trend_signals >= 3) // Требуем минимум 3 сигнала для подтверждения тренда
   {
      if(trend_direction > 0)
         return 1;  // Восходящий тренд
      else if(trend_direction < 0)
         return -1; // Нисходящий тренд
   }
   
   return 0; // Флэт или неопределенный тренд
}

//+------------------------------------------------------------------+
//| Обновление истории просадок                                       |
//+------------------------------------------------------------------+
void UpdateDrawdownHistory(double drawdown)
{
   // Если история еще не заполнена
   if(m_drawdown_history_count < m_drawdown_history_size)
   {
      m_drawdown_history[m_drawdown_history_count] = drawdown;
      m_drawdown_time_history[m_drawdown_history_count] = TimeCurrent();
      m_drawdown_history_count++;
   }
   else
   {
      // Сдвигаем массив на одну позицию влево
      for(int i = 0; i < m_drawdown_history_size - 1; i++)
      {
         m_drawdown_history[i] = m_drawdown_history[i + 1];
         m_drawdown_time_history[i] = m_drawdown_time_history[i + 1];
      }
      
      // Добавляем новое значение в конец
      m_drawdown_history[m_drawdown_history_size - 1] = drawdown;
      m_drawdown_time_history[m_drawdown_history_size - 1] = TimeCurrent();
   }
   
   // Расчет тренда просадки
   if(m_drawdown_history_count >= 3)
   {
      double sum_x = 0, sum_y = 0, sum_xy = 0, sum_x2 = 0;
      double n = (double)m_drawdown_history_count;
      
      for(int i = 0; i < m_drawdown_history_count; i++)
      {
         double x = (double)i;
         double y = m_drawdown_history[i];
         
         sum_x += x;
         sum_y += y;
         sum_xy += x * y;
         sum_x2 += x * x;
      }
      
      // Расчет коэффициента наклона (тренда)
      m_drawdown_trend = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x);
   }
}

//+------------------------------------------------------------------+
//| Прогнозирование максимальной просадки                            |
//+------------------------------------------------------------------+
double PredictMaximumDrawdown()
{
   // Если недостаточно данных для прогноза, возвращаем текущую просадку
   if(m_drawdown_history_count < 3)
      return m_current_drawdown_pct;
   
   // Простая линейная экстраполяция на основе тренда
   double predicted_max = m_current_drawdown_pct;
   
   // Если тренд положительный (просадка растет), прогнозируем будущее значение
   if(m_drawdown_trend > 0)
   {
      // Прогноз на несколько шагов вперед
      int forecast_steps = 5;
      predicted_max = m_current_drawdown_pct + m_drawdown_trend * forecast_steps;
      
      // Ограничиваем максимальную прогнозируемую просадку
      double max_allowed_prediction = m_current_drawdown_pct * 2.0;
      if(predicted_max > max_allowed_prediction)
         predicted_max = max_allowed_prediction;
   }
   
   return predicted_max;
}

//+------------------------------------------------------------------+
//| Определение причины просадки                                     |
//+------------------------------------------------------------------+
ENUM_DRAWDOWN_CAUSE DetectDrawdownCause()
{
   // Анализируем рыночные условия для определения причины просадки
   
   // Проверка на изменение тренда
   int current_trend = DetectMarketTrend();
   static int previous_trend = 0;
   
   if(previous_trend != 0 && current_trend != previous_trend)
   {
      previous_trend = current_trend;
      return DD_CAUSE_TREND_CHANGE;
   }
   
   previous_trend = current_trend;
   
   // Проверка на всплеск волатильности
   double atr[];
   int atr_handle = iATR(m_symbol, PERIOD_CURRENT, 14);
   
   if(atr_handle != INVALID_HANDLE)
   {
      if(CopyBuffer(atr_handle, 0, 0, 10, atr) > 0)
      {
         double avg_atr = 0;
         for(int i = 1; i < 10; i++)
            avg_atr += atr[i];
         
         avg_atr /= 9.0;
         
         // Если текущая волатильность значительно выше средней
         if(atr[0] > avg_atr * 1.5)
         {
            IndicatorRelease(atr_handle);
            return DD_CAUSE_VOLATILITY;
         }
      }
      
      IndicatorRelease(atr_handle);
   }
   
   // Проверка на влияние новостей (упрощенная)
   // В реальной системе здесь был бы анализ календаря новостей
   
   // По умолчанию - технический фактор
   return DD_CAUSE_TECHNICAL;
}

//+------------------------------------------------------------------+
//| Выбор оптимального метода восстановления                         |
//+------------------------------------------------------------------+
ENUM_RECOVERY_METHOD SelectOptimalRecoveryMethod(ENUM_DRAWDOWN_CAUSE cause)
{
   // Выбор метода восстановления в зависимости от причины просадки
   switch(cause)
   {
      case DD_CAUSE_TREND_CHANGE:
         // При смене тренда лучше использовать хеджирование
         return RECOVERY_HEDGING;
         
      case DD_CAUSE_VOLATILITY:
         // При всплеске волатильности лучше частично закрывать позиции
         return RECOVERY_PARTIAL_CLOSE;
         
      case DD_CAUSE_NEWS_IMPACT:
         // При влиянии новостей лучше использовать комбинированный метод
         return RECOVERY_COMBINED;
         
      default:
         // По умолчанию - усреднение
         return RECOVERY_AVERAGING;
   }
}

//+------------------------------------------------------------------+
//| Применение адаптивного трейлинга                                |
//+------------------------------------------------------------------+
void ApplyAdaptiveTrailing(double drawdown)
{
   // Адаптивный трейлинг в зависимости от просадки
   
   // Если просадка небольшая, используем стандартный трейлинг
   if(drawdown < m_low_risk_dd)
      return;
      
   // Рассчитываем коэффициент ускорения трейлинга
   double acceleration = 1.0;
   
   if(drawdown >= m_low_risk_dd && drawdown < m_medium_risk_dd)
      acceleration = 1.2;
   else if(drawdown >= m_medium_risk_dd && drawdown < m_high_risk_dd)
      acceleration = 1.5;
   else if(drawdown >= m_high_risk_dd && drawdown < m_extreme_risk_dd)
      acceleration = 1.8;
   else // drawdown >= m_extreme_risk_dd
      acceleration = 2.0;
      
   // Применяем ускоренный трейлинг к прибыльным позициям
   int positions_total = PositionsTotal();
   
   for(int i = positions_total - 1; i >= 0; i--)
   {
      if(PositionSelectByTicket(PositionGetTicket(i)))
      {
         if(PositionGetInteger(POSITION_MAGIC) == m_magic && 
            PositionGetString(POSITION_SYMBOL) == m_symbol)
         {
            double position_profit = PositionGetDouble(POSITION_PROFIT);
            
            // Применяем трейлинг только к прибыльным позициям
            if(position_profit > 0)
            {
               double current_sl = PositionGetDouble(POSITION_SL);
               double current_price = PositionGetDouble(POSITION_PRICE_CURRENT);
               double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
               ENUM_POSITION_TYPE position_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
               
               double new_sl = current_sl;
               
               // Расчет нового стоп-лосса с учетом ускорения
               if(position_type == POSITION_TYPE_BUY)
               {
                  double trailing_level = current_price - m_tralling_stop * m_point / acceleration;
                  if(trailing_level > current_sl && trailing_level > open_price)
                     new_sl = trailing_level;
               }
               else // POSITION_TYPE_SELL
               {
                  double trailing_level = current_price + m_tralling_stop * m_point / acceleration;
                  if(trailing_level < current_sl || current_sl == 0)
                     new_sl = trailing_level;
               }
               
               // Модифицируем стоп-лосс, если он изменился
               if(new_sl != current_sl)
               {
                  m_trade.PositionModify(PositionGetTicket(i), new_sl, PositionGetDouble(POSITION_TP));
                  if(m_detailed_logging)
                     Print("[AdaptiveTrailing] Модифицирован SL для позиции #", PositionGetTicket(i), 
                           ", новый SL: ", DoubleToString(new_sl, m_digits));
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Частичное закрытие позиций                                       |
//+------------------------------------------------------------------+
bool PartialClosePositions(double percent = 0)
{
   if(percent <= 0)
      percent = m_partial_close_percent;
      
   if(percent <= 0 || percent > 100)
      return false;
      
   bool result = false;
   int positions_total = PositionsTotal();
   
   // Создаем массив для хранения тикетов позиций, которые нужно закрыть
   ulong tickets_to_close[];
   int tickets_count = 0;
   
   // Сначала собираем все позиции, которые нужно частично закрыть
   for(int i = positions_total - 1; i >= 0; i--)
   {
      if(PositionSelectByTicket(PositionGetTicket(i)))
      {
         if(PositionGetInteger(POSITION_MAGIC) == m_magic && 
            PositionGetString(POSITION_SYMBOL) == m_symbol)
         {
            // Добавляем тикет в массив
            tickets_count++;
            ArrayResize(tickets_to_close, tickets_count);
            tickets_to_close[tickets_count - 1] = PositionGetTicket(i);
         }
      }
   }
   
   // Теперь частично закрываем каждую позицию
   for(int i = 0; i < tickets_count; i++)
   {
      if(PositionSelectByTicket(tickets_to_close[i]))
      {
         double position_volume = PositionGetDouble(POSITION_VOLUME);
         double close_volume = position_volume * percent / 100.0;
         
         // Проверяем, не меньше ли объем закрытия минимального лота
         double min_lot = CalculateMinLot();
         
         if(close_volume >= min_lot)
         {
            // Округляем объем до допустимого значения
            close_volume = NormalizeDouble(close_volume, 2);
            
            // Закрываем часть позиции
            if(m_trade.PositionClosePartial(tickets_to_close[i], close_volume))
            {
               Print("[PartialClose] Частично закрыта позиция #", tickets_to_close[i], 
                     ", объем: ", DoubleToString(close_volume, 2), 
                     " (", DoubleToString(percent, 1), "%)");
               result = true;
            }
            else
            {
               Print("[PartialClose] Ошибка при частичном закрытии позиции #", tickets_to_close[i], 
                     ", код ошибки: ", GetLastError());
            }
         }
         else
         {
            Print("[PartialClose] Объем для частичного закрытия (", DoubleToString(close_volume, 2), 
                  ") меньше минимального лота (", DoubleToString(min_lot, 2), ")");
         }
      }
   }
   
   return result;
}

//+------------------------------------------------------------------+
//| Применение хеджирования                                          |
//+------------------------------------------------------------------+
bool ApplyHedging()
{
   // Расчет общего объема открытых позиций по типам
   double buy_volume = 0.0;
   double sell_volume = 0.0;
   
   int positions_total = PositionsTotal();
   
   for(int i = positions_total - 1; i >= 0; i--)
   {
      if(PositionSelectByTicket(PositionGetTicket(i)))
      {
         if(PositionGetInteger(POSITION_MAGIC) == m_magic && 
            PositionGetString(POSITION_SYMBOL) == m_symbol)
         {
            ENUM_POSITION_TYPE position_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            double position_volume = PositionGetDouble(POSITION_VOLUME);
            
            if(position_type == POSITION_TYPE_BUY)
               buy_volume += position_volume;
            else
               sell_volume += position_volume;
         }
      }
   }
   
   // Определяем, какой тип позиций преобладает
   double hedge_volume = 0.0;
   ENUM_ORDER_TYPE hedge_type = ORDER_TYPE_BUY;
   
   if(buy_volume > sell_volume)
   {
      hedge_volume = buy_volume - sell_volume;
      hedge_type = ORDER_TYPE_SELL;
   }
   else if(sell_volume > buy_volume)
   {
      hedge_volume = sell_volume - buy_volume;
      hedge_type = ORDER_TYPE_BUY;
   }
   else
   {
      // Объемы равны, хеджирование не требуется
      return false;
   }
   
   // Проверяем, достаточен ли объем для хеджирования
   double min_lot = CalculateMinLot();
   
   if(hedge_volume < min_lot)
   {
      Print("[Hedging] Объем для хеджирования (", DoubleToString(hedge_volume, 2), 
            ") меньше минимального лота (", DoubleToString(min_lot, 2), ")");
      return false;
   }
   
   // Округляем объем до допустимого значения
   hedge_volume = NormalizeDouble(hedge_volume, 2);
   
   // Открываем хеджирующую позицию
   double price = (hedge_type == ORDER_TYPE_BUY) ? 
                  SymbolInfoDouble(m_symbol, SYMBOL_ASK) : 
                  SymbolInfoDouble(m_symbol, SYMBOL_BID);
                  
   if(m_trade.PositionOpen(m_symbol, hedge_type, hedge_volume, price, 0, 0, 
                           "Hedge_" + IntegerToString(TimeCurrent())))
   {
      Print("[Hedging] Открыта хеджирующая позиция, тип: ", 
            EnumToString(hedge_type), ", объем: ", DoubleToString(hedge_volume, 2));
      return true;
   }
   else
   {
      Print("[Hedging] Ошибка при открытии хеджирующей позиции, код ошибки: ", GetLastError());
      return false;
   }
}

//+------------------------------------------------------------------+
//| Расчет минимального лота                                         |
//+------------------------------------------------------------------+
double CalculateMinLot()
{
   return SymbolInfoDouble(m_symbol, SYMBOL_VOLUME_MIN);
}

//+------------------------------------------------------------------+
//| Оптимизированная система динамического ATR                       |
//+------------------------------------------------------------------+
void OptimizeATRPeriod()
{
   if(!m_use_enhanced_volatility || !m_use_optimal_atr_selection)
      return;

   // Проверяем, не слишком ли часто вызываем функцию (не чаще раза в час)
   if(TimeCurrent() - m_last_atr_optimization < 3600)
      return;

   m_last_atr_optimization = TimeCurrent();

   // Определяем текущий режим волатильности
   UpdateVolatilityRegime();

   // Тестируем эффективность разных периодов ATR
   int best_period = FindOptimalATRPeriod();

   if(best_period != m_optimal_atr_period && best_period > 0)
   {
      m_optimal_atr_period = best_period;
      m_current_atr_period = best_period;

      // Пересоздаем индикатор ATR с новым периодом
      if(m_atr_handle != INVALID_HANDLE)
         IndicatorRelease(m_atr_handle);

      m_atr_handle = iATR(m_symbol, PERIOD_CURRENT, m_optimal_atr_period);

      if(m_debug_mode)
         Print("Оптимизация ATR: новый оптимальный период = ", m_optimal_atr_period,
               ", режим волатильности = ", m_volatility_regime);
   }
}

//+------------------------------------------------------------------+
//| Определение режима волатильности                                 |
//+------------------------------------------------------------------+
void UpdateVolatilityRegime()
{
   if(!m_use_atr_volatility_regime)
      return;

   double atr_buffer[];
   if(CopyBuffer(m_atr_handle, 0, 0, 20, atr_buffer) <= 0)
      return;

   // Рассчитываем среднее значение ATR за последние 20 периодов
   double avg_atr = 0;
   for(int i = 0; i < 20; i++)
      avg_atr += atr_buffer[i];
   avg_atr /= 20;

   double current_atr = atr_buffer[0];
   double volatility_ratio = current_atr / avg_atr;

   // Определяем режим волатильности
   int old_regime = m_volatility_regime;

   if(volatility_ratio < m_low_volatility_threshold)
      m_volatility_regime = 0; // Низкая волатильность
   else if(volatility_ratio > m_high_volatility_threshold)
      m_volatility_regime = 2; // Высокая волатильность
   else
      m_volatility_regime = 1; // Средняя волатильность

   if(old_regime != m_volatility_regime && m_debug_mode)
   {
      string regime_names[] = {"Низкая", "Средняя", "Высокая"};
      Print("Смена режима волатильности: ", regime_names[old_regime], " -> ",
            regime_names[m_volatility_regime], " (коэффициент: ",
            DoubleToString(volatility_ratio, 3), ")");
   }
}

//+------------------------------------------------------------------+
//| Поиск оптимального периода ATR                                   |
//+------------------------------------------------------------------+
int FindOptimalATRPeriod()
{
   double best_efficiency = 0;
   int best_period = m_current_atr_period;

   // Тестируем разные периоды ATR в зависимости от режима волатильности
   int test_periods[];

   switch(m_volatility_regime)
   {
      case 0: // Низкая волатильность - используем более длинные периоды
         ArrayResize(test_periods, 5);
         test_periods[0] = m_atr_period_max;
         test_periods[1] = m_atr_period_max - 2;
         test_periods[2] = (m_atr_period_max + m_atr_period_min) / 2 + 2;
         test_periods[3] = (m_atr_period_max + m_atr_period_min) / 2;
         test_periods[4] = m_atr_period_min + 4;
         break;

      case 1: // Средняя волатильность - используем средние периоды
         ArrayResize(test_periods, 5);
         test_periods[0] = (m_atr_period_max + m_atr_period_min) / 2;
         test_periods[1] = (m_atr_period_max + m_atr_period_min) / 2 + 2;
         test_periods[2] = (m_atr_period_max + m_atr_period_min) / 2 - 2;
         test_periods[3] = m_atr_period_min + 3;
         test_periods[4] = m_atr_period_max - 3;
         break;

      case 2: // Высокая волатильность - используем более короткие периоды
         ArrayResize(test_periods, 5);
         test_periods[0] = m_atr_period_min;
         test_periods[1] = m_atr_period_min + 2;
         test_periods[2] = (m_atr_period_max + m_atr_period_min) / 2 - 2;
         test_periods[3] = (m_atr_period_max + m_atr_period_min) / 2;
         test_periods[4] = m_atr_period_max - 4;
         break;
   }

   // Тестируем каждый период
   for(int i = 0; i < ArraySize(test_periods); i++)
   {
      int period = test_periods[i];
      if(period < m_atr_period_min || period > m_atr_period_max)
         continue;

      double efficiency = CalculateATREfficiency(period);

      if(efficiency > best_efficiency)
      {
         best_efficiency = efficiency;
         best_period = period;
      }
   }

   // Возвращаем лучший период только если его эффективность превышает порог
   if(best_efficiency > m_atr_efficiency_threshold)
      return best_period;
   else
      return m_current_atr_period; // Оставляем текущий период
}

//+------------------------------------------------------------------+
//| Расчет эффективности периода ATR                                 |
//+------------------------------------------------------------------+
double CalculateATREfficiency(int atr_period)
{
   // Создаем временный индикатор ATR для тестирования
   int temp_atr_handle = iATR(m_symbol, PERIOD_CURRENT, atr_period);
   if(temp_atr_handle == INVALID_HANDLE)
      return 0.0;

   double atr_buffer[];
   if(CopyBuffer(temp_atr_handle, 0, 0, m_atr_optimization_period, atr_buffer) <= 0)
   {
      IndicatorRelease(temp_atr_handle);
      return 0.0;
   }

   // Получаем данные о ценах для анализа
   double high_buffer[], low_buffer[], close_buffer[];
   if(CopyHigh(m_symbol, PERIOD_CURRENT, 0, m_atr_optimization_period, high_buffer) <= 0 ||
      CopyLow(m_symbol, PERIOD_CURRENT, 0, m_atr_optimization_period, low_buffer) <= 0 ||
      CopyClose(m_symbol, PERIOD_CURRENT, 0, m_atr_optimization_period, close_buffer) <= 0)
   {
      IndicatorRelease(temp_atr_handle);
      return 0.0;
   }

   // Рассчитываем эффективность на основе точности предсказания движений
   double correct_predictions = 0;
   double total_predictions = 0;

   for(int i = 1; i < m_atr_optimization_period - 1; i++)
   {
      double atr_value = atr_buffer[i];
      double price_range = high_buffer[i] - low_buffer[i];
      double next_price_range = high_buffer[i + 1] - low_buffer[i + 1];

      // Проверяем, насколько точно ATR предсказывает следующий диапазон цен
      double prediction_accuracy = 1.0 - MathAbs(next_price_range - atr_value) / atr_value;
      if(prediction_accuracy > 0)
         correct_predictions += prediction_accuracy;

      total_predictions++;
   }

   IndicatorRelease(temp_atr_handle);

   return total_predictions > 0 ? correct_predictions / total_predictions : 0.0;
}

//+------------------------------------------------------------------+
//| Обновление сезонных паттернов волатильности                      |
//+------------------------------------------------------------------+
void UpdateSeasonalVolatilityPatterns()
{
   if(!m_use_enhanced_volatility || !m_use_seasonal_volatility)
      return;

   // Проверяем, не слишком ли часто обновляем (раз в день)
   if(TimeCurrent() - m_last_seasonal_update < 86400)
      return;

   m_last_seasonal_update = TimeCurrent();

   // Обновляем паттерны по часам
   if(m_use_hourly_pattern)
      UpdateHourlyVolatilityPattern();

   // Обновляем паттерны по дням недели
   if(m_use_daily_pattern)
      UpdateDailyVolatilityPattern();

   // Обновляем паттерны по торговым сессиям
   if(m_use_session_pattern)
      UpdateSessionVolatilityPattern();
}

//+------------------------------------------------------------------+
//| Обновление почасового паттерна волатильности                     |
//+------------------------------------------------------------------+
void UpdateHourlyVolatilityPattern()
{
   double hourly_atr_sums[24];
   int hourly_counts[24];

   ArrayInitialize(hourly_atr_sums, 0.0);
   ArrayInitialize(hourly_counts, 0);

   // Анализируем исторические данные
   int total_bars = m_volatility_history_days * 24; // Приблизительно
   double atr_buffer[];

   if(CopyBuffer(m_atr_handle, 0, 0, total_bars, atr_buffer) <= 0)
      return;

   datetime time_buffer[];
   if(CopyTime(m_symbol, PERIOD_H1, 0, total_bars, time_buffer) <= 0)
      return;

   // Группируем данные по часам
   for(int i = 0; i < MathMin(total_bars, ArraySize(atr_buffer)); i++)
   {
      MqlDateTime dt;
      TimeToStruct(time_buffer[i], dt);

      int hour = dt.hour;
      hourly_atr_sums[hour] += atr_buffer[i];
      hourly_counts[hour]++;
   }

   // Рассчитываем средние значения и нормализуем
   double total_avg = 0;
   int valid_hours = 0;

   for(int h = 0; h < 24; h++)
   {
      if(hourly_counts[h] > 0)
      {
         m_hourly_volatility_pattern[h] = hourly_atr_sums[h] / hourly_counts[h];
         total_avg += m_hourly_volatility_pattern[h];
         valid_hours++;
      }
   }

   if(valid_hours > 0)
   {
      total_avg /= valid_hours;

      // Нормализуем относительно среднего
      for(int h = 0; h < 24; h++)
      {
         if(hourly_counts[h] > 0)
            m_hourly_volatility_pattern[h] /= total_avg;
         else
            m_hourly_volatility_pattern[h] = 1.0;
      }
   }
}

//+------------------------------------------------------------------+
//| Обновление дневного паттерна волатильности                       |
//+------------------------------------------------------------------+
void UpdateDailyVolatilityPattern()
{
   double daily_atr_sums[7];
   int daily_counts[7];

   ArrayInitialize(daily_atr_sums, 0.0);
   ArrayInitialize(daily_counts, 0);

   // Анализируем исторические данные
   int total_bars = m_volatility_history_days;
   double atr_buffer[];

   if(CopyBuffer(m_atr_handle, 0, 0, total_bars, atr_buffer) <= 0)
      return;

   datetime time_buffer[];
   if(CopyTime(m_symbol, PERIOD_D1, 0, total_bars, time_buffer) <= 0)
      return;

   // Группируем данные по дням недели
   for(int i = 0; i < MathMin(total_bars, ArraySize(atr_buffer)); i++)
   {
      MqlDateTime dt;
      TimeToStruct(time_buffer[i], dt);

      int day_of_week = dt.day_of_week;
      daily_atr_sums[day_of_week] += atr_buffer[i];
      daily_counts[day_of_week]++;
   }

   // Рассчитываем средние значения и нормализуем
   double total_avg = 0;
   int valid_days = 0;

   for(int d = 0; d < 7; d++)
   {
      if(daily_counts[d] > 0)
      {
         m_daily_volatility_pattern[d] = daily_atr_sums[d] / daily_counts[d];
         total_avg += m_daily_volatility_pattern[d];
         valid_days++;
      }
   }

   if(valid_days > 0)
   {
      total_avg /= valid_days;

      // Нормализуем относительно среднего
      for(int d = 0; d < 7; d++)
      {
         if(daily_counts[d] > 0)
            m_daily_volatility_pattern[d] /= total_avg;
         else
            m_daily_volatility_pattern[d] = 1.0;
      }
   }
}

//+------------------------------------------------------------------+
//| Обновление паттерна волатильности по торговым сессиям            |
//+------------------------------------------------------------------+
void UpdateSessionVolatilityPattern()
{
   // 0 - Азиатская сессия (00:00-09:00 GMT)
   // 1 - Европейская сессия (08:00-17:00 GMT)
   // 2 - Американская сессия (13:00-22:00 GMT)
   // 3 - Тихоокеанская сессия (22:00-00:00 GMT)

   double session_atr_sums[4];
   int session_counts[4];

   ArrayInitialize(session_atr_sums, 0.0);
   ArrayInitialize(session_counts, 0);

   // Анализируем исторические данные по часам
   int total_bars = m_volatility_history_days * 24;
   double atr_buffer[];

   if(CopyBuffer(m_atr_handle, 0, 0, total_bars, atr_buffer) <= 0)
      return;

   datetime time_buffer[];
   if(CopyTime(m_symbol, PERIOD_H1, 0, total_bars, time_buffer) <= 0)
      return;

   // Группируем данные по торговым сессиям
   for(int i = 0; i < MathMin(total_bars, ArraySize(atr_buffer)); i++)
   {
      MqlDateTime dt;
      TimeToStruct(time_buffer[i], dt);

      int session = GetTradingSession(dt.hour);
      session_atr_sums[session] += atr_buffer[i];
      session_counts[session]++;
   }

   // Рассчитываем средние значения и нормализуем
   double total_avg = 0;
   int valid_sessions = 0;

   for(int s = 0; s < 4; s++)
   {
      if(session_counts[s] > 0)
      {
         m_session_volatility_pattern[s] = session_atr_sums[s] / session_counts[s];
         total_avg += m_session_volatility_pattern[s];
         valid_sessions++;
      }
   }

   if(valid_sessions > 0)
   {
      total_avg /= valid_sessions;

      // Нормализуем относительно среднего
      for(int s = 0; s < 4; s++)
      {
         if(session_counts[s] > 0)
            m_session_volatility_pattern[s] /= total_avg;
         else
            m_session_volatility_pattern[s] = 1.0;
      }
   }
}

//+------------------------------------------------------------------+
//| Определение торговой сессии по часу GMT                          |
//+------------------------------------------------------------------+
int GetTradingSession(int hour_gmt)
{
   if(hour_gmt >= 0 && hour_gmt < 8)
      return 0; // Азиатская сессия
   else if(hour_gmt >= 8 && hour_gmt < 13)
      return 1; // Европейская сессия (начало)
   else if(hour_gmt >= 13 && hour_gmt < 17)
      return 1; // Европейская сессия (пересечение с американской)
   else if(hour_gmt >= 17 && hour_gmt < 22)
      return 2; // Американская сессия
   else
      return 3; // Тихоокеанская сессия
}

//+------------------------------------------------------------------+
//| Получение сезонного фактора волатильности                        |
//+------------------------------------------------------------------+
double GetSeasonalVolatilityFactor()
{
   if(!m_use_enhanced_volatility || !m_use_seasonal_volatility)
      return 1.0;

   MqlDateTime dt;
   TimeToStruct(TimeCurrent(), dt);

   double seasonal_factor = 1.0;

   // Фактор по часам
   if(m_use_hourly_pattern)
      seasonal_factor *= m_hourly_volatility_pattern[dt.hour];

   // Фактор по дням недели
   if(m_use_daily_pattern)
      seasonal_factor *= m_daily_volatility_pattern[dt.day_of_week];

   // Фактор по торговым сессиям
   if(m_use_session_pattern)
   {
      int session = GetTradingSession(dt.hour);
      seasonal_factor *= m_session_volatility_pattern[session];
   }

   return seasonal_factor;
}

//+------------------------------------------------------------------+
//| Улучшенное обнаружение всплесков волатильности                   |
//+------------------------------------------------------------------+
bool DetectVolatilitySpikeAdvanced()
{
   if(!m_use_enhanced_volatility || !m_use_statistical_spike_detection)
      return DetectVolatilitySpike(); // Используем старый метод

   // Обновляем историю ATR для анализа
   UpdateATRHistoryForSpikes();

   // Рассчитываем статистические параметры
   CalculateATRStatistics();

   // Получаем текущее значение ATR
   double atr_buffer[];
   if(CopyBuffer(m_atr_handle, 0, 0, 1, atr_buffer) <= 0)
      return false;

   double current_atr = atr_buffer[0];

   // Проверяем на статистический всплеск
   bool is_spike = false;

   if(m_atr_std_dev > 0)
   {
      double z_score = (current_atr - m_atr_mean) / m_atr_std_dev;
      is_spike = (z_score > m_spike_detection_sigma);
   }

   // Обновляем адаптивный порог, если используется
   if(m_use_adaptive_spike_threshold)
      UpdateAdaptiveSpikeThreshold();

   // Дополнительная проверка с адаптивным порогом
   if(!is_spike && current_atr > m_atr_mean * m_current_spike_threshold)
      is_spike = true;

   return is_spike;
}

//+------------------------------------------------------------------+
//| Обновление истории ATR для анализа всплесков                     |
//+------------------------------------------------------------------+
void UpdateATRHistoryForSpikes()
{
   double atr_buffer[];
   if(CopyBuffer(m_atr_handle, 0, 0, 1, atr_buffer) <= 0)
      return;

   // Сдвигаем массив и добавляем новое значение
   for(int i = ArraySize(m_atr_history_for_spikes) - 1; i > 0; i--)
      m_atr_history_for_spikes[i] = m_atr_history_for_spikes[i - 1];

   m_atr_history_for_spikes[0] = atr_buffer[0];
}

//+------------------------------------------------------------------+
//| Расчет статистических параметров ATR                             |
//+------------------------------------------------------------------+
void CalculateATRStatistics()
{
   int count = 0;
   double sum = 0;

   // Рассчитываем среднее значение
   for(int i = 0; i < ArraySize(m_atr_history_for_spikes); i++)
   {
      if(m_atr_history_for_spikes[i] > 0)
      {
         sum += m_atr_history_for_spikes[i];
         count++;
      }
   }

   if(count > 0)
      m_atr_mean = sum / count;
   else
      return;

   // Рассчитываем стандартное отклонение
   double variance_sum = 0;
   for(int i = 0; i < ArraySize(m_atr_history_for_spikes); i++)
   {
      if(m_atr_history_for_spikes[i] > 0)
      {
         double diff = m_atr_history_for_spikes[i] - m_atr_mean;
         variance_sum += diff * diff;
      }
   }

   if(count > 1)
      m_atr_std_dev = MathSqrt(variance_sum / (count - 1));
}

//+------------------------------------------------------------------+
//| Обновление адаптивного порога всплесков                          |
//+------------------------------------------------------------------+
void UpdateAdaptiveSpikeThreshold()
{
   if(m_atr_std_dev > 0 && m_atr_mean > 0)
   {
      // Адаптируем порог на основе текущей волатильности
      double volatility_coefficient = m_atr_std_dev / m_atr_mean;

      // Чем выше относительная волатильность, тем выше порог
      m_current_spike_threshold = m_min_spike_threshold +
                                 (m_max_spike_threshold - m_min_spike_threshold) *
                                 MathMin(1.0, volatility_coefficient * 2.0);
   }
   else
   {
      m_current_spike_threshold = (m_min_spike_threshold + m_max_spike_threshold) / 2.0;
   }
}

//+------------------------------------------------------------------+
//| Интеграция всех систем волатильности                             |
//+------------------------------------------------------------------+
double GetEnhancedVolatilityFactor()
{
   if(!m_use_enhanced_volatility)
      return 1.0;

   double volatility_factor = 1.0;

   // 1. Базовый мультитаймфреймовый анализ
   double mtf_factor = GetMultiTimeframeVolatility();

   // 2. Сезонный фактор
   double seasonal_factor = GetSeasonalVolatilityFactor();

   // 3. Фактор режима волатильности
   double regime_factor = 1.0;
   switch(m_volatility_regime)
   {
      case 0: regime_factor = 0.8; break;  // Низкая волатильность
      case 1: regime_factor = 1.0; break;  // Средняя волатильность
      case 2: regime_factor = 1.3; break;  // Высокая волатильность
   }

   // 4. Комбинируем все факторы
   volatility_factor = mtf_factor *
                      (1.0 - m_seasonal_volatility_weight + seasonal_factor * m_seasonal_volatility_weight) *
                      regime_factor;

   // 5. Проверяем на всплески волатильности
   if(DetectVolatilitySpikeAdvanced())
      volatility_factor *= 0.5; // Снижаем активность при всплесках

   return MathMax(0.3, MathMin(2.0, volatility_factor));
}

// Конец файла