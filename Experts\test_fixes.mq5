//+------------------------------------------------------------------+
//| Test Fixes for Lot Normalization                                |
//+------------------------------------------------------------------+
#property copyright "Test"
#property version   "1.00"

//+------------------------------------------------------------------+
//| Нормализация размера лота                                       |
//+------------------------------------------------------------------+
double NormalizeLot(double lot, string symbol = "EURUSD")
{
   double min_lot = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MAX);
   double lot_step = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);
   
   if(lot_step <= 0) lot_step = 0.01; // Защита от нулевого шага
   
   // Округляем до ближайшего шага
   lot = MathRound(lot / lot_step) * lot_step;
   
   // Ограничиваем минимумом и максимумом
   lot = MathMax(min_lot, MathMin(max_lot, lot));
   
   // Дополнительная проверка на корректность
   if(lot < min_lot) lot = min_lot;
   
   // Форматируем до 2 знаков после запятой для стандартных лотов
   lot = NormalizeDouble(lot, 2);
   
   return lot;
}

//+------------------------------------------------------------------+
//| Тест распределения капитала                                     |
//+------------------------------------------------------------------+
void TestCapitalAllocation()
{
   Print("=== Тест распределения капитала ===");
   
   // Имитируем распределение 33.3% между 3 слоями
   double base_allocation = 1.0 / 3.0; // 0.333333...
   Print("Базовое распределение: ", DoubleToString(base_allocation, 8));
   
   // Тестируем проблемные лоты из лога
   double test_lots[] = {0.24, 0.30, 0.08};
   
   for(int i = 0; i < ArraySize(test_lots); i++)
   {
      double base_lot = test_lots[i];
      double adjusted_lot = base_lot * base_allocation;
      double normalized_lot = NormalizeLot(adjusted_lot);
      
      Print("Тест ", i+1, ":");
      Print("  Базовый лот: ", DoubleToString(base_lot, 2));
      Print("  После распределения: ", DoubleToString(adjusted_lot, 8));
      Print("  После нормализации: ", DoubleToString(normalized_lot, 2));
      Print("  ---");
   }
}

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("=== Тест исправлений нормализации лотов ===");
   
   // Тест 1: Проблемные значения из лога
   Print("=== Тест 1: Проблемные лоты ===");
   double problem_lots[] = {0.02666667, 0.01777778, 0.0533, 0.0800};
   
   for(int i = 0; i < ArraySize(problem_lots); i++)
   {
      double normalized = NormalizeLot(problem_lots[i]);
      Print("Проблемный лот ", i+1, ": ", DoubleToString(problem_lots[i], 8), " -> ", DoubleToString(normalized, 2));
   }
   
   // Тест 2: Распределение капитала
   TestCapitalAllocation();
   
   Print("=== Тест завершен ===");
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
}
