//+------------------------------------------------------------------+
//|                                       AF-TrendKiller_MT5.mq5     |
//|                         Copyright 2023, AFSID-Group.Cv           |
//|                                      https://afs-id.com          |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, AFSID-Group.Cv"
#property link      "https://afs-id.com"
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 5
#property indicator_plots   3

// Индикатор тренда
#property indicator_label1  "TK-UP"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrBlue
#property indicator_style1  STYLE_SOLID
#property indicator_width1  3

#property indicator_label2  "TK-DOWN"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrRed
#property indicator_style2  STYLE_SOLID
#property indicator_width2  3

#property indicator_label3  "TK-ZigZag"
#property indicator_type3   DRAW_SECTION
#property indicator_color3  clrGray
#property indicator_style3  STYLE_SOLID
#property indicator_width3  1

//--- Входные параметры
input int InpMAPeriod = 21;             // Период MA
input ENUM_MA_METHOD InpMAMethod = MODE_SMA; // Метод MA
input int InpFastMACD = 12;             // Быстрый период MACD
input int InpSlowMACD = 26;             // Медленный период MACD
input int InpSignalMACD = 9;            // Сигнальный период MACD
input int InpATRPeriod = 14;            // Период ATR
input double InpATRMultiplier = 2.0;    // Множитель ATR для ZigZag
input bool InpShowZigZag = true;        // Показывать ZigZag
input bool InpAlertEnabled = false;     // Включить оповещения
input bool InpEmailEnabled = false;     // Включить email оповещения

//--- Буферы индикатора
double UpBuffer[];
double DownBuffer[];
double ZigzagBuffer[];
double HighBuffer[];
double LowBuffer[];

//--- Глобальные переменные
int lastSignal = 0; // 0 - нет сигнала, 1 - восходящий, -1 - нисходящий
datetime lastAlertTime = 0;

// Константы стрелок
#define ARROW_UP    233
#define ARROW_DOWN  234

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   // Настройка буферов индикатора
   SetIndexBuffer(0, UpBuffer, INDICATOR_DATA);
   SetIndexBuffer(1, DownBuffer, INDICATOR_DATA);
   SetIndexBuffer(2, ZigzagBuffer, INDICATOR_DATA);
   SetIndexBuffer(3, HighBuffer, INDICATOR_CALCULATIONS);
   SetIndexBuffer(4, LowBuffer, INDICATOR_CALCULATIONS);
   
   // Установка символов для стрелок
   PlotIndexSetInteger(0, PLOT_ARROW, ARROW_UP);
   PlotIndexSetInteger(1, PLOT_ARROW, ARROW_DOWN);
   
   // Скрыть ZigZag при необходимости
   PlotIndexSetInteger(2, PLOT_DRAW_TYPE, InpShowZigZag ? DRAW_SECTION : DRAW_NONE);
   
   // Установка пустых значений
   PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, 0.0);
   PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, 0.0);
   PlotIndexSetDouble(2, PLOT_EMPTY_VALUE, 0.0);
   
   // Сдвиг стрелок
   PlotIndexSetInteger(0, PLOT_ARROW_SHIFT, 5);
   PlotIndexSetInteger(1, PLOT_ARROW_SHIFT, -5);
   
   // Установка имени индикатора
   string shortName = "AF-TrendKiller MT5 (" + 
                      string(InpMAPeriod) + "," + 
                      string(InpFastMACD) + "," +
                      string(InpSlowMACD) + "," +
                      string(InpATRPeriod) + ")";
   IndicatorSetString(INDICATOR_SHORTNAME, shortName);
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   // Проверка на достаточное количество баров
   if(rates_total < InpMAPeriod || rates_total < InpSlowMACD)
      return(0);
      
   // Вычисление начальной позиции
   int start;
   if(prev_calculated == 0)
   {
      start = InpMAPeriod + InpSlowMACD;
      ArrayInitialize(UpBuffer, 0);
      ArrayInitialize(DownBuffer, 0);
      ArrayInitialize(ZigzagBuffer, 0);
      ArrayInitialize(HighBuffer, 0);
      ArrayInitialize(LowBuffer, 0);
   }
   else
   {
      start = prev_calculated - 1;
   }
   
   // Получение хендлов индикаторов
   int maHandle = iMA(_Symbol, PERIOD_CURRENT, InpMAPeriod, 0, (ENUM_MA_METHOD)InpMAMethod, PRICE_CLOSE);
   int macdHandle = iMACD(_Symbol, PERIOD_CURRENT, InpFastMACD, InpSlowMACD, InpSignalMACD, PRICE_CLOSE);
   int atrHandle = iATR(_Symbol, PERIOD_CURRENT, InpATRPeriod);
   
   // Массивы для значений индикаторов
   double maValues[];
   double macdValues[];
   double macdSignal[];
   double atrValues[];
   
   // Выделение памяти
   ArraySetAsSeries(maValues, true);
   ArraySetAsSeries(macdValues, true);
   ArraySetAsSeries(macdSignal, true);
   ArraySetAsSeries(atrValues, true);
   
   // Основной цикл вычислений
   for(int i = start; i < rates_total && !IsStopped(); i++)
   {
      // Получение значений индикаторов
      CopyBuffer(maHandle, 0, rates_total-i-1, 2, maValues);
      CopyBuffer(macdHandle, 0, rates_total-i-1, 2, macdValues);
      CopyBuffer(macdHandle, 1, rates_total-i-1, 2, macdSignal);
      CopyBuffer(atrHandle, 0, rates_total-i-1, 1, atrValues);
      
      // Вычисление сигналов
      bool buySignal = false;
      bool sellSignal = false;
      
      // Сигнал на покупку (бычий тренд)
      if(close[i] > maValues[0] && macdValues[0] > macdSignal[0] && 
         macdValues[0] > macdValues[1] && macdValues[0] > 0)
      {
         buySignal = true;
      }
      
      // Сигнал на продажу (медвежий тренд)
      if(close[i] < maValues[0] && macdValues[0] < macdSignal[0] && 
         macdValues[0] < macdValues[1] && macdValues[0] < 0)
      {
         sellSignal = true;
      }
      
      // Установка сигналов в буферы
      UpBuffer[i] = buySignal ? low[i] - 10 * _Point : 0;
      DownBuffer[i] = sellSignal ? high[i] + 10 * _Point : 0;
      
      // Обработка ZigZag
      if(InpShowZigZag)
      {
         double atrValue = atrValues[0] * InpATRMultiplier;
         
         // Нахождение новых точек ZigZag
         if(high[i] > HighBuffer[i-1] + atrValue)
         {
            HighBuffer[i] = high[i];
            LowBuffer[i] = LowBuffer[i-1];
            ZigzagBuffer[i-1] = LowBuffer[i-1];
            ZigzagBuffer[i] = high[i];
         }
         else if(low[i] < LowBuffer[i-1] - atrValue)
         {
            LowBuffer[i] = low[i];
            HighBuffer[i] = HighBuffer[i-1];
            ZigzagBuffer[i-1] = HighBuffer[i-1];
            ZigzagBuffer[i] = low[i];
         }
         else
         {
            HighBuffer[i] = MathMax(HighBuffer[i-1], high[i]);
            LowBuffer[i] = MathMin(LowBuffer[i-1], low[i]);
            ZigzagBuffer[i] = 0;
         }
      }
      
      // Проверка на необходимость отправки уведомлений
      if(i == rates_total - 1) // только на последнем баре
      {
         int currentSignal = buySignal ? 1 : (sellSignal ? -1 : 0);
         
         // Отправка уведомлений, если сигнал изменился и прошло достаточно времени
         if(currentSignal != 0 && currentSignal != lastSignal && time[i] != lastAlertTime)
         {
            string message = _Symbol + " AF-TrendKiller MT5: " + 
                           (currentSignal == 1 ? "UP" : "DOWN") + " Signal";
            
            if(InpAlertEnabled) Alert(message);
            if(InpEmailEnabled) SendMail("AF-TrendKiller MT5 Signal", message);
            
            lastSignal = currentSignal;
            lastAlertTime = time[i];
         }
      }
   }
   
   // Освобождение хендлов индикаторов
   IndicatorRelease(maHandle);
   IndicatorRelease(macdHandle);
   IndicatorRelease(atrHandle);
   
   // Возвращаем значение количества обработанных баров
   return(rates_total);
}
//+------------------------------------------------------------------+ 