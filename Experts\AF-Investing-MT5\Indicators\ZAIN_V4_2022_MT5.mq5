//+------------------------------------------------------------------+
//|                                              ZAIN_V4_2022_MT5.mq5 |
//|                                Copyright 2023, AFSID-Group.Cv      |
//|                                         https://afs-id.com         |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, AFSID-Group.Cv"
#property link      "https://afs-id.com"
#property version   "4.0"
#property indicator_chart_window
#property indicator_buffers 4
#property indicator_plots   4

// Определение свойств для графиков индикатора
#property indicator_label1  "ZAIN Buy"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrLime
#property indicator_style1  STYLE_SOLID
#property indicator_width1  2

#property indicator_label2  "ZAIN Sell"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrRed
#property indicator_style2  STYLE_SOLID
#property indicator_width2  2

#property indicator_label3  "ZAIN Exit Buy"
#property indicator_type3   DRAW_ARROW
#property indicator_color3  clrYellow
#property indicator_style3  STYLE_SOLID
#property indicator_width3  2

#property indicator_label4  "ZAIN Exit Sell"
#property indicator_type4   DRAW_ARROW
#property indicator_color4  clrYellow
#property indicator_style4  STYLE_SOLID
#property indicator_width4  2

// Буферы индикатора
double BuyBuffer[];
double SellBuffer[];
double ExitBuyBuffer[];
double ExitSellBuffer[];

// Входные параметры
input int    InpPeriod = 14;           // Период
input int    InpShift = 0;             // Сдвиг
input double InpSensitivity = 2.0;     // Чувствительность
input bool   InpAlerts = true;         // Включить алерты
input bool   InpEmailAlerts = false;   // Включить email алерты
input bool   InpPushAlerts = false;    // Включить push алерты

// Глобальные переменные
datetime lastAlertTime = 0;
datetime lastSignalTime = 0;
int MA_Handle;
int RSI_Handle;
int CCI_Handle;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   // Инициализация буферов
   SetIndexBuffer(0, BuyBuffer, INDICATOR_DATA);
   SetIndexBuffer(1, SellBuffer, INDICATOR_DATA);
   SetIndexBuffer(2, ExitBuyBuffer, INDICATOR_DATA);
   SetIndexBuffer(3, ExitSellBuffer, INDICATOR_DATA);
   
   // Установка стрелок
   PlotIndexSetInteger(0, PLOT_ARROW, 233);
   PlotIndexSetInteger(1, PLOT_ARROW, 234);
   PlotIndexSetInteger(2, PLOT_ARROW, 251);
   PlotIndexSetInteger(3, PLOT_ARROW, 251);
   
   // Смещение стрелок
   PlotIndexSetInteger(0, PLOT_ARROW_SHIFT, 5);
   PlotIndexSetInteger(1, PLOT_ARROW_SHIFT, -5);
   PlotIndexSetInteger(2, PLOT_ARROW_SHIFT, 5);
   PlotIndexSetInteger(3, PLOT_ARROW_SHIFT, -5);
   
   // Установка пустых значений
   PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, EMPTY_VALUE);
   PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, EMPTY_VALUE);
   PlotIndexSetDouble(2, PLOT_EMPTY_VALUE, EMPTY_VALUE);
   PlotIndexSetDouble(3, PLOT_EMPTY_VALUE, EMPTY_VALUE);
   
   // Создание хэндлов индикаторов
   MA_Handle = iMA(_Symbol, PERIOD_CURRENT, InpPeriod, InpShift, MODE_EMA, PRICE_CLOSE);
   RSI_Handle = iRSI(_Symbol, PERIOD_CURRENT, InpPeriod, PRICE_CLOSE);
   CCI_Handle = iCCI(_Symbol, PERIOD_CURRENT, InpPeriod, PRICE_TYPICAL);
   
   if(MA_Handle == INVALID_HANDLE || RSI_Handle == INVALID_HANDLE || CCI_Handle == INVALID_HANDLE)
   {
      Print("Ошибка создания хэндлов индикаторов");
      return(INIT_FAILED);
   }
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   if(MA_Handle != INVALID_HANDLE)
      IndicatorRelease(MA_Handle);
   if(RSI_Handle != INVALID_HANDLE)
      IndicatorRelease(RSI_Handle);
   if(CCI_Handle != INVALID_HANDLE)
      IndicatorRelease(CCI_Handle);
}

//+------------------------------------------------------------------+
//| Функция отправки уведомлений                                      |
//+------------------------------------------------------------------+
void SendAlert(const string message, const datetime& current_time)
{
   if(current_time == lastAlertTime) return;
   
   if(InpAlerts)
      Alert(message);
      
   if(InpEmailAlerts)
      SendMail("ZAIN Signal", message);
      
   if(InpPushAlerts)
      SendNotification(message);
      
   lastAlertTime = current_time;
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   if(rates_total < InpPeriod) return(0);
   
   // Определяем стартовую позицию
   int start = prev_calculated > 0 ? prev_calculated - 1 : 0;
   
   // Копируем данные индикаторов
   double MA[], RSI[], CCI[];
   ArraySetAsSeries(MA, true);
   ArraySetAsSeries(RSI, true);
   ArraySetAsSeries(CCI, true);
   ArraySetAsSeries(close, true);
   ArraySetAsSeries(high, true);
   ArraySetAsSeries(low, true);
   ArraySetAsSeries(time, true);
   
   if(CopyBuffer(MA_Handle, 0, 0, rates_total, MA) <= 0) return(0);
   if(CopyBuffer(RSI_Handle, 0, 0, rates_total, RSI) <= 0) return(0);
   if(CopyBuffer(CCI_Handle, 0, 0, rates_total, CCI) <= 0) return(0);
   
   // Расчет сигналов
   for(int i = start; i < rates_total && !IsStopped(); i++)
   {
      // Инициализация значений
      BuyBuffer[i] = EMPTY_VALUE;
      SellBuffer[i] = EMPTY_VALUE;
      ExitBuyBuffer[i] = EMPTY_VALUE;
      ExitSellBuffer[i] = EMPTY_VALUE;
      
      // Проверка сигналов
      bool buySignal = close[i] > MA[i] && RSI[i] < 30 && CCI[i] < -100;
      bool sellSignal = close[i] < MA[i] && RSI[i] > 70 && CCI[i] > 100;
      bool exitBuy = RSI[i] > 70 || CCI[i] > 100;
      bool exitSell = RSI[i] < 30 || CCI[i] < -100;
      
      // Установка сигналов
      if(buySignal && time[i] != lastSignalTime)
      {
         BuyBuffer[i] = low[i] - 5 * _Point;
         string message = StringFormat("%s: Buy Signal at %s", _Symbol, TimeToString(time[i]));
         SendAlert(message, time[i]);
         lastSignalTime = time[i];
      }
      
      if(sellSignal && time[i] != lastSignalTime)
      {
         SellBuffer[i] = high[i] + 5 * _Point;
         string message = StringFormat("%s: Sell Signal at %s", _Symbol, TimeToString(time[i]));
         SendAlert(message, time[i]);
         lastSignalTime = time[i];
      }
      
      if(exitBuy && time[i] != lastSignalTime)
      {
         ExitBuyBuffer[i] = high[i] + 3 * _Point;
         string message = StringFormat("%s: Exit Buy Signal at %s", _Symbol, TimeToString(time[i]));
         SendAlert(message, time[i]);
         lastSignalTime = time[i];
      }
      
      if(exitSell && time[i] != lastSignalTime)
      {
         ExitSellBuffer[i] = low[i] - 3 * _Point;
         string message = StringFormat("%s: Exit Sell Signal at %s", _Symbol, TimeToString(time[i]));
         SendAlert(message, time[i]);
         lastSignalTime = time[i];
      }
   }
   
   return(rates_total);
} 