//+------------------------------------------------------------------+
//|                                                   MA_Shift.mq5 |
//|                                                    ChartPrime  |
//|                                        Перенесено на MT5       |
//+------------------------------------------------------------------+
#property copyright "ChartPrime"
#property link      ""
#property version   "1.00"
#property indicator_separate_window
#property indicator_buffers 7
#property indicator_plots   3

#property indicator_label1  "MA"
#property indicator_type1   DRAW_LINE
#property indicator_color1  clrDodgerBlue
#property indicator_style1  STYLE_SOLID
#property indicator_width1  2

#property indicator_label2  "MA Background"
#property indicator_type2   DRAW_LINE
#property indicator_color2  clrDodgerBlue
#property indicator_style2  STYLE_SOLID
#property indicator_width2  7

#property indicator_label3  "Oscillator"
#property indicator_type3   DRAW_COLOR_HISTOGRAM
#property indicator_color3  clrAqua, clrTeal, clrYellow, clrOrange
#property indicator_style3  STYLE_SOLID
#property indicator_width3  2

// Буферы индикатора
double MABuffer[];
double MABackBuffer[];
double OscBuffer[];
double OscColorBuffer[];
double UpperBuffer[];
double ZeroBuffer[];
double LowerBuffer[];

// Входные параметры
input group "MA";
input ENUM_MA_METHOD   MaType = MODE_SMA;         // Тип MA
input int              MaLength = 40;             // Длина MA
input ENUM_APPLIED_PRICE AppliedPrice = PRICE_MEDIAN; // Источник цены

input group "MA Shift Oscillator";
input int              OscLength = 15;            // Длина осциллятора
input double           OscThreshold = 0.5;        // Порог осциллятора
input bool             OscCross = true;           // Показывать пересечения

input group "Color";
input color            OscColorUp1 = clrAqua;     // Цвет осциллятора вверх 1
input color            OscColorUp2 = clrTeal;     // Цвет осциллятора вверх 2
input color            OscColorDn1 = clrYellow;   // Цвет осциллятора вниз 1
input color            OscColorDn2 = clrOrange;   // Цвет осциллятора вниз 2

// Глобальные переменные
int hma_handle;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   // Инициализация буферов
   SetIndexBuffer(0, MABuffer, INDICATOR_DATA);
   SetIndexBuffer(1, MABackBuffer, INDICATOR_DATA);
   SetIndexBuffer(2, OscBuffer, INDICATOR_DATA);
   SetIndexBuffer(3, OscColorBuffer, INDICATOR_COLOR_INDEX);
   SetIndexBuffer(4, UpperBuffer, INDICATOR_DATA);
   SetIndexBuffer(5, ZeroBuffer, INDICATOR_DATA);
   SetIndexBuffer(6, LowerBuffer, INDICATOR_DATA);
   
   // Инициализация HMA
   hma_handle = iMA(NULL, 0, 10, 0, MODE_LWMA, PRICE_CLOSE);
   if(hma_handle == INVALID_HANDLE)
   {
      Print("Ошибка создания хендла iMA");
      return(INIT_FAILED);
   }
   
   // Установка параметров индикатора
   string short_name = "MA Shift [ChartPrime]";
   IndicatorSetString(INDICATOR_SHORTNAME, short_name);
   
   // Установка точности отображения
   IndicatorSetInteger(INDICATOR_DIGITS, _Digits+1);
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   if(rates_total < MaLength) return(0);
   
   int start;
   if(prev_calculated == 0)
   {
      start = MaLength + OscLength + 10;
      ArrayInitialize(MABuffer, 0);
      ArrayInitialize(MABackBuffer, 0);
      ArrayInitialize(OscBuffer, 0);
      ArrayInitialize(OscColorBuffer, 0);
      ArrayInitialize(UpperBuffer, OscThreshold);
      ArrayInitialize(ZeroBuffer, 0);
      ArrayInitialize(LowerBuffer, -OscThreshold);
   }
   else
      start = prev_calculated - 1;
      
   // Заполнение буферов уровней
   for(int i = start; i < rates_total; i++)
   {
      UpperBuffer[i] = OscThreshold;
      ZeroBuffer[i] = 0;
      LowerBuffer[i] = -OscThreshold;
   }
   
   // Получаем значения источника цены
   double source_values[];
   int price_handle = iMA(NULL, 0, 1, 0, MODE_SMA, AppliedPrice);
   if(CopyBuffer(price_handle, 0, 0, rates_total, source_values) < 0)
      return(0);
   
   // Расчет MA
   double ma_values[];
   int ma_handle = iMA(NULL, 0, MaLength, 0, MaType, AppliedPrice);
   if(CopyBuffer(ma_handle, 0, 0, rates_total, ma_values) < 0)
      return(0);
   
   // Расчет осциллятора
   double diff[];
   ArrayResize(diff, rates_total);
   
   // Расчет разницы между ценой и MA
   for(int i = start; i < rates_total; i++)
   {
      MABuffer[i] = ma_values[i];
      MABackBuffer[i] = ma_values[i]; 
      diff[i] = source_values[i] - ma_values[i];
   }
   
   // Расчет перцентиля для нормализации (упрощенно)
   double max_diff = 0;
   for(int i = MaLength; i < rates_total; i++)
   {
      if(MathAbs(diff[i]) > max_diff)
         max_diff = MathAbs(diff[i]);
   }
   
   double perc_r = max_diff * 0.99; // Приблизительная оценка 99% перцентиля
   
   // Расчет изменения diff за OscLength периодов и применение HMA
   double temp_osc[];
   ArrayResize(temp_osc, rates_total);
   
   for(int i = OscLength + MaLength; i < rates_total; i++)
   {
      temp_osc[i] = (diff[i] - diff[i-OscLength]) / perc_r;
   }
   
   // Применяем Hull MA к осциллятору
   double hma_values[];
   ArraySetAsSeries(hma_values, true);
   if(CopyBuffer(hma_handle, 0, 0, rates_total, hma_values) < 0)
      return(0);
   
   ArraySetAsSeries(hma_values, false);
   
   for(int i = start; i < rates_total; i++)
   {
      OscBuffer[i] = temp_osc[i];
      
      // Определение цвета осциллятора
      if(OscBuffer[i] > 0)
      {
         if(OscBuffer[i] > OscBuffer[i-1])
            OscColorBuffer[i] = 0; // OscColorUp1
         else
            OscColorBuffer[i] = 1; // OscColorUp2
      }
      else
      {
         if(OscBuffer[i] < OscBuffer[i-1])
            OscColorBuffer[i] = 2; // OscColorDn1
         else 
            OscColorBuffer[i] = 3; // OscColorDn2
      }
   }
   
   return(rates_total);
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Освобождаем хэндлы
   IndicatorRelease(hma_handle);
}
//+------------------------------------------------------------------+ 