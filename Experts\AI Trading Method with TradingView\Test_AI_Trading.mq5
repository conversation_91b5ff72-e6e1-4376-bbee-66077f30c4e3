//+------------------------------------------------------------------+
//|                                      Test_AI_Trading.mq5 |
//|                                         Copyright ChartPrime |
//|                                                              |
//+------------------------------------------------------------------+
#property copyright "ChartPrime"
#property link      ""
#property version   "1.00"
#property script_show_inputs

// Подключение необходимых библиотек
#include <Trade\Trade.mqh>
#include "AI_Trading_Report.mqh"

// Входные параметры
input group "Тестирование индикаторов";
input bool     TestKernelRegression = true;   // Тестировать Multi_Kernel_Regression
input bool     TestRsiMfiML = true;           // Тестировать RSI_MFI_ML
input int      TestBars = 500;                // Количество баров для тестирования
input int      KernelPeriod = 55;             // Период Kernel Regression
input double   KernelSmoothness = 0.5;        // Коэффициент сглаживания для Kernel
input int      RSI_Period = 14;               // Период RSI
input int      MFI_Period = 14;               // Период MFI
input int      ML_LookbackPeriod = 100;       // Период обучения для ML
input int      ML_ForecastPeriod = 5;         // Период предсказания для ML
input double   ML_Threshold = 0.5;            // Порог для сигналов ML

// Глобальные переменные
int KR_Handle, ML_Handle;

// Буферы для хранения значений индикаторов
double KR_Buffer[];
double KR_Color[];
double ML_Buffer[];
double ML_Color[];
double ML_Signal[];

// Буферы для записи результатов
double KR_Results[];
double ML_Results[];
double Direction_Results[];

// Объявляем глобальную переменную для отчета
CAITradingReport report;

//+------------------------------------------------------------------+
//| Функция инициализации скрипта                                     |
//+------------------------------------------------------------------+
void OnStart()
{
   // Очищаем график перед тестированием
   ChartRedraw();
   
   // Выводим заголовок отчета
   Print("===== Начало тестирования AI Trading System =====");
   
   // Инициализируем хендлы индикаторов
   if(TestKernelRegression)
   {
      KR_Handle = iCustom(Symbol(), PERIOD_CURRENT, "AI Trading Method with TradingView\\Multi_Kernel_Regression", 
                          KernelPeriod, KernelSmoothness, PRICE_CLOSE, 0);
      
      if(KR_Handle == INVALID_HANDLE)
      {
         Print("Ошибка при создании хендла Multi_Kernel_Regression: ", GetLastError());
         return;
      }
      
      Print("Индикатор Multi_Kernel_Regression успешно инициализирован");
   }
   
   if(TestRsiMfiML)
   {
      ML_Handle = iCustom(Symbol(), PERIOD_CURRENT, "AI Trading Method with TradingView\\RSI_MFI_ML", 
                         RSI_Period, MFI_Period, ML_LookbackPeriod, ML_ForecastPeriod, ML_Threshold);
      
      if(ML_Handle == INVALID_HANDLE)
      {
         Print("Ошибка при создании хендла RSI_MFI_ML: ", GetLastError());
         return;
      }
      
      Print("Индикатор RSI_MFI_ML успешно инициализирован");
   }
   
   // Подготавливаем массивы
   ArraySetAsSeries(KR_Buffer, true);
   ArraySetAsSeries(KR_Color, true);
   ArraySetAsSeries(ML_Buffer, true);
   ArraySetAsSeries(ML_Color, true);
   ArraySetAsSeries(ML_Signal, true);
   
   // Массивы для результатов
   ArrayResize(KR_Results, TestBars);
   ArrayResize(ML_Results, TestBars);
   ArrayResize(Direction_Results, TestBars);
   ArrayInitialize(KR_Results, 0);
   ArrayInitialize(ML_Results, 0);
   ArrayInitialize(Direction_Results, 0);
   
   // Запускаем тестирование
   RunBacktest();
   
   // Выводим результаты
   ShowResults();
   
   // Выводим заключение
   Print("===== Тестирование AI Trading System завершено =====");
}

//+------------------------------------------------------------------+
//| Функция обратного тестирования индикаторов                       |
//+------------------------------------------------------------------+
void RunBacktest()
{
   // Получаем массив цен закрытия
   double close[];
   ArraySetAsSeries(close, true);
   int copied = CopyClose(Symbol(), PERIOD_CURRENT, 0, TestBars, close);
   
   if(copied <= 0)
   {
      Print("Ошибка при копировании данных цен: ", GetLastError());
      return;
   }
   
   // Переменные для статистики
   int kr_buy_signals = 0;
   int kr_sell_signals = 0;
   int ml_buy_signals = 0;
   int ml_sell_signals = 0;
   int combined_buy_signals = 0;
   int combined_sell_signals = 0;
   
   // Буферы для сохранения состояния предыдущих значений
   double prev_kr_value = 0;
   double prev_ml_value = 0;
   
   // Проходим по всем барам
   for(int i = TestBars - 1; i > 0; i--)
   {
      // Получаем данные индикатора Kernel Regression
      if(TestKernelRegression && 
         CopyBuffer(KR_Handle, 0, i, 1, KR_Buffer) > 0 && 
         CopyBuffer(KR_Handle, 1, i, 1, KR_Color) > 0)
      {
         // Анализ сигналов Kernel Regression
         int kr_signal = 0;
         
         // Определяем сигнал по цвету
         if(KR_Color[0] == 0) kr_signal = 1;       // Бычий тренд
         else if(KR_Color[0] == 1) kr_signal = -1; // Медвежий тренд
         
         // Записываем результат
         KR_Results[i] = kr_signal;
         
         // Подсчет сигналов
         if(kr_signal > 0) kr_buy_signals++;
         else if(kr_signal < 0) kr_sell_signals++;
         
         // Сохраняем текущее значение
         prev_kr_value = KR_Buffer[0];
      }
      
      // Получаем данные индикатора RSI_MFI_ML
      if(TestRsiMfiML && 
         CopyBuffer(ML_Handle, 0, i, 1, ML_Buffer) > 0 && 
         CopyBuffer(ML_Handle, 1, i, 1, ML_Color) > 0 &&
         CopyBuffer(ML_Handle, 2, i, 1, ML_Signal) > 0)
      {
         // Анализ сигналов ML
         int ml_signal = (int)ML_Signal[0];
         
         // Записываем результат
         ML_Results[i] = ml_signal;
         
         // Подсчет сигналов
         if(ml_signal > 0) ml_buy_signals++;
         else if(ml_signal < 0) ml_sell_signals++;
         
         // Сохраняем текущее значение
         prev_ml_value = ML_Buffer[0];
      }
      
      // Комбинированный сигнал (оба индикатора)
      if(TestKernelRegression && TestRsiMfiML)
      {
         int combined_signal = 0;
         
         // Логика комбинированного сигнала - требуем согласия обоих индикаторов
         if(KR_Results[i] > 0 && ML_Results[i] > 0) 
         {
            combined_signal = 1;
            combined_buy_signals++;
         }
         else if(KR_Results[i] < 0 && ML_Results[i] < 0)
         {
            combined_signal = -1;
            combined_sell_signals++;
         }
         
         // Записываем результат
         Direction_Results[i] = combined_signal;
      }
   }
   
   // Выводим статистику сигналов
   Print("Статистика сигналов:");
   if(TestKernelRegression)
   {
      Print("Multi_Kernel_Regression - Покупка: ", kr_buy_signals, ", Продажа: ", kr_sell_signals);
   }
   
   if(TestRsiMfiML)
   {
      Print("RSI_MFI_ML - Покупка: ", ml_buy_signals, ", Продажа: ", ml_sell_signals);
   }
   
   if(TestKernelRegression && TestRsiMfiML)
   {
      Print("Комбинированные сигналы - Покупка: ", combined_buy_signals, ", Продажа: ", combined_sell_signals);
   }
}

//+------------------------------------------------------------------+
//| Функция для вывода результатов тестирования                      |
//+------------------------------------------------------------------+
void ShowResults()
{
   // Сигналы с наивысшей вероятностью успеха
   int best_signals = 0;
   int worst_signals = 0;
   
   // Буфер для текущих цен
   double close[];
   ArraySetAsSeries(close, true);
   CopyClose(Symbol(), PERIOD_CURRENT, 0, TestBars, close);
   
   // Анализ прибыльности сигналов
   double kr_profit = 0, ml_profit = 0, combined_profit = 0;
   
   // Симулируем простую стратегию - открываем позицию по сигналу и закрываем на следующий бар
   for(int i = TestBars - 2; i > 0; i--)
   {
      // Kernel Regression сигналы
      if(KR_Results[i+1] != 0)
      {
         double entry_price = close[i+1];
         double exit_price = close[i];
         double profit = KR_Results[i+1] * (exit_price - entry_price) / _Point;
         kr_profit += profit;
      }
      
      // ML сигналы
      if(ML_Results[i+1] != 0)
      {
         double entry_price = close[i+1];
         double exit_price = close[i];
         double profit = ML_Results[i+1] * (exit_price - entry_price) / _Point;
         ml_profit += profit;
      }
      
      // Комбинированные сигналы
      if(Direction_Results[i+1] != 0)
      {
         double entry_price = close[i+1];
         double exit_price = close[i];
         double profit = Direction_Results[i+1] * (exit_price - entry_price) / _Point;
         combined_profit += profit;
      }
   }
   
   // Выводим результаты прибыльности
   Print("Результаты прибыльности (в пунктах):");
   if(TestKernelRegression)
   {
      Print("Multi_Kernel_Regression: ", DoubleToString(kr_profit, 2));
   }
   
   if(TestRsiMfiML)
   {
      Print("RSI_MFI_ML: ", DoubleToString(ml_profit, 2));
   }
   
   if(TestKernelRegression && TestRsiMfiML)
   {
      Print("Комбинированная стратегия: ", DoubleToString(combined_profit, 2));
   }
   
   // Анализ корреляции индикаторов
   if(TestKernelRegression && TestRsiMfiML)
   {
      int matching_signals = 0;
      int opposite_signals = 0;
      
      for(int i = 0; i < TestBars; i++)
      {
         if(KR_Results[i] != 0 && ML_Results[i] != 0)
         {
            if(KR_Results[i] == ML_Results[i])
               matching_signals++;
            else
               opposite_signals++;
         }
      }
      
      double correlation = 0;
      if(matching_signals + opposite_signals > 0)
      {
         correlation = 100.0 * (double)matching_signals / (matching_signals + opposite_signals);
      }
      
      Print("Корреляция индикаторов: ", DoubleToString(correlation, 2), "% совпадающих сигналов");
   }
   
   // Рекомендации на основе тестирования
   Print("Рекомендации:");
   
   string recommendation = "";
   
   if(TestKernelRegression && TestRsiMfiML)
   {
      if(combined_profit > kr_profit && combined_profit > ml_profit)
      {
         recommendation = "Рекомендуется использовать комбинированную стратегию";
         Print(recommendation);
      }
      else if(kr_profit > ml_profit)
      {
         recommendation = "Рекомендуется использовать стратегию на основе Multi_Kernel_Regression";
         Print(recommendation);
      }
      else
      {
         recommendation = "Рекомендуется использовать стратегию на основе RSI_MFI_ML";
         Print(recommendation);
      }
   }
   else if(TestKernelRegression)
   {
      if(kr_profit > 0)
      {
         recommendation = "Рекомендуется использовать стратегию на основе Multi_Kernel_Regression";
         Print(recommendation);
      }
      else
      {
         recommendation = "Требуется оптимизация параметров Multi_Kernel_Regression";
         Print(recommendation);
      }
   }
   else if(TestRsiMfiML)
   {
      if(ml_profit > 0)
      {
         recommendation = "Рекомендуется использовать стратегию на основе RSI_MFI_ML";
         Print(recommendation);
      }
      else
      {
         recommendation = "Требуется оптимизация параметров RSI_MFI_ML";
         Print(recommendation);
      }
   }
   
   // Сохраняем отчет о тестировании
   SaveTestReport(kr_profit, ml_profit, combined_profit, recommendation);
}

//+------------------------------------------------------------------+
//| Функция для сохранения отчета о тестировании                     |
//+------------------------------------------------------------------+
void SaveTestReport(double kr_profit, double ml_profit, double combined_profit, string recommendation)
{
   // Инициализируем отчет
   if(!report.Initialize(Symbol(), Period()))
   {
      Print("Ошибка при инициализации отчета!");
      return;
   }
   
   // Добавляем сигналы и результаты в отчет
   for(int i = TestBars - 1; i >= 0; i--)
   {
      datetime time = iTime(Symbol(), Period(), i);
      
      // Добавляем KR сигналы
      if(TestKernelRegression && KR_Results[i] != 0)
      {
         double result = 0;
         if(i > 0) // Рассчитываем результат сигнала
         {
            double close[];
            ArraySetAsSeries(close, true);
            CopyClose(Symbol(), Period(), 0, TestBars, close);
            
            double entry_price = close[i];
            double exit_price = close[i-1];
            result = KR_Results[i] * (exit_price - entry_price) / _Point;
         }
         
         report.AddSignal(time, KR_Results[i], 1.0, 0.0, result);
      }
      
      // Добавляем ML сигналы
      if(TestRsiMfiML && ML_Results[i] != 0)
      {
         double result = 0;
         if(i > 0) // Рассчитываем результат сигнала
         {
            double close[];
            ArraySetAsSeries(close, true);
            CopyClose(Symbol(), Period(), 0, TestBars, close);
            
            double entry_price = close[i];
            double exit_price = close[i-1];
            result = ML_Results[i] * (exit_price - entry_price) / _Point;
         }
         
         report.AddSignal(time, ML_Results[i], 0.0, 1.0, result);
      }
      
      // Добавляем комбинированные сигналы
      if(TestKernelRegression && TestRsiMfiML && Direction_Results[i] != 0)
      {
         double result = 0;
         if(i > 0) // Рассчитываем результат сигнала
         {
            double close[];
            ArraySetAsSeries(close, true);
            CopyClose(Symbol(), Period(), 0, TestBars, close);
            
            double entry_price = close[i];
            double exit_price = close[i-1];
            result = Direction_Results[i] * (exit_price - entry_price) / _Point;
         }
         
         report.AddSignal(time, Direction_Results[i], 0.5, 0.5, result);
      }
   }
   
   // Генерируем итоговый отчет
   if(report.GenerateReport(true, true))
   {
      Print("Отчет о тестировании успешно сохранен!");
   }
   else
   {
      Print("Ошибка при сохранении отчета о тестировании!");
   }
}

//+------------------------------------------------------------------+ 