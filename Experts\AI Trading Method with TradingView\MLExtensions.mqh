//+------------------------------------------------------------------+
//|                                                MLExtensions.mqh |
//|                                                                  |
//|                                        Перенесено из PineScript  |
//+------------------------------------------------------------------+
#property copyright "jdehorty"
#property link      ""

//+------------------------------------------------------------------+
//| Набор функций для расчета нормализованных индикаторов            |
//+------------------------------------------------------------------+

// Нормализованный RSI
double n_rsi(double &price[], int length, int smooth_k)
{
   int bars = ArraySize(price);
   if(bars < length) return 0;
   
   double rsi[];
   ArrayResize(rsi, bars);
   
   for(int i = length; i < bars; i++)
   {
      double up = 0, down = 0;
      for(int j = 0; j < length; j++)
      {
         double diff = price[i-j] - price[i-j-1];
         if(diff > 0)
            up += diff;
         else
            down -= diff;
      }
      
      if(down == 0)
         rsi[i] = 100;
      else
      {
         double rs = up / down;
         rsi[i] = 100 - (100 / (1 + rs));
      }
   }
   
   // Нормализация между 0 и 1
   double result = (rsi[bars-1] - 0) / (100 - 0);
   
   // Дополнительное сглаживание, если указано
   if(smooth_k > 1)
   {
      double sum = 0;
      for(int i = 0; i < smooth_k; i++)
         sum += rsi[bars-1-i];
      result = (sum / smooth_k - 0) / (100 - 0);
   }
   
   return result;
}

// Нормализованный Wave Trend (WT)
double n_wt(double &price[], int channel_len, int avg_len)
{
   int bars = ArraySize(price);
   if(bars < MathMax(channel_len, avg_len)) return 0;
   
   double ema1[];
   double ema2[];
   double esa[];
   double d[];
   double ci[];
   double tci[];
   
   ArrayResize(ema1, bars);
   ArrayResize(ema2, bars);
   ArrayResize(esa, bars);
   ArrayResize(d, bars);
   ArrayResize(ci, bars);
   ArrayResize(tci, bars);
   
   // Расчет ESA = EMA(src, channel_len)
   for(int i = 0; i < bars; i++)
   {
      if(i < channel_len)
         esa[i] = price[i];
      else
      {
         double alpha = 2.0 / (channel_len + 1);
         esa[i] = alpha * price[i] + (1 - alpha) * esa[i-1];
      }
   }
   
   // Расчет D = EMA(abs(src - ESA), channel_len)
   for(int i = 0; i < bars; i++)
   {
      if(i < channel_len)
         d[i] = 0;
      else
      {
         double alpha = 2.0 / (channel_len + 1);
         d[i] = alpha * MathAbs(price[i] - esa[i]) + (1 - alpha) * d[i-1];
      }
   }
   
   // Расчет CI = (src - ESA) / (0.015 * D)
   for(int i = 0; i < bars; i++)
   {
      if(i < channel_len || d[i] == 0)
         ci[i] = 0;
      else
         ci[i] = (price[i] - esa[i]) / (0.015 * d[i]);
   }
   
   // Расчет TCI = EMA(CI, avg_len)
   for(int i = 0; i < bars; i++)
   {
      if(i < avg_len)
         tci[i] = ci[i];
      else
      {
         double alpha = 2.0 / (avg_len + 1);
         tci[i] = alpha * ci[i] + (1 - alpha) * tci[i-1];
      }
   }
   
   // Нормализация между 0 и 1 (диапазон WT обычно от -60 до 60)
   double min_range = -60;
   double max_range = 60;
   double result = (tci[bars-1] - min_range) / (max_range - min_range);
   
   // Обрезаем значения до диапазона [0,1]
   if(result > 1) result = 1;
   if(result < 0) result = 0;
   
   return result;
}

// Нормализованный CCI
double n_cci(double &price[], int length, int smooth_k)
{
   int bars = ArraySize(price);
   if(bars < length) return 0;
   
   double cci[];
   ArrayResize(cci, bars);
   
   for(int i = length; i < bars; i++)
   {
      double sum = 0;
      for(int j = 0; j < length; j++)
         sum += price[i-j];
      
      double sma = sum / length;
      
      double sum_dev = 0;
      for(int j = 0; j < length; j++)
         sum_dev += MathAbs(price[i-j] - sma);
      
      double md = sum_dev / length;
      
      cci[i] = (price[i] - sma) / (0.015 * md);
   }
   
   // Нормализация между 0 и 1 (диапазон CCI обычно от -300 до 300)
   double min_range = -300;
   double max_range = 300;
   double result = (cci[bars-1] - min_range) / (max_range - min_range);
   
   // Дополнительное сглаживание, если указано
   if(smooth_k > 1)
   {
      double sum = 0;
      for(int i = 0; i < smooth_k; i++)
         sum += cci[bars-1-i];
      result = (sum / smooth_k - min_range) / (max_range - min_range);
   }
   
   // Обрезаем значения до диапазона [0,1]
   if(result > 1) result = 1;
   if(result < 0) result = 0;
   
   return result;
}

// Нормализованный ADX
double n_adx(double &high[], double &low[], double &close[], int length)
{
   int bars = ArraySize(close);
   if(bars < length) return 0;
   
   double pdm[];
   double mdm[];
   double tr[];
   double plus_di[];
   double minus_di[];
   double dx[];
   double adx[];
   
   ArrayResize(pdm, bars);
   ArrayResize(mdm, bars);
   ArrayResize(tr, bars);
   ArrayResize(plus_di, bars);
   ArrayResize(minus_di, bars);
   ArrayResize(dx, bars);
   ArrayResize(adx, bars);
   
   // Расчет +DM, -DM и TR
   for(int i = 1; i < bars; i++)
   {
      double h_diff = high[i] - high[i-1];
      double l_diff = low[i-1] - low[i];
      
      pdm[i] = (h_diff > l_diff && h_diff > 0) ? h_diff : 0;
      mdm[i] = (l_diff > h_diff && l_diff > 0) ? l_diff : 0;
      
      double tr1 = MathAbs(high[i] - low[i]);
      double tr2 = MathAbs(high[i] - close[i-1]);
      double tr3 = MathAbs(low[i] - close[i-1]);
      tr[i] = MathMax(tr1, MathMax(tr2, tr3));
   }
   
   // Расчет сглаженных значений
   double smooth_pdm = 0, smooth_mdm = 0, smooth_tr = 0;
   
   for(int i = 1; i < length; i++)
   {
      smooth_pdm += pdm[i];
      smooth_mdm += mdm[i];
      smooth_tr += tr[i];
   }
   
   // Первое значение ATR для длины length
   smooth_pdm /= length;
   smooth_mdm /= length;
   smooth_tr /= length;
   
   // Расчет первых значений +DI и -DI
   plus_di[length] = 100 * smooth_pdm / smooth_tr;
   minus_di[length] = 100 * smooth_mdm / smooth_tr;
   
   // Расчет первого значения DX
   double dx_val = MathAbs(plus_di[length] - minus_di[length]) / (plus_di[length] + minus_di[length]) * 100;
   dx[length] = dx_val;
   
   // Продолжение расчета для остальных баров
   for(int i = length + 1; i < bars; i++)
   {
      smooth_pdm = smooth_pdm - (smooth_pdm / length) + pdm[i];
      smooth_mdm = smooth_mdm - (smooth_mdm / length) + mdm[i];
      smooth_tr = smooth_tr - (smooth_tr / length) + tr[i];
      
      plus_di[i] = 100 * smooth_pdm / smooth_tr;
      minus_di[i] = 100 * smooth_mdm / smooth_tr;
      
      dx_val = MathAbs(plus_di[i] - minus_di[i]) / (plus_di[i] + minus_di[i]) * 100;
      dx[i] = dx_val;
      
      // Расчет ADX
      if(i == length + 1)
         adx[i] = dx[i];
      else
         adx[i] = (adx[i-1] * (length - 1) + dx[i]) / length;
   }
   
   // Нормализация между 0 и 1 (диапазон ADX обычно от 0 до 100)
   double result = adx[bars-1] / 100.0;
   
   return result;
}

// Функция для определения цвета в зависимости от значения прогноза (зеленый)
color color_green(double value)
{
   // Ограничение значения в диапазоне [0, 1]
   value = MathMax(0, MathMin(1, value));
   
   // Градиент от серого (#787b86) до зеленого (#009988)
   int r = (int)(120 - value * 120);
   int g = (int)(123 + value * 30);
   int b = (int)(134 - value * 70);
   
   return (color)((r << 16) | (g << 8) | b);
}

// Функция для определения цвета в зависимости от значения прогноза (красный)
color color_red(double value)
{
   // Значение должно быть положительным для этой функции
   value = MathAbs(value);
   
   // Ограничение значения в диапазоне [0, 1]
   value = MathMax(0, MathMin(1, value));
   
   // Градиент от серого (#787b86) до красного (#CC3311)
   int r = (int)(120 + value * 84);
   int g = (int)(123 - value * 73);
   int b = (int)(134 - value * 103);
   
   return (color)((r << 16) | (g << 8) | b);
}

// Фильтры

// Фильтр волатильности
bool filter_volatility(int period_fast, int period_slow, bool use_filter)
{
   if(!use_filter) return true;
   
   int handle_fast = iATR(NULL, 0, period_fast);
   int handle_slow = iATR(NULL, 0, period_slow);
   
   if(handle_fast == INVALID_HANDLE || handle_slow == INVALID_HANDLE)
      return true;
      
   double fast_atr[1], slow_atr[1];
   if(CopyBuffer(handle_fast, 0, 0, 1, fast_atr) <= 0 || CopyBuffer(handle_slow, 0, 0, 1, slow_atr) <= 0)
      return true;
      
   IndicatorRelease(handle_fast);
   IndicatorRelease(handle_slow);
   
   return fast_atr[0] > slow_atr[0];
}

// Фильтр режима рынка
bool regime_filter(double price, double threshold, bool use_filter)
{
   if(!use_filter) return true;
   
   int handle_slow = iMA(NULL, 0, 50, 0, MODE_LWMA, PRICE_CLOSE);
   int handle_fast = iMA(NULL, 0, 20, 0, MODE_LWMA, PRICE_CLOSE);
   
   if(handle_slow == INVALID_HANDLE || handle_fast == INVALID_HANDLE)
      return true;
      
   double hma_slow[1], hma_fast[1];
   if(CopyBuffer(handle_slow, 0, 0, 1, hma_slow) <= 0 || CopyBuffer(handle_fast, 0, 0, 1, hma_fast) <= 0)
      return true;
      
   IndicatorRelease(handle_slow);
   IndicatorRelease(handle_fast);
   
   double regime_coef = (hma_fast[0] - hma_slow[0]) / hma_slow[0] * 100;
   
   return regime_coef > threshold;
}

// Фильтр ADX
bool filter_adx(double price, int adx_period, int adx_threshold, bool use_filter)
{
   if(!use_filter) return true;
   
   int handle_adx = iADX(NULL, 0, adx_period);
   
   if(handle_adx == INVALID_HANDLE)
      return true;
      
   double adx[1];
   if(CopyBuffer(handle_adx, 0, 0, 1, adx) <= 0)
      return true;
      
   IndicatorRelease(handle_adx);
   
   return adx[0] > adx_threshold;
}

// Функции для тестирования

// Инициализация таблицы
int init_table()
{
   return 0; // Заглушка для совместимости
}

// Функция для бэктестинга
double backtest(double &high[], double &low[], double &open[], 
               bool startLongTrade, bool endLongTrade, 
               bool startShortTrade, bool endShortTrade, 
               bool isEarlySignalFlip, int maxBarsBackIndex, 
               int bar_index, double price, bool useWorstCase)
{
   static int totalWins = 0;
   static int totalLosses = 0;
   static int totalEarlySignalFlips = 0;
   static int totalTrades = 0;
   static string tradeStatsHeader = "Trade Stats (approximated)";
   
   // Обработка сигналов
   if(startLongTrade) totalTrades++;
   if(startShortTrade) totalTrades++;
   if(endLongTrade) totalWins++;
   if(endShortTrade) totalWins++;
   if(isEarlySignalFlip) totalEarlySignalFlips++;
   
   totalLosses = totalTrades - totalWins;
   
   double winLossRatio = totalLosses > 0 ? (double)totalWins / totalLosses : 0;
   double winRate = totalTrades > 0 ? (double)totalWins / totalTrades : 0;
   
   double result[7];
   result[0] = totalWins;
   result[1] = totalLosses;
   result[2] = totalEarlySignalFlips;
   result[3] = totalTrades;
   result[4] = 0; // placeholder для tradeStatsHeader
   result[5] = winLossRatio;
   result[6] = winRate;
   
   return winRate; // Возвращаем хотя бы какое-то значение
}
//+------------------------------------------------------------------+ 