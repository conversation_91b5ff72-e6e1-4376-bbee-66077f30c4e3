//+------------------------------------------------------------------+
//| Тест улучшенных настроек для управления просадкой                |
//+------------------------------------------------------------------+
#property copyright "Test"
#property version   "1.00"
#property strict

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("=== ТЕСТ УЛУЧШЕННЫХ НАСТРОЕК ===");
   
   // Проверяем новые настройки просадки
   Print("Новые настройки просадки:");
   Print("- Максимальная просадка: 25% (было 15%)");
   Print("- Время в просадке: 72 часа (было 48)");
   Print("- Целевая прибыль: 2% (было 1%)");
   Print("- Минимальная прибыль для закрытия: 25 пунктов (было 15)");
   
   // Проверяем настройки слоев
   Print("\nНовые настройки слоев:");
   Print("Скальпинг:");
   Print("- TP: 15 пунктов (было 8)");
   Print("- SL: отключен (было 12)");
   Print("- Максимум позиций: 8 (было 5)");
   Print("- Шаг сетки: 15 (было 8)");
   
   Print("\nТренд:");
   Print("- TP: 50 пунктов (было 35)");
   Print("- SL: отключен (было 25)");
   Print("- Максимум позиций: 5 (было 3)");
   Print("- Шаг сетки: 25 (было 20)");
   
   Print("\nХеджирование:");
   Print("- TP: 30 пунктов (было 15)");
   Print("- SL: отключен (было 30)");
   Print("- Максимум позиций: 6 (было 4)");
   Print("- Множитель лота: 1.3 (было 1.5)");
   
   // Проверяем настройки трейлинга
   Print("\nНовые настройки трейлинга:");
   Print("- Защитный TP: 15 пунктов (было 7)");
   Print("- Трейлинг-стоп: 15 пунктов (было 7)");
   Print("- Трейлинг TP: 25 пунктов (было 15)");
   
   // Проверяем настройки рисков
   Print("\nНовые пороги рисков:");
   Print("- Низкий риск: 8% (было 5%)");
   Print("- Средний риск: 15% (было 10%)");
   Print("- Высокий риск: 25% (было 15%)");
   Print("- Экстремальный риск: 35% (было 20%)");
   
   Print("\n=== ОСНОВНЫЕ УЛУЧШЕНИЯ ===");
   Print("1. Отключены стоп-лоссы для сеточной торговли");
   Print("2. Увеличены тейк-профиты для лучшей прибыли");
   Print("3. Увеличено количество позиций в слоях");
   Print("4. Улучшена логика закрытия позиций");
   Print("5. Увеличены пороги просадки для стабильности");
   Print("6. Добавлена функция расчета прибыли слоя");
   
   Print("\n=== ТЕСТ ЗАВЕРШЕН ===");
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("Тест улучшенных настроек завершен");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Тестовая функция - ничего не делает
}
