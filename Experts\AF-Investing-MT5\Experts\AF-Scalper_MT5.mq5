//+------------------------------------------------------------------+
//|                                             AF-Scalper_MT5.mq5 |
//|                                Copyright 2023, AFSID-Group.Cv     |
//|                                       https://afs-id.com          |
//+------------------------------------------------------------------+
#property copyright "Copyright 2023, AFSID-Group.Cv"
#property link      "https://afs-id.com"
#property version   "1.00"
#property description "AF-Scalper - стратегия из AF-Investing с индикаторами CVN Signal и XARD FX"

// Подключение необходимых библиотек
#include <Trade\Trade.mqh>
#include <Trade\SymbolInfo.mqh>
#include <Trade\AccountInfo.mqh>

// Определение основных констант
#define EXPERT_MAGIC 22324  // Magic для AF-Scalper

// Структура для сигналов
struct SAFScalperSignals
{
   bool buySignal;      // Сигнал на покупку
   bool sellSignal;     // Сигнал на продажу
   bool closeBuySignal; // Сигнал на закрытие покупки
   bool closeSellSignal;// Сигнал на закрытие продажи
};

// Параметры стратегии
input string GeneralSettings = "==== Общие настройки ====";
input string EAName = "AF-Scalper MT5";
input bool LabelInfo = true;               // Отображать информацию на графике
input int CustomMagic = 0;                 // Пользовательский Magic Number (0 - использовать по умолчанию)

// Параметры управления рисками
input string RiskSettings = "==== Управление рисками ====";
input double Lots = 0.01;                  // Фиксированный размер лота
input bool AutoLotSize = true;             // Автоматический расчет размера лота
input double RiskInPercent = 2.0;          // Риск от депозита (%)
input int lotdecimal = 2;                  // Десятичные знаки в размере лота
input double MaxLots = 10.0;               // Максимальный размер лота
input double SlipPage = 5.0;               // Проскальзывание

// Параметры управления ордерами
input string OrderSettings = "==== Управление ордерами ====";
input int MaxTrades = 10;                  // Максимальное количество сделок
input double TakeProfit = 50.0;            // Тейк-профит (в пунктах)
input double PipStep = 150.0;              // Шаг для мартингейла (в пунктах)
input double LotExponent = 1.2;            // Множитель лота для мартингейла

// Параметры трейлинг-стопа
input string TrailingSettings = "==== Трейлинг-стоп ====";
input bool UseTrailingStop = true;         // Использовать трейлинг-стоп
input double TrailStart = 20.0;            // Начало трейлинга (в пунктах)
input double TrailStop = 10.0;             // Шаг трейлинга (в пунктах)

// Параметры контроля убытков
input string EquitySettings = "==== Контроль убытков ====";
input bool UseEquityStop = true;           // Использовать остановку по эквити
input double TotalEquityRisk = 10.0;       // Риск эквити (%)

// Параметры индикаторов CVN Signal
input string CVNSettings = "==== Настройки CVN Signal ====";
input int CVN_BandsPeriod = 20;            // Период Bollinger Bands
input double CVN_BandsDeviation = 2.0;     // Отклонение Bollinger Bands
input int CVN_RSIPeriod = 14;              // Период RSI
input int CVN_RSIBuyLevel = 30;            // Уровень перепроданности RSI
input int CVN_RSISellLevel = 70;           // Уровень перекупленности RSI
input int CVN_StochKPeriod = 8;            // K период стохастика
input int CVN_StochDPeriod = 3;            // D период стохастика
input int CVN_StochSlowing = 3;            // Замедление стохастика

// Параметры индикатора XU XARDFX
input string XARDFXSettings = "==== Настройки XU XARDFX ====";
input int XARDFX_RSIPeriod = 14;           // Период RSI
input int XARDFX_MAPeriod = 100;           // Период MA
input ENUM_MA_METHOD XARDFX_MAMethod = MODE_EMA; // Метод MA

// Экземпляры классов
CTrade Trade;
CSymbolInfo SymbolInfo;
CAccountInfo AccountInfo;

// Глобальные переменные
int cvn_signal_handle = INVALID_HANDLE;   // Хэндл индикатора CVN Signal
int xardfx_handle = INVALID_HANDLE;       // Хэндл индикатора XU XARDFX
datetime LastTradeTime = 0;               // Время последней сделки
bool IsNews = false;                      // Флаг новостей
double initialBalance = 0;                // Начальный баланс
double maxEquity = 0;                     // Максимальный эквити
double maxDrawdown = 0;                   // Максимальная просадка
int wins = 0, losses = 0;                 // Статистика сделок
double openLots = 0;                      // Открытый объем

// Имена объектов на графике
string LabelName = "AF-Scalper_Info";

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // Инициализация символа
   if(!SymbolInfo.Name(_Symbol))
   {
      Print("Ошибка инициализации символа");
      return(INIT_FAILED);
   }
   
   // Установка Magic Number
   int magic = (CustomMagic > 0) ? CustomMagic : EXPERT_MAGIC;
   Trade.SetExpertMagicNumber(magic);
   
   // Инициализация индикаторов
   cvn_signal_handle = iCustom(_Symbol, PERIOD_CURRENT, "CVN_Signal_MT5",
                             CVN_BandsPeriod, CVN_BandsDeviation, 
                             CVN_RSIPeriod, CVN_RSIBuyLevel, CVN_RSISellLevel,
                             CVN_StochKPeriod, CVN_StochDPeriod, CVN_StochSlowing);
                            
   xardfx_handle = iCustom(_Symbol, PERIOD_CURRENT, "XU_XARDFX_MT5", 
                          XARDFX_RSIPeriod, XARDFX_RSIPeriod/2, XARDFX_MAPeriod, XARDFX_MAMethod);
   
   if(cvn_signal_handle == INVALID_HANDLE || xardfx_handle == INVALID_HANDLE)
   {
      Print("Ошибка загрузки индикаторов: CVN=", cvn_signal_handle, ", XU_XARDFX=", xardfx_handle);
      return(INIT_FAILED);
   }
   
   // Инициализация графической панели
   if(LabelInfo)
   {
      CreateInfoPanel();
   }
   
   // Инициализация статистики
   initialBalance = AccountInfo.Balance();
   maxEquity = AccountInfo.Equity();
   
   Print("AF-Scalper MT5 инициализирован успешно");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Освобождение хэндлов индикаторов
   if(cvn_signal_handle != INVALID_HANDLE)
      IndicatorRelease(cvn_signal_handle);
   if(xardfx_handle != INVALID_HANDLE)
      IndicatorRelease(xardfx_handle);
   
   // Удаление графических объектов
   if(LabelInfo)
   {
      ObjectDelete(0, LabelName);
   }
   
   Print("AF-Scalper MT5 выгружен, причина: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Обновляем информацию о символе
   SymbolInfo.Refresh();
   SymbolInfo.RefreshRates();
   
   // Обновляем статистику и информационную панель
   UpdateStats();
   if(LabelInfo)
   {
      UpdateInfoPanel();
   }
   
   // Проверка возможности торговли
   if(!CanTrade())
   {
      Print("Торговля невозможна в данный момент");
      return;
   }
   
   // Управление открытыми позициями
   if(UseTrailingStop)
   {
      TrailingPositions();
   }
   
   // Проверка эквити-стопа
   if(UseEquityStop && CheckEquityStop())
   {
      Print("Сработал эквити-стоп, новые позиции не открываются");
      return;
   }
   
   // Проверка сигналов и открытие новых позиций
   SAFScalperSignals signals = GetSignals();
   
   // Закрытие позиций по сигналам
   if(signals.closeBuySignal || signals.closeSellSignal)
   {
      ClosePositionsBySignal(signals.closeBuySignal, signals.closeSellSignal);
   }
   
   // Открытие новых позиций
   int total = CountPositions();
   if(total < MaxTrades)
   {
      if(signals.buySignal && CanOpenBuy())
      {
         OpenPosition(ORDER_TYPE_BUY);
      }
      
      if(signals.sellSignal && CanOpenSell())
      {
         OpenPosition(ORDER_TYPE_SELL);
      }
   }
}

//+------------------------------------------------------------------+
//| Проверка возможности торговли                                    |
//+------------------------------------------------------------------+
bool CanTrade()
{
   // Проверка доступности рынка
   if(!MQLInfoInteger(MQL_TRADE_ALLOWED))
   {
      Print("Торговля запрещена. Включите в настройках терминала.");
      return false;
   }
   
   // Проверка наличия лицензии и т.д.
   
   return true;
}

//+------------------------------------------------------------------+
//| Получение сигналов от индикаторов                                |
//+------------------------------------------------------------------+
SAFScalperSignals GetSignals()
{
   SAFScalperSignals signals = {0};
   
   // Получаем данные CVN Signal
   double cvnBuy[], cvnSell[], cvnExitBuy[], cvnExitSell[];
   ArraySetAsSeries(cvnBuy, true);
   ArraySetAsSeries(cvnSell, true);
   ArraySetAsSeries(cvnExitBuy, true);
   ArraySetAsSeries(cvnExitSell, true);
   
   // Получаем данные XU XARDFX
   double xardfxBuy[], xardfxSell[], xardfxExitBuy[], xardfxExitSell[];
   ArraySetAsSeries(xardfxBuy, true);
   ArraySetAsSeries(xardfxSell, true);
   ArraySetAsSeries(xardfxExitBuy, true);
   ArraySetAsSeries(xardfxExitSell, true);
   
   // Копируем данные из индикаторов
   if(CopyBuffer(cvn_signal_handle, 0, 0, 3, cvnBuy) <= 0 || 
      CopyBuffer(cvn_signal_handle, 1, 0, 3, cvnSell) <= 0 ||
      CopyBuffer(cvn_signal_handle, 2, 0, 3, cvnExitBuy) <= 0 ||
      CopyBuffer(cvn_signal_handle, 3, 0, 3, cvnExitSell) <= 0)
   {
      Print("Ошибка получения данных индикатора CVN Signal");
      return signals;
   }
   
   if(CopyBuffer(xardfx_handle, 0, 0, 3, xardfxBuy) <= 0 || 
      CopyBuffer(xardfx_handle, 1, 0, 3, xardfxSell) <= 0 ||
      CopyBuffer(xardfx_handle, 2, 0, 3, xardfxExitBuy) <= 0 ||
      CopyBuffer(xardfx_handle, 3, 0, 3, xardfxExitSell) <= 0)
   {
      Print("Ошибка получения данных индикатора XU XARDFX");
      return signals;
   }
   
   // Проверяем сигналы
   // Сигнал на покупку: оба индикатора показывают сигнал на покупку
   if(cvnBuy[1] != EMPTY_VALUE && xardfxBuy[1] != EMPTY_VALUE)
   {
      signals.buySignal = true;
   }
   
   // Сигнал на продажу: оба индикатора показывают сигнал на продажу
   if(cvnSell[1] != EMPTY_VALUE && xardfxSell[1] != EMPTY_VALUE)
   {
      signals.sellSignal = true;
   }
   
   // Сигналы на закрытие позиций: любой из индикаторов показывает выход
   if(cvnExitBuy[1] != EMPTY_VALUE || xardfxExitBuy[1] != EMPTY_VALUE)
   {
      signals.closeBuySignal = true;
   }
   
   if(cvnExitSell[1] != EMPTY_VALUE || xardfxExitSell[1] != EMPTY_VALUE)
   {
      signals.closeSellSignal = true;
   }
   
   return signals;
}

//+------------------------------------------------------------------+
//| Создание информационной панели                                   |
//+------------------------------------------------------------------+
void CreateInfoPanel()
{
   if(ObjectFind(0, LabelName) >= 0)
      ObjectDelete(0, LabelName);
      
   ObjectCreate(0, LabelName, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(0, LabelName, OBJPROP_CORNER, CORNER_RIGHT_UPPER);
   ObjectSetInteger(0, LabelName, OBJPROP_XDISTANCE, 10);
   ObjectSetInteger(0, LabelName, OBJPROP_YDISTANCE, 15);
   ObjectSetInteger(0, LabelName, OBJPROP_COLOR, clrWhite);
   ObjectSetInteger(0, LabelName, OBJPROP_FONTSIZE, 9);
   ObjectSetInteger(0, LabelName, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, LabelName, OBJPROP_BACK, false);
}

//+------------------------------------------------------------------+
//| Обновление информационной панели                                 |
//+------------------------------------------------------------------+
void UpdateInfoPanel()
{
   string text = "=== AF-Scalper MT5 ===\n";
   text += "Баланс: " + DoubleToString(AccountInfo.Balance(), 2) + "\n";
   text += "Эквити: " + DoubleToString(AccountInfo.Equity(), 2) + "\n";
   text += "Просадка: " + DoubleToString(maxDrawdown, 2) + "%\n";
   text += "Открытые позиции: " + IntegerToString(CountPositions()) + "\n";
   text += "Общий объем: " + DoubleToString(openLots, 2) + "\n";
   text += "Выигрыши/Проигрыши: " + IntegerToString(wins) + "/" + IntegerToString(losses);
   
   ObjectSetString(0, LabelName, OBJPROP_TEXT, text);
}

//+------------------------------------------------------------------+
//| Обновление статистики торговли                                   |
//+------------------------------------------------------------------+
void UpdateStats()
{
   double equity = AccountInfo.Equity();
   
   // Обновление максимального эквити
   if(equity > maxEquity)
      maxEquity = equity;
      
   // Расчет текущей просадки
   double currentDrawdown = 0;
   if(maxEquity > 0)
      currentDrawdown = (maxEquity - equity) / maxEquity * 100.0;
      
   // Обновление максимальной просадки
   if(currentDrawdown > maxDrawdown)
      maxDrawdown = currentDrawdown;
      
   // Подсчет открытого объема
   openLots = 0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == Trade.RequestMagic())
      {
         openLots += PositionGetDouble(POSITION_VOLUME);
      }
   }
}

//+------------------------------------------------------------------+
//| Проверка эквити-стопа                                            |
//+------------------------------------------------------------------+
bool CheckEquityStop()
{
   if(!UseEquityStop)
      return false;
      
   double equity = AccountInfo.Equity();
   
   // Проверка просадки
   if(maxDrawdown >= TotalEquityRisk)
   {
      return true;
   }
   
   return false;
}

//+------------------------------------------------------------------+
//| Подсчет количества открытых позиций                              |
//+------------------------------------------------------------------+
int CountPositions()
{
   int count = 0;
   
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == Trade.RequestMagic())
      {
         count++;
      }
   }
   
   return count;
}

//+------------------------------------------------------------------+
//| Проверка возможности открытия покупки                            |
//+------------------------------------------------------------------+
bool CanOpenBuy()
{
   // Проверка времени последней сделки
   if(TimeCurrent() - LastTradeTime < 60)
      return false;
      
   // Проверка новостей
   if(IsNews)
      return false;
      
   // Проверка максимального количества сделок
   if(CountPositions() >= MaxTrades)
      return false;
      
   // Дополнительные проверки...
   
   return true;
}

//+------------------------------------------------------------------+
//| Проверка возможности открытия продажи                            |
//+------------------------------------------------------------------+
bool CanOpenSell()
{
   // Проверка времени последней сделки
   if(TimeCurrent() - LastTradeTime < 60)
      return false;
      
   // Проверка новостей
   if(IsNews)
      return false;
      
   // Проверка максимального количества сделок
   if(CountPositions() >= MaxTrades)
      return false;
      
   // Дополнительные проверки...
   
   return true;
}

//+------------------------------------------------------------------+
//| Открытие позиции                                                 |
//+------------------------------------------------------------------+
void OpenPosition(ENUM_ORDER_TYPE orderType)
{
   double volume = CalculateLotSize();
   double price = 0;
   double sl = 0;
   double tp = 0;
   
   if(orderType == ORDER_TYPE_BUY)
   {
      price = SymbolInfo.Ask();
      sl = price - PipStep * _Point * 10;
      tp = price + TakeProfit * _Point * 10;
      
      if(Trade.Buy(volume, _Symbol, price, sl, tp, "AF-Scalper Buy"))
      {
         Print("Buy order opened: ", Trade.ResultOrder(), ", Volume: ", volume, ", Price: ", price);
         LastTradeTime = TimeCurrent();
      }
      else
      {
         Print("Error opening Buy order: ", Trade.ResultRetcode(), ", ", Trade.ResultRetcodeDescription());
      }
   }
   else if(orderType == ORDER_TYPE_SELL)
   {
      price = SymbolInfo.Bid();
      sl = price + PipStep * _Point * 10;
      tp = price - TakeProfit * _Point * 10;
      
      if(Trade.Sell(volume, _Symbol, price, sl, tp, "AF-Scalper Sell"))
      {
         Print("Sell order opened: ", Trade.ResultOrder(), ", Volume: ", volume, ", Price: ", price);
         LastTradeTime = TimeCurrent();
      }
      else
      {
         Print("Error opening Sell order: ", Trade.ResultRetcode(), ", ", Trade.ResultRetcodeDescription());
      }
   }
}

//+------------------------------------------------------------------+
//| Расчет размера лота                                             |
//+------------------------------------------------------------------+
double CalculateLotSize()
{
   double lotSize = Lots;
   
   // Расчет размера лота в зависимости от мартингейла
   int positions = CountPositions();
   if(positions > 0)
   {
      lotSize = NormalizeDouble(Lots * MathPow(LotExponent, positions), lotdecimal);
   }
   
   // Автоматический расчет размера лота по риску
   if(AutoLotSize)
   {
      double balance = AccountInfo.Balance();
      double riskAmount = balance * RiskInPercent / 100.0;
      double pipValue = SymbolInfo.TickValue() * 10;
      double pipRisk = PipStep; // Риск в пунктах
      
      if(pipValue > 0 && pipRisk > 0)
      {
         lotSize = NormalizeDouble(riskAmount / (pipValue * pipRisk), lotdecimal);
      }
   }
   
   // Ограничения размера лота
   double minLot = SymbolInfo.LotsMin();
   double maxLot = MathMin(SymbolInfo.LotsMax(), MaxLots);
   double stepLot = SymbolInfo.LotsStep();
   
   lotSize = MathMax(minLot, lotSize);
   lotSize = MathMin(maxLot, lotSize);
   lotSize = NormalizeDouble(lotSize, lotdecimal);
   
   return lotSize;
}

//+------------------------------------------------------------------+
//| Закрытие позиций по сигналу                                      |
//+------------------------------------------------------------------+
void ClosePositionsBySignal(bool closeBuy, bool closeSell)
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == Trade.RequestMagic())
      {
         ulong ticket = PositionGetTicket(i);
         if(PositionSelectByTicket(ticket))
         {
            ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            
            if((posType == POSITION_TYPE_BUY && closeBuy) || (posType == POSITION_TYPE_SELL && closeSell))
            {
               Trade.PositionClose(ticket);
               Print("Позиция закрыта по сигналу: ", ticket);
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Применение трейлинг-стопа к позициям                             |
//+------------------------------------------------------------------+
void TrailingPositions()
{
   if(!UseTrailingStop)
      return;
      
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == Trade.RequestMagic())
      {
         ulong ticket = PositionGetTicket(i);
         if(PositionSelectByTicket(ticket))
         {
            double currentSL = PositionGetDouble(POSITION_SL);
            double currentTP = PositionGetDouble(POSITION_TP);
            double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
            double currentPrice = PositionGetDouble(POSITION_PRICE_CURRENT);
            ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            
            double newSL = 0;
            bool modifySL = false;
            
            if(posType == POSITION_TYPE_BUY)
            {
               // Для длинных позиций
               double profit = currentPrice - openPrice;
               if(profit >= TrailStart * _Point * 10)
               {
                  newSL = currentPrice - TrailStop * _Point * 10;
                  if(newSL > currentSL || currentSL == 0)
                  {
                     modifySL = true;
                  }
               }
            }
            else if(posType == POSITION_TYPE_SELL)
            {
               // Для коротких позиций
               double profit = openPrice - currentPrice;
               if(profit >= TrailStart * _Point * 10)
               {
                  newSL = currentPrice + TrailStop * _Point * 10;
                  if(newSL < currentSL || currentSL == 0)
                  {
                     modifySL = true;
                  }
               }
            }
            
            if(modifySL)
            {
               if(Trade.PositionModify(ticket, newSL, currentTP))
               {
                  Print("Трейлинг-стоп применен для позиции #", ticket, ": новый SL = ", newSL);
               }
            }
         }
      }
   }
} 