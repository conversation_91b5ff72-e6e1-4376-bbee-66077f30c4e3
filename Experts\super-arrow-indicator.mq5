//+------------------------------------------------------------------+
//| Super Arrow Indicator                                             |
//| Copyright © 2009-2023, TradingSystemForex.Com                    |
//+------------------------------------------------------------------+

#property copyright "Copyright © 2009-2023, TradingSystemForex.Com"
#property link      "http://www.tradingsystemforex.com/"
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 2
#property indicator_plots   2
#property indicator_type1   DRAW_ARROW
#property indicator_type2   DRAW_ARROW
#property indicator_color1  clrLime      // Более яркий зеленый цвет
#property indicator_color2  clrRed       // Красный цвет
#property indicator_width1  3            // Увеличенная толщина стрелок
#property indicator_width2  3            // Увеличенная толщина стрелок
#property indicator_label1  "Buy Signal"  // Метка для сигналов покупки
#property indicator_label2  "Sell Signal" // Метка для сигналов продажи

// Входные параметры
input ENUM_TIMEFRAMES TimeFrame = PERIOD_CURRENT;  // Таймфрейм
input int FasterMovingAverage = 5;            // Период быстрой скользящей средней
input int SlowerMovingAverage = 12;           // Период медленной скользящей средней
input int RSIPeriod = 12;                     // Период RSI
input int MagicFilterPeriod = 1;              // Период Magic Filter
input int BollingerbandsPeriod = 10;          // Период Bollinger Bands
input int BollingerbandsShift = 0;            // Сдвиг Bollinger Bands
input double BollingerbandsDeviation = 0.5;   // Отклонение Bollinger Bands
input int BullsPowerPeriod = 50;              // Период Bulls Power
input int BearsPowerPeriod = 50;              // Период Bears Power
input bool Alerts = true;                     // Включить оповещения
input int Utstup = 10;                        // Отступ для стрелок

// Буферы индикатора
double BuyBuffer[];
double SellBuffer[];

// Глобальные переменные
int last_signal = 0;
bool last_buy_alert = false;
bool last_sell_alert = false;
datetime last_alert_time = 0;

//+------------------------------------------------------------------+
//| Инициализация индикатора                                         |
//+------------------------------------------------------------------+
int OnInit()
{
   // Установка буферов
   SetIndexBuffer(0, BuyBuffer, INDICATOR_DATA);
   SetIndexBuffer(1, SellBuffer, INDICATOR_DATA);
   
   // Установка стиля стрелок
   PlotIndexSetInteger(0, PLOT_ARROW, 233);
   PlotIndexSetInteger(1, PLOT_ARROW, 234);
   
   // Установка смещения стрелок
   PlotIndexSetInteger(0, PLOT_ARROW_SHIFT, 5);  // Небольшое смещение для лучшей видимости
   PlotIndexSetInteger(1, PLOT_ARROW_SHIFT, 5);
   
   // Инициализация буферов значениями EMPTY_VALUE
   ArrayInitialize(BuyBuffer, EMPTY_VALUE);
   ArrayInitialize(SellBuffer, EMPTY_VALUE);
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Деинициализация индикатора                                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // При деинициализации ничего не делаем
}

//+------------------------------------------------------------------+
//| Расчет индикатора                                                |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   if(rates_total < 3) return(0);
   
   // Определяем используемый таймфрейм
   ENUM_TIMEFRAMES tf = (TimeFrame == PERIOD_CURRENT) ? Period() : TimeFrame;
   
   int limit;
   
   // Определяем начальную позицию для расчета
   if(prev_calculated == 0)
   {
      // Инициализируем буферы
      ArrayInitialize(BuyBuffer, EMPTY_VALUE);
      ArrayInitialize(SellBuffer, EMPTY_VALUE);
      limit = rates_total - 2;
   }
   else
   {
      limit = rates_total - prev_calculated;
      if(limit > 1) limit = 2; // Пересчитываем несколько баров для обновления сигналов
   }
   
   // Получение данных индикаторов
   double ma_fast[], ma_slow[], rsi[], bulls[], bears[], bb_upper[], bb_lower[], bb_middle[];
   double price_high[], price_low[];
   
   // Установка массивов как таймсерии
   ArraySetAsSeries(ma_fast, true);
   ArraySetAsSeries(ma_slow, true);
   ArraySetAsSeries(rsi, true);
   ArraySetAsSeries(bulls, true);
   ArraySetAsSeries(bears, true);
   ArraySetAsSeries(bb_upper, true);
   ArraySetAsSeries(bb_lower, true);
   ArraySetAsSeries(bb_middle, true);
   ArraySetAsSeries(price_high, true);
   ArraySetAsSeries(price_low, true);
   ArraySetAsSeries(close, true);
   ArraySetAsSeries(high, true);
   ArraySetAsSeries(low, true);
   ArraySetAsSeries(time, true);
   
   // Получение хендлов индикаторов
   int ma_fast_handle = iMA(_Symbol, tf, FasterMovingAverage, 0, MODE_EMA, PRICE_CLOSE);
   int ma_slow_handle = iMA(_Symbol, tf, SlowerMovingAverage, 0, MODE_EMA, PRICE_CLOSE);
   int rsi_handle = iRSI(_Symbol, tf, RSIPeriod, PRICE_CLOSE);
   int bulls_handle = iBullsPower(_Symbol, tf, BullsPowerPeriod);
   int bears_handle = iBearsPower(_Symbol, tf, BearsPowerPeriod);
   int bb_handle = iBands(_Symbol, tf, BollingerbandsPeriod, 
                         BollingerbandsDeviation, BollingerbandsShift, PRICE_CLOSE);
   
   // Копирование данных цен для Magic Filter
   CopyHigh(_Symbol, tf, 0, rates_total, price_high);
   CopyLow(_Symbol, tf, 0, rates_total, price_low);
   
   // Копирование данных индикаторов
   if(CopyBuffer(ma_fast_handle, 0, 0, rates_total, ma_fast) <= 0) return(0);
   if(CopyBuffer(ma_slow_handle, 0, 0, rates_total, ma_slow) <= 0) return(0);
   if(CopyBuffer(rsi_handle, 0, 0, rates_total, rsi) <= 0) return(0);
   if(CopyBuffer(bulls_handle, 0, 0, rates_total, bulls) <= 0) return(0);
   if(CopyBuffer(bears_handle, 0, 0, rates_total, bears) <= 0) return(0);
   if(CopyBuffer(bb_handle, 0, 0, rates_total, bb_middle) <= 0) return(0);
   if(CopyBuffer(bb_handle, 1, 0, rates_total, bb_upper) <= 0) return(0);
   if(CopyBuffer(bb_handle, 2, 0, rates_total, bb_lower) <= 0) return(0);
   
   // Расчет сигналов
   for(int i = limit; i >= 0; i--)
   {
      BuyBuffer[i] = EMPTY_VALUE;
      SellBuffer[i] = EMPTY_VALUE;
      
      // Расчет Magic Filter
      double highest = price_high[ArrayMaximum(price_high, i, MagicFilterPeriod)];
      double lowest = price_low[ArrayMinimum(price_low, i, MagicFilterPeriod)];
      double magic_diff = highest - lowest;
      
      // Проверка условий для сигналов
      bool ma_cross_up = (ma_fast[i] > ma_slow[i] && ma_fast[i+1] <= ma_slow[i+1]);
      bool ma_cross_down = (ma_fast[i] < ma_slow[i] && ma_fast[i+1] >= ma_slow[i+1]);
      
      bool rsi_up = (rsi[i] > 50.0);
      bool rsi_down = (rsi[i] < 50.0);
      
      bool bulls_up = (bulls[i] > 0.0);
      bool bears_down = (bears[i] < 0.0);
      
      bool price_above_bb = (close[i] > bb_upper[i]);
      bool price_below_bb = (close[i] < bb_lower[i]);
      
      // Формирование сигналов
      if(ma_cross_up && rsi_up && price_below_bb && bulls_up && magic_diff > 0.0)
      {
         BuyBuffer[i] = low[i] - (_Point * Utstup);
         if(i == 0 && Alerts && (last_signal != 1 || time[0] != last_alert_time))
         {
            string alert_msg = _Symbol + " " + EnumToString(tf) + " BUY Signal";
            Alert(alert_msg);
            last_buy_alert = true;
            last_sell_alert = false;
            last_signal = 1;
            last_alert_time = time[0];
         }
      }
      
      if(ma_cross_down && rsi_down && price_above_bb && bears_down && magic_diff > 0.0)
      {
         SellBuffer[i] = high[i] + (_Point * Utstup);
         if(i == 0 && Alerts && (last_signal != 2 || time[0] != last_alert_time))
         {
            string alert_msg = _Symbol + " " + EnumToString(tf) + " SELL Signal";
            Alert(alert_msg);
            last_sell_alert = true;
            last_buy_alert = false;
            last_signal = 2;
            last_alert_time = time[0];
         }
      }
   }
   
   // Освобождение ресурсов
   IndicatorRelease(ma_fast_handle);
   IndicatorRelease(ma_slow_handle);
   IndicatorRelease(rsi_handle);
   IndicatorRelease(bulls_handle);
   IndicatorRelease(bears_handle);
   IndicatorRelease(bb_handle);
   
   return(rates_total);
} 