//+------------------------------------------------------------------+
//|                                              KernelFunctions.mqh |
//|                                                                  |
//|                                        Перенесено из PineScript  |
//+------------------------------------------------------------------+
#property copyright "jdehorty"
#property link      ""

//+------------------------------------------------------------------+
//| Набор функций для реализации Kernel Regression                    |
//+------------------------------------------------------------------+

// Рациональное квадратичное ядро
double rationalQuadratic(double &data[], int h, double r, int x)
{
   int bars = ArraySize(data);
   if(bars < h) return 0;
   
   double y_hat = 0;
   double weights_sum = 0;
   
   // Индекс текущего бара, для которого делаем прогноз
   int t = bars - 1;
   
   // Расчет взвешенной суммы
   for(int i = t-h+1; i <= t; i++)
   {
      if(i < 0) continue;
      
      // Расчет расстояния
      double distance = MathPow(((double)(i - t) / x), 2);
      
      // Расчет веса по формуле ядра
      double weight = MathPow(1 + distance/(2*r), -r);
      
      // Обновление взвешенной суммы
      y_hat += weight * data[i];
      weights_sum += weight;
   }
   
   // Нормализация по сумме весов
   if(weights_sum != 0)
      y_hat /= weights_sum;
   else
      y_hat = data[t]; // Если веса равны нулю, берем текущее значение
   
   return y_hat;
}

// Ядро Гаусса
double gaussian(double &data[], int h, int x)
{
   int bars = ArraySize(data);
   if(bars < h) return 0;
   
   double y_hat = 0;
   double weights_sum = 0;
   
   // Индекс текущего бара, для которого делаем прогноз
   int t = bars - 1;
   
   // Расчет взвешенной суммы
   for(int i = t-h+1; i <= t; i++)
   {
      if(i < 0) continue;
      
      // Расчет расстояния
      double distance = MathPow(((double)(i - t) / x), 2);
      
      // Расчет веса по формуле ядра Гаусса
      double weight = MathExp(-distance);
      
      // Обновление взвешенной суммы
      y_hat += weight * data[i];
      weights_sum += weight;
   }
   
   // Нормализация по сумме весов
   if(weights_sum != 0)
      y_hat /= weights_sum;
   else
      y_hat = data[t]; // Если веса равны нулю, берем текущее значение
   
   return y_hat;
}
//+------------------------------------------------------------------+ 