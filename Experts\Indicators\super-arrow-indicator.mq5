//+------------------------------------------------------------------+
//|                                         super-arrow-indicator.mq5 |
//|                      Copyright © 2023, TradingSystemForex.Com    |
//|                             http://www.tradingsystemforex.com/   |
//+------------------------------------------------------------------+
#property copyright "Copyright © 2023, TradingSystemForex.Com"
#property link      "http://www.tradingsystemforex.com/"
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 2
#property indicator_plots   2

// Настройки отображения индикатора для буфера покупок (UP Arrow)
#property indicator_label1  "Buy Signal"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrLime
#property indicator_style1  STYLE_SOLID
#property indicator_width1  2

// Настройки отображения индикатора для буфера продаж (DOWN Arrow)
#property indicator_label2  "Sell Signal"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrRed
#property indicator_style2  STYLE_SOLID
#property indicator_width2  2

// Входные параметры индикатора
input ENUM_TIMEFRAMES TimeFrame = PERIOD_CURRENT;    // Таймфрейм
input int FasterMovingAverage = 5;                   // Период быстрой скользящей средней
input int SlowerMovingAverage = 12;                  // Период медленной скользящей средней
input int RSIPeriod = 12;                            // Период RSI
input int MagicFilterPeriod = 1;                     // Период Magic Filter
input int BollingerbandsPeriod = 10;                 // Период Bollinger Bands
input int BollingerbandsShift = 0;                   // Сдвиг Bollinger Bands
input double BollingerbandsDeviation = 0.5;          // Отклонение Bollinger Bands
input int BullsPowerPeriod = 50;                     // Период Bulls Power
input int BearsPowerPeriod = 50;                     // Период Bears Power
input int ArrowUtstup = 10;                         // Отступ для стрелок
input bool EmailAlert = true;                       // Отправлять Email уведомления
input bool PushAlert = false;                       // Отправлять Push уведомления
input bool SoundAlert = false;                      // Включить звуковые уведомления
input string SoundFile = "alert.wav";              // Звуковой файл для уведомлений

// Глобальные переменные и буферы
double UpArrowBuffer[];    // Буфер для сигналов покупки
double DownArrowBuffer[];  // Буфер для сигналов продажи

// Хендлы индикаторов
int ma_fast_handle;
int ma_slow_handle;
int rsi_handle;
int bulls_handle;
int bears_handle;
int bb_handle;

// Переменные для хранения времени последних сигналов
datetime lastBuySignalTime = 0;
datetime lastSellSignalTime = 0;

// Символы стрелок
const int UP_ARROW = 233;      // Код символа стрелки вверх
const int DOWN_ARROW = 234;    // Код символа стрелки вниз

//+------------------------------------------------------------------+
//| Инициализация индикатора                                          |
//+------------------------------------------------------------------+
int OnInit()
{
   // Установка символов стрелок для буферов
   PlotIndexSetInteger(0, PLOT_ARROW, UP_ARROW);
   PlotIndexSetInteger(1, PLOT_ARROW, DOWN_ARROW);
   
   // Инициализация буферов индикатора
   SetIndexBuffer(0, UpArrowBuffer, INDICATOR_DATA);
   SetIndexBuffer(1, DownArrowBuffer, INDICATOR_DATA);
   
   // Установка пустого значения
   PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, EMPTY_VALUE);
   PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, EMPTY_VALUE);
   
   // Создание хендлов индикаторов
   ma_fast_handle = iMA(_Symbol, TimeFrame, FasterMovingAverage, 0, MODE_EMA, PRICE_CLOSE);
   ma_slow_handle = iMA(_Symbol, TimeFrame, SlowerMovingAverage, 0, MODE_EMA, PRICE_CLOSE);
   rsi_handle = iRSI(_Symbol, TimeFrame, RSIPeriod, PRICE_CLOSE);
   bulls_handle = iBullsPower(_Symbol, TimeFrame, BullsPowerPeriod);
   bears_handle = iBearsPower(_Symbol, TimeFrame, BearsPowerPeriod);
   bb_handle = iBands(_Symbol, TimeFrame, BollingerbandsPeriod, BollingerbandsShift, BollingerbandsDeviation, PRICE_CLOSE);
   
   // Проверка успешности создания хендлов
   if(ma_fast_handle == INVALID_HANDLE || ma_slow_handle == INVALID_HANDLE || 
      rsi_handle == INVALID_HANDLE || bulls_handle == INVALID_HANDLE || 
      bears_handle == INVALID_HANDLE || bb_handle == INVALID_HANDLE)
   {
      Print("Ошибка создания хендлов индикаторов: ", GetLastError());
      return(INIT_FAILED);
   }
   
   // Установка названия индикатора
   string short_name = "Super Arrow Indicator MTF";
   IndicatorSetString(INDICATOR_SHORTNAME, short_name);
   
   // Инициализация успешна
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Деинициализация индикатора                                        |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Освобождение ресурсов индикаторов
   if(ma_fast_handle != INVALID_HANDLE) IndicatorRelease(ma_fast_handle);
   if(ma_slow_handle != INVALID_HANDLE) IndicatorRelease(ma_slow_handle);
   if(rsi_handle != INVALID_HANDLE) IndicatorRelease(rsi_handle);
   if(bulls_handle != INVALID_HANDLE) IndicatorRelease(bulls_handle);
   if(bears_handle != INVALID_HANDLE) IndicatorRelease(bears_handle);
   if(bb_handle != INVALID_HANDLE) IndicatorRelease(bb_handle);
}

//+------------------------------------------------------------------+
//| Основная функция расчета индикатора                               |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   // Проверка количества доступных баров
   if(rates_total < 3) return(0);
   
   // Установка индексации массивов от последнего элемента
   ArraySetAsSeries(time, true);
   ArraySetAsSeries(high, true);
   ArraySetAsSeries(low, true);
   ArraySetAsSeries(close, true);
   
   // Определяем количество баров для расчета
   int start;
   if(prev_calculated == 0)
   {
      // Заполняем буферы пустыми значениями при первом расчете
      ArrayInitialize(UpArrowBuffer, EMPTY_VALUE);
      ArrayInitialize(DownArrowBuffer, EMPTY_VALUE);
      start = rates_total - 3; // Начинаем с 3-го бара от конца
   }
   else
   {
      start = rates_total - prev_calculated; // Рассчитываем только новые бары
      if(start < 1) start = 1; // Должен быть как минимум 1 бар
   }
   
   // Массивы для данных индикаторов
   double ma_fast[], ma_slow[], rsi[], bulls[], bears[], bb_upper[], bb_lower[];
   ArraySetAsSeries(ma_fast, true);
   ArraySetAsSeries(ma_slow, true);
   ArraySetAsSeries(rsi, true);
   ArraySetAsSeries(bulls, true);
   ArraySetAsSeries(bears, true);
   ArraySetAsSeries(bb_upper, true);
   ArraySetAsSeries(bb_lower, true);
   
   // Копируем данные индикаторов
   if(CopyBuffer(ma_fast_handle, 0, 0, rates_total, ma_fast) <= 0) return(0);
   if(CopyBuffer(ma_slow_handle, 0, 0, rates_total, ma_slow) <= 0) return(0);
   if(CopyBuffer(rsi_handle, 0, 0, rates_total, rsi) <= 0) return(0);
   if(CopyBuffer(bulls_handle, 0, 0, rates_total, bulls) <= 0) return(0);
   if(CopyBuffer(bears_handle, 0, 0, rates_total, bears) <= 0) return(0);
   if(CopyBuffer(bb_handle, 1, 0, rates_total, bb_upper) <= 0) return(0);
   if(CopyBuffer(bb_handle, 2, 0, rates_total, bb_lower) <= 0) return(0);
   
   // Расчет индикатора для указанных баров
   for(int i = start; i > 0; i--)
   {
      // Инициализируем буферы пустыми значениями
      UpArrowBuffer[i] = EMPTY_VALUE;
      DownArrowBuffer[i] = EMPTY_VALUE;
      
      // Расчет условий для сигналов
      bool ma_cross_up = (ma_fast[i] > ma_slow[i] && ma_fast[i+1] < ma_slow[i+1]);
      bool ma_cross_down = (ma_fast[i] < ma_slow[i] && ma_fast[i+1] > ma_slow[i+1]);
      
      bool rsi_up = (rsi[i] > 50.0 && rsi[i+1] < 50.0);
      bool rsi_down = (rsi[i] < 50.0 && rsi[i+1] > 50.0);
      
      bool bulls_up = (bulls[i] > 0.0);
      bool bears_down = (bears[i] < 0.0);
      
      bool price_above_bb = (close[i] > bb_upper[i]);
      bool price_below_bb = (close[i] < bb_lower[i]);
      
      // Расчет Magic Filter
      double highest = high[iHighest(_Symbol, TimeFrame, MODE_HIGH, MagicFilterPeriod, i)];
      double lowest = low[iLowest(_Symbol, TimeFrame, MODE_LOW, MagicFilterPeriod, i)];
      double magic_diff = ((highest - 0.0) / 10.0) - ((lowest - 0.0) / 10.0);
      
      bool magic_filter_up = (magic_diff >= 0.0);
      bool magic_filter_down = (magic_diff < 0.0);
      
      // Формирование сигналов
      // Для BUY: нужно пересечение MA вверх ИЛИ RSI вверх, И положительный Bulls Power, И положительный Magic Filter
      if((ma_cross_up || rsi_up) && bulls_up && magic_filter_up)
      {
         UpArrowBuffer[i] = low[i] - _Point * ArrowUtstup;
         // Проверяем, это новый сигнал
         if(time[i] > lastBuySignalTime)
         {
            lastBuySignalTime = time[i];
            // Отправляем уведомления
            if(i == 1) // Сигнал на предпоследнем баре (новый сигнал)
            {
               string message = _Symbol + " : BUY сигнал на " + EnumToString(TimeFrame);
               if(EmailAlert) SendMail("Super Arrow Indicator", message);
               if(PushAlert) SendNotification(message);
               if(SoundAlert) PlaySound(SoundFile);
            }
         }
      }
      
      // Для SELL: нужно пересечение MA вниз ИЛИ RSI вниз, И отрицательный Bears Power, И отрицательный Magic Filter
      if((ma_cross_down || rsi_down) && bears_down && magic_filter_down)
      {
         DownArrowBuffer[i] = high[i] + _Point * ArrowUtstup;
         // Проверяем, это новый сигнал
         if(time[i] > lastSellSignalTime)
         {
            lastSellSignalTime = time[i];
            // Отправляем уведомления
            if(i == 1) // Сигнал на предпоследнем баре (новый сигнал)
            {
               string message = _Symbol + " : SELL сигнал на " + EnumToString(TimeFrame);
               if(EmailAlert) SendMail("Super Arrow Indicator", message);
               if(PushAlert) SendNotification(message);
               if(SoundAlert) PlaySound(SoundFile);
            }
         }
      }
   }
   
   // Возвращаем количество рассчитанных баров
   return(rates_total);
}

//+------------------------------------------------------------------+
//| Функция для получения индекса наибольшего значения в массиве      |
//+------------------------------------------------------------------+
int iHighest(string symbol, ENUM_TIMEFRAMES timeframe, int type, int count, int start)
{
   // Объявляем массивы
   double array[];
   ArraySetAsSeries(array, true);
   
   // Копируем данные в зависимости от типа
   switch(type)
   {
      case MODE_HIGH:
         if(CopyHigh(symbol, timeframe, start, count, array) <= 0) return -1;
         break;
      case MODE_LOW:
         if(CopyLow(symbol, timeframe, start, count, array) <= 0) return -1;
         break;
      case MODE_OPEN:
         if(CopyOpen(symbol, timeframe, start, count, array) <= 0) return -1;
         break;
      case MODE_CLOSE:
         if(CopyClose(symbol, timeframe, start, count, array) <= 0) return -1;
         break;
      default:
         return -1;
   }
   
   // Находим индекс максимального значения
   int maxIndex = ArrayMaximum(array, 0, count);
   if(maxIndex == -1) return -1;
   
   return start + maxIndex;
}

//+------------------------------------------------------------------+
//| Функция для получения индекса наименьшего значения в массиве      |
//+------------------------------------------------------------------+
int iLowest(string symbol, ENUM_TIMEFRAMES timeframe, int type, int count, int start)
{
   // Объявляем массивы
   double array[];
   ArraySetAsSeries(array, true);
   
   // Копируем данные в зависимости от типа
   switch(type)
   {
      case MODE_HIGH:
         if(CopyHigh(symbol, timeframe, start, count, array) <= 0) return -1;
         break;
      case MODE_LOW:
         if(CopyLow(symbol, timeframe, start, count, array) <= 0) return -1;
         break;
      case MODE_OPEN:
         if(CopyOpen(symbol, timeframe, start, count, array) <= 0) return -1;
         break;
      case MODE_CLOSE:
         if(CopyClose(symbol, timeframe, start, count, array) <= 0) return -1;
         break;
      default:
         return -1;
   }
   
   // Находим индекс минимального значения
   int minIndex = ArrayMinimum(array, 0, count);
   if(minIndex == -1) return -1;
   
   return start + minIndex;
} 