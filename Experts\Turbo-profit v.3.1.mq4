
#property copyright "Copyright � 2012, Forex-Investor.net"
#property link      "Forex-Investor.net"

//int g_acc_number_76 = 2457451;
string gs_80 = "FRX_";
string gsa_88[7];
string gs_92;
string gs_dummy_100;
int gi_108;
extern string __r__ = "����� ������";
extern bool Exit_mode = FALSE;
bool gi_unused_124 = TRUE;
extern string __l__ = "��������� ������ (����)";
extern bool LotConst_or_not = FALSE;
extern double Lot = 0.01;
double gd_148;
extern double RiskPercent = 1.0;
double gd_164;
double g_maxlot_172 = 0.0;
extern double LotMultiplicator = 1.25;
bool gi_188 = FALSE;
extern string __s__ = "��������� �������� �����";
extern int Work_Sloy_mode = 1;
extern int TorgSloy = 3;
extern bool D_D_LOT_auto_calc = TRUE;
extern double D_D_Lot = 0.5;
double gd_220;
double gd_236;
extern int N_enable_Sloy = 7;
extern string _____ = "��������� �������� ����� (�������)";
extern int hSETKY = 10;
int gi_unused_260 = 30;
extern int Uvel_hSETKY = 1;
extern int ShagUvel_hSETKY = 10;
int gi_272 = 0;
int gi_276 = 0;
extern bool mode_enable_3_sloy = FALSE;
extern double h_D_T = 500.0;
extern double pr_h_3_sl = 25.0;
extern int slippage = 10;
extern int ProtectionTP = 7;
extern int TrallingStop = 7;
extern int TrallTP = 15;
int gi_unused_316 = 1;
double gd_unused_320 = 40.0;
extern string ______________ = "�����.��.����.��.�����";
string gs_unused_336 = "� % �� ��������";
extern double Min_Proc_Sv_Sr = 80.0;
extern int Magic = 1230;
extern bool ShowTableOnTesting = TRUE;
extern int Text_Syze = 14;
extern color ColorTableOnTesting = Yellow;
extern color ColorLogotipName = Maroon;
extern color ColorLogotipSite = Gray;
extern color color_Trade_area_of_3rd_layer = DarkSlateGray;
color g_color_380;
double gd_384;
int gi_392 = 1000;
double gd_396;
double g_price_404;
double g_price_412;
double g_order_open_price_420;
double g_order_open_price_428;
int gia_unused_436[3];
int gia_unused_440[3];
int gia_unused_444[3];
int gia_unused_448[3];
int gia_unused_452[3];
int gia_unused_456[3];
int gia_unused_460[3];
int gia_unused_464[3];
double gd_476;
double gd_484;
double g_ask_500;
double g_bid_508;
double gd_516;
double gd_524;
int gia_532[7];
int gia_unused_536[7];
double gd_540;
int gia_548[3][2] = {16711680, 255,
   15570276, 7504122,
   13959039, 17919};
string gsa_552[3][2] = {"NextBUY_0", "NextSELL_0",
   "NextBUY_1", "NextSELL_1",
   "NextBUY_2", "NextSELL_2"};
string g_name_556 = "Trade area of 3rd layer";
int g_index_564;
int gi_568 = 7;
int gia_572[7] = {1, 0, 0, 0, 0, 0, 0};
string gsa_576[7];
int gia_580[7] = {10, 1, 0, 1, 1, 1, 1};
int gia_584[7] = {5, 3, 3, 3, 3, 3, 3};
int gia_588[7] = {20, 20, 20, 20, 20, 20, 20};
int gia_592[7] = {7, 7, 7, 7, 7, 7, 7};
int gia_596[7] = {111, 222, 333, 444, 555, 777, 999};
double gda_unused_600[7];
double gda_unused_604[7];
double gda_unused_608[7];
double gda_unused_612[7];
double gda_unused_616[7];
double gda_unused_620[7];
double gda_unused_624[7];
int gia_628[10] = {1, 5, 15, 30, 60, 240, 1440, 10080, 43200, 0};
double gda_632[7][16][50];
double gda_636[7][16][50];
double gda_640[7][16][50];
int gia_644[7][16][50];
double gda_648[7][16][50];
double gda_652[7][16][50];
double gda_656[7];
double gda_660[7];
double gda_664[7][16];
double gda_668[7][16];
int gia_672[7];
int gia_676[7];
int gia_680[7];
int gia_684[7];
int gia_688[7];
int gia_692[7];
int g_pos_696;
bool gi_700;
int g_order_total_704;
int gi_716;
int gi_720;
int gia_732[7];
int gia_736[7];
int gia_740[7];
int gia_744[7];
int gia_748[7];
int gia_752[7];
int gia_756[7];
int gia_760[7];
int gia_764[7];
int gia_768[7];
int gia_772[7];
int gia_776[7];
int gia_780[7];
int gia_784[7];
int gia_unused_788[7];
int gia_unused_792[7];
int gia_796[7];
int gia_800[7];
int gia_unused_804[7];
int gi_808;
double gda_812[7];
double gda_816[7];
double gda_820[7];
double gda_824[7];
int gia_828[7];
int gia_832[7];
double gda_836[7];
double gda_840[7];
double gda_844[7];
double gda_848[7];
double gda_852[7];
double gda_856[7];
double gda_860[7];
double gda_864[7];
int gi_unused_868;
int gi_unused_872;
bool gi_876;
bool gi_880;
double g_price_900;
double g_price_908;
double g_price_916;
double g_price_924;
double gda_932[7];
double gda_936[7];
int gi_unused_940 = 10;

int init() {
   gi_808 = 1;
   if (Digits == 5 || Digits == 3) gi_808 = 10;
   for (gi_716 = 1; gi_716 < 8; gi_716++) {
      gia_732[gi_716] = 0;
      gia_736[gi_716] = 0;
      gia_748[gi_716] = 0;
      gia_752[gi_716] = 0;
      gia_756[gi_716] = 0;
      gia_760[gi_716] = 0;
      gia_764[gi_716] = 0;
      gia_768[gi_716] = 0;
      Print(gia_732[gi_716], gia_736[gi_716], gia_748[gi_716], gia_752[gi_716], gia_756[gi_716], gia_760[gi_716], gia_764[gi_716], gia_768[gi_716]);
   }
   return (0);
}

void deinit() {
   Comment("");
}

int start() {
   int li_20;
   int li_72;
   int li_76;
   int li_80;
   int li_84;
   int li_88;
   bool li_92;
   bool li_96;
   bool li_100;
   bool li_104;
   double price_108;
   double price_116;
   gs_92 = "��������";
   if (Exit_mode) gs_92 = "EXIT";
   string symbol_0 = Symbol();
   gi_108 = 0;
   gd_384 = AccountBalance() * Min_Proc_Sv_Sr / 100.0;
   if (Min_Proc_Sv_Sr < 100.0 && AccountFreeMargin() < AccountBalance() * Min_Proc_Sv_Sr / 100.0) gi_108 = 1;
/*   if (AccountNumber() != g_acc_number_76 && (!IsDemo())) {
      Comment("�������� ����� �������� ������ �� ����� " + g_acc_number_76 + ", ��� ����������� ����������� � ������� ����� ��������� �� ���� forex-investor.net");
      return;
   } */
   gd_164 = RiskPercent;
   gd_148 = Lot;
   double ld_8 = MarketInfo(symbol_0, MODE_MINLOT);
   int li_unused_16 = MarketInfo(symbol_0, MODE_DIGITS);
   if (ld_8 == 0.01) li_20 = 2;
   if (ld_8 == 0.1) li_20 = 1;
   if (ld_8 >= 1.0) li_20 = 0;
   double ld_24 = AccountBalance();
   int stoplevel_32 = MarketInfo(symbol_0, MODE_STOPLEVEL);
   int spread_36 = MarketInfo(symbol_0, MODE_SPREAD);
   double point_40 = MarketInfo(symbol_0, MODE_POINT);
   double bid_48 = MarketInfo(symbol_0, MODE_BID);
   double ask_56 = MarketInfo(symbol_0, MODE_ASK);
   double lotstep_64 = MarketInfo(symbol_0, MODE_LOTSTEP);
   if (lotstep_64 == 0.01) li_72 = 2;
   if (lotstep_64 == 0.1) li_72 = 1;
   if (lotstep_64 >= 1.0) li_72 = 0;
   if (g_maxlot_172 == 0.0) g_maxlot_172 = MarketInfo(symbol_0, MODE_MAXLOT);
   h_D_T *= gi_808;
   if (LotConst_or_not) gd_220 = gd_148;
   else gd_220 = NormalizeDouble(ld_24 * gd_164 / 1000000.0, li_20);
   if (NormalizeDouble(ld_24 * gd_164 / 1000000.0, li_20) < ld_8) gd_220 = ld_8;
   gia_572[0] = 0;
   gia_572[1] = 0;
   gia_572[2] = 0;
   g_order_open_price_420 = 10000;
   g_order_open_price_428 = 0;
   g_index_564 = 0;
   gsa_576[g_index_564] = symbol_0;
   gia_596[g_index_564] = Magic + g_index_564;
   f0_4();
   g_index_564 = 1;
   gsa_576[g_index_564] = symbol_0;
   gia_596[g_index_564] = Magic + g_index_564;
   f0_4();
   g_index_564 = 2;
   gsa_576[g_index_564] = symbol_0;
   gia_596[g_index_564] = Magic + g_index_564;
   f0_4();
   g_order_total_704 = OrdersTotal();
   for (g_pos_696 = 0; g_pos_696 < g_order_total_704; g_pos_696++) {
      if (OrderMagicNumber() == Magic || OrderMagicNumber() == Magic + 1 || OrderMagicNumber() == Magic + 2 && OrderSymbol() == gsa_576[g_index_564]) {
         OrderSelect(g_pos_696, SELECT_BY_POS, MODE_TRADES);
         if (g_order_open_price_420 > OrderOpenPrice()) g_order_open_price_420 = OrderOpenPrice();
         if (g_order_open_price_428 < OrderOpenPrice()) g_order_open_price_428 = OrderOpenPrice();
      }
   }
   gd_396 = (bid_48 + ask_56) / 2.0;
   g_price_412 = g_order_open_price_428 - (g_order_open_price_428 - g_order_open_price_420) / 2.0 + (g_order_open_price_428 - g_order_open_price_420) * pr_h_3_sl / 200.0;
   g_price_404 = g_order_open_price_420 + (g_order_open_price_428 - g_order_open_price_420) / 2.0 - (g_order_open_price_428 - g_order_open_price_420) * pr_h_3_sl / 200.0;
   if (gia_672[0] == 0 && gia_676[0] == 0 && gia_672[1] == 0 && gia_676[1] == 0 && gia_672[2] == 0 && gia_676[2] == 0 && Exit_mode == FALSE) gia_572[0] = 2;
   else {
      if (gia_672[0] > 0 || gia_676[0] > 0) gia_572[0] = 1;
      if (gia_672[1] > 0 || gia_676[1] > 0) gia_572[1] = 1;
      if (gia_672[2] > 0 || gia_676[2] > 0) gia_572[2] = 1;
      if (Work_Sloy_mode == 0 && TorgSloy == 1) {
         if (gia_672[0] > 0 || gia_676[0] > 0 && gia_672[1] == 0 && gia_676[1] == 0 && gia_672[2] == 0 && gia_676[2] == 0) gia_572[0] = 1;
         if (gia_672[0] == 0 && gia_676[0] == 0 && gia_672[1] > 0 || gia_676[1] > 0 && gia_672[2] == 0 && gia_676[2] == 0) gia_572[1] = 1;
         if (gia_672[0] == 0 && gia_676[0] == 0 && gia_672[1] == 0 && gia_676[1] == 0 && gia_672[2] > 0 || gia_676[2] > 0) gia_572[2] = 1;
      }
      if (Work_Sloy_mode == 0 && TorgSloy == 2) {
         if (gia_672[0] > 0 || gia_676[0] > 0 && gia_672[0] > N_enable_Sloy || gia_676[0] > N_enable_Sloy && gia_672[1] == 0 && gia_676[1] == 0 && gia_672[2] == 0 && gia_676[2] == 0 &&
            Exit_mode == FALSE) {
            gia_572[0] = 1;
            gia_572[1] = 1;
         }
         if (gia_672[0] == 0 && gia_676[0] == 0 && gia_672[1] > 0 || gia_676[1] > 0 && gia_672[1] > N_enable_Sloy || gia_676[1] > N_enable_Sloy && gia_672[2] == 0 && gia_676[2] == 0 &&
            Exit_mode == FALSE) {
            gia_572[0] = 1;
            gia_572[1] = 1;
         }
         if (gia_672[0] == 0 && gia_676[0] == 0 && gia_672[1] == 0 && gia_676[1] == 0 && gia_672[2] > 0 || gia_676[2] > 0 && gia_672[2] > N_enable_Sloy || gia_676[2] > N_enable_Sloy &&
            Exit_mode == FALSE) {
            gia_572[0] = 1;
            gia_572[2] = 1;
         }
         if (gia_672[0] > 0 || gia_676[0] > 0 && gia_672[1] > 0 || gia_676[1] > 0 && Exit_mode == FALSE) {
            gia_572[0] = 1;
            gia_572[1] = 1;
         }
         if (gia_672[0] > 0 || gia_676[0] > 0 && gia_672[2] > 0 || gia_676[2] > 0 && Exit_mode == FALSE) {
            gia_572[0] = 1;
            gia_572[2] = 1;
         }
         if (gia_672[1] > 0 || gia_676[1] > 0 && gia_672[2] > 0 || gia_676[2] > 0 && Exit_mode == FALSE) {
            gia_572[1] = 1;
            gia_572[2] = 1;
         }
      }
      if (Work_Sloy_mode == 0 && TorgSloy == 3) {
         if (gia_672[0] > 0 || gia_676[0] > 0 && gia_672[0] > N_enable_Sloy || gia_676[0] > N_enable_Sloy && gia_672[1] == 0 && gia_676[1] == 0 && gia_672[2] == 0 && gia_676[2] == 0 &&
            Exit_mode == FALSE) {
            gia_572[0] = 1;
            gia_572[1] = 1;
         }
         if (gia_672[0] == 0 && gia_676[0] == 0 && gia_672[1] > 0 || gia_676[1] > 0 && gia_672[1] > N_enable_Sloy || gia_676[1] > N_enable_Sloy && gia_672[2] == 0 && gia_676[2] == 0 &&
            Exit_mode == FALSE) {
            gia_572[0] = 1;
            gia_572[1] = 1;
         }
         if (gia_672[0] == 0 && gia_676[0] == 0 && gia_672[1] == 0 && gia_676[1] == 0 && gia_672[2] > 0 || gia_676[2] > 0 && gia_672[2] > N_enable_Sloy || gia_676[2] > N_enable_Sloy &&
            Exit_mode == FALSE) {
            gia_572[0] = 1;
            gia_572[2] = 1;
         }
         if (gia_672[0] > 0 || gia_676[0] > 0 && gia_672[0] > N_enable_Sloy || gia_676[0] > N_enable_Sloy && gia_672[1] > 0 || gia_676[1] > 0 && gia_672[1] > N_enable_Sloy ||
            gia_676[1] > N_enable_Sloy && gia_672[2] == 0 && gia_676[2] == 0 && Exit_mode == FALSE) {
            gia_572[0] = 1;
            gia_572[1] = 1;
            gia_572[2] = 1;
         }
         if (gia_672[0] > 0 || gia_676[0] > 0 && gia_672[0] > N_enable_Sloy || gia_676[0] > N_enable_Sloy && gia_672[1] == 0 && gia_676[1] == 0 && gia_672[2] > 0 || gia_676[2] > 0 &&
            gia_672[2] > N_enable_Sloy || gia_676[2] > N_enable_Sloy && Exit_mode == FALSE) {
            gia_572[0] = 1;
            gia_572[1] = 1;
            gia_572[2] = 1;
         }
         if (gia_672[0] == 0 && gia_676[0] == 0 && gia_672[1] > 0 || gia_676[1] > 0 && gia_672[1] > N_enable_Sloy || gia_676[1] > N_enable_Sloy && gia_672[2] > 0 || gia_676[2] > 0 &&
            gia_672[2] > N_enable_Sloy || gia_676[2] > N_enable_Sloy && Exit_mode == FALSE) {
            gia_572[0] = 1;
            gia_572[1] = 1;
            gia_572[2] = 1;
         }
      }
      if (Work_Sloy_mode == 1 && TorgSloy == 1) {
         if (gia_672[0] > 0 || gia_676[0] > 0 && gia_672[1] == 0 && gia_676[1] == 0 && gia_672[2] == 0 && gia_676[2] == 0) gia_572[0] = 1;
         if (gia_672[0] == 0 && gia_676[0] == 0 && gia_672[1] > 0 || gia_676[1] > 0 && gia_672[2] == 0 && gia_676[2] == 0) gia_572[1] = 1;
         if (gia_672[0] == 0 && gia_676[0] == 0 && gia_672[1] == 0 && gia_676[1] == 0 && gia_672[2] > 0 || gia_676[2] > 0) gia_572[2] = 1;
      }
      if (Work_Sloy_mode == 1 && TorgSloy == 2) {
         gd_220 = 0;
         gd_236 = D_D_Lot;
         if (D_D_LOT_auto_calc) {
            if (LotConst_or_not) gd_236 = gd_148;
            else gd_236 = NormalizeDouble(ld_24 * gd_164 / 1000000.0, li_20);
            if (NormalizeDouble(ld_24 * gd_164 / 1000000.0, li_20) < ld_8) gd_236 = ld_8;
            for (gi_716 = 1; gi_716 < N_enable_Sloy; gi_716++) gd_236 *= LotMultiplicator;
         }
         if (gia_672[0] > 0 || gia_676[0] > 0 && gia_672[1] == 0 && gia_676[1] == 0 && gia_672[2] == 0 && gia_676[2] == 0 && Exit_mode == FALSE) {
            if (gda_664[0][1] - gda_664[0][0] > gd_220) {
               gia_572[0] = 1;
               gia_572[1] = 2;
               if (gi_188 && LotConst_or_not && gda_664[0][1] - gda_664[0][0] > gd_236) gd_164 = (gda_664[0][1] - gda_664[0][0]) * RiskPercent / gd_236;
               if (gi_188 && LotConst_or_not == FALSE && gda_664[0][1] - gda_664[0][0] > gd_236) gd_148 = (gda_664[0][1] - gda_664[0][0]) * Lot / gd_236;
            }
            if (gda_664[0][0] - gda_664[0][1] > gd_220) {
               gia_572[0] = 1;
               gia_572[1] = 3;
               if (gi_188 && LotConst_or_not && gda_664[0][0] - gda_664[0][1] > gd_236) gd_164 = (gda_664[0][0] - gda_664[0][1]) * RiskPercent / gd_236;
               if (gi_188 && LotConst_or_not == FALSE && gda_664[0][0] - gda_664[0][1] > gd_236) gd_148 = (gda_664[0][0] - gda_664[0][1]) * Lot / gd_236;
            }
         }
         if (gia_672[0] == 0 && gia_676[0] == 0 && gia_672[1] > 0 || gia_676[1] > 0 && gia_672[2] == 0 && gia_676[2] == 0 && Exit_mode == FALSE) {
            if (gda_664[1][1] - gda_664[1][0] > gd_220) {
               gia_572[0] = 2;
               gia_572[1] = 1;
               if (gi_188 && LotConst_or_not && gda_664[1][1] - gda_664[1][0] > gd_236) gd_164 = (gda_664[1][1] - gda_664[1][0]) * RiskPercent / gd_236;
               if (gi_188 && LotConst_or_not == FALSE && gda_664[1][1] - gda_664[1][0] > gd_236) gd_148 = (gda_664[1][1] - gda_664[1][0]) * Lot / gd_236;
            }
            if (gda_664[1][0] - gda_664[1][1] > gd_220) {
               gia_572[0] = 3;
               gia_572[1] = 1;
               if (gi_188 && LotConst_or_not && gda_664[1][0] - gda_664[1][1] > gd_236) gd_164 = (gda_664[1][0] - gda_664[1][1]) * RiskPercent / gd_236;
               if (gi_188 && LotConst_or_not == FALSE && gda_664[1][0] - gda_664[1][1] > gd_236) gd_148 = (gda_664[1][0] - gda_664[1][1]) * Lot / gd_236;
            }
         }
         if (gia_672[0] == 0 && gia_676[0] == 0 && gia_672[1] == 0 && gia_676[1] == 0 && gia_672[2] > 0 || gia_676[2] > 0 && Exit_mode == FALSE) {
            if (gda_664[2][1] - gda_664[2][0] > gd_220) {
               gia_572[0] = 2;
               gia_572[2] = 1;
               if (gi_188 && LotConst_or_not && gda_664[2][1] - gda_664[2][0] > gd_236) gd_164 = (gda_664[2][1] - gda_664[2][0]) * RiskPercent / gd_236;
               if (gi_188 && LotConst_or_not == FALSE && gda_664[2][1] - gda_664[2][0] > gd_236) gd_148 = (gda_664[2][1] - gda_664[2][0]) * Lot / gd_236;
            }
            if (gda_664[2][0] - gda_664[2][1] > gd_220) {
               gia_572[0] = 3;
               gia_572[2] = 1;
               if (gi_188 && LotConst_or_not && gda_664[2][0] - gda_664[2][1] > gd_236) gd_164 = (gda_664[2][0] - gda_664[2][1]) * RiskPercent / gd_236;
               if (gi_188 && LotConst_or_not == FALSE && gda_664[2][0] - gda_664[2][1] > gd_236) gd_148 = (gda_664[2][0] - gda_664[2][1]) * Lot / gd_236;
            }
         }
         if (gia_672[0] > 0 || gia_676[0] > 0 && gia_672[1] > 0 || gia_676[1] > 0 && gia_672[2] == 0 && gia_676[2] == 0 && Exit_mode == FALSE) {
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) > MathAbs(gda_664[1][1] - gda_664[1][0]) && gda_664[0][0] + gda_664[1][0] - gda_664[0][1] - gda_664[1][1] < gd_220) {
               gia_572[0] = 1;
               gia_572[1] = 2;
            }
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) > MathAbs(gda_664[1][1] - gda_664[1][0]) && gda_664[0][0] + gda_664[1][0] - gda_664[0][1] - gda_664[1][1] > gd_220) {
               gia_572[0] = 1;
               gia_572[1] = 3;
            }
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) >= MathAbs(gda_664[1][1] - gda_664[1][0]) && gda_664[0][0] + gda_664[1][0] - gda_664[0][1] - gda_664[1][1] == gd_220) {
               gia_572[0] = 1;
               gia_572[1] = 1;
            }
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) < MathAbs(gda_664[1][1] - gda_664[1][0]) && gda_664[0][0] + gda_664[1][0] - gda_664[0][1] - gda_664[1][1] < gd_220) {
               gia_572[0] = 2;
               gia_572[1] = 1;
            }
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) < MathAbs(gda_664[1][1] - gda_664[1][0]) && gda_664[0][0] + gda_664[1][0] - gda_664[0][1] - gda_664[1][1] > gd_220) {
               gia_572[0] = 3;
               gia_572[1] = 1;
            }
         }
         if (gia_672[0] == 0 && gia_676[0] == 0 && gia_672[1] > 0 || gia_676[1] > 0 && gia_672[2] > 0 || gia_676[2] > 0 && Exit_mode == FALSE) {
            if (MathAbs(gda_664[1][1] - gda_664[1][0]) > MathAbs(gda_664[2][1] - gda_664[2][0]) && gda_664[1][0] + gda_664[2][0] - gda_664[1][1] - gda_664[2][1] < gd_220) {
               gia_572[1] = 1;
               gia_572[2] = 2;
            }
            if (MathAbs(gda_664[1][1] - gda_664[1][0]) > MathAbs(gda_664[2][1] - gda_664[2][0]) && gda_664[1][0] + gda_664[2][0] - gda_664[1][1] - gda_664[2][1] > gd_220) {
               gia_572[1] = 1;
               gia_572[2] = 3;
            }
            if (MathAbs(gda_664[1][1] - gda_664[1][0]) >= MathAbs(gda_664[2][1] - gda_664[2][0]) && gda_664[1][0] + gda_664[2][0] - gda_664[1][1] - gda_664[2][1] == gd_220) {
               gia_572[1] = 1;
               gia_572[2] = 1;
            }
            if (MathAbs(gda_664[1][1] - gda_664[1][0]) < MathAbs(gda_664[2][1] - gda_664[2][0]) && gda_664[1][0] + gda_664[2][0] - gda_664[1][1] - gda_664[2][1] < gd_220) {
               gia_572[1] = 2;
               gia_572[2] = 1;
            }
            if (MathAbs(gda_664[1][1] - gda_664[1][0]) < MathAbs(gda_664[2][1] - gda_664[2][0]) && gda_664[1][0] + gda_664[2][0] - gda_664[1][1] - gda_664[2][1] > gd_220) {
               gia_572[1] = 3;
               gia_572[2] = 1;
            }
         }
         if (gia_672[0] > 0 || gia_676[0] > 0 && gia_672[1] == 0 && gia_676[1] == 0 && gia_672[2] > 0 || gia_676[2] > 0 && Exit_mode == FALSE) {
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) > MathAbs(gda_664[2][1] - gda_664[2][0]) && gda_664[0][0] + gda_664[2][0] - gda_664[0][1] - gda_664[2][1] < gd_220) {
               gia_572[0] = 1;
               gia_572[2] = 2;
            }
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) > MathAbs(gda_664[2][1] - gda_664[2][0]) && gda_664[0][0] + gda_664[2][0] - gda_664[0][1] - gda_664[2][1] > gd_220) {
               gia_572[0] = 1;
               gia_572[2] = 3;
            }
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) >= MathAbs(gda_664[2][1] - gda_664[2][0]) && gda_664[0][0] + gda_664[2][0] - gda_664[0][1] - gda_664[2][1] == gd_220) {
               gia_572[0] = 1;
               gia_572[2] = 1;
            }
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) < MathAbs(gda_664[2][1] - gda_664[2][0]) && gda_664[0][0] + gda_664[2][0] - gda_664[0][1] - gda_664[2][1] < gd_220) {
               gia_572[0] = 2;
               gia_572[2] = 1;
            }
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) < MathAbs(gda_664[2][1] - gda_664[2][0]) && gda_664[0][0] + gda_664[2][0] - gda_664[0][1] - gda_664[2][1] > gd_220) {
               gia_572[0] = 3;
               gia_572[2] = 1;
            }
         }
      }
      if (Work_Sloy_mode == 1 && TorgSloy == 3) {
         gd_220 = 0;
         gd_236 = D_D_Lot;
         if (D_D_LOT_auto_calc) {
            if (LotConst_or_not) gd_236 = gd_148;
            else gd_236 = NormalizeDouble(ld_24 * gd_164 / 1000000.0, li_20);
            if (NormalizeDouble(ld_24 * gd_164 / 1000000.0, li_20) < ld_8) gd_236 = ld_8;
            for (gi_716 = 1; gi_716 < N_enable_Sloy; gi_716++) gd_236 *= LotMultiplicator;
         }
         if (gia_672[0] > 0 || gia_676[0] > 0 && gia_672[1] == 0 && gia_676[1] == 0 && gia_672[2] == 0 && gia_676[2] == 0 && Exit_mode == FALSE) {
            if (gda_664[0][1] - gda_664[0][0] > gd_220) {
               gia_572[0] = 1;
               gia_572[1] = 2;
               if (gi_188 && LotConst_or_not && gda_664[0][1] - gda_664[0][0] > gd_236) gd_164 = (gda_664[0][1] - gda_664[0][0]) * RiskPercent / gd_236;
               if (gi_188 && LotConst_or_not == FALSE && gda_664[0][1] - gda_664[0][0] > gd_236) gd_148 = (gda_664[0][1] - gda_664[0][0]) * Lot / gd_236;
            }
            if (gda_664[0][0] - gda_664[0][1] > gd_220) {
               gia_572[0] = 1;
               gia_572[1] = 3;
               if (gi_188 && LotConst_or_not && gda_664[0][0] - gda_664[0][1] > gd_236) gd_164 = (gda_664[0][0] - gda_664[0][1]) * RiskPercent / gd_236;
               if (gi_188 && LotConst_or_not == FALSE && gda_664[0][0] - gda_664[0][1] > gd_236) gd_148 = (gda_664[0][0] - gda_664[0][1]) * Lot / gd_236;
            }
         }
         if (gia_672[0] == 0 && gia_676[0] == 0 && gia_672[1] > 0 || gia_676[1] > 0 && gia_672[2] == 0 && gia_676[2] == 0 && Exit_mode == FALSE) {
            if (gda_664[1][1] - gda_664[1][0] > gd_220) {
               gia_572[0] = 2;
               gia_572[1] = 1;
               if (gi_188 && LotConst_or_not && gda_664[1][1] - gda_664[1][0] > gd_236) gd_164 = (gda_664[1][1] - gda_664[1][0]) * RiskPercent / gd_236;
               if (gi_188 && LotConst_or_not == FALSE && gda_664[1][1] - gda_664[1][0] > gd_236) gd_148 = (gda_664[1][1] - gda_664[1][0]) * Lot / gd_236;
            }
            if (gda_664[1][0] - gda_664[1][1] > gd_220) {
               gia_572[0] = 3;
               gia_572[1] = 1;
               if (gi_188 && LotConst_or_not && gda_664[1][0] - gda_664[1][1] > gd_236) gd_164 = (gda_664[1][0] - gda_664[1][1]) * RiskPercent / gd_236;
               if (gi_188 && LotConst_or_not == FALSE && gda_664[1][0] - gda_664[1][1] > gd_236) gd_148 = (gda_664[1][0] - gda_664[1][1]) * Lot / gd_236;
            }
         }
         if (gia_672[0] == 0 && gia_676[0] == 0 && gia_672[1] == 0 && gia_676[1] == 0 && gia_672[2] > 0 || gia_676[2] > 0 && Exit_mode == FALSE) {
            if (gda_664[2][1] - gda_664[2][0] > gd_220) {
               gia_572[0] = 2;
               gia_572[2] = 1;
               if (gi_188 && LotConst_or_not && gda_664[2][1] - gda_664[2][0] > gd_236) gd_164 = (gda_664[2][1] - gda_664[2][0]) * RiskPercent / gd_236;
               if (gi_188 && LotConst_or_not == FALSE && gda_664[2][1] - gda_664[2][0] > gd_236) gd_148 = (gda_664[2][1] - gda_664[2][0]) * Lot / gd_236;
            }
            if (gda_664[2][0] - gda_664[2][1] > gd_220) {
               gia_572[0] = 3;
               gia_572[2] = 1;
               if (gi_188 && LotConst_or_not && gda_664[2][0] - gda_664[2][1] > gd_236) gd_164 = (gda_664[2][0] - gda_664[2][1]) * RiskPercent / gd_236;
               if (gi_188 && LotConst_or_not == FALSE && gda_664[2][0] - gda_664[2][1] > gd_236) gd_148 = (gda_664[2][0] - gda_664[2][1]) * Lot / gd_236;
            }
         }
         if (gia_672[0] > 0 || gia_676[0] > 0 && gia_672[1] > 0 || gia_676[1] > 0 && gia_672[2] == 0 && gia_676[2] == 0 && (MathAbs(gda_664[0][1] - gda_664[0][0]) <= gd_236 &&
            MathAbs(gda_664[1][1] - gda_664[1][0]) <= gd_236) || (MathAbs(gda_664[0][1] - gda_664[0][0]) > gd_236 && MathAbs(gda_664[1][1] - gda_664[1][0]) <= gd_236) || (MathAbs(gda_664[0][1] - gda_664[0][0]) <= gd_236 && MathAbs(gda_664[1][1] - gda_664[1][0]) > gd_236) && Exit_mode == FALSE) {
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) > MathAbs(gda_664[1][1] - gda_664[1][0]) && gda_664[0][0] + gda_664[1][0] - gda_664[0][1] - gda_664[1][1] < gd_220) {
               gia_572[0] = 1;
               gia_572[1] = 2;
            }
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) > MathAbs(gda_664[1][1] - gda_664[1][0]) && gda_664[0][0] + gda_664[1][0] - gda_664[0][1] - gda_664[1][1] > gd_220) {
               gia_572[0] = 1;
               gia_572[1] = 3;
            }
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) >= MathAbs(gda_664[1][1] - gda_664[1][0]) && gda_664[0][0] + gda_664[1][0] - gda_664[0][1] - gda_664[1][1] == gd_220) {
               gia_572[0] = 1;
               gia_572[1] = 1;
            }
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) < MathAbs(gda_664[1][1] - gda_664[1][0]) && gda_664[0][0] + gda_664[1][0] - gda_664[0][1] - gda_664[1][1] < gd_220) {
               gia_572[0] = 2;
               gia_572[1] = 1;
            }
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) < MathAbs(gda_664[1][1] - gda_664[1][0]) && gda_664[0][0] + gda_664[1][0] - gda_664[0][1] - gda_664[1][1] > gd_220) {
               gia_572[0] = 3;
               gia_572[1] = 1;
            }
         }
         if (gia_672[0] == 0 && gia_676[0] == 0 && gia_672[1] > 0 || gia_676[1] > 0 && gia_672[2] > 0 || gia_676[2] > 0 && (MathAbs(gda_664[1][1] - gda_664[1][0]) <= gd_236 &&
            MathAbs(gda_664[2][1] - gda_664[2][0]) <= gd_236) || (MathAbs(gda_664[1][1] - gda_664[1][0]) > gd_236 && MathAbs(gda_664[2][1] - gda_664[2][0]) <= gd_236) || (MathAbs(gda_664[1][1] - gda_664[1][0]) <= gd_236 && MathAbs(gda_664[2][1] - gda_664[2][0]) > gd_236) && Exit_mode == FALSE) {
            if (MathAbs(gda_664[1][1] - gda_664[1][0]) > MathAbs(gda_664[2][1] - gda_664[2][0]) && gda_664[1][0] + gda_664[2][0] - gda_664[1][1] - gda_664[2][1] < gd_220) {
               gia_572[1] = 1;
               gia_572[2] = 2;
            }
            if (MathAbs(gda_664[1][1] - gda_664[1][0]) > MathAbs(gda_664[2][1] - gda_664[2][0]) && gda_664[1][0] + gda_664[2][0] - gda_664[1][1] - gda_664[2][1] > gd_220) {
               gia_572[1] = 1;
               gia_572[2] = 3;
            }
            if (MathAbs(gda_664[1][1] - gda_664[1][0]) >= MathAbs(gda_664[2][1] - gda_664[2][0]) && gda_664[1][0] + gda_664[2][0] - gda_664[1][1] - gda_664[2][1] == gd_220) {
               gia_572[1] = 1;
               gia_572[2] = 1;
            }
            if (MathAbs(gda_664[1][1] - gda_664[1][0]) < MathAbs(gda_664[2][1] - gda_664[2][0]) && gda_664[1][0] + gda_664[2][0] - gda_664[1][1] - gda_664[2][1] < gd_220) {
               gia_572[1] = 2;
               gia_572[2] = 1;
            }
            if (MathAbs(gda_664[1][1] - gda_664[1][0]) < MathAbs(gda_664[2][1] - gda_664[2][0]) && gda_664[1][0] + gda_664[2][0] - gda_664[1][1] - gda_664[2][1] > gd_220) {
               gia_572[1] = 3;
               gia_572[2] = 1;
            }
         }
         if (gia_672[0] > 0 || gia_676[0] > 0 && gia_672[1] == 0 && gia_676[1] == 0 && gia_672[2] > 0 || gia_676[2] > 0 && (MathAbs(gda_664[0][1] - gda_664[0][0]) <= gd_236 &&
            MathAbs(gda_664[2][1] - gda_664[2][0]) <= gd_236) || (MathAbs(gda_664[0][1] - gda_664[0][0]) > gd_236 && MathAbs(gda_664[2][1] - gda_664[2][0]) <= gd_236) || (MathAbs(gda_664[0][1] - gda_664[0][0]) <= gd_236 && MathAbs(gda_664[2][1] - gda_664[2][0]) > gd_236) && Exit_mode == FALSE) {
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) > MathAbs(gda_664[2][1] - gda_664[2][0]) && gda_664[0][0] + gda_664[2][0] - gda_664[0][1] - gda_664[2][1] < gd_220) {
               gia_572[0] = 1;
               gia_572[2] = 2;
            }
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) > MathAbs(gda_664[2][1] - gda_664[2][0]) && gda_664[0][0] + gda_664[2][0] - gda_664[0][1] - gda_664[2][1] > gd_220) {
               gia_572[0] = 1;
               gia_572[2] = 3;
            }
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) >= MathAbs(gda_664[2][1] - gda_664[2][0]) && gda_664[0][0] + gda_664[2][0] - gda_664[0][1] - gda_664[2][1] == gd_220) {
               gia_572[0] = 1;
               gia_572[2] = 1;
            }
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) < MathAbs(gda_664[2][1] - gda_664[2][0]) && gda_664[0][0] + gda_664[2][0] - gda_664[0][1] - gda_664[2][1] < gd_220) {
               gia_572[0] = 2;
               gia_572[2] = 1;
            }
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) < MathAbs(gda_664[2][1] - gda_664[2][0]) && gda_664[0][0] + gda_664[2][0] - gda_664[0][1] - gda_664[2][1] > gd_220) {
               gia_572[0] = 3;
               gia_572[2] = 1;
            }
         }
         if (mode_enable_3_sloy) {
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) > gd_236 && MathAbs(gda_664[1][1] - gda_664[1][0]) > gd_236 && MathAbs(MathAbs(gda_664[0][1] - gda_664[0][0]) - MathAbs(gda_664[1][1] - gda_664[1][0])) > gd_236 ||
               (gda_664[0][0] - gda_664[0][1] > gd_220 && gda_664[1][0] - gda_664[1][1] > gd_220) || (gda_664[0][1] - gda_664[0][0] > gd_220 && gda_664[1][1] - gda_664[1][0] > gd_220) && gia_672[2] == 0 && gia_676[2] == 0 && Exit_mode == FALSE) {
               if (gda_664[0][0] + gda_664[1][0] - gda_664[0][1] - gda_664[1][1] < gd_220) {
                  gia_572[0] = 1;
                  gia_572[1] = 1;
                  gia_572[2] = 2;
                  if (gda_664[0][1] - gda_664[0][0] - (gda_664[1][0] - gda_664[1][1]) > gd_236 && gi_188 && LotConst_or_not) gd_164 = (gda_664[0][1] - gda_664[0][0] - (gda_664[1][0] - gda_664[1][1])) * RiskPercent / gd_236;
                  if (gda_664[0][1] - gda_664[0][0] - (gda_664[1][0] - gda_664[1][1]) > gd_236 && gi_188 && LotConst_or_not == FALSE) gd_148 = (gda_664[0][1] - gda_664[0][0] - (gda_664[1][0] - gda_664[1][1])) * Lot / gd_236;
               }
               if (gda_664[0][0] + gda_664[1][0] - gda_664[0][1] - gda_664[1][1] > gd_220) {
                  gia_572[0] = 1;
                  gia_572[1] = 1;
                  gia_572[2] = 3;
                  if (gda_664[0][0] - gda_664[0][1] - (gda_664[1][1] - gda_664[1][0]) > gd_236 && gi_188 && LotConst_or_not) gd_164 = (gda_664[0][0] - gda_664[0][1] - (gda_664[1][1] - gda_664[1][0])) * RiskPercent / gd_236;
                  if (gda_664[0][0] - gda_664[0][1] - (gda_664[1][1] - gda_664[1][0]) > gd_236 && gi_188 && LotConst_or_not == FALSE) gd_148 = (gda_664[0][0] - gda_664[0][1] - (gda_664[1][1] - gda_664[1][0])) * Lot / gd_236;
               }
            }
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) > gd_236 && gia_672[1] == 0 && gia_676[1] == 0 && MathAbs(gda_664[2][1] - gda_664[2][0]) > gd_236 && MathAbs(MathAbs(gda_664[0][1] - gda_664[0][0]) - MathAbs(gda_664[2][1] - gda_664[2][0])) > gd_236 ||
               (gda_664[0][0] - gda_664[0][1] > gd_220 && gda_664[2][0] - gda_664[2][1] > gd_220) || (gda_664[0][1] - gda_664[0][0] > gd_220 && gda_664[2][1] - gda_664[2][0] > gd_220) && Exit_mode == FALSE) {
               if (gda_664[0][0] + gda_664[2][0] - gda_664[0][1] - gda_664[2][1] < gd_220) {
                  gia_572[0] = 1;
                  gia_572[1] = 2;
                  gia_572[2] = 1;
                  if (gda_664[0][1] - gda_664[0][0] - (gda_664[2][0] - gda_664[2][1]) > gd_236 && gi_188 && LotConst_or_not) gd_164 = (gda_664[0][1] - gda_664[0][0] - (gda_664[2][0] - gda_664[2][1])) * RiskPercent / gd_236;
                  if (gda_664[0][1] - gda_664[0][0] - (gda_664[2][0] - gda_664[2][1]) > gd_236 && gi_188 && LotConst_or_not == FALSE) gd_148 = (gda_664[0][1] - gda_664[0][0] - (gda_664[2][0] - gda_664[2][1])) * Lot / gd_236;
               }
               if (gda_664[0][0] + gda_664[2][0] - gda_664[0][1] - gda_664[2][1] > gd_220) {
                  gia_572[0] = 1;
                  gia_572[1] = 3;
                  gia_572[2] = 1;
                  if (gda_664[0][0] - gda_664[0][1] - (gda_664[2][1] - gda_664[2][0]) > gd_236 && gi_188 && LotConst_or_not) gd_164 = (gda_664[0][0] - gda_664[0][1] - (gda_664[2][1] - gda_664[2][0])) * RiskPercent / gd_236;
                  if (gda_664[0][0] - gda_664[0][1] - (gda_664[2][1] - gda_664[2][0]) > gd_236 && gi_188 && LotConst_or_not == FALSE) gd_148 = (gda_664[0][0] - gda_664[0][1] - (gda_664[2][1] - gda_664[2][0])) * Lot / gd_236;
               }
            }
            if (gia_672[0] == 0 && gia_676[0] == 0 && MathAbs(gda_664[1][1] - gda_664[1][0]) > gd_236 && MathAbs(gda_664[2][1] - gda_664[2][0]) > gd_236 && MathAbs(MathAbs(gda_664[1][1] - gda_664[1][0]) - MathAbs(gda_664[2][1] - gda_664[2][0])) > gd_236 ||
               (gda_664[1][0] - gda_664[1][1] > gd_220 && gda_664[2][0] - gda_664[2][1] > gd_220) || (gda_664[1][1] - gda_664[1][0] > gd_220 && gda_664[2][1] - gda_664[2][0] > gd_220) && Exit_mode == FALSE) {
               if (gda_664[1][0] + gda_664[2][0] - gda_664[1][1] - gda_664[2][1] < gd_220) {
                  gia_572[0] = 2;
                  gia_572[1] = 1;
                  gia_572[2] = 1;
                  if (gda_664[1][1] - gda_664[1][0] - (gda_664[2][0] - gda_664[2][1]) > gd_236 && gi_188 && LotConst_or_not) gd_164 = (gda_664[1][1] - gda_664[1][0] - (gda_664[2][0] - gda_664[2][1])) * RiskPercent / gd_236;
                  if (gda_664[1][1] - gda_664[1][0] - (gda_664[2][0] - gda_664[2][1]) > gd_236 && gi_188 && LotConst_or_not == FALSE) gd_148 = (gda_664[1][1] - gda_664[1][0] - (gda_664[2][0] - gda_664[2][1])) * Lot / gd_236;
               }
               if (gda_664[1][0] + gda_664[2][0] - gda_664[1][1] - gda_664[2][1] > gd_220) {
                  gia_572[0] = 3;
                  gia_572[1] = 1;
                  gia_572[2] = 1;
                  if (gda_664[1][0] - gda_664[1][1] - (gda_664[2][1] - gda_664[2][0]) > gd_236 && gi_188 && LotConst_or_not) gd_164 = (gda_664[1][0] - gda_664[1][1] - (gda_664[2][1] - gda_664[2][0])) * RiskPercent / gd_236;
                  if (gda_664[1][0] - gda_664[1][1] - (gda_664[2][1] - gda_664[2][0]) > gd_236 && gi_188 && LotConst_or_not == FALSE) gd_148 = (gda_664[1][0] - gda_664[1][1] - (gda_664[2][1] - gda_664[2][0])) * Lot / gd_236;
               }
            }
         } else {
            if (g_order_open_price_428 - g_order_open_price_420 < h_D_T * point_40) ObjectDelete(g_name_556);
            if (g_order_open_price_428 - g_order_open_price_420 >= h_D_T * point_40) {
               ObjectDelete(g_name_556);
               ObjectCreate(g_name_556, OBJ_RECTANGLE, 0, Time[WindowFirstVisibleBar()], g_price_404, Time[0], g_price_412);
               ObjectSet(g_name_556, OBJPROP_COLOR, color_Trade_area_of_3rd_layer);
               ObjectSet(g_name_556, OBJPROP_STYLE, 7);
               ObjectSet(g_name_556, OBJPROP_BACK, TRUE);
            }
            if (g_order_open_price_428 - g_order_open_price_420 > h_D_T * point_40 && gd_396 > g_price_404 && gd_396 < g_price_412) {
               if (gia_672[0] > 0 || gia_676[0] > 0 && gia_672[1] > 0 || gia_676[1] > 0 && gia_672[2] == 0 && gia_676[2] == 0 && Exit_mode == FALSE) {
                  if (gda_664[0][0] + gda_664[1][0] - gda_664[0][1] - gda_664[1][1] < gd_220) {
                     gia_572[0] = 1;
                     gia_572[1] = 1;
                     gia_572[2] = 2;
                     if (gda_664[0][1] - gda_664[0][0] - (gda_664[1][0] - gda_664[1][1]) > gd_236 && gi_188 && LotConst_or_not) gd_164 = (gda_664[0][1] - gda_664[0][0] - (gda_664[1][0] - gda_664[1][1])) * RiskPercent / gd_236;
                     if (gda_664[0][1] - gda_664[0][0] - (gda_664[1][0] - gda_664[1][1]) > gd_236 && gi_188 && LotConst_or_not == FALSE) gd_148 = (gda_664[0][1] - gda_664[0][0] - (gda_664[1][0] - gda_664[1][1])) * Lot / gd_236;
                  }
                  if (gda_664[0][0] + gda_664[1][0] - gda_664[0][1] - gda_664[1][1] > gd_220) {
                     gia_572[0] = 1;
                     gia_572[1] = 1;
                     gia_572[2] = 3;
                     if (gda_664[0][0] - gda_664[0][1] - (gda_664[1][1] - gda_664[1][0]) > gd_236 && gi_188 && LotConst_or_not) gd_164 = (gda_664[0][0] - gda_664[0][1] - (gda_664[1][1] - gda_664[1][0])) * RiskPercent / gd_236;
                     if (gda_664[0][0] - gda_664[0][1] - (gda_664[1][1] - gda_664[1][0]) > gd_236 && gi_188 && LotConst_or_not == FALSE) gd_148 = (gda_664[0][0] - gda_664[0][1] - (gda_664[1][1] - gda_664[1][0])) * Lot / gd_236;
                  }
               }
               if (gia_672[0] > 0 || gia_676[0] > 0 && gia_672[1] == 0 && gia_676[1] == 0 && gia_672[1] > 0 || gia_676[1] > 0 && Exit_mode == FALSE) {
                  if (gda_664[0][0] + gda_664[2][0] - gda_664[0][1] - gda_664[2][1] < gd_220) {
                     gia_572[0] = 1;
                     gia_572[1] = 2;
                     gia_572[2] = 1;
                     if (gda_664[0][1] - gda_664[0][0] - (gda_664[2][0] - gda_664[2][1]) > gd_236 && gi_188 && LotConst_or_not) gd_164 = (gda_664[0][1] - gda_664[0][0] - (gda_664[2][0] - gda_664[2][1])) * RiskPercent / gd_236;
                     if (gda_664[0][1] - gda_664[0][0] - (gda_664[2][0] - gda_664[2][1]) > gd_236 && gi_188 && LotConst_or_not == FALSE) gd_148 = (gda_664[0][1] - gda_664[0][0] - (gda_664[2][0] - gda_664[2][1])) * Lot / gd_236;
                  }
                  if (gda_664[0][0] + gda_664[2][0] - gda_664[0][1] - gda_664[2][1] > gd_220) {
                     gia_572[0] = 1;
                     gia_572[1] = 3;
                     gia_572[2] = 1;
                     if (gda_664[0][0] - gda_664[0][1] - (gda_664[2][1] - gda_664[2][0]) > gd_236 && gi_188 && LotConst_or_not) gd_164 = (gda_664[0][0] - gda_664[0][1] - (gda_664[2][1] - gda_664[2][0])) * RiskPercent / gd_236;
                     if (gda_664[0][0] - gda_664[0][1] - (gda_664[2][1] - gda_664[2][0]) > gd_236 && gi_188 && LotConst_or_not == FALSE) gd_148 = (gda_664[0][0] - gda_664[0][1] - (gda_664[2][1] - gda_664[2][0])) * Lot / gd_236;
                  }
               }
               if (gia_672[0] == 0 && gia_676[0] == 0 && gia_672[1] > 0 || gia_676[1] > 0 && gia_672[2] > 0 || gia_676[2] > 0 && Exit_mode == FALSE) {
                  if (gda_664[1][0] + gda_664[2][0] - gda_664[1][1] - gda_664[2][1] < gd_220) {
                     gia_572[0] = 2;
                     gia_572[1] = 1;
                     gia_572[2] = 1;
                     if (gda_664[1][1] - gda_664[1][0] - (gda_664[2][0] - gda_664[2][1]) > gd_236 && gi_188 && LotConst_or_not) gd_164 = (gda_664[1][1] - gda_664[1][0] - (gda_664[2][0] - gda_664[2][1])) * RiskPercent / gd_236;
                     if (gda_664[1][1] - gda_664[1][0] - (gda_664[2][0] - gda_664[2][1]) > gd_236 && gi_188 && LotConst_or_not == FALSE) gd_148 = (gda_664[1][1] - gda_664[1][0] - (gda_664[2][0] - gda_664[2][1])) * Lot / gd_236;
                  }
                  if (gda_664[1][0] + gda_664[2][0] - gda_664[1][1] - gda_664[2][1] > gd_220) {
                     gia_572[0] = 3;
                     gia_572[1] = 1;
                     gia_572[2] = 1;
                     if (gda_664[1][0] - gda_664[1][1] - (gda_664[2][1] - gda_664[2][0]) > gd_236 && gi_188 && LotConst_or_not) gd_164 = (gda_664[1][0] - gda_664[1][1] - (gda_664[2][1] - gda_664[2][0])) * RiskPercent / gd_236;
                     if (gda_664[1][0] - gda_664[1][1] - (gda_664[2][1] - gda_664[2][0]) > gd_236 && gi_188 && LotConst_or_not == FALSE) gd_148 = (gda_664[1][0] - gda_664[1][1] - (gda_664[2][1] - gda_664[2][0])) * Lot / gd_236;
                  }
               }
            }
         }
         if (gia_672[0] > 0 || gia_676[0] > 0 && gia_672[1] > 0 || gia_676[1] > 0 && gia_672[2] > 0 || gia_676[2] > 0 && Exit_mode == FALSE) {
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) >= gd_236 && MathAbs(gda_664[1][1] - gda_664[1][0]) >= gd_236 && MathAbs(gda_664[2][1] - gda_664[2][0]) < gd_236) {
               if (gda_664[0][1] - gda_664[0][0] > gda_664[1][0] - gda_664[1][1]) {
                  gia_572[0] = 1;
                  gia_572[1] = 1;
                  gia_572[2] = 2;
               }
               if (gda_664[0][0] - gda_664[0][1] > gda_664[1][1] - gda_664[1][0]) {
                  gia_572[0] = 1;
                  gia_572[1] = 1;
                  gia_572[2] = 3;
               }
            }
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) >= gd_236 && MathAbs(gda_664[1][1] - gda_664[1][0]) < gd_236 && MathAbs(gda_664[2][1] - gda_664[2][0]) >= gd_236) {
               if (gda_664[0][1] - gda_664[0][0] > gda_664[2][0] - gda_664[2][1]) {
                  gia_572[0] = 1;
                  gia_572[1] = 2;
                  gia_572[2] = 1;
               }
               if (gda_664[0][0] - gda_664[0][1] > gda_664[2][1] - gda_664[2][0]) {
                  gia_572[0] = 1;
                  gia_572[1] = 3;
                  gia_572[2] = 1;
               }
            }
            if (MathAbs(gda_664[0][1] - gda_664[0][0]) < gd_236 && MathAbs(gda_664[1][1] - gda_664[1][0]) >= gd_236 && MathAbs(gda_664[2][1] - gda_664[2][0]) >= gd_236) {
               if (gda_664[1][1] - gda_664[1][0] > gda_664[2][0] - gda_664[2][1]) {
                  gia_572[0] = 2;
                  gia_572[1] = 1;
                  gia_572[2] = 1;
               }
               if (gda_664[1][0] - gda_664[1][1] > gda_664[2][1] - gda_664[2][0]) {
                  gia_572[0] = 3;
                  gia_572[1] = 1;
                  gia_572[2] = 1;
               }
            }
         }
      }
   }
   g_color_380 = ColorTableOnTesting;
   if (gi_108 > 0) {
      g_color_380 = Red;
      if (TorgSloy == 1) {
         if (MathAbs(gda_664[0][1] - gda_664[0][0]) > 0.0) gia_572[0] = 0;
         if (MathAbs(gda_664[1][1] - gda_664[1][0]) > 0.0) gia_572[1] = 0;
         if (MathAbs(gda_664[2][1] - gda_664[2][0]) > 0.0) gia_572[2] = 0;
      }
      if (TorgSloy == 2) {
         if (gia_672[0] == 0 && gia_676[0] == 0 && MathAbs(gda_664[1][1] - gda_664[1][0]) >= MathAbs(gda_664[2][1] - gda_664[2][0])) gia_572[1] = 0;
         else gia_572[2] = 0;
         if (gia_672[1] == 0 && gia_676[1] == 0 && MathAbs(gda_664[0][1] - gda_664[0][0]) >= MathAbs(gda_664[2][1] - gda_664[2][0])) gia_572[0] = 0;
         else gia_572[2] = 0;
         if (gia_672[2] == 0 && gia_676[2] == 0 && MathAbs(gda_664[0][1] - gda_664[0][0]) >= MathAbs(gda_664[1][1] - gda_664[1][0])) gia_572[0] = 0;
         else gia_572[1] = 0;
      }
      if (TorgSloy == 3) {
         if (MathAbs(gda_664[1][1] - gda_664[1][0]) > MathAbs(gda_664[2][1] - gda_664[2][0]) && MathAbs(gda_664[1][1] - gda_664[1][0]) > MathAbs(gda_664[0][1] - gda_664[0][0])) {
            if (MathAbs(gda_664[0][0] - gda_664[0][1] + gda_664[2][0] - gda_664[2][1]) < MathAbs(gda_664[1][0] - gda_664[1][1])) gia_572[1] = 0;
            if (MathAbs(gda_664[0][0] - gda_664[0][1] + gda_664[2][0] - gda_664[2][1]) > MathAbs(gda_664[1][0] - gda_664[1][1])) {
               if (MathAbs(gda_664[0][0] - gda_664[0][1]) > MathAbs(gda_664[2][0] - gda_664[2][1])) gia_572[0] = 0;
               if (MathAbs(gda_664[0][0] - gda_664[0][1]) <= MathAbs(gda_664[2][0] - gda_664[2][1])) gia_572[2] = 0;
            }
         }
         if (MathAbs(gda_664[2][1] - gda_664[2][0]) > MathAbs(gda_664[1][1] - gda_664[1][0]) && MathAbs(gda_664[2][1] - gda_664[2][0]) > MathAbs(gda_664[0][1] - gda_664[0][0])) {
            if (MathAbs(gda_664[0][0] - gda_664[0][1] + gda_664[1][0] - gda_664[1][1]) < MathAbs(gda_664[2][0] - gda_664[2][1])) gia_572[2] = 0;
            if (MathAbs(gda_664[0][0] - gda_664[0][1] + gda_664[1][0] - gda_664[1][1]) > MathAbs(gda_664[2][0] - gda_664[2][1])) {
               if (MathAbs(gda_664[0][0] - gda_664[0][1]) > MathAbs(gda_664[1][0] - gda_664[1][1])) gia_572[0] = 0;
               if (MathAbs(gda_664[0][0] - gda_664[0][1]) <= MathAbs(gda_664[1][0] - gda_664[1][1])) gia_572[1] = 0;
            }
         }
         if (MathAbs(gda_664[0][1] - gda_664[0][0]) > MathAbs(gda_664[1][1] - gda_664[1][0]) && MathAbs(gda_664[0][1] - gda_664[0][0]) > MathAbs(gda_664[2][1] - gda_664[2][0])) {
            if (MathAbs(gda_664[2][0] - gda_664[2][1] + gda_664[1][0] - gda_664[1][1]) < MathAbs(gda_664[0][0] - gda_664[0][1])) gia_572[0] = 0;
            if (MathAbs(gda_664[2][0] - gda_664[2][1] + gda_664[1][0] - gda_664[1][1]) > MathAbs(gda_664[0][0] - gda_664[0][1])) {
               if (MathAbs(gda_664[2][0] - gda_664[2][1]) > MathAbs(gda_664[1][0] - gda_664[1][1])) gia_572[2] = 0;
               if (MathAbs(gda_664[2][0] - gda_664[2][1]) <= MathAbs(gda_664[1][0] - gda_664[1][1])) gia_572[1] = 0;
            }
         }
      }
   }
   if ((gia_672[0] == 0 && gia_676[0] == 0) || gia_572[0] == 0) {
      ObjectDelete(gsa_552[0][0]);
      ObjectDelete(gsa_552[0][1]);
   }
   if ((gia_672[1] == 0 && gia_676[1] == 0) || gia_572[1] == 0) {
      ObjectDelete(gsa_552[1][0]);
      ObjectDelete(gsa_552[1][1]);
   }
   if ((gia_672[2] == 0 && gia_676[2] == 0) || gia_572[2] == 0) {
      ObjectDelete(gsa_552[2][0]);
      ObjectDelete(gsa_552[2][1]);
   }
   for (g_index_564 = 0; g_index_564 < gi_568; g_index_564++) {
      Sleep(100);
      if (gia_572[g_index_564] > 0) {
         gia_596[g_index_564] = Magic + g_index_564;
         if (TrallingStop * gi_808 < stoplevel_32) gia_828[g_index_564] = stoplevel_32 + 5 * gi_808;
         else gia_828[g_index_564] = TrallingStop * gi_808;
         if (TrallingStop * gi_808 < stoplevel_32) gia_832[g_index_564] = stoplevel_32 + 5 * gi_808;
         else gia_832[g_index_564] = TrallingStop * gi_808;
         gda_812[g_index_564] = gi_272 * gi_808;
         gda_816[g_index_564] = gi_272 * gi_808;
         gda_820[g_index_564] = gi_276 * gi_808;
         gda_824[g_index_564] = gi_276 * gi_808;
         gda_840[g_index_564] = hSETKY * gi_808;
         gda_848[g_index_564] = hSETKY * gi_808;
         gda_860[g_index_564] = ProtectionTP * gi_808;
         gda_864[g_index_564] = ProtectionTP * gi_808;
         gda_932[g_index_564] = TrallTP * gi_808;
         gda_936[g_index_564] = TrallTP * gi_808;
         slippage *= gi_808;
         if (LotConst_or_not) {
            gda_852[g_index_564] = gd_148;
            gda_856[g_index_564] = gd_148;
         } else {
            gda_852[g_index_564] = NormalizeDouble(ld_24 * gd_164 / 1000000.0, li_20);
            gda_856[g_index_564] = NormalizeDouble(ld_24 * gd_164 / 1000000.0, li_20);
         }
         if (gda_852[g_index_564] < ld_8) gda_852[g_index_564] = ld_8;
         if (gda_856[g_index_564] < ld_8) gda_856[g_index_564] = ld_8;
         if (((!IsOptimization()) && !IsTesting() && (!IsVisualMode())) || (ShowTableOnTesting && IsTesting() && (!IsOptimization()))) {
            f0_8();
            f0_6();
         }
         f0_4();
         if (gia_672[g_index_564] > 0) {
            li_76 = f0_0(0, gia_672[g_index_564]);
            li_80 = f0_9(0, gia_672[g_index_564]);
         }
         if (gia_676[g_index_564] > 0) {
            li_84 = f0_0(1, gia_676[g_index_564]);
            li_88 = f0_9(1, gia_676[g_index_564]);
         }
         if (gia_672[g_index_564] > 1) {
            li_92 = f0_2(0, gia_672[g_index_564], ask_56);
            li_96 = f0_3(0, gia_672[g_index_564], ask_56);
         } else {
            li_92 = FALSE;
            li_96 = FALSE;
         }
         if (gia_676[g_index_564] > 1) {
            li_100 = f0_2(1, gia_676[g_index_564], bid_48);
            li_104 = f0_3(1, gia_676[g_index_564], bid_48);
         } else {
            li_100 = FALSE;
            li_104 = FALSE;
         }
         gda_836[g_index_564] = gda_840[g_index_564] * point_40;
         gda_844[g_index_564] = gda_848[g_index_564] * point_40;
         if (Uvel_hSETKY == 0) {
            gda_836[g_index_564] = gda_840[g_index_564] * point_40;
            gda_844[g_index_564] = gda_848[g_index_564] * point_40;
         }
         if (Uvel_hSETKY == 1) {
            if (gia_672[g_index_564] < 2) gda_836[g_index_564] = gda_840[g_index_564] * point_40;
            if (gia_676[g_index_564] < 2) gda_844[g_index_564] = gda_848[g_index_564] * point_40;
            if (gia_672[g_index_564] > 1) gda_836[g_index_564] = (gda_840[g_index_564] + ShagUvel_hSETKY * (gia_672[g_index_564] - 1) * gi_808) * point_40;
            if (gia_676[g_index_564] > 1) gda_844[g_index_564] = (gda_848[g_index_564] + ShagUvel_hSETKY * (gia_676[g_index_564] - 1) * gi_808) * point_40;
         }
         if (Uvel_hSETKY == 2) {
            if (gia_672[g_index_564] < 2) gda_836[g_index_564] = gda_840[g_index_564] * point_40;
            if (gia_676[g_index_564] < 2) gda_844[g_index_564] = gda_848[g_index_564] * point_40;
            if (gia_672[g_index_564] > 1) gda_836[g_index_564] = (gda_840[g_index_564] - ShagUvel_hSETKY * (gia_672[g_index_564] - 1) * gi_808) * point_40;
            if (gia_676[g_index_564] > 1) gda_844[g_index_564] = (gda_848[g_index_564] - ShagUvel_hSETKY * (gia_676[g_index_564] - 1) * gi_808) * point_40;
         }
         if (gda_836[g_index_564] < 10 * gi_808 * point_40) gda_836[g_index_564] = 10 * gi_808 * point_40;
         if (gda_844[g_index_564] < 10 * gi_808 * point_40) gda_844[g_index_564] = 10 * gi_808 * point_40;
         gia_772[g_index_564] = 0;
         gia_780[g_index_564] = 0;
         gia_776[g_index_564] = 0;
         gia_784[g_index_564] = 0;
         if (gia_672[g_index_564] == 0) gia_772[g_index_564] = 1;
         if (gia_676[g_index_564] == 0) gia_776[g_index_564] = 1;
         if (gia_672[g_index_564] > 0 && Ask < gda_636[g_index_564][0][li_80] - gda_836[g_index_564]) gia_772[g_index_564] = 1;
         if (gia_672[g_index_564] > 0 && Ask > gda_636[g_index_564][0][li_76] + gda_836[g_index_564]) gia_772[g_index_564] = 1;
         if (gia_676[g_index_564] > 0 && Bid > gda_636[g_index_564][1][li_84] + gda_844[g_index_564]) gia_776[g_index_564] = 1;
         if (gia_676[g_index_564] > 0 && Bid < gda_636[g_index_564][1][li_88] - gda_844[g_index_564]) gia_776[g_index_564] = 1;
         if (gia_672[g_index_564] > 0 && gia_672[g_index_564] > gia_676[g_index_564] && Ask < gda_636[g_index_564][0][li_80] - gda_836[g_index_564]) gia_772[g_index_564] = 1;
         if (gia_672[g_index_564] > 0 && gia_672[g_index_564] > gia_676[g_index_564] && Ask > gda_636[g_index_564][0][li_76] + gda_836[g_index_564]) gia_772[g_index_564] = 1;
         if (gia_676[g_index_564] > 0 && gia_676[g_index_564] > gia_672[g_index_564] && Bid > gda_636[g_index_564][1][li_84] + gda_844[g_index_564]) gia_776[g_index_564] = 1;
         if (gia_676[g_index_564] > 0 && gia_676[g_index_564] > gia_672[g_index_564] && Bid < gda_636[g_index_564][1][li_88] - gda_844[g_index_564]) gia_776[g_index_564] = 1;
         gia_796[g_index_564] = 0;
         gia_800[g_index_564] = 0;
         if (gia_772[g_index_564] > 0 || gia_776[g_index_564] > 0) {
            if (gia_672[g_index_564] == 0 && gia_676[g_index_564] == 0) {
               gia_796[g_index_564] = 1;
               gia_800[g_index_564] = 1;
            }
            if (gia_672[g_index_564] == 1 && gia_676[g_index_564] == 1 && gia_772[g_index_564] == 1) gia_796[g_index_564] = 1;
            if (gia_672[g_index_564] == 1 && gia_676[g_index_564] == 1 && gia_776[g_index_564] == 1) gia_800[g_index_564] = 1;
            if (gia_672[g_index_564] > 1 && gia_676[g_index_564] > 0 && bid_48 < gda_636[g_index_564][0][li_80] - gda_836[g_index_564]) gia_796[g_index_564] = 1;
            if (gia_672[g_index_564] > 0 && gia_676[g_index_564] > 1 && bid_48 > gda_636[g_index_564][1][li_84] + gda_844[g_index_564]) gia_800[g_index_564] = 1;
            if (gia_672[g_index_564] > 0 && gia_676[g_index_564] == 0 && bid_48 < gda_636[g_index_564][0][li_80] - gda_844[g_index_564]) gia_796[g_index_564] = 1;
            if (gia_672[g_index_564] == 0 && gia_676[g_index_564] > 0 && bid_48 > gda_636[g_index_564][1][li_84] + gda_836[g_index_564]) gia_800[g_index_564] = 1;
         }
         if (gsa_576[g_index_564] == symbol_0) {
            if (gia_672[g_index_564] > gia_676[g_index_564]) {
               if (gia_572[g_index_564] != 3) {
                  ObjectCreate(gsa_552[g_index_564][0], OBJ_HLINE, 0, Time[0], gda_636[g_index_564][0][li_80] - gda_836[g_index_564]);
                  ObjectSet(gsa_552[g_index_564][0], OBJPROP_PRICE1, gda_636[g_index_564][0][li_80] - gda_836[g_index_564]);
                  ObjectSet(gsa_552[g_index_564][0], OBJPROP_COLOR, gia_548[g_index_564][0]);
                  ObjectSet(gsa_552[g_index_564][0], OBJPROP_STYLE, 7);
               }
               if (gia_572[g_index_564] != 2) {
                  ObjectCreate(gsa_552[g_index_564][1], OBJ_HLINE, 0, Time[0], gda_636[g_index_564][0][li_76] + gda_844[g_index_564]);
                  ObjectSet(gsa_552[g_index_564][1], OBJPROP_PRICE1, gda_636[g_index_564][0][li_76] + gda_844[g_index_564]);
                  ObjectSet(gsa_552[g_index_564][1], OBJPROP_COLOR, gia_548[g_index_564][1]);
                  ObjectSet(gsa_552[g_index_564][1], OBJPROP_STYLE, 7);
               }
            }
            if (gia_672[g_index_564] < gia_676[g_index_564]) {
               if (gia_572[g_index_564] != 3) {
                  ObjectCreate(gsa_552[g_index_564][0], OBJ_HLINE, 0, Time[0], gda_636[g_index_564][1][li_88] - gda_836[g_index_564]);
                  ObjectSet(gsa_552[g_index_564][0], OBJPROP_PRICE1, gda_636[g_index_564][1][li_88] - gda_836[g_index_564]);
                  ObjectSet(gsa_552[g_index_564][0], OBJPROP_COLOR, gia_548[g_index_564][0]);
                  ObjectSet(gsa_552[g_index_564][0], OBJPROP_STYLE, 7);
               }
               if (gia_572[g_index_564] != 2) {
                  ObjectCreate(gsa_552[g_index_564][1], OBJ_HLINE, 0, Time[0], gda_636[g_index_564][1][li_84] + gda_844[g_index_564]);
                  ObjectSet(gsa_552[g_index_564][1], OBJPROP_PRICE1, gda_636[g_index_564][1][li_84] + gda_844[g_index_564]);
                  ObjectSet(gsa_552[g_index_564][1], OBJPROP_COLOR, gia_548[g_index_564][1]);
                  ObjectSet(gsa_552[g_index_564][1], OBJPROP_STYLE, 7);
               }
            }
            if (gia_672[g_index_564] == gia_676[g_index_564]) {
               if (gia_572[g_index_564] != 3) {
                  ObjectCreate(gsa_552[g_index_564][0], OBJ_HLINE, 0, Time[0], gda_636[g_index_564][0][li_80] - gda_836[g_index_564]);
                  ObjectSet(gsa_552[g_index_564][0], OBJPROP_PRICE1, gda_636[g_index_564][0][li_80] - gda_836[g_index_564]);
                  ObjectSet(gsa_552[g_index_564][0], OBJPROP_COLOR, gia_548[g_index_564][0]);
                  ObjectSet(gsa_552[g_index_564][0], OBJPROP_STYLE, 7);
               }
               if (gia_572[g_index_564] != 2) {
                  ObjectCreate(gsa_552[g_index_564][1], OBJ_HLINE, 0, Time[0], gda_636[g_index_564][1][li_84] + gda_844[g_index_564]);
                  ObjectSet(gsa_552[g_index_564][1], OBJPROP_PRICE1, gda_636[g_index_564][1][li_84] + gda_844[g_index_564]);
                  ObjectSet(gsa_552[g_index_564][1], OBJPROP_COLOR, gia_548[g_index_564][1]);
                  ObjectSet(gsa_552[g_index_564][1], OBJPROP_STYLE, 7);
               }
            }
         }
         gi_unused_868 = 0;
         gi_unused_872 = 0;
         gi_876 = FALSE;
         gi_880 = FALSE;
         if (gia_772[g_index_564] == 1 && gia_796[g_index_564] == 1) gi_876 = TRUE;
         if (gia_776[g_index_564] == 1 && gia_800[g_index_564] == 1) gi_880 = TRUE;
         if ((gi_876 == TRUE && gia_572[g_index_564] == 2) || (gia_572[g_index_564] == 1 && gi_876 == TRUE || gi_880 == TRUE)) {
            if (gia_672[g_index_564] == 0 && gia_676[g_index_564] == 0) gda_656[g_index_564] = NormalizeDouble(gda_852[g_index_564], li_20);
            if (gia_672[g_index_564] > 0 && gi_876 == TRUE) gda_656[g_index_564] = NormalizeDouble(gda_632[g_index_564][0][li_80] * LotMultiplicator, li_72);
            if (gia_672[g_index_564] > 0 && gi_880 == TRUE) gda_656[g_index_564] = NormalizeDouble(gda_632[g_index_564][1][li_84] * LotMultiplicator, li_72);
            if (gia_672[g_index_564] > 0 && gia_676[g_index_564] == 0) {
               gda_656[g_index_564] = NormalizeDouble(gda_632[g_index_564][0][li_80] * LotMultiplicator, li_72);
               gda_660[g_index_564] = gda_656[g_index_564];
            }
            if (gda_656[g_index_564] < ld_8) gda_656[g_index_564] = ld_8;
            if (gda_656[g_index_564] > g_maxlot_172) gda_656[g_index_564] = g_maxlot_172;
            g_ask_500 = MarketInfo(gsa_576[g_index_564], MODE_ASK);
            gd_476 = 0;
            gd_484 = gia_548[g_index_564][0];
            gd_524 = g_ask_500 - gda_820[g_index_564] * point_40;
            if (gda_820[g_index_564] == 0.0) gd_524 = 0;
            gd_516 = g_ask_500 + gda_812[g_index_564] * point_40;
            if (gda_812[g_index_564] == 0.0 || gia_672[g_index_564] > 0) gd_516 = 0;
            gi_700 = f0_1(gsa_576[g_index_564], gd_476, gda_656[g_index_564], g_ask_500, gd_524, gd_516, gia_596[g_index_564], gd_484);
            Sleep(gi_392);
            if (gi_700 > 0) {
               gia_644[g_index_564][0][gia_672[g_index_564] + 1] = gi_700;
               gia_732[g_index_564] = 0;
            }
            if (gi_700 < 0) gia_732[g_index_564] = 1;
            gia_532[g_index_564] += gia_732[g_index_564];
            gia_732[g_index_564] = 0;
         }
         if ((gi_880 == TRUE && gia_572[g_index_564] == 3) || (gia_572[g_index_564] == 1 && gi_876 == TRUE || gi_880 == TRUE)) {
            if (gia_672[g_index_564] == 0 && gia_676[g_index_564] == 0) gda_660[g_index_564] = NormalizeDouble(gda_856[g_index_564], li_20);
            if (gia_676[g_index_564] > 0 && gi_876 == TRUE) gda_660[g_index_564] = NormalizeDouble(gda_632[g_index_564][0][li_80] * LotMultiplicator, li_72);
            if (gia_676[g_index_564] > 0 && gi_880 == TRUE) gda_660[g_index_564] = NormalizeDouble(gda_632[g_index_564][1][li_84] * LotMultiplicator, li_72);
            if (gia_672[g_index_564] == 0 && gia_676[g_index_564] > 0) {
               gda_660[g_index_564] = NormalizeDouble(gda_632[g_index_564][1][li_84] * LotMultiplicator, li_72);
               gda_656[g_index_564] = gda_660[g_index_564];
            }
            if (gda_660[g_index_564] < ld_8) gda_660[g_index_564] = ld_8;
            if (gda_660[g_index_564] > g_maxlot_172) gda_660[g_index_564] = g_maxlot_172;
            if (gi_876 == TRUE || gi_880 == TRUE) g_bid_508 = MarketInfo(gsa_576[g_index_564], MODE_BID);
            gd_476 = 1;
            gd_484 = gia_548[g_index_564][1];
            gd_524 = g_bid_508 + gda_824[g_index_564] * point_40;
            if (gda_824[g_index_564] == 0.0) gd_524 = 0;
            gd_516 = g_bid_508 - gda_816[g_index_564] * point_40;
            if (gda_816[g_index_564] == 0.0 || gia_676[g_index_564] > 0) gd_516 = 0;
            gi_700 = f0_1(gsa_576[g_index_564], gd_476, gda_660[g_index_564], g_bid_508, gd_524, gd_516, gia_596[g_index_564], gd_484);
            Sleep(gi_392);
            if (gi_700 > 0) gia_644[g_index_564][1][gia_676[g_index_564] + 1] = gi_700;
            if (gi_700 < 0) gia_736[g_index_564] = 1;
            gia_532[g_index_564] += gia_736[g_index_564];
            gia_736[g_index_564] = 0;
         }
         f0_4();
         if (gia_672[g_index_564] > 1 && gia_676[g_index_564] > 1 && Ask > gda_636[g_index_564][0][li_76] + gda_836[g_index_564]) gia_780[g_index_564] = 1;
         if (gia_676[g_index_564] > 1 && gia_672[g_index_564] > 1 && Bid < gda_636[g_index_564][1][li_88] - gda_844[g_index_564]) gia_784[g_index_564] = 1;
         gd_540 = 0;
         g_price_900 = 0;
         g_price_908 = 0;
         if (gia_780[g_index_564] == 1 || gia_740[g_index_564] == 1) {
            for (gi_720 = 1; gi_720 < gia_672[g_index_564]; gi_720++) {
               OrderSelect(gia_644[g_index_564][0][gi_720], SELECT_BY_TICKET, MODE_TRADES);
               gi_700 = OrderClose(OrderTicket(), OrderLots(), MarketInfo(gsa_576[g_index_564], MODE_BID), slippage, gia_548[g_index_564][0]);
               if (gi_700 == 1 && gia_740[g_index_564] != 1) gia_740[g_index_564] = 0;
               if (gi_700 == FALSE) gia_740[g_index_564] = 1;
               gia_532[g_index_564] += gia_740[g_index_564];
            }
            gia_740[g_index_564] = 0;
         }
         f0_4();
         gd_540 = f0_10(gia_672[g_index_564], gia_676[g_index_564]);
         if (gia_780[g_index_564] == 1 || gia_748[g_index_564] == 1) {
            for (gi_716 = 1; gi_716 <= gia_672[g_index_564]; gi_716++) {
               OrderSelect(gia_644[g_index_564][0][gi_716], SELECT_BY_TICKET, MODE_TRADES);
               if (OrderType() == OP_BUY && OrderMagicNumber() == gia_596[g_index_564] && OrderSymbol() == gsa_576[g_index_564]) {
                  if (gd_540 - gda_864[g_index_564] * point_40 != OrderStopLoss() || OrderStopLoss() == 0.0) {
                     gi_700 = OrderModify(OrderTicket(), OrderOpenPrice(), gd_540 - gda_864[g_index_564] * point_40, g_price_900, 0, gia_548[g_index_564][0]);
                     if (gi_700 == FALSE) gia_748[g_index_564] = 1;
                     if (gi_700 == 1 && gia_748[g_index_564] != 1) gia_748[g_index_564] = 0;
                  }
               }
            }
            gia_532[g_index_564] += gia_748[g_index_564];
            gia_748[g_index_564] = 0;
         }
         if (gia_780[g_index_564] == 1 || gia_752[g_index_564] == 1) {
            for (gi_716 = 1; gi_716 <= gia_676[g_index_564]; gi_716++) {
               OrderSelect(gia_644[g_index_564][1][gi_716], SELECT_BY_TICKET, MODE_TRADES);
               if (OrderType() == OP_SELL && OrderMagicNumber() == gia_596[g_index_564] && OrderSymbol() == gsa_576[g_index_564]) {
                  if (gd_540 - (gda_864[g_index_564] - spread_36) * point_40 != OrderTakeProfit() || OrderTakeProfit() == 0.0) {
                     gi_700 = OrderModify(OrderTicket(), OrderOpenPrice(), g_price_908, gd_540 - (gda_864[g_index_564] - spread_36) * point_40, 0, gia_548[g_index_564][1]);
                     if (gi_700 == FALSE) gia_752[g_index_564] = 1;
                     if (gi_700 == 1 && gia_752[g_index_564] == 0) gia_752[g_index_564] = 0;
                  }
               }
            }
            gia_532[g_index_564] += gia_752[g_index_564];
            gia_752[g_index_564] = 0;
         }
         if (gia_784[g_index_564] == 1 || gia_744[g_index_564] == 1) {
            for (gi_720 = 1; gi_720 < gia_676[g_index_564]; gi_720++) {
               OrderSelect(gia_644[g_index_564][1][gi_720], SELECT_BY_TICKET, MODE_TRADES);
               gi_700 = OrderClose(OrderTicket(), OrderLots(), MarketInfo(gsa_576[g_index_564], MODE_ASK), slippage, gia_548[g_index_564][1]);
               if (gi_700 == 1 && gia_744[g_index_564] != 1) gia_744[g_index_564] = 0;
               if (gi_700 == FALSE) gia_744[g_index_564] = 1;
               gia_532[g_index_564] += gia_744[g_index_564];
            }
            gia_744[g_index_564] = 0;
         }
         f0_4();
         gd_540 = f0_10(gia_672[g_index_564], gia_676[g_index_564]);
         if (gia_784[g_index_564] == 1 || gia_756[g_index_564] == 1) {
            for (gi_716 = 1; gi_716 <= gia_672[g_index_564]; gi_716++) {
               OrderSelect(gia_644[g_index_564][0][gi_716], SELECT_BY_TICKET, MODE_TRADES);
               if (OrderType() == OP_BUY && OrderMagicNumber() == gia_596[g_index_564] && OrderSymbol() == gsa_576[g_index_564]) {
                  if (NormalizeDouble(gd_540 + gda_860[g_index_564] * point_40, Digits) != OrderTakeProfit() || OrderTakeProfit() == 0.0) {
                     gi_700 = OrderModify(OrderTicket(), OrderOpenPrice(), NormalizeDouble(g_price_900, Digits), NormalizeDouble(gd_540 + gda_860[g_index_564] * point_40, Digits), 0,
                        gia_548[g_index_564][0]);
                     if (gi_700 == FALSE) gia_756[g_index_564] = 1;
                     if (gi_700 == 1 && gia_756[g_index_564] != 1) gia_756[g_index_564] = 0;
                  }
               }
            }
            gia_532[g_index_564] += gia_756[g_index_564];
            gia_756[g_index_564] = 0;
         }
         if (gia_784[g_index_564] == 1 || gia_760[g_index_564] == 1) {
            for (gi_716 = 1; gi_716 <= gia_676[g_index_564]; gi_716++) {
               OrderSelect(gia_644[g_index_564][1][gi_716], SELECT_BY_TICKET, MODE_TRADES);
               if (OrderType() == OP_SELL && OrderMagicNumber() == gia_596[g_index_564] && OrderSymbol() == gsa_576[g_index_564]) {
                  if (NormalizeDouble(gd_540 + (gda_860[g_index_564] + spread_36) * point_40, Digits) != OrderStopLoss() || OrderStopLoss() == 0.0) {
                     gi_700 = OrderModify(OrderTicket(), OrderOpenPrice(), NormalizeDouble(gd_540 + (gda_860[g_index_564] + spread_36) * point_40, Digits), NormalizeDouble(g_price_908,
                        Digits), 0, gia_548[g_index_564][1]);
                     if (gi_700 == FALSE) gia_760[g_index_564] = 1;
                     if (gi_700 == 1 && gia_760[g_index_564] != 1) gia_760[g_index_564] = 0;
                  }
               }
            }
            gia_532[g_index_564] += gia_760[g_index_564];
            gia_760[g_index_564] = 0;
         }
         gsa_88[g_index_564] = "��������. �� - �����";
         f0_4();
         if ((gia_672[g_index_564] > 1 && gia_676[g_index_564] > 1) || (gia_672[g_index_564] > 1 && gia_676[g_index_564] > 0 && gda_648[g_index_564][1][gia_676[g_index_564]] == 0.0 &&
            gda_652[g_index_564][1][gia_676[g_index_564]] == 0.0) || (gia_672[g_index_564] > 0 && gia_676[g_index_564] > 1 && gda_648[g_index_564][0][gia_672[g_index_564]] == 0.0 && gda_652[g_index_564][0][gia_672[g_index_564]] == 0.0)) {
            price_108 = NormalizeDouble(f0_7(gia_672[g_index_564], 0) + gda_932[g_index_564] * point_40, Digits);
            price_116 = NormalizeDouble(f0_7(gia_676[g_index_564], 1) - gda_936[g_index_564] * point_40, Digits);
            gsa_88[g_index_564] = "������ �� TrallingStop";
         }
         if ((gia_672[g_index_564] > 0 && gia_676[g_index_564] == 0) || (gia_672[g_index_564] > 1 && gia_676[g_index_564] > 1 || (gia_672[g_index_564] > 1 && gia_676[g_index_564] > 0 &&
            gda_648[g_index_564][1][gia_676[g_index_564]] == 0.0 && gda_652[g_index_564][1][gia_676[g_index_564]] == 0.0))) {
            g_price_916 = NormalizeDouble(f0_10(gia_672[g_index_564], gia_676[g_index_564]) + gda_932[g_index_564] * point_40, Digits);
            g_price_900 = 0;
            if (gia_672[g_index_564] > 1 && gia_676[g_index_564] > 1) {
               g_price_916 = price_108;
               g_price_900 = 0;
            }
            gsa_88[g_index_564] = "�������� ������ BUY";
            if (gsa_576[g_index_564] == symbol_0) {
               ObjectCreate("UBB_" + g_index_564, OBJ_HLINE, 0, Time[0], g_price_916);
               ObjectSet("UBB_" + g_index_564, OBJPROP_PRICE1, g_price_916);
               ObjectSet("UBB_" + g_index_564, OBJPROP_COLOR, gia_548[g_index_564][0]);
               ObjectSet("UBB_" + g_index_564, OBJPROP_WIDTH, 2);
               ObjectCreate("SLB_" + g_index_564, OBJ_HLINE, 0, Time[0], bid_48 - gia_828[g_index_564] * point_40);
               ObjectSet("SLB_" + g_index_564, OBJPROP_PRICE1, bid_48 - gia_828[g_index_564] * point_40);
               ObjectSet("SLB_" + g_index_564, OBJPROP_COLOR, gia_548[g_index_564][0]);
               ObjectSet("SLB_" + g_index_564, OBJPROP_STYLE, STYLE_DASHDOT);
            }
            if (gia_672[g_index_564] > 0 && g_price_916 < bid_48 - gia_828[g_index_564] * point_40) {
               for (gi_716 = 1; gi_716 <= gia_672[g_index_564]; gi_716++) {
                  OrderSelect(gia_644[g_index_564][0][gi_716], SELECT_BY_TICKET, MODE_TRADES);
                  if (OrderType() == OP_BUY && OrderMagicNumber() == gia_596[g_index_564] && OrderSymbol() == gsa_576[g_index_564]) {
                     if (bid_48 - gia_828[g_index_564] * point_40 > OrderStopLoss() || OrderStopLoss() == 0.0) {
                        gi_700 = OrderModify(gia_644[g_index_564][0][gi_716], OrderOpenPrice(), NormalizeDouble(bid_48 - gia_828[g_index_564] * point_40, Digits), NormalizeDouble(g_price_900,
                           Digits), 0, gia_548[g_index_564][0]);
                     }
                  }
               }
            }
         } else {
            ObjectDelete("UBB_" + g_index_564);
            ObjectDelete("SLB_" + g_index_564);
         }
         if ((gia_672[g_index_564] == 0 && gia_676[g_index_564] > 0) || (gia_672[g_index_564] > 1 && gia_676[g_index_564] > 1 || (gia_672[g_index_564] > 0 && gia_676[g_index_564] > 1 &&
            gda_648[g_index_564][0][gia_672[g_index_564]] == 0.0 && gda_652[g_index_564][0][gia_672[g_index_564]] == 0.0))) {
            g_price_924 = NormalizeDouble(f0_10(gia_672[g_index_564], gia_676[g_index_564]) - gda_936[g_index_564] * point_40, Digits);
            g_price_908 = 0;
            if (gia_672[g_index_564] > 1 && gia_676[g_index_564] > 1) {
               g_price_924 = price_116;
               g_price_908 = 0;
            }
            gsa_88[g_index_564] = "�������� ������ SELL";
            if (gsa_576[g_index_564] == symbol_0) {
               ObjectCreate("UBS_" + g_index_564, OBJ_HLINE, 0, Time[0], g_price_924);
               ObjectSet("UBS_" + g_index_564, OBJPROP_PRICE1, g_price_924);
               ObjectSet("UBS_" + g_index_564, OBJPROP_COLOR, gia_548[g_index_564][1]);
               ObjectSet("UBS_" + g_index_564, OBJPROP_WIDTH, 2);
               ObjectCreate("SLS_" + g_index_564, OBJ_HLINE, 0, Time[0], ask_56 + gia_832[g_index_564] * point_40);
               ObjectSet("SLS_" + g_index_564, OBJPROP_PRICE1, ask_56 + gia_832[g_index_564] * point_40);
               ObjectSet("SLS_" + g_index_564, OBJPROP_COLOR, gia_548[g_index_564][1]);
               ObjectSet("SLS_" + g_index_564, OBJPROP_STYLE, STYLE_DASHDOT);
            }
            if (gia_676[g_index_564] > 0 && g_price_924 > ask_56 + gia_832[g_index_564] * point_40) {
               for (gi_716 = 1; gi_716 <= gia_676[g_index_564]; gi_716++) {
                  OrderSelect(gia_644[g_index_564][1][gi_716], SELECT_BY_TICKET, MODE_TRADES);
                  if (OrderType() == OP_SELL && OrderMagicNumber() == gia_596[g_index_564] && OrderSymbol() == gsa_576[g_index_564]) {
                     if (ask_56 + gia_832[g_index_564] * point_40 < OrderStopLoss() || OrderStopLoss() == 0.0) {
                        gi_700 = OrderModify(gia_644[g_index_564][1][gi_716], OrderOpenPrice(), NormalizeDouble(ask_56 + gia_832[g_index_564] * point_40, Digits), NormalizeDouble(g_price_908,
                           Digits), 0, gia_548[g_index_564][1]);
                     }
                  }
               }
            }
         } else {
            ObjectDelete("UBS_" + g_index_564);
            ObjectDelete("SLS_" + g_index_564);
         }
         gia_740[g_index_564] = 0;
         gia_744[g_index_564] = 0;
         gia_748[g_index_564] = 0;
         gia_752[g_index_564] = 0;
         gia_756[g_index_564] = 0;
         gia_760[g_index_564] = 0;
         if (gia_672[g_index_564] == 1 && gia_676[g_index_564] > 1) {
            for (gi_716 = 1; gi_716 <= gia_676[g_index_564]; gi_716++) {
               if (gda_648[g_index_564][1][gi_716] == 0.0 && gda_652[g_index_564][1][gi_716] == 0.0) {
                  gia_748[g_index_564] = 1;
                  gia_752[g_index_564] = 1;
               }
            }
            if (gda_648[g_index_564][0][gia_672[g_index_564]] == 0.0 && gda_652[g_index_564][0][gia_672[g_index_564]] == 0.0) {
               gia_748[g_index_564] = 1;
               gia_752[g_index_564] = 1;
            }
         }
         if (gia_672[g_index_564] > 1 && gia_676[g_index_564] == 1) {
            for (gi_716 = 1; gi_716 <= gia_672[g_index_564]; gi_716++) {
               if (gda_648[g_index_564][0][gi_716] == 0.0 && gda_652[g_index_564][0][gi_716] == 0.0) {
                  gia_756[g_index_564] = 1;
                  gia_760[g_index_564] = 1;
               }
            }
            if (gda_648[g_index_564][1][gia_676[g_index_564]] == 0.0 && gda_652[g_index_564][1][gia_676[g_index_564]] == 0.0) {
               gia_756[g_index_564] = 1;
               gia_760[g_index_564] = 1;
            }
         }
      }
   }
   if (gia_672[0] > 0 || gia_676[0] > 0 && gia_572[0] == 0) gsa_88[0] = "���� ����������";
   if (gia_672[1] > 0 || gia_676[1] > 0 && gia_572[1] == 0) gsa_88[1] = "���� ����������";
   if (gia_672[2] > 0 || gia_676[2] > 0 && gia_572[2] == 0) gsa_88[2] = "���� ����������";
   if (gia_672[0] > 0 || gia_676[0] > 0 && gia_672[1] > 0 || gia_676[1] > 0 && gia_672[2] > 0 || gia_676[2] > 0) {
      Comment("                         +-----------------------------------------------------------------------+", 
         "\n", "                          |   ����� - ", gs_92, "   ����.������: ", gda_664[0][0] - gda_664[0][1] + (gda_664[1][0] - gda_664[1][1]) + (gda_664[2][0] - gda_664[2][1]), 
         "\n", "                         +-----------------------------------------------------------------------+", 
         "\n", "                          |   ���� ���. - ", TorgSloy, "   ������� ���. 3-�� ����: ", gd_236, 
         "\n", "                         +-----------------------------------------------------------------------+", 
         "\n", "                          |   �����. ������� 0 ���� = ", gia_672[0] + gia_676[0], " (", gia_672[0], "+", gia_676[0], ")", 
         "\n", "                          |   ����. ������ - ", gsa_88[0], 
         "\n", "                          |   ��������� �����: ", gda_664[0][0] - gda_664[0][1], 
         "\n", "                          |   ", 
         "\n", "                          |   �����. ������� 1 ���� = ", gia_672[1] + gia_676[1], " (", gia_672[1], "+", gia_676[1], ")", 
         "\n", "                          |   ����. ������ - ", gsa_88[1], 
         "\n", "                          |   ��������� �����: ", gda_664[1][0] - gda_664[1][1], 
         "\n", "                          |   ", 
         "\n", "                          |   �����. ������� 2 ���� = ", gia_672[2] + gia_676[2], " (", gia_672[2], "+", gia_676[2], ")", 
         "\n", "                          |   ����. ������ - ", gsa_88[2], 
         "\n", "                          |   ��������� �����: ", gda_664[2][0] - gda_664[2][1], 
      "\n", "                         +-----------------------------------------------------------------------+");
   }
   if (gia_672[0] > 0 || gia_676[0] > 0 && gia_672[1] > 0 || gia_676[1] > 0 && gia_672[2] == 0 && gia_676[2] == 0) {
      Comment("                         +-----------------------------------------------------------------------+", 
         "\n", "                          |   ����� - ", gs_92, "   ����.������: ", gda_664[0][0] - gda_664[0][1] + (gda_664[1][0] - gda_664[1][1]) + (gda_664[2][0] - gda_664[2][1]), 
         "\n", "                         +-----------------------------------------------------------------------+", 
         "\n", "                          |   ���� ���. - ", TorgSloy, "   ������� ���. 3-�� ����: ", gd_236, 
         "\n", "                         +-----------------------------------------------------------------------+", 
         "\n", "                          |   �����. ������� 0 ���� = ", gia_672[0] + gia_676[0], " (", gia_672[0], "+", gia_676[0], ")", 
         "\n", "                          |   ����. ������ - ", gsa_88[0], 
         "\n", "                          |   ��������� �����: ", gda_664[0][0] - gda_664[0][1], 
         "\n", "                          |   ", 
         "\n", "                          |   �����. ������� 1 ���� = ", gia_672[1] + gia_676[1], " (", gia_672[1], "+", gia_676[1], ")", 
         "\n", "                          |   ����. ������ - ", gsa_88[1], 
         "\n", "                          |   ��������� �����: ", gda_664[1][0] - gda_664[1][1], 
      "\n", "                         +-----------------------------------------------------------------------+");
   }
   if (gia_672[0] > 0 || gia_676[0] > 0 && gia_672[2] > 0 || gia_676[2] > 0 && gia_672[1] == 0 && gia_676[1] == 0) {
      Comment("                         +-----------------------------------------------------------------------+", 
         "\n", "                          |   ����� - ", gs_92, "   ����.������: ", gda_664[0][0] - gda_664[0][1] + (gda_664[1][0] - gda_664[1][1]) + (gda_664[2][0] - gda_664[2][1]), 
         "\n", "                         +-----------------------------------------------------------------------+", 
         "\n", "                          |   ���� ���. - ", TorgSloy, "   ������� ���. 3-�� ����: ", gd_236, 
         "\n", "                         +-----------------------------------------------------------------------+", 
         "\n", "                          |   �����. ������� 0 ���� = ", gia_672[0] + gia_676[0], " (", gia_672[0], "+", gia_676[0], ")", 
         "\n", "                          |   ����. ������ - ", gsa_88[0], 
         "\n", "                          |   ��������� �����: ", gda_664[0][0] - gda_664[0][1], 
         "\n", "                          |   ", 
         "\n", "                          |   �����. ������� 2 ���� = ", gia_672[2] + gia_676[2], " (", gia_672[2], "+", gia_676[2], ")", 
         "\n", "                          |   ����. ������ - ", gsa_88[2], 
         "\n", "                          |   ��������� �����: ", gda_664[2][0] - gda_664[2][1], 
      "\n", "                         +-----------------------------------------------------------------------+");
   }
   if (gia_672[0] == 0 && gia_676[0] == 0 && gia_672[1] > 0 || gia_676[1] > 0 && gia_672[2] > 0 || gia_676[2] > 0) {
      Comment("                         +-----------------------------------------------------------------------+", 
         "\n", "                          |   ����� - ", gs_92, "   ����.������: ", gda_664[0][0] - gda_664[0][1] + (gda_664[1][0] - gda_664[1][1]) + (gda_664[2][0] - gda_664[2][1]), 
         "\n", "                         +-----------------------------------------------------------------------+", 
         "\n", "                          |   ���� ���. - ", TorgSloy, "   ������� ���. 3-�� ����: ", gd_236, 
         "\n", "                         +-----------------------------------------------------------------------+", 
         "\n", "                          |   �����. ������� 1 ���� = ", gia_672[1] + gia_676[1], " (", gia_672[1], "+", gia_676[1], ")", 
         "\n", "                          |   ����. ������ - ", gsa_88[1], 
         "\n", "                          |   ��������� �����: ", gda_664[1][0] - gda_664[1][1], 
         "\n", "                          |   ", 
         "\n", "                          |   �����. ������� 2 ���� = ", gia_672[2] + gia_676[2], " (", gia_672[2], "+", gia_676[2], ")", 
         "\n", "                          |   ����. ������ - ", gsa_88[2], 
         "\n", "                          |   ��������� �����: ", gda_664[2][0] - gda_664[2][1], 
      "\n", "                         +-----------------------------------------------------------------------+");
   }
   if (gia_672[0] > 0 || gia_676[0] > 0 && gia_672[1] == 0 && gia_676[1] == 0 && gia_672[2] == 0 && gia_676[2] == 0) {
      Comment("                         +-----------------------------------------------------------------------+", 
         "\n", "                          |   ����� - ", gs_92, "   ����.������: ", gda_664[0][0] - gda_664[0][1] + (gda_664[1][0] - gda_664[1][1]) + (gda_664[2][0] - gda_664[2][1]), 
         "\n", "                         +-----------------------------------------------------------------------+", 
         "\n", "                          |   ���� ���. - ", TorgSloy, "   ������� ���. 3-�� ����: ", gd_236, 
         "\n", "                         +-----------------------------------------------------------------------+", 
         "\n", "                          |   �����. ������� 0 ���� = ", gia_672[0] + gia_676[0], " (", gia_672[0], "+", gia_676[0], ")", 
         "\n", "                          |   ����. ������ - ", gsa_88[0], 
         "\n", "                          |   ��������� �����: ", gda_664[0][0] - gda_664[0][1], 
      "\n", "                         +-----------------------------------------------------------------------+");
   }
   if (gia_672[0] == 0 && gia_676[0] == 0 && gia_672[1] > 0 || gia_676[1] > 0 && gia_672[2] == 0 && gia_676[2] == 0) {
      Comment("                         +-----------------------------------------------------------------------+", 
         "\n", "                          |   ����� - ", gs_92, "   ����.������: ", gda_664[0][0] - gda_664[0][1] + (gda_664[1][0] - gda_664[1][1]) + (gda_664[2][0] - gda_664[2][1]), 
         "\n", "                         +-----------------------------------------------------------------------+", 
         "\n", "                          |   ���� ���. - ", TorgSloy, "   ������� ���. 3-�� ����: ", gd_236, 
         "\n", "                         +-----------------------------------------------------------------------+", 
         "\n", "                          |   �����. ������� 1 ���� = ", gia_672[1] + gia_676[1], " (", gia_672[1], "+", gia_676[1], ")", 
         "\n", "                          |   ����. ������ - ", gsa_88[1], 
         "\n", "                          |   ��������� �����: ", gda_664[1][0] - gda_664[1][1], 
      "\n", "                         +-----------------------------------------------------------------------+");
   }
   if (gia_672[0] == 0 && gia_676[0] == 0 && gia_672[1] == 0 && gia_676[1] == 0 && gia_672[2] > 0 || gia_676[2] > 0) {
      Comment("                         +-----------------------------------------------------------------------+", 
         "\n", "                          |   ����� - ", gs_92, "   ����.������: ", gda_664[0][0] - gda_664[0][1] + (gda_664[1][0] - gda_664[1][1]) + (gda_664[2][0] - gda_664[2][1]), 
         "\n", "                         +-----------------------------------------------------------------------+", 
         "\n", "                          |   ���� ���. - ", TorgSloy, "   ������� ���. 3-�� ����: ", gd_236, 
         "\n", "                         +-----------------------------------------------------------------------+", 
         "\n", "                          |   �����. ������� 2 ���� = ", gia_672[2] + gia_676[2], " (", gia_672[2], "+", gia_676[2], ")", 
         "\n", "                          |   ����. ������ - ", gsa_88[2], 
         "\n", "                          |   ��������� �����: ", gda_664[2][0] - gda_664[2][1], 
      "\n", "                         +-----------------------------------------------------------------------+");
   }
   return (0);
}

int f0_1(string a_symbol_0, int a_cmd_8, double a_lots_12, double a_price_20, double a_price_28, double a_price_36, int a_magic_44, color a_color_48) {
   int ticket_52;
   ticket_52 = OrderSend(a_symbol_0, a_cmd_8, a_lots_12, a_price_20, slippage, a_price_28, a_price_36, gsa_576[g_index_564] + " MGK " + a_magic_44 + " - ����. ��. � " +
      g_index_564, a_magic_44, 0, a_color_48);
   return (ticket_52);
}

double f0_7(int ai_0, int ai_4) {
   double ld_8 = 0;
   double ld_16 = 0;
   for (gi_716 = 1; gi_716 <= ai_0; gi_716++) {
      ld_8 += gda_636[g_index_564][ai_4][gi_716] * gda_632[g_index_564][ai_4][gi_716];
      ld_16 += gda_632[g_index_564][ai_4][gi_716];
   }
   if (ld_16 == 0.0) return (0);
   return (ld_8 / ld_16);
}

double f0_10(int ai_0, int ai_4) {
   double ld_8 = 0;
   double ld_16 = 0;
   double ld_24 = 0;
   double ld_32 = 0;
   for (gi_716 = 1; gi_716 <= ai_0; gi_716++) {
      ld_8 += gda_636[g_index_564][0][gi_716] * gda_632[g_index_564][0][gi_716];
      ld_16 += gda_632[g_index_564][0][gi_716];
   }
   for (gi_716 = 1; gi_716 <= ai_4; gi_716++) {
      ld_24 += gda_636[g_index_564][1][gi_716] * gda_632[g_index_564][1][gi_716];
      ld_32 += gda_632[g_index_564][1][gi_716];
   }
   if (ld_16 - ld_32 == 0.0) return (0);
   return ((ld_8 - ld_24) / (ld_16 - ld_32));
}

int f0_9(int ai_0, int ai_4) {
   int li_ret_16;
   double ld_8 = 1000;
   for (gi_716 = 1; gi_716 <= ai_4; gi_716++) {
      if (ld_8 >= gda_636[g_index_564][ai_0][gi_716]) {
         ld_8 = gda_636[g_index_564][ai_0][gi_716];
         li_ret_16 = gi_716;
      }
   }
   return (li_ret_16);
}

int f0_0(int ai_0, int ai_4) {
   int li_ret_16;
   double ld_8 = 0;
   for (gi_716 = 1; gi_716 <= ai_4; gi_716++) {
      if (ld_8 <= gda_636[g_index_564][ai_0][gi_716]) {
         ld_8 = gda_636[g_index_564][ai_0][gi_716];
         li_ret_16 = gi_716;
      }
   }
   return (li_ret_16);
}

int f0_2(int ai_0, int ai_4, double ad_8) {
   int li_ret_24;
   double ld_16 = 1000;
   for (gi_716 = 1; gi_716 <= ai_4; gi_716++) {
      if (ld_16 >= gda_636[g_index_564][ai_0][gi_716] && ad_8 <= gda_636[g_index_564][ai_0][gi_716]) {
         ld_16 = gda_636[g_index_564][ai_0][gi_716];
         li_ret_24 = gi_716;
      }
   }
   return (li_ret_24);
}

int f0_3(int ai_0, int ai_4, double ad_8) {
   int li_ret_24;
   double ld_16 = 0;
   for (gi_716 = 1; gi_716 <= ai_4; gi_716++) {
      if (ld_16 <= gda_636[g_index_564][ai_0][gi_716] && ad_8 >= gda_636[g_index_564][ai_0][gi_716]) {
         ld_16 = gda_636[g_index_564][ai_0][gi_716];
         li_ret_24 = gi_716;
      }
   }
   return (li_ret_24);
}

void f0_4() {
   gia_672[g_index_564] = 0;
   gia_676[g_index_564] = 0;
   gia_680[g_index_564] = 0;
   gia_684[g_index_564] = 0;
   int count_0 = 0;
   int count_4 = 0;
   int count_8 = 0;
   int count_12 = 0;
   int count_16 = 0;
   int count_20 = 0;
   gda_664[g_index_564][0] = 0;
   gda_664[g_index_564][1] = 0;
   gda_664[g_index_564][2] = 0;
   gda_664[g_index_564][3] = 0;
   gda_668[g_index_564][0] = 0;
   gda_668[g_index_564][1] = 0;
   g_order_total_704 = OrdersTotal();
   for (g_pos_696 = 0; g_pos_696 < g_order_total_704; g_pos_696++) {
      OrderSelect(g_pos_696, SELECT_BY_POS, MODE_TRADES);
      if (OrderType() == OP_BUY && OrderMagicNumber() == gia_596[g_index_564] && OrderSymbol() == gsa_576[g_index_564]) {
         count_0++;
         gia_644[g_index_564][0][count_0] = OrderTicket();
         gda_636[g_index_564][0][count_0] = OrderOpenPrice();
         gda_632[g_index_564][0][count_0] = OrderLots();
         gda_640[g_index_564][0][count_0] = OrderProfit();
         gda_648[g_index_564][0][count_0] = OrderTakeProfit();
         gda_652[g_index_564][0][count_0] = OrderStopLoss();
         gda_668[g_index_564][0] += gda_640[g_index_564][0][count_0];
         gda_664[g_index_564][0] += gda_632[g_index_564][0][count_0];
         if (gda_652[g_index_564][0][count_0] > 0.0) count_8++;
      }
      if (OrderType() == OP_SELL && OrderMagicNumber() == gia_596[g_index_564] && OrderSymbol() == gsa_576[g_index_564]) {
         count_4++;
         gia_644[g_index_564][1][count_4] = OrderTicket();
         gda_636[g_index_564][1][count_4] = OrderOpenPrice();
         gda_632[g_index_564][1][count_4] = OrderLots();
         gda_640[g_index_564][1][count_4] = OrderProfit();
         gda_648[g_index_564][1][count_4] = OrderTakeProfit();
         gda_652[g_index_564][1][count_4] = OrderStopLoss();
         gda_668[g_index_564][1] += gda_640[g_index_564][1][count_4];
         gda_664[g_index_564][1] += gda_632[g_index_564][1][count_4];
         if (gda_652[g_index_564][1][count_4] > 0.0) count_12++;
      }
      if (OrderType() == OP_BUYSTOP && OrderMagicNumber() == gia_596[g_index_564] && OrderSymbol() == gsa_576[g_index_564]) {
         count_16++;
         gia_644[g_index_564][2][count_16] = OrderTicket();
         gda_636[g_index_564][2][count_16] = OrderOpenPrice();
         gda_648[g_index_564][2][count_16] = OrderTakeProfit();
         gda_652[g_index_564][2][count_16] = OrderStopLoss();
         gda_632[g_index_564][2][count_16] = OrderLots();
         gda_664[g_index_564][2] += gda_632[g_index_564][2][count_16];
      }
      if (OrderType() == OP_SELLSTOP && OrderMagicNumber() == gia_596[g_index_564] && OrderSymbol() == gsa_576[g_index_564]) {
         count_20++;
         gda_636[g_index_564][3][count_20] = OrderOpenPrice();
         gia_644[g_index_564][3][count_20] = OrderTicket();
         gda_648[g_index_564][3][count_20] = OrderTakeProfit();
         gda_652[g_index_564][3][count_20] = OrderStopLoss();
         gda_632[g_index_564][3][count_20] = OrderLots();
         gda_664[g_index_564][3] += gda_632[g_index_564][3][count_20];
      }
   }
   gia_672[g_index_564] = count_0;
   gia_676[g_index_564] = count_4;
   gia_688[g_index_564] = count_8;
   gia_692[g_index_564] = count_12;
   gia_680[g_index_564] = count_16;
   gia_684[g_index_564] = count_20;
}

void f0_8() {
   double ld_0 = f0_5(0);
   string name_8 = gs_80 + "1";
   if (ObjectFind(name_8) == -1) {
      ObjectCreate(name_8, OBJ_LABEL, 0, 0, 0);
      ObjectSet(name_8, OBJPROP_CORNER, 1);
      ObjectSet(name_8, OBJPROP_XDISTANCE, 10);
      ObjectSet(name_8, OBJPROP_YDISTANCE, 15);
   }
   ObjectSetText(name_8, "��������� �������: " + DoubleToStr(ld_0, 2), Text_Syze, "Courier New", ColorTableOnTesting);
   ld_0 = f0_5(1);
   name_8 = gs_80 + "2";
   if (ObjectFind(name_8) == -1) {
      ObjectCreate(name_8, OBJ_LABEL, 0, 0, 0);
      ObjectSet(name_8, OBJPROP_CORNER, 1);
      ObjectSet(name_8, OBJPROP_XDISTANCE, 10);
      ObjectSet(name_8, OBJPROP_YDISTANCE, 33);
   }
   ObjectSetText(name_8, "��������� �����: " + DoubleToStr(ld_0, 2), Text_Syze, "Courier New", ColorTableOnTesting);
   ld_0 = f0_5(2);
   name_8 = gs_80 + "3";
   if (ObjectFind(name_8) == -1) {
      ObjectCreate(name_8, OBJ_LABEL, 0, 0, 0);
      ObjectSet(name_8, OBJPROP_CORNER, 1);
      ObjectSet(name_8, OBJPROP_XDISTANCE, 10);
      ObjectSet(name_8, OBJPROP_YDISTANCE, 51);
   }
   ObjectSetText(name_8, "��������� ���������: " + DoubleToStr(ld_0, 2), Text_Syze, "Courier New", ColorTableOnTesting);
   name_8 = gs_80 + "4";
   if (ObjectFind(name_8) == -1) {
      ObjectCreate(name_8, OBJ_LABEL, 0, 0, 0);
      ObjectSet(name_8, OBJPROP_CORNER, 1);
      ObjectSet(name_8, OBJPROP_XDISTANCE, 10);
      ObjectSet(name_8, OBJPROP_YDISTANCE, 76);
   }
   ObjectSetText(name_8, "������ : " + DoubleToStr(AccountBalance(), 2), Text_Syze, "Courier New", ColorTableOnTesting);
   name_8 = gs_80 + "5";
   if (ObjectFind(name_8) == -1) {
      ObjectCreate(name_8, OBJ_LABEL, 0, 0, 0);
      ObjectSet(name_8, OBJPROP_CORNER, 1);
      ObjectSet(name_8, OBJPROP_XDISTANCE, 10);
      ObjectSet(name_8, OBJPROP_YDISTANCE, 96);
   }
   ObjectSetText(name_8, "����. ��������: " + DoubleToStr(AccountFreeMargin(), 2), Text_Syze, "Courier New", g_color_380);
   name_8 = gs_80 + "6";
   if (ObjectFind(name_8) == -1) {
      ObjectCreate(name_8, OBJ_LABEL, 0, 0, 0);
      ObjectSet(name_8, OBJPROP_CORNER, 1);
      ObjectSet(name_8, OBJPROP_XDISTANCE, 10);
      ObjectSet(name_8, OBJPROP_YDISTANCE, 116);
   }
   ObjectSetText(name_8, "�����. �������: " + DoubleToStr(gd_384, 2), Text_Syze, "Courier New", ColorTableOnTesting);
}

void f0_6() {
   string name_0 = gs_80 + "L_1";
   if (ObjectFind(name_0) == -1) {
      ObjectCreate(name_0, OBJ_LABEL, 0, 0, 0);
      ObjectSet(name_0, OBJPROP_CORNER, 0);
      ObjectSet(name_0, OBJPROP_XDISTANCE, 390);
      ObjectSet(name_0, OBJPROP_YDISTANCE, 10);
   }
   ObjectSetText(name_0, "F O R E X", 28, "Arial", ColorLogotipName);
   name_0 = gs_80 + "L_2";
   if (ObjectFind(name_0) == -1) {
      ObjectCreate(name_0, OBJ_LABEL, 0, 0, 0);
      ObjectSet(name_0, OBJPROP_CORNER, 0);
      ObjectSet(name_0, OBJPROP_XDISTANCE, 382);
      ObjectSet(name_0, OBJPROP_YDISTANCE, 50);
   }
   ObjectSetText(name_0, "I  N  V  E  S  T  O  R", 16, "Arial", ColorLogotipName);
   name_0 = gs_80 + "L_3";
   if (ObjectFind(name_0) == -1) {
      ObjectCreate(name_0, OBJ_LABEL, 0, 0, 0);
      ObjectSet(name_0, OBJPROP_CORNER, 0);
      ObjectSet(name_0, OBJPROP_XDISTANCE, 397);
      ObjectSet(name_0, OBJPROP_YDISTANCE, 75);
   }
   ObjectSetText(name_0, "www.forex-investor.net", 12, "Arial", ColorLogotipSite);
   name_0 = gs_80 + "L_4";
   if (ObjectFind(name_0) == -1) {
      ObjectCreate(name_0, OBJ_LABEL, 0, 0, 0);
      ObjectSet(name_0, OBJPROP_CORNER, 0);
      ObjectSet(name_0, OBJPROP_XDISTANCE, 382);
      ObjectSet(name_0, OBJPROP_YDISTANCE, 57);
   }
   ObjectSetText(name_0, "_____________________", 12, "Arial", Gray);
   name_0 = gs_80 + "L_5";
   if (ObjectFind(name_0) == -1) {
      ObjectCreate(name_0, OBJ_LABEL, 0, 0, 0);
      ObjectSet(name_0, OBJPROP_CORNER, 0);
      ObjectSet(name_0, OBJPROP_XDISTANCE, 382);
      ObjectSet(name_0, OBJPROP_YDISTANCE, 76);
   }
   ObjectSetText(name_0, "_____________________", 12, "Arial", Gray);
}

double f0_5(int ai_0) {
   double ld_ret_4 = 0;
   for (int pos_12 = 0; pos_12 < OrdersHistoryTotal(); pos_12++) {
      if (!(OrderSelect(pos_12, SELECT_BY_POS, MODE_HISTORY))) break;
      if (OrderSymbol() == gsa_576[g_index_564] && OrderMagicNumber() == gia_596[0] || OrderMagicNumber() == gia_596[1] || OrderMagicNumber() == gia_596[2])
         if (OrderCloseTime() >= iTime(gsa_576[g_index_564], PERIOD_D1, ai_0) && OrderCloseTime() < iTime(gsa_576[g_index_564], PERIOD_D1, ai_0) + 86400) ld_ret_4 = ld_ret_4 + OrderProfit() + OrderCommission() + OrderSwap();
   }
   return (ld_ret_4);
}