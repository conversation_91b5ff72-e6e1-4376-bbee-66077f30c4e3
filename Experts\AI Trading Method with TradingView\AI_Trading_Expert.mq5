//+------------------------------------------------------------------+
//|                                        AI_Trading_Expert.mq5 |
//|                                         Copyright ChartPrime |
//|                                                              |
//+------------------------------------------------------------------+
#property copyright "ChartPrime"
#property link      ""
#property version   "1.00"

// Подключение необходимых библиотек
#include <Trade\Trade.mqh>
#include <Trade\SymbolInfo.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\AccountInfo.mqh>
#include <Indicators\Trend.mqh>
#include <Indicators\Oscilators.mqh>

// Подключение кастомных библиотек
#include "KernelFunctions.mqh"
#include "MLExtensions.mqh"
#include "AI_Trading_Report.mqh"

// Входные параметры
input int KernelPeriod = 20;          // Период для Kernel Regression
input double BandWidth = 0.5;         // Ширина полосы для ядра
input int RSI_Period = 14;            // Период RSI
input int MFI_Period = 14;            // Период MFI
input int ML_LookbackPeriod = 50;     // Период для ML
input int LorentzianNeighbors = 8;    // Количество соседей для Lorentzian
input int MaxBarsBack = 1000;         // Максимальное количество баров для анализа
input double RiskPercent = 2.0;       // Процент риска на сделку
input int StopLoss = 50;              // Стоп-лосс в пунктах
input int TakeProfit = 100;           // Тейк-профит в пунктах
input int TrailingStop = 30;          // Трейлинг-стоп в пунктах
input int BreakEven = 40;             // Уровень безубытка в пунктах
input bool UseBreakEven = true;       // Использовать безубыток
input bool UseTrailingStop = true;    // Использовать трейлинг-стоп
input bool EnableLogging = true;      // Включить логирование

// Параметры фильтров
input int FastATR = 3;           // Период быстрого ATR (уменьшен для быстрой реакции)
input int SlowATR = 10;          // Период медленного ATR (уменьшен для текущей волатильности)
input double VolatilityRatio = 1.2; // Допустимое соотношение быстрого и медленного ATR
input bool AdaptiveVolatility = true; // Использовать адаптивную волатильность
input double RegimeThreshold = 0.001; // Порог для фильтра режима рынка
input int ADXPeriod = 14;       // Период ADX
input int ADXThreshold = 15;    // Порог ADX

// Параметры ML
input double OverBoughtLevel = 0.65;  // Уровень перекупленности
input double OverSoldLevel = 0.35;    // Уровень перепроданности
input double ADXMinLevel = 0.15;      // Минимальный уровень ADX

// Параметры для сигналов
input double RSI_UpperLevel = 65.0;     // Верхний уровень RSI
input double RSI_LowerLevel = 35.0;     // Нижний уровень RSI
input double MFI_UpperLevel = 65.0;     // Верхний уровень MFI
input double MFI_LowerLevel = 35.0;     // Нижний уровень MFI
input double TrendStrengthMin = 0.5;    // Минимальная сила тренда
input double PriceChangeMin = 0.00001;  // Минимальное изменение цены (уменьшено)

// Входные параметры для ML
input int ML_FastPeriod = 5;           // Быстрый период для ML
input int ML_SlowPeriod = 15;          // Медленный период для ML
input double ML_Threshold = 0.3;        // Порог для ML сигналов
input bool UseVolatilityFilter = true;  // Использовать фильтр волатильности для ML
input double MinPriceChange = 0.000005; // Минимальное изменение цены для сигнала
input double TrendWeight = 0.4;         // Вес трендового компонента
input double MAWeight = 0.4;            // Вес компонента средних
input double MomentumWeight = 0.2;      // Вес компонента моментума

// Объявление глобальных переменных
CTrade Trade;
CSymbolInfo SymbolInfo;
CPositionInfo PositionInfo;
CAccountInfo AccountInfo;
CAITradingReport Report;

// Флаг для контроля вычислений
bool calculation_in_progress = false;

// Объявление структуры для хранения признаков
struct CFeatures
{
   double f1;
   double f2;
   double f3;
   double f4;
   double f5;
};

// Объявление переменных для индикаторов
int KR_Handle;
int ML_Handle;
int Lorentzian_Handle;

// Буферы для индикаторов
double KR_Buffer[];
double KR_Color[];
double ML_Buffer[];
double ML_Color[];
double ML_Signal[];
double BuyBuffer[];
double SellBuffer[];
double PredictionBuffer[];
double RSI_Buffer[];
double MFI_Buffer[];

// Время последнего бара
datetime LastBarTime = 0;
bool IsNewBar = false;

// Исключаем повторное определение функций
#ifndef LORENTZIAN_FUNCTIONS_DEFINED
#define LORENTZIAN_FUNCTIONS_DEFINED

// Функция для расчета Lorentzian Distance
double LorentzianDistance(
   double f1, double f2, double f3, double f4, double f5,
   double t1, double t2, double t3, double t4, double t5,
   int feature_count)
{
   // Инициализация
   double distance = 0.0;
   double c = 0.0;
   
   // Суммирование расстояний по каждому признаку
   if(feature_count >= 1)
   {
      c = MathAbs(f1 - t1);
      distance += MathLog(1.0 + c);
   }
   
   if(feature_count >= 2)
   {
      c = MathAbs(f2 - t2);
      distance += MathLog(1.0 + c);
   }
   
   if(feature_count >= 3)
   {
      c = MathAbs(f3 - t3);
      distance += MathLog(1.0 + c);
   }
   
   if(feature_count >= 4)
   {
      c = MathAbs(f4 - t4);
      distance += MathLog(1.0 + c);
   }
   
   if(feature_count >= 5)
   {
      c = MathAbs(f5 - t5);
      distance += MathLog(1.0 + c);
   }
   
   return distance;
}

// Ядро Лапласа для ядерной регрессии
double LaplaceKernel(double distance, double bandwidth)
{
   return MathExp(-MathSqrt(distance) / bandwidth);
}

#endif // LORENTZIAN_FUNCTIONS_DEFINED

//+------------------------------------------------------------------+
//| Функции для Multi Kernel Regression                               |
//+------------------------------------------------------------------+

// Функция расчета ядра Гаусса
double GaussianKernel(double x, double bandwidth)
{
   return MathExp(-(x * x) / (2 * bandwidth * bandwidth));
}

// Функция расчета ядерной регрессии
void CalculateKernelRegression(const double &price[], double &kr_buffer[], double &kr_color[])
{
   int bars = ArraySize(price);
   
   for(int i = 0; i < bars; i++)
   {
      double weightedSum = 0;
      double weightSum = 0;
      
      for(int j = MathMax(0, i - KernelPeriod); j <= MathMin(bars - 1, i + KernelPeriod); j++)
      {
         double timeDiff = (i - j) / (double)KernelPeriod;
         double weight = GaussianKernel(timeDiff, BandWidth);
         
         weightedSum += weight * price[j];
         weightSum += weight;
      }
      
      kr_buffer[i] = weightedSum / weightSum;
      
      // Определение цвета (тренда)
      kr_color[i] = (i > 0) ? (kr_buffer[i] > kr_buffer[i-1] ? 1 : 0) : 0;
   }
}

//+------------------------------------------------------------------+
//| Функции для RSI_MFI_ML индикатора                                |
//+------------------------------------------------------------------+

// Функция расчета RSI
void CalculateRSI(const double &price[], double &rsi_buffer[])
{
   double pos = 0, neg = 0;
   
   for(int i = 1; i < ArraySize(price); i++)
   {
      double diff = price[i] - price[i-1];
      
      if(diff > 0)
         pos = (pos * (RSI_Period - 1) + diff) / RSI_Period;
      else
         neg = (neg * (RSI_Period - 1) - diff) / RSI_Period;
         
      if(i >= RSI_Period)
      {
         if(neg == 0)
            rsi_buffer[i] = 100;
         else
            rsi_buffer[i] = 100 - (100 / (1 + pos/neg));
      }
   }
}

// Функция расчета MFI
void CalculateMFI(const double &price[], const long &volume[], double &mfi_buffer[])
{
   double pos_mf = 0, neg_mf = 0;
   double typical_price[];
   ArrayResize(typical_price, ArraySize(price));
   
   // Получаем данные High, Low, Close
   double high[], low[], close[];
   ArraySetAsSeries(high, true);
   ArraySetAsSeries(low, true);
   ArraySetAsSeries(close, true);
   
   int copied = CopyHigh(Symbol(), PERIOD_CURRENT, 0, ArraySize(price), high);
   if(copied <= 0) return;
   
   copied = CopyLow(Symbol(), PERIOD_CURRENT, 0, ArraySize(price), low);
   if(copied <= 0) return;
   
   copied = CopyClose(Symbol(), PERIOD_CURRENT, 0, ArraySize(price), close);
   if(copied <= 0) return;
   
   for(int i = 1; i < ArraySize(price); i++)
   {
      typical_price[i] = (high[i] + low[i] + close[i]) / 3;
      double money_flow = typical_price[i] * (double)volume[i];
      
      if(typical_price[i] > typical_price[i-1])
         pos_mf = (pos_mf * (MFI_Period - 1) + money_flow) / MFI_Period;
      else
         neg_mf = (neg_mf * (MFI_Period - 1) + money_flow) / MFI_Period;
         
      if(i >= MFI_Period)
      {
         if(neg_mf == 0)
            mfi_buffer[i] = 100;
         else
            mfi_buffer[i] = 100 - (100 / (1 + pos_mf/neg_mf));
      }
   }
}

//+------------------------------------------------------------------+
//| Структура для хранения обучающих данных                          |
//+------------------------------------------------------------------+
struct STrainingData
{
   double features[5];  // RSI, MFI, RSI_trend, MFI_trend, Price_change
   double label;        // 1 для роста цены, -1 для падения, 0 для флета
};

//+------------------------------------------------------------------+
//| Глобальные переменные для ML                                      |
//+------------------------------------------------------------------+
STrainingData TrainingData[];  // Массив для хранения обучающих данных
int TrainingDataSize = 1000;   // Размер обучающей выборки
int K_Neighbors = 5;           // Количество ближайших соседей для KNN

//+------------------------------------------------------------------+
//| Функция для сбора обучающих данных                               |
//+------------------------------------------------------------------+
void CollectTrainingData()
{
   if(EnableLogging) Print("Начало сбора обучающих данных...");
   
   ArrayResize(TrainingData, TrainingDataSize);
   
   double close[];
   ArraySetAsSeries(close, true);
   int copied = CopyClose(Symbol(), PERIOD_CURRENT, 0, TrainingDataSize + 10, close);
   if(copied <= 0) return;
   
   // Получаем исторические значения RSI и MFI
   double rsi[], mfi[];
   ArraySetAsSeries(rsi, true);
   ArraySetAsSeries(mfi, true);
   
   int rsi_handle = iRSI(Symbol(), PERIOD_CURRENT, RSI_Period, PRICE_CLOSE);
   int mfi_handle = iMFI(Symbol(), PERIOD_CURRENT, MFI_Period, VOLUME_TICK);
   
   if(rsi_handle == INVALID_HANDLE || mfi_handle == INVALID_HANDLE) return;
   
   CopyBuffer(rsi_handle, 0, 0, TrainingDataSize + 10, rsi);
   CopyBuffer(mfi_handle, 0, 0, TrainingDataSize + 10, mfi);
   
   IndicatorRelease(rsi_handle);
   IndicatorRelease(mfi_handle);
   
   // Заполняем обучающие данные
   for(int i = 10; i < TrainingDataSize + 9; i++)
   {
      int idx = TrainingDataSize - (i - 9);
      
      // Признаки
      TrainingData[idx].features[0] = rsi[i];
      TrainingData[idx].features[1] = mfi[i];
      TrainingData[idx].features[2] = rsi[i] - rsi[i+1];  // RSI тренд
      TrainingData[idx].features[3] = mfi[i] - mfi[i+1];  // MFI тренд
      TrainingData[idx].features[4] = close[i] - close[i+1];  // Изменение цены
      
      // Метка класса (определяем по будущему движению цены)
      double future_change = close[i-1] - close[i];
      double threshold = 0.0001;  // Порог для определения значимого движения
      
      if(MathAbs(future_change) < threshold)
         TrainingData[idx].label = 0;  // Флет
      else
         TrainingData[idx].label = (future_change > 0) ? 1 : -1;  // Рост или падение
   }
   
   if(EnableLogging) Print("Обучающие данные собраны. Размер: ", TrainingDataSize);
}

//+------------------------------------------------------------------+
//| Функция для расчета расстояния между наборами признаков           |
//+------------------------------------------------------------------+
double CalculateDistance(double &features1[], double &features2[])
{
   double distance = 0;
   for(int i = 0; i < 5; i++)
   {
      double diff = features1[i] - features2[i];
      distance += diff * diff;
   }
   return MathSqrt(distance);
}

//+------------------------------------------------------------------+
//| Функция предсказания на основе KNN                                |
//+------------------------------------------------------------------+
double PredictKNN(double &current_features[])
{
   double distances[];
   double labels[];
   ArrayResize(distances, TrainingDataSize);
   ArrayResize(labels, TrainingDataSize);
   
   // Рассчитываем расстояния до всех точек в обучающей выборке
   for(int i = 0; i < TrainingDataSize; i++)
   {
      distances[i] = CalculateDistance(current_features, TrainingData[i].features);
      labels[i] = TrainingData[i].label;
   }
   
   // Сортируем расстояния и метки
   ArraySort(distances, labels);
   
   // Считаем голоса ближайших соседей
   double votes[3] = {0, 0, 0};  // для -1, 0, 1
   for(int i = 0; i < K_Neighbors; i++)
   {
      if(labels[i] == -1) votes[0]++;
      else if(labels[i] == 0) votes[1]++;
      else votes[2]++;
   }
   
   // Находим класс с максимальным количеством голосов
   int max_votes_idx = ArrayMaximum(votes);
   return max_votes_idx - 1;  // Преобразуем индекс в метку класса
}

//+------------------------------------------------------------------+
//| Функция для ML сигналов                                          |
//+------------------------------------------------------------------+
void CalculateMLSignals(const double &rsi[], const double &mfi[], double &ml_buffer[], double &ml_color[], double &ml_signal[])
{
   if(EnableLogging)
   {
      Print("--- Расчет ML сигналов ---");
   }
   
   // Получаем цены для расчета индикаторов
   double close[], high[], low[];
   ArraySetAsSeries(close, true);
   ArraySetAsSeries(high, true);
   ArraySetAsSeries(low, true);
   
   int copied = CopyClose(Symbol(), PERIOD_CURRENT, 0, ML_LookbackPeriod + 10, close);
   if(copied <= 0) return;
   
   copied = CopyHigh(Symbol(), PERIOD_CURRENT, 0, ML_LookbackPeriod + 10, high);
   if(copied <= 0) return;
   
   copied = CopyLow(Symbol(), PERIOD_CURRENT, 0, ML_LookbackPeriod + 10, low);
   if(copied <= 0) return;
   
   // Нормализуем индикаторы
   double n_rsi_value = n_rsi(close, RSI_Period, 3);
   double n_wt_value = n_wt(close, 10, 21);
   double n_cci_value = n_cci(close, 20, 3);
   double n_adx_value = n_adx(high, low, close, ADXPeriod);
   
         if(EnableLogging)
   {
      Print("Нормализованные значения индикаторов:");
      Print("n_RSI: ", n_rsi_value, ", n_WT: ", n_wt_value);
      Print("n_CCI: ", n_cci_value, ", n_ADX: ", n_adx_value);
   }
   
   // Применяем фильтры с новыми параметрами
   bool vol_filter = CheckVolatility();
   bool reg_filter = regime_filter(close[0], RegimeThreshold, true);
   bool adx_filter = filter_adx(close[0], ADXPeriod, ADXThreshold, true);
   
         if(EnableLogging)
   {
      Print("Состояние фильтров:");
      Print("Волатильность: ", vol_filter ? "активен" : "не активен");
      Print("Режим рынка: ", reg_filter ? "активен" : "не активен");
      Print("ADX: ", adx_filter ? "активен" : "не активен");
   }
   
   // Формируем сигналы на основе нормализованных индикаторов
   for(int i = ML_LookbackPeriod; i < ArraySize(rsi); i++)
   {
      double signal = 0;
      
      // Рассчитываем среднее значение индикаторов
      double avg_indicators = (n_rsi_value + n_wt_value + n_cci_value) / 3;
      
      // Условия для сигналов покупки (достаточно, чтобы 2 из 3 индикаторов подтверждали сигнал)
      int oversold_count = 0;
      if(n_rsi_value < OverSoldLevel) oversold_count++;
      if(n_wt_value < OverSoldLevel) oversold_count++;
      if(n_cci_value < OverSoldLevel) oversold_count++;
      
      // Условия для сигналов продажи
      int overbought_count = 0;
      if(n_rsi_value > OverBoughtLevel) overbought_count++;
      if(n_wt_value > OverBoughtLevel) overbought_count++;
      if(n_cci_value > OverBoughtLevel) overbought_count++;
      
      // Формируем сигналы с учетом фильтров
      if(n_adx_value >= ADXMinLevel)  // Проверяем силу тренда
      {
         if(oversold_count >= 2 && (reg_filter || vol_filter))  // Достаточно одного из фильтров
         {
            signal = 1;  // Сигнал на покупку
         }
         else if(overbought_count >= 2 && (reg_filter || vol_filter))
         {
            signal = -1; // Сигнал на продажу
         }
      }
      
      ml_buffer[i] = avg_indicators;
      ml_color[i] = signal != 0 ? 1 : 0;
      ml_signal[i] = signal;
      
      // Логирование для текущего бара
      if(i == ArraySize(rsi)-1 && EnableLogging)
      {
         string signal_type = signal == 1 ? "ПОКУПКА" : (signal == -1 ? "ПРОДАЖА" : "НЕТ СИГНАЛА");
         Print("Сформированный ML сигнал: ", signal_type);
         Print("Количество перепроданных индикаторов: ", oversold_count);
         Print("Количество перекупленных индикаторов: ", overbought_count);
         
         if(signal != 0)
         {
            color signal_color = signal > 0 ? color_green(avg_indicators) : color_red(avg_indicators);
            Print("Цвет сигнала: ", ColorToString(signal_color));
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Expert initialization function                                     |
//+------------------------------------------------------------------+
int OnInit()
{
   // Инициализация объекта торговли
   Trade.SetExpertMagicNumber(123456);
   Trade.SetMarginMode();
   Trade.SetTypeFillingBySymbol(Symbol());
   Trade.SetDeviationInPoints(10);
   
   // Инициализация символа
   if(!SymbolInfo.Name(Symbol()) || !SymbolInfo.RefreshRates()) 
   {
      Print("Ошибка при инициализации символа");
      return(INIT_FAILED);
   }
   
   // Оптимизация использования памяти - уменьшаем размеры буферов
   int bars = MathMin(1000, iBars(Symbol(), PERIOD_CURRENT));
   
   // Инициализация буферов с меньшим размером
   ArraySetAsSeries(KR_Buffer, true);
   ArraySetAsSeries(KR_Color, true);
   ArraySetAsSeries(ML_Buffer, true);
   ArraySetAsSeries(ML_Color, true);
   ArraySetAsSeries(ML_Signal, true);
   ArraySetAsSeries(RSI_Buffer, true);
   ArraySetAsSeries(MFI_Buffer, true);
   
   // Выделение памяти для буферов с оптимизированным размером
   ArrayResize(KR_Buffer, bars);
   ArrayResize(KR_Color, bars);
   ArrayResize(ML_Buffer, bars);
   ArrayResize(ML_Color, bars);
   ArrayResize(ML_Signal, bars);
   ArrayResize(RSI_Buffer, bars);
   ArrayResize(MFI_Buffer, bars);
   
   // Инициализация отчета
   if(!Report.Initialize(Symbol(), PERIOD_CURRENT))
   {
      Print("Ошибка при инициализации отчета");
      return(INIT_FAILED);
   }
   
   // Устанавливаем таймер для оптимизации вычислений
   EventSetTimer(1); // Проверка каждую секунду
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Статические переменные для оптимизации
   static datetime last_calc_time = 0;
   
   // Защита от повторных вычислений
   if(calculation_in_progress) return;
   
   // Проверка нового бара
   CheckNewBar();
   if(!IsNewBar) 
   {
      // Проверяем необходимость обновления позиций
      if(TimeCurrent() - last_calc_time > 5) // Обновляем каждые 5 секунд
      {
         ManagePositions();
         last_calc_time = TimeCurrent();
      }
      return;
   }
   
   calculation_in_progress = true;
   
   if(EnableLogging)
   {
      Print("=== Новый бар ===");
      Print("Время: ", TimeToString(TimeCurrent()));
   }
   
   // Получаем данные цен с оптимизированным количеством баров
   double close[], high[], low[];
   long volume[];
   ArraySetAsSeries(close, true);
   ArraySetAsSeries(high, true);
   ArraySetAsSeries(low, true);
   ArraySetAsSeries(volume, true);
   
   int bars_to_copy = MathMin(MaxBarsBack, 100); // Ограничиваем количество баров
   
   int copied = CopyClose(Symbol(), PERIOD_CURRENT, 0, bars_to_copy, close);
   if(copied <= 0)
   {
      if(EnableLogging) Print("Ошибка получения цен Close");
      calculation_in_progress = false;
      return;
   }
   
   // Оптимизированный расчет индикаторов
   if(!CalculateOptimizedIndicators(close))
   {
      calculation_in_progress = false;
      return;
   }
   
   // Проверка сигналов
   int signal = CheckSignals();
   
   // Открытие позиции при наличии сигнала
   if(signal != 0)
   {
      OpenPosition(signal);
   }
   
   // Управление открытыми позициями
   ManagePositions();
   
   calculation_in_progress = false;
   last_calc_time = TimeCurrent();
}

//+------------------------------------------------------------------+
//| Оптимизированный расчет индикаторов                              |
//+------------------------------------------------------------------+
bool CalculateOptimizedIndicators(const double &close[])
{
   int bars = ArraySize(close);
   if(bars < 2) return false;
   
   // Используем статические хендлы индикаторов
   static int rsi_handle = INVALID_HANDLE;
   static int mfi_handle = INVALID_HANDLE;
   
   // Инициализируем хендлы при необходимости
   if(rsi_handle == INVALID_HANDLE)
      rsi_handle = iRSI(Symbol(), PERIOD_CURRENT, RSI_Period, PRICE_CLOSE);
      
   if(mfi_handle == INVALID_HANDLE)
      mfi_handle = iMFI(Symbol(), PERIOD_CURRENT, MFI_Period, VOLUME_TICK);
      
   if(rsi_handle == INVALID_HANDLE || mfi_handle == INVALID_HANDLE)
   {
      Print("Ошибка создания хендлов индикаторов");
      return false;
   }
   
   // Копируем данные индикаторов
   if(CopyBuffer(rsi_handle, 0, 0, 2, RSI_Buffer) <= 0 ||
      CopyBuffer(mfi_handle, 0, 0, 2, MFI_Buffer) <= 0)
   {
      Print("Ошибка копирования данных индикаторов");
      return false;
   }
   
   // Упрощенный расчет ML сигналов
   CalculateSimplifiedMLSignals();
   
   return true;
}

//+------------------------------------------------------------------+
//| Упрощенный расчет ML сигналов                                    |
//+------------------------------------------------------------------+
void CalculateSimplifiedMLSignals()
{
   if(EnableLogging) Print("=== Расчет ML сигналов ===");
   
   // Инициализация сигнала
   ML_Signal[0] = 0;
   double signal = 0;
   
   // Получаем больше данных для анализа
   double rsi[], mfi[], close[], high[], low[];
   ArraySetAsSeries(rsi, true);
   ArraySetAsSeries(mfi, true);
   ArraySetAsSeries(close, true);
   ArraySetAsSeries(high, true);
   ArraySetAsSeries(low, true);
   
   // Копируем достаточно данных для расчета
   int bars_needed = MathMax(ML_SlowPeriod, 20);
   ArrayResize(rsi, bars_needed);
   ArrayResize(mfi, bars_needed);
   
   if(CopyClose(Symbol(), PERIOD_CURRENT, 0, bars_needed, close) <= 0 ||
      CopyHigh(Symbol(), PERIOD_CURRENT, 0, bars_needed, high) <= 0 ||
      CopyLow(Symbol(), PERIOD_CURRENT, 0, bars_needed, low) <= 0)
   {
      Print("Ошибка копирования ценовых данных");
      return;
   }
   
   // Копируем значения RSI и MFI
   int rsi_handle = iRSI(Symbol(), PERIOD_CURRENT, RSI_Period, PRICE_CLOSE);
   int mfi_handle = iMFI(Symbol(), PERIOD_CURRENT, MFI_Period, VOLUME_TICK);
   
   if(rsi_handle == INVALID_HANDLE || mfi_handle == INVALID_HANDLE)
   {
      Print("Ошибка получения хендлов индикаторов");
      return;
   }
   
   if(CopyBuffer(rsi_handle, 0, 0, bars_needed, rsi) <= 0 ||
      CopyBuffer(mfi_handle, 0, 0, bars_needed, mfi) <= 0)
   {
      Print("Ошибка копирования данных индикаторов");
      IndicatorRelease(rsi_handle);
      IndicatorRelease(mfi_handle);
      return;
   }
   
   IndicatorRelease(rsi_handle);
   IndicatorRelease(mfi_handle);
   
   // Рассчитываем тренды с учетом нескольких периодов и экспоненциального сглаживания
   double rsi_trend = 0;
   double mfi_trend = 0;
   double alpha = 2.0 / (3 + 1); // Коэффициент для EMA
   
   for(int i = 0; i < 3 && i < bars_needed-1; i++)
   {
      double weight = MathPow(1.0 - alpha, i);
      rsi_trend += (rsi[i] - rsi[i+1]) * weight;
      mfi_trend += (mfi[i] - mfi[i+1]) * weight;
   }
   
   // Рассчитываем средние значения с экспоненциальным сглаживанием
   double rsi_fast = 0, rsi_slow = 0;
   double mfi_fast = 0, mfi_slow = 0;
   double sum_weights_fast = 0;
   double sum_weights_slow = 0;
   
   for(int i = 0; i < ML_FastPeriod && i < bars_needed; i++)
   {
      double weight = MathPow(1.0 - alpha, i);
      rsi_fast += rsi[i] * weight;
      mfi_fast += mfi[i] * weight;
      sum_weights_fast += weight;
   }
   rsi_fast /= sum_weights_fast;
   mfi_fast /= sum_weights_fast;
   
   for(int i = 0; i < ML_SlowPeriod && i < bars_needed; i++)
   {
      double weight = MathPow(1.0 - alpha, i);
      rsi_slow += rsi[i] * weight;
      mfi_slow += mfi[i] * weight;
      sum_weights_slow += weight;
   }
   rsi_slow /= sum_weights_slow;
   mfi_slow /= sum_weights_slow;
   
   // Рассчитываем волатильность
   double volatility = 0;
   double sum_weights_vol = 0;
   
   for(int i = 1; i < bars_needed; i++)
   {
      double weight = MathPow(1.0 - alpha, i-1);
      volatility += MathAbs(high[i] - low[i]) * weight;
      sum_weights_vol += weight;
   }
   volatility /= sum_weights_vol;
   
   // Рассчитываем изменение цены с экспоненциальным сглаживанием
   double price_change = 0;
   double sum_weights_price = 0;
   
   for(int i = 0; i < 3 && i < bars_needed-1; i++)
   {
      double weight = MathPow(1.0 - alpha, i);
      price_change += (close[i] - close[i+1]) * weight;
      sum_weights_price += weight;
   }
   price_change /= sum_weights_price;
   
   // Нормализуем компоненты сигнала с учетом силы движения
   double trend_strength = MathAbs(rsi_trend + mfi_trend) / 2;
   double trend_component = trend_strength > 0.0001 ? (rsi_trend + mfi_trend) / (2 * trend_strength) : 0;
   
   double ma_strength = MathAbs(rsi_fast - rsi_slow + mfi_fast - mfi_slow) / 2;
   double ma_component = ma_strength > 0.0001 ? ((rsi_fast - rsi_slow) + (mfi_fast - mfi_slow)) / (2 * ma_strength) : 0;
   
   double momentum_component = MathAbs(price_change) > MinPriceChange ? price_change / MathAbs(price_change) : 0;
   
   if(EnableLogging)
   {
      Print("Нормализованные компоненты сигнала:");
      Print("Тренд: ", trend_component, " (", rsi_trend, "/", mfi_trend, ") Сила: ", trend_strength);
      Print("MA: ", ma_component, " (", rsi_fast-rsi_slow, "/", mfi_fast-mfi_slow, ") Сила: ", ma_strength);
      Print("Моментум: ", momentum_component, " (", price_change, ")");
   }
   
   // Взвешенный расчет силы сигнала с учетом силы компонентов
   double signal_strength = TrendWeight * trend_component * trend_strength + 
                          MAWeight * ma_component * ma_strength + 
                          MomentumWeight * momentum_component;
   
   // Проверяем согласованность компонентов и силу тренда
   bool components_aligned = (MathSign(trend_component) == MathSign(ma_component) || 
                            MathSign(ma_component) == MathSign(momentum_component));
   
   bool trend_strong = trend_strength > 0.3 && ma_strength > 0.3;
   
   if(EnableLogging)
   {
      Print("Общая сила сигнала: ", signal_strength);
      Print("Компоненты согласованы: ", components_aligned ? "Да" : "Нет");
      Print("Сила тренда: ", trend_strong ? "Сильный" : "Слабый");
   }
   
   // Формируем итоговый сигнал
   if(MathAbs(signal_strength) >= ML_Threshold)
   {
      if(!UseVolatilityFilter || volatility <= 0.0015)
      {
         if(components_aligned || trend_strong)  // Достаточно одного условия
         {
            if(signal_strength > 0 && price_change > MinPriceChange)
            {
               signal = 1;
               if(EnableLogging) Print("Формируется СИЛЬНЫЙ сигнал на покупку");
            }
            else if(signal_strength < 0 && price_change < -MinPriceChange)
            {
               signal = -1;
               if(EnableLogging) Print("Формируется СИЛЬНЫЙ сигнал на продажу");
            }
            else if(EnableLogging)
            {
               Print("Сигнал отклонен: недостаточное изменение цены (", price_change, ")");
            }
         }
         else if(EnableLogging)
         {
            if(!components_aligned) Print("Сигнал отклонен: компоненты не согласованы");
            if(!trend_strong) Print("Сигнал отклонен: слабый тренд");
         }
      }
      else if(EnableLogging)
      {
         Print("Сигнал отклонен из-за высокой волатильности");
      }
   }
   else if(EnableLogging)
   {
      Print("Сигнал слишком слабый (", signal_strength, " < ", ML_Threshold, ")");
   }
   
   ML_Signal[0] = signal;
   
   if(EnableLogging)
   {
      Print("Итоговый ML сигнал: ", signal);
      Print("=== Конец расчета ML сигналов ===");
   }
}

//+------------------------------------------------------------------+
//| Вспомогательная функция для определения знака числа              |
//+------------------------------------------------------------------+
double MathSign(double value)
{
   if(value > 0) return 1;
   if(value < 0) return -1;
   return 0;
}

//+------------------------------------------------------------------+
//| Timer function                                                    |
//+------------------------------------------------------------------+
void OnTimer()
{
   // Проверяем и обновляем позиции по таймеру
   if(!calculation_in_progress)
   {
      ManagePositions();
   }
}

//+------------------------------------------------------------------+
//| Deinit function                                                  |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Освобождаем ресурсы
   EventKillTimer();
   
   // Генерируем финальный отчет
   Report.GenerateReport();
}

//+------------------------------------------------------------------+
//| Проверка нового бара                                             |
//+------------------------------------------------------------------+
void CheckNewBar()
{
   datetime current_time = iTime(Symbol(), PERIOD_CURRENT, 0);
   IsNewBar = (current_time != LastBarTime);
   if(IsNewBar) LastBarTime = current_time;
}

//+------------------------------------------------------------------+
//| Получение значений индикаторов                                   |
//+------------------------------------------------------------------+
bool GetIndicatorValues()
{
   return CopyBuffer(KR_Handle, 0, 0, 3, KR_Buffer) > 0 &&
          CopyBuffer(KR_Handle, 1, 0, 3, KR_Color) > 0 &&
          CopyBuffer(ML_Handle, 0, 0, 3, ML_Buffer) > 0 &&
          CopyBuffer(ML_Handle, 1, 0, 3, ML_Color) > 0 &&
          CopyBuffer(ML_Handle, 2, 0, 3, ML_Signal) > 0;
}

//+------------------------------------------------------------------+
//| Расчет размера лота                                              |
//+------------------------------------------------------------------+
double CalculateLotSize(double risk_percent, double stop_loss_points)
{
   double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double tick_value = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
   double tick_size = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);
   double lot_step = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);
   double min_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
   
   if(stop_loss_points <= 0 || tick_size <= 0 || tick_value <= 0)
   {
      Print("Ошибка расчета лота: некорректные входные данные");
      return min_lot;
   }
   
   double risk_amount = account_balance * risk_percent / 100.0;
   double stop_loss_money = (stop_loss_points * _Point) / tick_size * tick_value;
   
   if(stop_loss_money <= 0)
   {
      Print("Ошибка расчета лота: некорректный размер стоп-лосса в деньгах");
      return min_lot;
   }
   
   double lot_size = risk_amount / stop_loss_money;
   lot_size = MathFloor(lot_size / lot_step) * lot_step;
   
   // Ограничиваем размер лота
   lot_size = MathMin(max_lot, MathMax(min_lot, lot_size));
   
   return lot_size;
}

//+------------------------------------------------------------------+
//| Вспомогательная функция сортировки массивов                      |
//+------------------------------------------------------------------+
void ArraySort(double &distances[], double &predictions[])
{
   int size = ArraySize(distances);
   for(int i = 0; i < size - 1; i++)
   {
      for(int j = 0; j < size - i - 1; j++)
      {
         if(distances[j] > distances[j + 1])
         {
            double tempDist = distances[j];
            distances[j] = distances[j + 1];
            distances[j + 1] = tempDist;
            
            double tempPred = predictions[j];
            predictions[j] = predictions[j + 1];
            predictions[j + 1] = tempPred;
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Функция проверки волатильности                                    |
//+------------------------------------------------------------------+
bool CheckVolatility()
{
   int atr_fast = iATR(Symbol(), PERIOD_CURRENT, FastATR);
   int atr_slow = iATR(Symbol(), PERIOD_CURRENT, SlowATR);
   
   if(atr_fast == INVALID_HANDLE || atr_slow == INVALID_HANDLE)
      return true;
      
   double fast_atr[1], slow_atr[1];
   if(CopyBuffer(atr_fast, 0, 0, 1, fast_atr) <= 0 || 
      CopyBuffer(atr_slow, 0, 0, 1, slow_atr) <= 0)
   {
      IndicatorRelease(atr_fast);
      IndicatorRelease(atr_slow);
      return true;
   }
   
   IndicatorRelease(atr_fast);
   IndicatorRelease(atr_slow);
   
   if(EnableLogging)
   {
      Print("Быстрый ATR: ", fast_atr[0], ", Медленный ATR: ", slow_atr[0]);
      Print("Соотношение ATR: ", fast_atr[0]/slow_atr[0]);
   }
   
   // При адаптивной волатильности увеличиваем допустимое соотношение
   double currentRatio = VolatilityRatio;
   if(AdaptiveVolatility)
   {
      if(slow_atr[0] > 0.0010) // Повышенная волатильность
         currentRatio *= 1.5;
      else if(slow_atr[0] < 0.0005) // Низкая волатильность
         currentRatio *= 0.8;
   }
   
   return fast_atr[0]/slow_atr[0] <= currentRatio;
}

//+------------------------------------------------------------------+
//| Проверка сигналов для открытия позиций                           |
//+------------------------------------------------------------------+
int CheckSignals()
{
   if(EnableLogging)
   {
      Print("--- Проверка сигналов ---");
      Print("RSI: ", RSI_Buffer[0], ", MFI: ", MFI_Buffer[0]);
   }
   
   int signal = 0;
   double current_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
   double prev_price = iClose(Symbol(), PERIOD_CURRENT, 1);
   double price_change = current_price - prev_price;
   
   // Проверяем волатильность и другие фильтры
   bool volatility_ok = CheckVolatility();
   bool regime_ok = regime_filter(current_price, RegimeThreshold, true);
   bool adx_ok = filter_adx(current_price, ADXPeriod, ADXThreshold, true);
   
   if(EnableLogging)
   {
      Print("Проверка фильтров:");
      Print("Волатильность: ", volatility_ok ? "в норме" : "повышенная");
      Print("Режим рынка: ", regime_ok ? "подтвержден" : "не подтвержден");
      Print("ADX: ", adx_ok ? "подтвержден" : "не подтвержден");
      Print("Изменение цены: ", price_change);
   }
   
   // Получаем значение ADX для дополнительной фильтрации
   double adx_buffer[];
   ArraySetAsSeries(adx_buffer, true);
   
   int adx_handle = iADX(Symbol(), PERIOD_CURRENT, ADXPeriod);
   if(adx_handle != INVALID_HANDLE)
   {
      if(CopyBuffer(adx_handle, 0, 0, 1, adx_buffer) > 0)
      {
         double adx_value = adx_buffer[0];
         
         // Формируем сигнал с учетом всех условий и минимального изменения цены
         if(volatility_ok && regime_ok && adx_ok && adx_value > ADXMinLevel && 
            MathAbs(price_change) >= PriceChangeMin)
         {
            double ml_signal = ML_Signal[0];
            
            // Сигнал на покупку
            if(ml_signal > 0 && price_change > 0)
            {
               signal = 1;
               if(EnableLogging)
               {
                  Print("Сформирован сигнал на покупку");
                  Print("ML сигнал: ", ml_signal);
                  Print("ADX: ", adx_value);
                  Print("Изменение цены: ", price_change);
               }
            }
            // Сигнал на продажу
            else if(ml_signal < 0 && price_change < 0)
            {
               signal = -1;
               if(EnableLogging)
               {
                  Print("Сформирован сигнал на продажу");
                  Print("ML сигнал: ", ml_signal);
                  Print("ADX: ", adx_value);
                  Print("Изменение цены: ", price_change);
               }
            }
         }
         else if(EnableLogging)
         {
            if(!volatility_ok) Print("Сигнал отклонен: повышенная волатильность");
            if(!regime_ok) Print("Сигнал отклонен: режим рынка не подтвержден");
            if(!adx_ok) Print("Сигнал отклонен: ADX не подтвержден");
            if(adx_value <= ADXMinLevel) Print("Сигнал отклонен: слабый тренд (ADX: ", adx_value, ")");
            if(MathAbs(price_change) < PriceChangeMin) Print("Сигнал отклонен: слабое изменение цены (", price_change, ")");
         }
      }
      IndicatorRelease(adx_handle);
   }
   
   // Добавляем запись о сигнале в отчет
   if(signal != 0)
   {
      Report.AddSignal(TimeCurrent(), signal, current_price, ML_Signal[0]);
   }
   
   return signal;
}

//+------------------------------------------------------------------+
//| Открытие позиции                                                  |
//+------------------------------------------------------------------+
bool OpenPosition(int signal)
{
   if(EnableLogging)
   {
      Print("--- Попытка открытия позиции ---");
      Print("Тип сигнала: ", (signal > 0 ? "Покупка" : "Продажа"));
   }
   
   // Проверяем наличие открытых позиций
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionSelectByTicket(PositionGetTicket(i)))
      {
         if(PositionGetInteger(POSITION_MAGIC) == 123456 && 
            PositionGetString(POSITION_SYMBOL) == Symbol())
         {
            if(EnableLogging) Print("Уже есть открытая позиция, пропускаем сигнал");
            return false;
         }
      }
   }
   
   // Обновляем цены
   if(!SymbolInfo.RefreshRates())
   {
      Print("Ошибка обновления цен");
      return false;
   }
   
   double ask = SymbolInfo.Ask();
   double bid = SymbolInfo.Bid();
   
   if(ask <= 0 || bid <= 0)
   {
      Print("Ошибка: некорректные цены Ask/Bid");
      return false;
   }
   
   if(EnableLogging)
   {
      Print("Текущие цены - Ask: ", ask, ", Bid: ", bid);
   }
   
   // Рассчитываем размер лота
   double lot_size = CalculateLotSize(RiskPercent, StopLoss);
   
   // Проверяем корректность размера лота
   double min_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
   lot_size = MathMin(max_lot, MathMax(min_lot, lot_size));
   
   if(EnableLogging)
   {
      Print("Рассчитанный размер лота: ", lot_size);
   }
   
   // Рассчитываем стоп-лосс и тейк-профит
   double sl = 0, tp = 0;
   
   if(signal > 0) // Сигнал на покупку
   {
      if(StopLoss > 0) sl = NormalizeDouble(ask - StopLoss * _Point, _Digits);
      if(TakeProfit > 0) tp = NormalizeDouble(ask + TakeProfit * _Point, _Digits);
      
      if(EnableLogging)
      {
         Print("Параметры Buy - Цена: ", ask, ", SL: ", sl, ", TP: ", tp);
      }
      
      // Проверяем корректность стопов
      if(sl >= ask || tp <= ask)
      {
         Print("Ошибка: некорректные уровни SL/TP для Buy");
         return false;
      }
      
      // Отправляем ордер на покупку
      if(!Trade.Buy(lot_size, Symbol(), ask, sl, tp, "AI Trading Expert"))
      {
         Print("Ошибка при открытии позиции Buy: ", GetLastError());
         return false;
      }
      else
      {
         if(EnableLogging) Print("Позиция Buy успешно открыта");
      }
   }
   else if(signal < 0) // Сигнал на продажу
   {
      if(StopLoss > 0) sl = NormalizeDouble(bid + StopLoss * _Point, _Digits);
      if(TakeProfit > 0) tp = NormalizeDouble(bid - TakeProfit * _Point, _Digits);
      
      if(EnableLogging)
      {
         Print("Параметры Sell - Цена: ", bid, ", SL: ", sl, ", TP: ", tp);
      }
      
      // Проверяем корректность стопов
      if(sl <= bid || tp >= bid)
      {
         Print("Ошибка: некорректные уровни SL/TP для Sell");
         return false;
      }
      
      // Отправляем ордер на продажу
      if(!Trade.Sell(lot_size, Symbol(), bid, sl, tp, "AI Trading Expert"))
      {
         Print("Ошибка при открытии позиции Sell: ", GetLastError());
         return false;
      }
      else
      {
         if(EnableLogging) Print("Позиция Sell успешно открыта");
      }
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Управление открытыми позициями                                    |
//+------------------------------------------------------------------+
void ManagePositions()
{
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionSelectByTicket(PositionGetTicket(i)))
      {
         // Проверяем, что это наша позиция
         if(PositionGetInteger(POSITION_MAGIC) != 123456 || 
            PositionGetString(POSITION_SYMBOL) != Symbol())
            continue;
         
         // Получаем информацию о позиции
         long position_type = PositionGetInteger(POSITION_TYPE);
         double position_price = PositionGetDouble(POSITION_PRICE_OPEN);
         double position_sl = PositionGetDouble(POSITION_SL);
         double position_tp = PositionGetDouble(POSITION_TP);
         double current_price = (position_type == POSITION_TYPE_BUY) ? 
                              SymbolInfo.Bid() : SymbolInfo.Ask();
         
         // Управление безубытком
         if(UseBreakEven && BreakEven > 0)
         {
            if(position_type == POSITION_TYPE_BUY)
            {
               if(current_price - position_price >= BreakEven * _Point && 
                  (position_sl < position_price || position_sl == 0))
               {
                  double new_sl = position_price + 5 * _Point;
                  if(Trade.PositionModify(PositionGetTicket(i), new_sl, position_tp))
                  {
                     if(EnableLogging)
                        Print("Позиция #", PositionGetTicket(i), " переведена в безубыток");
                  }
               }
            }
            else // POSITION_TYPE_SELL
            {
               if(position_price - current_price >= BreakEven * _Point && 
                  (position_sl > position_price || position_sl == 0))
               {
                  double new_sl = position_price - 5 * _Point;
                  if(Trade.PositionModify(PositionGetTicket(i), new_sl, position_tp))
                  {
                     if(EnableLogging)
                        Print("Позиция #", PositionGetTicket(i), " переведена в безубыток");
                  }
               }
            }
         }
         
         // Управление трейлинг-стопом
         if(UseTrailingStop && TrailingStop > 0)
         {
            if(position_type == POSITION_TYPE_BUY)
            {
               if(current_price - position_price > TrailingStop * _Point)
               {
                  double new_sl = current_price - TrailingStop * _Point;
                  if(new_sl > position_sl || position_sl == 0)
                  {
                     if(Trade.PositionModify(PositionGetTicket(i), new_sl, position_tp))
                     {
                        if(EnableLogging)
                           Print("Трейлинг-стоп обновлен для позиции #", PositionGetTicket(i));
                     }
                  }
               }
            }
            else // POSITION_TYPE_SELL
            {
               if(position_price - current_price > TrailingStop * _Point)
               {
                  double new_sl = current_price + TrailingStop * _Point;
                  if(new_sl < position_sl || position_sl == 0)
                  {
                     if(Trade.PositionModify(PositionGetTicket(i), new_sl, position_tp))
                     {
                        if(EnableLogging)
                           Print("Трейлинг-стоп обновлен для позиции #", PositionGetTicket(i));
                     }
                  }
               }
            }
         }
      }
   }
} 