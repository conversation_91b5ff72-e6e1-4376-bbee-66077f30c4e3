
#property copyright "G<PERSON>"
#property link      "www.sniperforex.com"

#property indicator_chart_window
#property indicator_buffers 6
#property indicator_color1 Blue
#property indicator_color2 Red
#property indicator_color3 Blue
#property indicator_color4 Red
#property indicator_color5 Blue
#property indicator_color6 Red

extern int SniperPeriod = 30;
extern int SniperType = 3;
extern int arrow_shift = 30;
extern int Alerts = 1;
double g_ibuf_92[];
double g_ibuf_96[];
double g_ibuf_100[];
double g_ibuf_104[];
double g_ibuf_108[];
double g_ibuf_112[];
bool gi_116 = TRUE;
int gi_unused_120;
bool gi_124;
bool gi_128;
string gs_shma_132 = "SHMA";
string gs_140 = "Sniper Forex, copyright GJ Pavkovich 88754897533568";

int init() {
   ObjectsDeleteAll();
   SetIndexBuffer(0, g_ibuf_92);
   SetIndexBuffer(1, g_ibuf_96);
   SetIndexBuffer(2, g_ibuf_100);
   SetIndexBuffer(3, g_ibuf_104);
   SetIndexBuffer(4, g_ibuf_108);
   SetIndexBuffer(5, g_ibuf_112);
   SetIndexStyle(0, DRAW_LINE, STYLE_SOLID, 2);
   SetIndexStyle(1, DRAW_LINE, STYLE_SOLID, 2);
   SetIndexStyle(2, DRAW_LINE, STYLE_SOLID, 2);
   SetIndexStyle(3, DRAW_LINE, STYLE_SOLID, 2);
   SetIndexStyle(4, DRAW_LINE, STYLE_SOLID, 2);
   SetIndexStyle(5, DRAW_LINE, STYLE_SOLID, 2);
   gi_116 = TRUE;
   return (0);
}

int start() {
   
   if (gi_116) {
      gi_116 = FALSE;
      for (int li_20 = Bars; li_20 >= 0; li_20--) {
         g_ibuf_92[li_20] = iCustom(Symbol(), 0, gs_shma_132, SniperPeriod, SniperType, 2, gs_140, 1, li_20);
         g_ibuf_96[li_20] = iCustom(Symbol(), 0, gs_shma_132, SniperPeriod, SniperType, 2, gs_140, 2, li_20);
         g_ibuf_100[li_20] = iCustom(Symbol(), 0, gs_shma_132, SniperPeriod, SniperType, 3, gs_140, 1, li_20);
         g_ibuf_104[li_20] = iCustom(Symbol(), 0, gs_shma_132, SniperPeriod, SniperType, 3, gs_140, 2, li_20);
         g_ibuf_108[li_20] = iCustom(Symbol(), 0, gs_shma_132, SniperPeriod, SniperType, 4, gs_140, 1, li_20);
         g_ibuf_112[li_20] = iCustom(Symbol(), 0, gs_shma_132, SniperPeriod, SniperType, 4, gs_140, 2, li_20);
         DoCheck(li_20);
      }
   } else {
      for (li_20 = 3; li_20 >= 0; li_20--) {
         g_ibuf_92[li_20] = iCustom(Symbol(), 0, gs_shma_132, SniperPeriod, SniperType, 2, gs_140, 1, li_20);
         g_ibuf_96[li_20] = iCustom(Symbol(), 0, gs_shma_132, SniperPeriod, SniperType, 2, gs_140, 2, li_20);
         g_ibuf_100[li_20] = iCustom(Symbol(), 0, gs_shma_132, SniperPeriod, SniperType, 3, gs_140, 1, li_20);
         g_ibuf_104[li_20] = iCustom(Symbol(), 0, gs_shma_132, SniperPeriod, SniperType, 3, gs_140, 2, li_20);
         g_ibuf_108[li_20] = iCustom(Symbol(), 0, gs_shma_132, SniperPeriod, SniperType, 4, gs_140, 1, li_20);
         g_ibuf_112[li_20] = iCustom(Symbol(), 0, gs_shma_132, SniperPeriod, SniperType, 4, gs_140, 2, li_20);
      }
      DoCheck(0);
      gi_unused_120 = Bars;
   }
   return (0);
}

void DoCheck(int ai_0) {
   string l_name_4;
   if (g_ibuf_108[ai_0] != EMPTY_VALUE && g_ibuf_108[ai_0 + 1] != EMPTY_VALUE && !gi_124) {
      gi_124 = TRUE;
      if (ai_0 == 0) {
         l_name_4 = "Imminent long trade alert on " + Symbol() + ", " + GetTimeFrame() + ", " + TimeToStr(TimeLocal()) + ", Bid=" + DoubleToStr(Bid, Digits);
         Alert(l_name_4);
         SendMail(l_name_4, "");
      }
   }
   if (g_ibuf_112[ai_0] != EMPTY_VALUE && g_ibuf_112[ai_0 + 1] != EMPTY_VALUE && gi_124) {
      gi_124 = FALSE;
      if (ai_0 == 0) {
         l_name_4 = "Imminent short trade alert on " + Symbol() + ", " + GetTimeFrame() + ", " + TimeToStr(TimeLocal()) + ", Bid=" + DoubleToStr(Bid, Digits);
         Alert(l_name_4);
         SendMail(l_name_4, "");
      }
   }
   if (g_ibuf_92[ai_0] != EMPTY_VALUE && g_ibuf_92[ai_0 + 1] != EMPTY_VALUE && g_ibuf_100[ai_0] != EMPTY_VALUE && g_ibuf_100[ai_0 + 1] != EMPTY_VALUE && g_ibuf_108[ai_0] != EMPTY_VALUE &&
      g_ibuf_108[ai_0 + 1] != EMPTY_VALUE && !gi_128) {
      gi_128 = TRUE;
      if (ai_0 == 0) {
         l_name_4 = "Long entry on " + Symbol() + ", " + GetTimeFrame() + ", " + TimeToStr(TimeLocal()) + ", Bid=" + DoubleToStr(Bid, Digits);
         Alert(l_name_4);
         SendMail(l_name_4, "");
      }
      l_name_4 = TimeToStr(Time[ai_0]);
      ObjectCreate(l_name_4, OBJ_ARROW, 0, Time[ai_0], g_ibuf_100[ai_0]);
      ObjectSet(l_name_4, OBJPROP_ARROWCODE, SYMBOL_ARROWUP);
      ObjectSet(l_name_4, OBJPROP_WIDTH, 2);
      ObjectSet(l_name_4, OBJPROP_COLOR, Blue);
   }
   if (g_ibuf_96[ai_0] != EMPTY_VALUE && g_ibuf_96[ai_0 + 1] != EMPTY_VALUE && g_ibuf_104[ai_0] != EMPTY_VALUE && g_ibuf_104[ai_0 + 1] != EMPTY_VALUE && g_ibuf_112[ai_0] != EMPTY_VALUE &&
      g_ibuf_112[ai_0 + 1] != EMPTY_VALUE && gi_128) {
      gi_128 = FALSE;
      if (ai_0 == 0) {
         l_name_4 = "Short entry on " + Symbol() + ", " + GetTimeFrame() + ", " + TimeToStr(TimeLocal()) + ", Bid=" + DoubleToStr(Bid, Digits);
         Alert(l_name_4);
         SendMail(l_name_4, "");
      }
      l_name_4 = TimeToStr(Time[ai_0]);
      ObjectCreate(l_name_4, OBJ_ARROW, 0, Time[ai_0], g_ibuf_96[ai_0] + arrow_shift * Point);
      ObjectSet(l_name_4, OBJPROP_ARROWCODE, SYMBOL_ARROWDOWN);
      ObjectSet(l_name_4, OBJPROP_WIDTH, 2);
      ObjectSet(l_name_4, OBJPROP_COLOR, Red);
   }
}

string GetTimeFrame() {
   string ls_ret_0;
   switch (Period()) {
   case PERIOD_M1:
      ls_ret_0 = "M1";
      break;
   case PERIOD_M5:
      ls_ret_0 = "M5";
      break;
   case PERIOD_M15:
      ls_ret_0 = "M15";
      break;
   case PERIOD_M30:
      ls_ret_0 = "M30";
      break;
   case PERIOD_H1:
      ls_ret_0 = "H1";
      break;
   case PERIOD_H4:
      ls_ret_0 = "H4";
      break;
   case PERIOD_D1:
      ls_ret_0 = "D1";
      break;
   case PERIOD_W1:
      ls_ret_0 = "W1";
      break;
   case PERIOD_MN1:
      ls_ret_0 = "MN";
   }
   return (ls_ret_0);
}