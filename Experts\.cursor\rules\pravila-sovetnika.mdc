---
description: 
globs: 
alwaysApply: true
---
// =========================
// ОБЯЗАТЕЛЬНЫЕ ПРАВИЛА ДЛЯ РАЗРАБОТКИ СОВЕТНИКОВ MT4/MT5
// (на основе анализа Investing.mq4 и лучших практик)
// =========================

1. Архитектура и модульность
1.1. Все повторяющиеся операции (открытие/закрытие ордеров, расчет лотов, логирование, обработка ошибок) реализуются через универсальные функции.
    // Пример: OpenOrder(type, lots, price, sl, tp, magic, comment)
1.2. Минимизировать использование глобальных переменных. Каждая глобальная переменная должна быть явно задокументирована (назначение, область применения).
1.3. Строгое разделение кода на секции: инициализация, обработка событий, деинициализация, вспомогательные функции.
1.4. Структура файлов и функций должна быть логичной и модульной. Каждый модуль — одна задача.
1.5. Соблюдать стандарты именования переменных и функций (CamelCase или snake_case, без сокращений).

2. Логирование и обработка ошибок
2.1. Все критические операции (торговые, вычислительные, сетевые) обязательно логируются с параметрами, кодами ошибок, временем и контекстом.
    // Пример: Print("Ошибка открытия ордера: ", GetLastError(), " lots=", lots, " price=", price);
2.2. При ошибках торговых операций реализовывать повторные попытки с задержкой и логированием каждой неудачной попытки.
    // Пример: for (int i=0; i<MAX_RETRIES; i++) { ... if (GetLastError()==...){ Sleep(1000); continue; } }
2.3. Валидация всех входных параметров с логированием ошибок.
    // Пример: if (Lots<=0) { Print("Ошибка: Lots<=0"); return; }
2.4. Формат логов должен быть единым и легко анализируемым.

3. Документирование и читаемость
3.1. Каждая функция и каждый блок логики должны иметь подробный комментарий на русском языке (назначение, параметры, возможные ошибки).
    // Пример: // Функция открытия ордера. Возвращает тикет или -1 в случае ошибки.
3.2. Комментарии обязательны для всех глобальных переменных.
3.3. Комментарии к ключевым блокам логики обязательны.
3.4. Все комментарии — только на русском языке.

4. Тестируемость и поддержка
4.1. Для всех критических функций должны быть описаны тестовые сценарии (в комментариях или отдельном тестовом файле).
    // Пример: // Тест: открыть ордер с некорректным лотом, проверить обработку ошибки
4.2. Вести changelog и историю изменений прямо в коде или отдельном файле (с датой, автором, описанием изменений).
4.3. Поддержка миграции на MT5 через препроцессорные директивы (#ifdef __MQL4__, #ifdef __MQL5__) и абстракции.
4.4. Рекомендуется автоматизация тестирования (скрипты, CI/CD, тесты на истории).

5. Архитектурные паттерны и SOLID
5.1. Использовать паттерны проектирования (Strategy, Singleton, Factory) для повышения гибкости и расширяемости кода.
5.2. Соблюдать принципы SOLID (одна задача — один модуль, легкая расширяемость, отсутствие жёстких связей).

6. Примеры и пояснения
6.1. Для каждого нового правила — пример кода и пояснение, почему это важно.
6.2. Везде, где возможно, приводить типичные ошибки и способы их предотвращения.

7. Стиль кода и структура
7.1. Соблюдать единый стиль оформления кода (отступы, пробелы, длина строки, скобки).
7.2. Все файлы должны иметь шапку с описанием, версией, автором и датой последнего изменения.
7.3. Структура проекта должна быть понятной: отдельные файлы для стратегий, вспомогательных функций, тестов, документации.

8. Changelog и контроль версий
8.1. Changelog обязателен для каждого релиза/изменения (с датой, автором, кратким описанием).
8.2. Рекомендуется использовать систему контроля версий (git или аналог).

// =========================
// Все правила обязательны к исполнению для всех новых и дорабатываемых советников.
// Нарушение любого пункта должно фиксироваться при ревью и устраняться до релиза.
// =========================

// --- РАСШИРЕННЫЕ ПРАВИЛА ДЛЯ РАЗРАБОТКИ СОВЕТНИКОВ MT4/MT5 (на основе анализа Investing.mq4) ---

// 1. Архитектура и модульность
// Обязательное выделение общих функций для всех повторяющихся операций (открытие/закрытие ордеров, расчет лотов, логирование, обработка ошибок).
// Пример: функция OpenOrder(type, lots, price, sl, tp, magic, comment) вместо дублирования кода для разных стратегий.
// Минимизация глобальных переменных. Каждая глобальная переменная должна быть явно задокументирована.
// Строгая структура секций: инициализация, обработка событий, деинициализация, вспомогательные функции.

// 2. Логирование и обработка ошибок
// Расширенное логирование всех критических операций с параметрами, кодами ошибок, временем и контекстом.
// Пример: Print("Ошибка открытия ордера: ", GetLastError(), " lots=", lots, " price=", price);
// Обработка ошибок с повторными попытками и логированием каждой неудачной попытки.
// Пример: for (int i=0; i<MAX_RETRIES; i++) { ... if (GetLastError()==...){ Sleep(1000); continue; } }
// Валидация всех входных параметров с логированием ошибок.
// Пример: if (Lots<=0) { Print("Ошибка: Lots<=0"); return; }

// 3. Документирование и читаемость
// Подробные комментарии к каждой функции: назначение, входные/выходные параметры, возможные ошибки.
// Пример: // Функция открытия ордера. Возвращает тикет или -1 в случае ошибки.
// Комментарии к ключевым блокам логики.
// Документирование глобальных переменных.

// 4. Тестируемость и поддержка
// Тестовые сценарии для ключевых функций (в комментариях или отдельном файле).
// Пример: // Тест: открыть ордер с некорректным лотом, проверить обработку ошибки
// Changelog и история изменений — обязательно вести прямо в коде или отдельном файле.
// Поддержка миграции на MT5 через препроцессорные директивы и абстракции.

// 5. Архитектурные паттерны и SOLID
// Использование паттернов проектирования (Strategy, Singleton, Factory) для повышения гибкости и расширяемости кода.
// Соблюдение принципов SOLID.

// 6. Примеры и пояснения
// Для каждого нового правила — пример кода и пояснение, почему это важно.
// Везде, где возможно, приводить типичные ошибки и способы их предотвращения.

// --- КОНЕЦ ДОПОЛНЕНИЙ ---
// Все новые и изменённые правила снабжены пояснениями и примерами для лучшего понимания.
// Усилено внимание к модульности, логированию, обработке ошибок и тестируемости.
// Добавлены требования к поддержке миграции на MT5 и ведению истории изменений.
// Все рекомендации основаны на реальных проблемах, выявленных при анализе советника Investing.mq4.
// ... существующие правила ...

