//+------------------------------------------------------------------+
//|                                     Multi_Kernel_Regression.mq5 |
//|                                         Copyright ChartPrime |
//|                                                              |
//+------------------------------------------------------------------+
#property copyright "ChartPrime"
#property link      ""
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 2
#property indicator_plots   2

//--- plot KR
#property indicator_label1  "KR"
#property indicator_type1   DRAW_LINE
#property indicator_color1  clrBlue
#property indicator_style1  STYLE_SOLID
#property indicator_width1  2

//--- plot KR_Color
#property indicator_label2  "KR_Color"
#property indicator_type2   DRAW_COLOR
#property indicator_color2  clrGreen,clrRed
#property indicator_style2  STYLE_SOLID
#property indicator_width2  2

//--- input parameters
input int      KernelPeriod = 20;          // Период для Kernel Regression
input double   BandWidth = 0.5;            // Ширина полосы для ядра
input ENUM_APPLIED_PRICE AppliedPrice = PRICE_CLOSE; // Применяемая цена

//--- indicator buffers
double         KRBuffer[];
double         KRColorBuffer[];
double         PriceBuffer[];

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- indicator buffers mapping
   SetIndexBuffer(0, KRBuffer, INDICATOR_DATA);
   SetIndexBuffer(1, KRColorBuffer, INDICATOR_COLOR_INDEX);
   
   //--- установка точности отображения
   IndicatorSetInteger(INDICATOR_DIGITS, _Digits);
   
   //--- установка пустого значения
   PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, 0.0);
   
   //--- имена для отображения в окне данных и всплывающей подсказке
   IndicatorSetString(INDICATOR_SHORTNAME, "Multi Kernel Regression");
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Функция расчета ядра Гаусса                                      |
//+------------------------------------------------------------------+
double GaussianKernel(double x, double bandwidth)
{
   return MathExp(-(x * x) / (2 * bandwidth * bandwidth));
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   //--- проверка количества баров на достаточность
   if(rates_total < KernelPeriod) return(0);
   
   //--- получаем цены в зависимости от выбранного параметра
   ArrayResize(PriceBuffer, rates_total);
   switch(AppliedPrice)
   {
      case PRICE_CLOSE:  ArrayCopy(PriceBuffer, close); break;
      case PRICE_OPEN:   ArrayCopy(PriceBuffer, open); break;
      case PRICE_HIGH:   ArrayCopy(PriceBuffer, high); break;
      case PRICE_LOW:    ArrayCopy(PriceBuffer, low); break;
      case PRICE_MEDIAN: 
         for(int i = 0; i < rates_total; i++)
            PriceBuffer[i] = (high[i] + low[i]) / 2;
         break;
      case PRICE_TYPICAL:
         for(int i = 0; i < rates_total; i++)
            PriceBuffer[i] = (high[i] + low[i] + close[i]) / 3;
         break;
      case PRICE_WEIGHTED:
         for(int i = 0; i < rates_total; i++)
            PriceBuffer[i] = (high[i] + low[i] + close[i] + close[i]) / 4;
         break;
   }
   
   //--- расчет начинаем с prev_calculated
   int start = prev_calculated > 0 ? prev_calculated - 1 : 0;
   
   //--- основной цикл расчета
   for(int i = start; i < rates_total; i++)
   {
      double weightedSum = 0;
      double weightSum = 0;
      
      //--- расчет взвешенной суммы
      for(int j = MathMax(0, i - KernelPeriod); j <= MathMin(rates_total - 1, i + KernelPeriod); j++)
      {
         double timeDiff = (i - j) / (double)KernelPeriod;
         double weight = GaussianKernel(timeDiff, BandWidth);
         
         weightedSum += weight * PriceBuffer[j];
         weightSum += weight;
      }
      
      //--- расчет значения регрессии
      KRBuffer[i] = weightedSum / weightSum;
      
      //--- определение цвета (тренда)
      KRColorBuffer[i] = (i > 0) ? (KRBuffer[i] > KRBuffer[i-1] ? 0 : 1) : 0;
   }
   
   //--- return value of prev_calculated for next call
   return(rates_total);
} 